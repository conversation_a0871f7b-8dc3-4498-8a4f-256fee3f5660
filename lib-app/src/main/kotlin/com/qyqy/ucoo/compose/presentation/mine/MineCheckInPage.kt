package com.qyqy.ucoo.compose.presentation.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.VerticalDivider
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asComposePath
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CommonButton
import com.qyqy.ucoo.compose.ui.PathShape
import com.qyqy.ucoo.utils.SimplePath

@Composable
fun MineCheckInPage(
    modifier: Modifier,
    onClick: () -> Unit = {},
) {
    val shape = with(LocalDensity.current) {
        remember {
            PathShape { size, _ ->
                SimplePath.builder()
                    .moveTo(0f, 0f)
                    .lineTo(8.dp.toPx(), 0f, 3.dp.toPx(), 3.dp.toPx())
                    .lineTo(21.dp.toPx(), 24.dp.toPx(), 3.dp.toPx(), 3.dp.toPx())
                    .lineTo(size.width, 24.dp.toPx(), 6.dp.toPx(), 6.dp.toPx())
                    .lineTo(size.width, size.height, 12.dp.toPx(), 12.dp.toPx())
                    .lineTo(0f, size.height)
                    .close()
                    .build()
                    .asComposePath()
            }
        }
    }
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(78.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxHeight()
                .weight(1f)
                .background(Color(0xFFD5BBFF), RoundedCornerShape(topStart = 12.dp, bottomStart = 12.dp)),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_sign_icon),
                contentDescription = null,
                modifier = Modifier
                    .padding(start = 12.dp)
                    .size(54.dp),
            )

            Column(
                modifier = Modifier.padding(start = 12.dp),
                verticalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                AppText(
                    text = stringResource(id = R.string.新人任务奖励),
                    color = Color(0xFF5B28C3),
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold
                )
                AppText(text = stringResource(id = R.string.你有金币待领取), color = Color(0xFF8062BC), fontSize = 14.sp)
            }
        }
        Box(
            modifier = Modifier
                .fillMaxHeight()
                .width(94.dp)
        ) {
            Box(
                modifier = Modifier
                    .padding(top = 6.dp, end = 6.dp)
                    .fillMaxWidth()
                    .height(18.dp)
                    .background(Color(0xFF8C57FC), RoundedCornerShape(topEnd = 6.dp)),
                contentAlignment = Alignment.CenterEnd
            ) {
                AppText(
                    text = stringResource(id = R.string.金币天天领),
                    modifier = Modifier.padding(end = 6.dp),
                    color = Color(0xFFEDE3FF),
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Medium,
                    letterSpacing = 1.5.sp,
                )
            }

            Spacer(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color(0xFFD5BBFF), shape)
            )

            CommonButton(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(bottom = 12.dp, end = 8.dp)
                    .size(80.dp, 30.dp),
                gradient = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))),
                shape = RoundedCornerShape(15.dp),
                onClick = onClick,
            ) {
                AppText(
                    text = stringResource(id = R.string.get_now),
                    color = Color(0xFFEDE3FF),
                    fontSize = 14.sp,
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewCheckInPage() {
    MineCheckInPage(modifier = Modifier.fillMaxWidth())
}

@Composable
fun MyAppIncome(balance: String, unit: String, modifier: Modifier = Modifier, onClick: () -> Unit = {}) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp, vertical = 12.dp),
    ) {
        AppText(
            text = stringResource(id = R.string.我的收益),
            fontSize = 14.sp,
            color = Color.White,
            fontWeight = FontWeight.Bold
        )

        HorizontalDivider(
            modifier = Modifier.padding(top = 12.dp),
            thickness = 0.5.dp,
            color = Color(0x0DFFFFFF)
        )

        Row(
            modifier = Modifier
                .padding(top = 8.dp)
                .fillMaxWidth(),
            verticalAlignment = Alignment.Bottom,
        ) {
            AutoSizeText(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.可提现余额))
                    withStyle(
                        SpanStyle(
                            fontSize = 24.sp,
                            color = Color.White,
                            fontFamily = D_DIN,
                            fontWeight = FontWeight.Bold,
                        )
                    ) {
                        append(" $balance ")
                    }
                    append(unit)
                },
                color = colorResource(id = R.color.white_alpha_50),
                fontSize = 14.sp,
                modifier = Modifier
                    .padding(end = 8.dp)
                    .weight(1f),
                lineSpacingRatio = Float.NaN,
            )
            CommonButton(
                modifier = Modifier.size(80.dp, 30.dp),
                gradient = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))),
                shape = CircleShape,
                contentPadding = PaddingValues(horizontal = 5.dp),
                onClick = onClick,
            ) {
                AutoSizeText(text = stringResource(R.string.立即提现), fontSize = 14.sp)
            }
        }
    }
}

@Preview
@Composable
fun PreviewMyAppIncome() {
    MyAppIncome("0.00", "TWD", modifier = Modifier.fillMaxWidth())
}

@Composable
fun AnchorIncomeLevel(
    balance: String,
    unit: String,
    modifier: Modifier = Modifier,
    onClickIncome: () -> Unit = {},
    onClickLevel: () -> Unit = {}
) {
    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        Row(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 12.dp)
                .clickable(onClick = onClickIncome),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
            ) {
                AppText(text = stringResource(R.string.anchor_balance), color = Color.White, fontSize = 16.sp)
                Spacer(modifier = Modifier.height(12.dp))
                AppText(text = buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            fontSize = 16.sp,
                            color = Color.White,
                            fontFamily = D_DIN,
                            fontWeight = FontWeight.Bold,
                        )
                    ) {
                        append(" $balance ")
                    }
                    withStyle(
                        SpanStyle(
                            fontSize = 12.sp,
                            color = colorWhite50Alpha,
                        )
                    ) {
                        append(unit)
                    }
                })
            }
            Image(
                painter = painterResource(id = R.drawable.ic_income_img),
                contentDescription = "",
                modifier = Modifier.size(40.dp),
                contentScale = ContentScale.Fit
            )
        }
        VerticalDivider(modifier = Modifier.height(40.dp), thickness = 0.5.dp, color = Color(0x0DFFFFFF))
        Row(
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 12.dp)
                .clickable(onClick = onClickLevel),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                AppText(text = stringResource(R.string.anchor_level), color = Color.White, fontSize = 16.sp)
                Spacer(modifier = Modifier.height(12.dp))
                AppText(text = stringResource(R.string.get_more_flow), color = colorWhite50Alpha, fontSize = 12.sp)
            }
            Image(
                painter = painterResource(id = R.drawable.ic_level_img),
                contentDescription = "",
                modifier = Modifier.size(40.dp),
                contentScale = ContentScale.Fit
            )
        }
    }
}

@Preview
@Composable
fun AnchorIncomeLevelPreview() {
    AnchorIncomeLevel("99999.0", "元")
}