package com.qyqy.ucoo.compose.vm.virtually

import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.data.VirtualMatchInfo
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.state.MviViewModel

interface VirtuallyMvi {

    sealed interface ViewEvent : MviViewModel.MviEvent {

        data class SelectTag(val tagId: Int) : ViewEvent

        object StartMatch : ViewEvent

        data class CancelMatch(val finish: Boolean) : ViewEvent

        data class SendMatchResult(val matchId: Int, val user: User? = null, val error: String? = null) : ViewEvent

    }

    sealed interface ViewResult : MviViewModel.MviViewResult {
        object NoOp : ViewResult

        data class SelectResult(val id: Int) : ViewResult

        data class LoadingResult(val show: Boolean) : ViewResult

        object PreMatchResult : ViewResult

        data class MatchIdResult(val id: Int) : ViewResult

        object CancelResult : ViewResult

        // effect

        data class ErrorResult(val toastError: String? = null) : ViewResult

        data class MatchSuccess(val user: User) : ViewResult

        object Finish : ViewResult
    }

    /**
     * [step]
     * 0: 引导页面
     * 1: 预匹配中
     * 2: 匹配中
     */
    data class ViewState(
        val showLoading: Boolean = false,
        val step: Int = 0,
        val matchId: Int = -1,
        val info: VirtualMatchInfo = VirtualMatchInfo(),
        val tagId: Int = info.tags.firstOrNull()?.id.orDefault(-1)
    ) : MviViewModel.MviViewState


    interface ViewEffect : MviViewModel.MviSideEffect {

        data class Toast(val msg: String?) : ViewEffect

        object Finish : ViewEffect

        data class MatchSuccess(val user: User) : ViewEffect

    }
}

