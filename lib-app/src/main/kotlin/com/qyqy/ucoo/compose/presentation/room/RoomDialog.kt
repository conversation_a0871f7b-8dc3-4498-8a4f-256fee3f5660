package com.qyqy.ucoo.compose.presentation.room

import android.content.Context
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorWhite30Alpha
import com.qyqy.ucoo.compose.ui.ComposeDialog

class DragonTaskDialog(context: Context, val message: String, val extra: String) : ComposeDialog(context) {
    @Composable
    override fun Content() {
        DragonTaskContent(message, extra) {
            dismiss()
        }
    }
}

@Composable
@Preview(device = "id:Nexus One")
fun DragonTaskDialogPreview() {
    DragonTaskContent(message = "恭喜 幼儿园搬花花... 完成【语音房挂麦任务】", extra = "获得 9999 龙蛋成长值")
}


@Composable
fun DragonTaskContent(message: String = "", extra: String = "", onClick: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth(1f)
            .background(Brush.verticalGradient(listOf(0xFFFF4251, 0xFFFFC085).map { Color(it) }), Shapes.corner12)
            .padding(8.dp, 20.dp), horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Image(
            painter = painterResource(id = R.drawable.ic_dragon_egg), "",
            modifier = Modifier
                .size(80.dp)
                .background(colorWhite30Alpha, Shapes.small)
        )
        Spacer(modifier = Modifier.height(16.dp))
        Text(text = message, fontSize = 12.sp, color = Color(0xFFFFFCE1))
        Spacer(modifier = Modifier.height(12.dp))
        Text(text = extra, fontSize = 14.sp, color = Color(0xFF801715))
        Spacer(modifier = Modifier.height(16.dp))

        Text(
            text = stringResource(id = R.string.rc_confirm),
            modifier = Modifier
                .size(120.dp, 36.dp)
                .background(
                    Brush.verticalGradient(listOf(0xFFFFF5E1, 0xFFFFDC98).map { Color(it) }),
                    shape = RoundedCornerShape(18.dp)
                )
                .clickable(onClick = onClick),
            color = Color(0xFFA72927),
            fontSize = 16.sp,
            textAlign = TextAlign.Center,
            lineHeight = 36.sp,
            fontWeight = FontWeight.Medium
        )
    }
}

