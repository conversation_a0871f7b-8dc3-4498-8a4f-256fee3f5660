package com.qyqy.ucoo.compose.presentation.game

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.getSampleImageUrl
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.theme.themeButtonColors
import com.qyqy.ucoo.compose.ui.AppText


@Composable
fun RacingInvite(title: String, desc: String,buttonText:String, onClick: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .border(1.dp, Brush.verticalGradient(listOf(Color(0x59FFFFFF), Color(0x00FFFFFF))), Shapes.small)
            .background(Brush.verticalGradient(listOf(Color(0xFF720D82), Color(0xFF29042F))), Shapes.small)
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AppText(text = title, color = Color.White, fontSize = 15.sp)
        Spacer(modifier = Modifier.height(12.dp))
        Image(
            painter = painterResource(id = R.drawable.icon_game_racing),
            contentScale = ContentScale.Crop,
            contentDescription = "",
            modifier = Modifier
                .size(128.dp, 128.dp)
                .clip(RoundedCornerShape(4.dp))
        )
        Spacer(modifier = Modifier.height(12.dp))
        AppText(text = desc, color = colorWhite50Alpha, fontSize = 12.sp)
        Spacer(modifier = Modifier.height(20.dp))
        Button(
            onClick = onClick, modifier = Modifier
                .requiredWidthIn(min = 160.dp)
                .padding(vertical = 8.dp),
            colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFB82FD2))
        ) {
            AppText(text = buttonText, color = Color.White, fontSize = 16.sp)
        }
    }
}

@Preview
@Composable
fun RacingInvitePreview() {
    RacingInvite("恭喜免费获赠100升汽油！ 可用于极速赛车玩法", "参与极速赛车玩法，可获得钻石奖励","立即体验")
}