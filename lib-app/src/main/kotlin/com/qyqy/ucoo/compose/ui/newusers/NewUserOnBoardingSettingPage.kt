package com.qyqy.ucoo.compose.ui.newusers

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Badge
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.ext.launch
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.base.show
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.greets.GreetingsApi
import com.qyqy.ucoo.compose.presentation.room.LevelBadge
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.presentation.room.SimpleItem
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LoadResult
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.compose.ui.PagingScope
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.compose.ui.rememberPaginateState
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.mine.UploadImageContract
import com.qyqy.ucoo.mine.UploadImageViewModel
import com.qyqy.ucoo.mine.url
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper
import com.qyqy.ucoo.utils.takeIsNotEmpty
import com.qyqy.ucoo.utils.toOSSObject
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString

@Immutable
@Serializable
data class NewcomerReceptionConfig(
    @SerialName("has_set_interact_unlock_message") val hasSetInteractUnlockMessage: Boolean = false, // false
    val intro: List<RichItem>? = null,
)

@Immutable
@Serializable
data class NewcomerReceptionHistoryList(
    @SerialName("page_token") val pageToken: String = "", // 12
    @SerialName("has_more") val hasMore: Boolean = false, // false
    val items: List<NewcomerReceptionHistory>? = null,
)

@Immutable
@Serializable
data class NewcomerReceptionHistory(
    @SerialName("is_online") val isOnline: Boolean = false, // true
    @SerialName("last_active_label") val lastActiveLabel: String = "", // 在线
    @SerialName("service_time_label") val serviceTimeLabel: String = "", // 2024-12-30 18:30
    val user: AppUser, // {nickname, avatar_url, level}
    @SerialName("has_read") val hasRead: Boolean = false,
)

@Composable
fun NewcomerReceptionSettingsScreen(onEdit: () -> Unit = {}) {
    var rich by remember {
        mutableStateOf<List<RichItem>?>(null)
    }

    var hasPic by remember {
        mutableStateOf(false)
    }

    val list = remember {
        mutableStateListOf<NewcomerReceptionHistory>()
    }

    val api = remember {
        createApi<GreetingsApi>()
    }

    LifecycleStartEffect(Unit) {

        launch {
            runApiCatching {
                api.getNewcomerReceptionConfig()
            }.onSuccess {
                hasPic = it.hasSetInteractUnlockMessage
                rich = it.intro?.run {
                    buildList {
                        add(RichItem(1, "<br><br>"))
                        addAll(this@run)
                    }
                }
            }
        }

        onStopOrDispose { }
    }

    NewcomerReceptionSettingsScreen(rich = rich, hasPic = hasPic, historyList = list, onEdit = onEdit) {
        runApiCatching {
            api.getNewcomerReceptionHistory(it.key)
        }.fold({
            it.items?.let { it1 -> list.addAll(it1) }
            LoadResult.Page(
                if (it.hasMore) {
                    it.pageToken.takeIsNotEmpty()
                } else {
                    null
                }
            )
        }) { e ->
            LoadResult.Error(e)
        }
    }
}

@Composable
private fun NewcomerReceptionSettingsScreen(
    rich: List<RichItem>?,
    hasPic: Boolean,
    historyList: List<NewcomerReceptionHistory>,
    onEdit: () -> Unit = {},
    onLoad: suspend (PagingScope<String>) -> LoadResult<String> = {
        LoadResult.Page(null)
    },
) {
    UCOOScreen(stringResource(id = R.string.新人专属女友匹配)) {
        val parentScrollState = rememberScrollState()

        val nestedScrollConnection = remember(parentScrollState) {
            object : NestedScrollConnection {
                override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                    // 父组件优先消耗滚动
                    val deltaY = -available.y
                    val consumedY = -parentScrollState.dispatchRawDelta(deltaY)
                    return available.copy(y = consumedY)
                }
            }
        }

        BoxWithConstraints(
            modifier = Modifier
                .padding(top = 8.dp)
                .fillMaxSize()
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(parentScrollState)
                    .nestedScroll(nestedScrollConnection)
                    .padding(horizontal = 16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {

                var isExpand by rememberSaveable {
                    mutableStateOf(false)
                }

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .animateContentSize()
                        .heightIn(min = 46.dp)
                        .background(Color(0xFF272829), Shapes.small)
                        .padding(start = 16.dp, end = 12.dp, top = 14.dp, bottom = 12.dp), verticalArrangement = Arrangement.spacedBy((-28).dp)
                ) {
                    Row(
                        modifier = Modifier.noEffectClickable(enabled = !rich.isNullOrEmpty()) {
                            isExpand = !isExpand
                        },
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = stringResource(id = R.string.说明),
                            color = Color.White,
                            fontSize = 14.sp,
                            lineHeight = 14.sp
                        )

                        Spacer(modifier = Modifier.weight(1f))

                        if (!rich.isNullOrEmpty()) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_arrow_down),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(14.dp)
                                    .rotate(if (isExpand) 180f else 0f),
                                colorFilter = ColorFilter.tint(Color(0x80FFFFFF))
                            )
                        }
                    }

                    if (!rich.isNullOrEmpty()) {
                        RichText(
                            rich = rich,
                            modifier = Modifier.fillMaxWidth(),
                            color = Color(0xFFCACACA),
                            fontSize = 12.sp,
                            lineHeight = 18.sp,
                            maxLines = if (isExpand) Int.MAX_VALUE else 1
                        )
                    }

                }

                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(46.dp)
                        .clip(Shapes.small)
                        .background(Color(0xFF272829))
                        .clickable(onClick = onEdit)
                        .padding(start = 16.dp, end = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(id = R.string.新人承接照片设置),
                        color = Color.White,
                        fontSize = 14.sp,
                    )

                    Spacer(modifier = Modifier.weight(1f))

                    Text(
                        text = if (hasPic) stringResource(id = R.string.已设置) else stringResource(id = R.string.未设置),
                        color = if (hasPic) Color(0xFF945EFF) else Color(0x80FFFFFF),
                        fontSize = 12.sp,
                    )

                    Image(
                        painter = painterResource(id = R.drawable.ic_arrow_right),
                        contentDescription = null,
                        modifier = Modifier.size(14.dp),
                        colorFilter = ColorFilter.tint(Color(0x80FFFFFF))
                    )
                }

                val context = LocalContext.current

                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .run {
                            if (historyList.isEmpty()) {
                                animateContentSize().weight(1f)
                            } else {
                                height(<EMAIL>)
                            }
                        }
                        .background(Color(0xFF272829), RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp))
                        .padding(horizontal = 16.dp, vertical = 12.dp)
                ) {
                    Text(
                        text = stringResource(id = R.string.匹配记录),
                        color = Color.White,
                        fontSize = 14.sp,
                    )

                    val listState = rememberLazyListState()

                    LazyColumn(
                        modifier = Modifier
                            .padding(top = 16.dp)
                            .fillMaxSize(),
                        state = listState,
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        contentPadding = PaddingValues(bottom = 30.dp)
                    ) {

                        if (historyList.isEmpty()) {
                            item {
                                Column(
                                    modifier = Modifier.fillParentMaxSize(),
                                    verticalArrangement = Arrangement.Center,
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Image(
                                        painter = painterResource(id = R.drawable.ic_empty_new_user_onboarding),
                                        contentDescription = null,
                                        modifier = Modifier.size(145.dp)
                                    )
                                    Text(
                                        text = stringResource(id = R.string.暂无匹配记录),
                                        color = Color(0x4DFFFFFF),
                                        fontSize = 14.sp
                                    )
                                }
                            }
                        }

                        items(historyList) {
                            SimpleItem(
                                modifier = Modifier.click(noEffect = true) {
                                    context.startActivity(ChatActivity.createIntent(context, it.user))
                                },
                                startContent = {
                                    CircleComposeImage(
                                        model = it.user.avatarUrl,
                                        modifier = Modifier.size(56.dp)
                                    )
                                },
                                centerContent = {
                                    Column(
                                        modifier = Modifier.padding(horizontal = 8.dp), verticalArrangement = Arrangement.spacedBy(4.dp)
                                    ) {
                                        Row(verticalAlignment = Alignment.CenterVertically) {
                                            Text(
                                                text = it.user.nickname,
                                                modifier = Modifier.weight(1f, false),
                                                color = Color.White,
                                                fontSize = 14.sp,
                                            )
                                            Spacer(modifier = Modifier.width(4.dp))
                                            LevelBadge(level = it.user.level)
                                        }
                                        Text(
                                            text = stringResource(id = R.string.匹配于, it.serviceTimeLabel),
                                            color = Color(0x99FFFFFF), fontSize = 12.sp,
                                        )
                                    }
                                }
                            ) {
                                Column(
                                    horizontalAlignment = Alignment.End,
                                    verticalArrangement = Arrangement.spacedBy(5.dp)
                                ) {

                                    Text(
                                        text = it.lastActiveLabel,
                                        color = if (it.isOnline) Color(0xFF00B42A) else Color(0x99FFFFFF),
                                        fontSize = 12.sp,
                                    )

                                    if (!it.hasRead) {
                                        Badge(
                                            modifier = Modifier
                                                .widthIn(min = 16.dp)
                                                .height(16.dp),
                                            containerColor = Color(0xFFF53F3F),
                                            contentColor = Color.White,
                                        ) {
                                            Text(text = "1")
                                        }
                                    }
                                }
                            }
                        }
                    }

                    val paginateState = rememberPaginateState<String>()

                    paginateState.LaunchAttach(listState = listState, onLoad = onLoad)
                }
            }
        }
    }
}

@Preview
@Composable
fun PreviewNewcomerReceptionSettingsScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
    ) {
        NewcomerReceptionSettingsScreen(buildList {
            add(RichItem(1, "<br><br>"))
            add(RichItem(1, "1.新人完成注册后，将会自动匹配一个主播作为他的专属女友，匹配成功后将会自动跳转进入私密小屋聊天。"))
            add(RichItem(1, "2.在新人视角内，被匹配的主播将在消息列表置顶展示，并显示”专属女友“标签。"))
            add(RichItem(1, "3.只有在语音房麦上保持开麦状态，且当前没有CP的主播才能被匹配。与新人的私密小屋聊天时间越长，被匹配的概率越高。"))
            add(RichItem(1, "4.数据显示，有30%的新人最终都和最佳匹配的主播成为了CP。下列是你的新人专属女友匹配记录，好好跟他们聊天吧！"))
            add(RichItem(1, "5.为了获得更高的回复率，建议你设置吸引力强的照片或者语音，在私密小屋结束后，系统将为你自动发送。"))
        }, true, listOf(NewcomerReceptionHistory(user = userForPreview), NewcomerReceptionHistory(user = userForPreview)))
    }
}

@Immutable
@Serializable
data class NewcomerReceptionPhotoConfig(
    @SerialName("intro_text") val introText: String? = null,
    @SerialName("intro_image") val introImage: String? = null,
    @SerialName("pics") val mediaInfos: List<MediaInfo>? = null,
    @SerialName("status") val status: Int = 0,
)

@Composable
fun NewcomerReceptionPhotoSettingsScreen() {
    val api = remember {
        createApi<GreetingsApi>()
    }

    val context = LocalContext.current

    var introText by remember {
        mutableStateOf(context.getString(R.string.在与新人的私密小屋结束后))
    }

    var introImage by remember {
        mutableStateOf<Any>(R.drawable.ic_blur_pic_example)
    }

    var mediaInfo by remember {
        mutableStateOf<MediaInfo?>(null)
    }

    var status by remember {
        mutableIntStateOf(0)
    }

    LifecycleStartEffect(Unit) {

        launch {
            runApiCatching {
                api.getNewcomerReceptionPhotoConfig()
            }.onSuccess {
                if (it.introText != null) {
                    introText = it.introText
                }
                if (it.introImage != null) {
                    introImage = it.introImage
                }

                mediaInfo = it.mediaInfos?.firstOrNull()
                status = it.status
            }
        }

        onStopOrDispose { }
    }

    val scope = rememberCoroutineScope()
    NewcomerReceptionPhotoSettingsScreen(introText, introImage, mediaInfo, status) {
        scope.launch {
            runApiCatching {
                api.saveNewcomerReceptionPhoto(buildMap {
                    put("pics", sAppJson.encodeToString(listOf(it)))
                })
            }.onSuccess { ret ->
                mediaInfo = it
                status = ret.getIntOrNull("status") ?: 0
            }
        }
    }
}

@Composable
private fun NewcomerReceptionPhotoSettingsScreen(
    introText: String,
    introImage: Any,
    mediaInfo: MediaInfo?,
    status: Int,
    onSelectedMediaInfo: (MediaInfo) -> Unit,
) {
    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
    UCOOScreen(stringResource(id = R.string.新人承接照片设置), {
        onBackPressedDispatcher?.onBackPressed()
    }) {
        Column(
            modifier = Modifier
                .padding(start = 16.dp, top = 8.dp, end = 16.dp)
                .fillMaxWidth()
                .background(Color(0xFF272829), Shapes.small)
                .padding(start = 16.dp, top = 16.dp, end = 16.dp, bottom = 12.dp)
        ) {
            Text(
                text = introText,
                color = Color(0xFFCACACA),
                fontSize = 12.sp
            )

            Row(
                modifier = Modifier
                    .padding(top = 12.dp)
                    .fillMaxWidth()
                    .background(Color(0xFF1C1D1E), Shapes.extraSmall)
                    .padding(12.dp)
            ) {
                CircleComposeImage(
                    model = sUser.avatarUrl,
                    modifier = Modifier
                        .padding(end = 5.dp)
                        .size(26.dp)
                )

                Column(
                    modifier = Modifier
                        .background(Color(0x0DFFFFFF), Shapes.extraSmall)
                        .padding(8.dp),
                    verticalArrangement = Arrangement.spacedBy(7.dp)
                ) {
                    Text(
                        text = stringResource(id = R.string.对方发送了一张个人私密照片),
                        color = Color(0xFFFFFFFF),
                        fontSize = 9.sp,
                        lineHeight = 9.sp
                    )

                    Image(
                        painter = painterResource(id = R.drawable.ic_blur_pic_example),
                        contentDescription = null,
                        modifier = Modifier.size(102.dp)
                    )

                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_pic_unlock_progress_1),
                            contentDescription = null,
                            modifier = Modifier
                                .padding(end = 5.dp)
                                .size(21.dp)
                        )

                        Text(
                            text = stringResource(id = R.string.与对方互发消息3个回合), color = Color(0xFFFF82C3), fontSize = 8.sp, lineHeight = 11.sp
                        )
                    }
                }
            }
        }

        val url = mediaInfo?.url

        Box(modifier = Modifier.weight(1f)) {
            Column(
                modifier = Modifier
                    .padding(start = 16.dp, top = 12.dp, end = 16.dp)
                    .fillMaxWidth()
                    .heightIn(max = 290.dp)
                    .fillMaxHeight(1f)
                    .background(Color(0xFF272829), Shapes.small),
            ) {
                Text(
                    text = stringResource(id = R.string.新人承接照片设置),
                    modifier = Modifier.padding(16.dp),
                    color = Color(0xFFFFFFFF),
                    fontSize = 14.sp
                )

                if (url.isNullOrEmpty()) {
                    Column(
                        modifier = Modifier.fillMaxSize(),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_empty_new_user_onboarding_pic),
                            contentDescription = null,
                            modifier = Modifier
                                .fillMaxHeight(0.6f)
                                .aspectRatio(1f)
                        )
                        Text(
                            text = stringResource(id = R.string.你还未设置照片),
                            color = Color(0x4DFFFFFF),
                            fontSize = 14.sp
                        )
                    }
                } else {
                    ComposeImage(
                        model = url,
                        modifier = Modifier
                            .padding(bottom = 20.dp)
                            .align(Alignment.CenterHorizontally)
                            .fillMaxHeight()
                            .aspectRatio(mediaInfo.let {
                                it.width
                                    .toFloat()
                                    .div(it.height)
                            })
                            .clip(Shapes.small),
                    )
                }
            }
        }

        val context = LocalContext.current

        val uploadViewModel = viewModel<UploadImageViewModel>()

        val loading by uploadViewModel.uiState.map {
            it.loadingState.isLoading
        }.collectAsStateWithLifecycle(false)

        val uploadImageHelper = rememberRequestAlbumPermissionHelper(context = context, needCrop = false, isCupid = false) {
            val result = it.list.first()
            uploadViewModel.lastMediaData = result
            uploadViewModel.sendEvent(UploadImageContract.Event.UploadImage(result.name, result.toOSSObject(), false))
        }

        LaunchedEffect(Unit) {

            launch {
                uploadViewModel.uiState.map {
                    it.uploadUrl
                }.filter {
                    !it.isNullOrEmpty()
                }.onEach {
                    onSelectedMediaInfo(uploadViewModel.lastMediaData.run {
                        MediaInfo(it, this?.width ?: 0, this?.height ?: 0, this?.fileSize ?: 0)
                    })
                }.launchIn(this)
            }

            launch {
                uploadViewModel.effect.onEach {
                    it.show()
                }.launchIn(this)
            }

        }

        if (loading) {
            Loading {
                uploadViewModel.sendEvent(UploadImageContract.Event.CancelUploadImage)
            }
        }

        Box(
            modifier = Modifier
                .padding(bottom = 54.dp)
                .align(Alignment.CenterHorizontally)
        ) {
            Box(
                modifier = Modifier
                    .padding(top = 30.dp)
                    .size(255.dp, 48.dp)
                    .clip(CircleShape)
                    .background(Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))))
                    .clickable(enabled = url.isNullOrEmpty() || status != 0) { // 不在审核中
                        uploadImageHelper.start()
                    }
                    .padding(horizontal = 12.dp),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = when {
                        url.isNullOrEmpty() -> stringResource(id = R.string.点击上传照片)
                        status == 0 -> stringResource(id = R.string.审核中v2)
                        status == -10 -> stringResource(id = R.string.未通过审核)
                        status == 10 -> stringResource(id = R.string.更换照片)
                        else -> stringResource(id = R.string.点击上传照片)
                    },
                    color = Color.White,
                    fontSize = 16.sp,
                    lineHeight = 18.sp,
                    textAlign = TextAlign.Center
                )
            }

            if (url.isNullOrEmpty()) {
                Box(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(top = 20.dp)
                        .height(20.dp)
                        .background(
                            brush = Brush.horizontalGradient(listOf(Color(0xFFFFA5C0), Color(0xFFFF2FD1))),
                            shape = RoundedCornerShape(topStart = 12.dp, bottomEnd = 12.dp)
                        )
                        .padding(horizontal = 6.dp), contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = stringResource(id = R.string.更多流量推荐),
                        color = Color(0xFFFFFFFF),
                        fontSize = 10.sp,
                        lineHeight = 10.sp
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun PreviewNewcomerReceptionPhotoSettingsScreen() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
    ) {
        NewcomerReceptionPhotoSettingsScreen()
    }
}
