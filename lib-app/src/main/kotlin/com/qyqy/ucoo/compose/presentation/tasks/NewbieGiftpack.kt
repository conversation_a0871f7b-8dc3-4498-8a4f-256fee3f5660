package com.qyqy.ucoo.compose.presentation.tasks

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.formatTimeWithHours
import com.qyqy.ucoo.compose.getSampleImageUrl
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.router.UCOOPreviewTheme
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.sUserKV
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive


@Composable
fun NewbieGiftPackPendant(iconUrl: String = getSampleImageUrl(), onClick: () -> Unit = {}, onClose: () -> Unit = {}) {
    Box(
        modifier = Modifier
            .fillMaxSize(1f)
            .noEffectClickable(onClick = onClick)
    ) {
        ComposeImage(model = iconUrl, modifier = Modifier.fillMaxSize(1f))
//        Image(
//            painter = painterResource(id = R.drawable.ic_close_dialog),
//            modifier = Modifier
//                .size(28.dp)
//                .padding(4.dp)
//                .noEffectClickable(onClick = onClose)
//                .align(Alignment.TopEnd),
//            contentDescription = "close"
//        )
        //2.40.0 去掉倒计时
//        if(!isPreviewOnCompose){
//            //倒计时
//            CountdownUI(
//                modifier = Modifier
//                    .padding(bottom = 8.dp)
//                    .align(Alignment.BottomCenter)
//            )
//        }
    }
}


@Composable
fun CountdownUI(
    modifier: Modifier = Modifier,
    duration: Long = 24 * 60 * 60 * 1000,
) {
    val start = remember {
        var start = sUserKV.getLong("newbie_tick", -1L)
        if (start == -1L) {
            start = System.currentTimeMillis()
            sUserKV.putLong("newbie_tick", start)
        }
        start
    }

    val list = remember {
        mutableStateListOf<String>().apply {
            addAll(formatTimeWithHours(duration.minus(System.currentTimeMillis().minus(start).rem(duration))).split(":"))
        }
    }

    LaunchedEffect(Unit) {
        while (isActive) {
            delay(1000L)
            list.clear()
            list.addAll(formatTimeWithHours(duration.minus(System.currentTimeMillis().minus(start).rem(duration))).split(":"))
        }
    }

    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        list.forEachIndexed { index, s ->
            Box(
                modifier = Modifier
                    .size(20.dp, 24.dp)
                    .background(Brush.verticalGradient(listOf(Color(0xFFFFF4F5), Color(0xFFFFE7D2))), RoundedCornerShape(3.dp)),
                contentAlignment = Alignment.Center
            ) {
                AppText(text = s, fontSize = 14.sp, color = Color(0xFFFF0428), fontWeight = FontWeight.Bold)
            }
            if (index < 2) {
                AppText(text = ":", color = Color.White, fontSize = 12.sp, fontWeight = FontWeight.Bold)
            }
        }
    }
}

@Preview(widthDp = 200, heightDp = 300)
@Composable
private fun CountUIPreview() {
//    CountdownUI(initialTimeInSeconds = 24 * 60 * 60)
    UCOOPreviewTheme {
        NewbieGiftPackPendant()
    }
}
