package com.qyqy.ucoo.compose.presentation.greets

import com.qyqy.ucoo.compose.ui.newusers.NewcomerReceptionConfig
import com.qyqy.ucoo.compose.ui.newusers.NewcomerReceptionHistoryList
import com.qyqy.ucoo.compose.ui.newusers.NewcomerReceptionPhotoConfig
import com.qyqy.ucoo.feat.like.SystemTopConvEntries
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.mine.MediaInfo
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface GreetingsApi {
    @GET("api/broadcaster/v1/hicontent/list")
    suspend fun getGreetings(): ApiResponse<Greetings>

    /**
     *  content_type
     *  content_id
     *  duration
     * @param map
     */
    @POST("api/broadcaster/v1/hicontent/add")
    suspend fun addGreeting(/* parameters */@Body map: Map<String, String>): ApiResponse<Greetings.Tab.Content>

    /**
     * content_type
     * content_id
     *
     * @param map
     */
    @POST("api/broadcaster/v1/hicontent/delete")
    suspend fun deleteGreeting(/* parameters */@Body map: Map<String, String>): ApiResponse<JsonObject>


    @GET("api/broadcaster/v1/chat/recommend/users")
    suspend fun getRecommendUsers(@Query("last_id") last_id: Int): ApiResponse<JsonObject>


    @POST("api/ucuser/v1/default/sound_brand")
    suspend fun addSound(/* parameters */@Body map: Map<String, String>): ApiResponse<JsonObject>

    /**
     * content_type
     * content_id
     *
     * @param map
     */
    @POST("api/ucuser/v1/default/sound_brand/delete")
    suspend fun deleteSound(): ApiResponse<JsonObject>


    @GET("api/privatechat/v1/client/chat/list/top/entries")
    suspend fun getSystemTopConvConfig(): ApiResponse<SystemTopConvEntries>

    @GET("api/broadcaster/v1/newbie/guide/settings")
    suspend fun getNewcomerReceptionConfig(): ApiResponse<NewcomerReceptionConfig>

    @GET("api/broadcaster/v1/newbie/guide/history/list")
    suspend fun getNewcomerReceptionHistory(@Query("page_token") page: String?): ApiResponse<NewcomerReceptionHistoryList>

    @GET("api/broadcaster/v1/newbie/guide/pics/settings")
    suspend fun getNewcomerReceptionPhotoConfig(): ApiResponse<NewcomerReceptionPhotoConfig>

    @POST("api/broadcaster/v1/newbie/guide/pics/upload")
    suspend fun saveNewcomerReceptionPhoto(@Body body: Map<String, String>): ApiResponse<JsonObject>
}