package com.qyqy.ucoo.compose.presentation.bless

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorSecondBlack
import com.qyqy.ucoo.im.bean.BlessEntity


@Composable
fun ShowLove(bless: BlessEntity, onButtonClick: (String) -> Unit = {}) {
    var blessWord by remember {
        mutableStateOf("")
    }
    val colorText = Color(0x80FFFFFF)
    val textLimit by remember {
        derivedStateOf { "${blessWord.length}/${bless.maxLength}" }
    }
    Column(
        modifier = Modifier
            .background(colorSecondBlack, Shapes.small)
            .padding(16.dp, 20.dp)
    ) {
        Text(
            text = bless.title,
            color = Color.White,
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(bottom = 12.dp),
            textAlign = TextAlign.Center,
            fontSize = 17.sp
        )
        Box(
            modifier = Modifier
                .background(color = Color(0x0DFFFFFF), shape = RoundedCornerShape(2.dp))
                .fillMaxWidth(1f)
                .height(96.dp),
        ) {
            MaterialTheme(
                colorScheme = MaterialTheme.colorScheme.copy(
                    surfaceVariant = Color.Transparent,
                    onBackground = Color.White
                )
            ) {
                TextField(
                    value = blessWord,
                    onValueChange = {
                        blessWord = it.take(20)
                    },
                    placeholder = { Text(text = bless.placeholder, fontSize = 14.sp, color = colorText) },
                    colors = TextFieldDefaults.colors(
                        focusedIndicatorColor = Color.Transparent, // 设置聚焦时的横线颜色为透明
                        unfocusedIndicatorColor = Color.Transparent, // 设置非聚焦时的横线颜色为透明
                        disabledIndicatorColor = Color.Transparent // 设置禁用时的横线颜色为透明
                    ),
                    textStyle = TextStyle(
                        textDecoration = TextDecoration.None,
                        color = Color.White,
                        background = Color.Transparent,
                        textAlign = TextAlign.Start
                    ),
                    modifier = Modifier
                        .fillMaxSize(1f),

                    )
            }

            Text(
                text = textLimit, color = Color(0xBFFFFFFF), modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .offset((-8).dp, (-8).dp)
            )
        }
        Text(
            text = bless.hint,
            lineHeight = 16.sp, fontSize = 13.sp,
            color = Color(0x80FFFFFF),
            modifier = Modifier.padding(top = 12.dp, bottom = 20.dp)
        )
        Text(
            text = stringResource(R.string.confirm_and_give),
            fontSize = 16.sp,
            modifier = Modifier
                .fillMaxWidth(1f)
                .background(Brush.verticalGradient(listOf(Color(0xFFF47BFF), Color(0xFFFF5388))), RoundedCornerShape(50))
                .padding(vertical = 12.dp)
                .clickable(onClick = {
                    onButtonClick(blessWord)
                })
                .graphicsLayer {
                    alpha = if (blessWord.isNotEmpty()) 1f else 0.5f
                },
            textAlign = TextAlign.Center,
            color = Color.White
        )
    }
}

@Preview
@Composable
private fun ShowLovePreview() {
    ShowLove(bless = BlessEntity(hint = "520降至", title = "填写白噢白", placeholder = "hahhaha"))
}