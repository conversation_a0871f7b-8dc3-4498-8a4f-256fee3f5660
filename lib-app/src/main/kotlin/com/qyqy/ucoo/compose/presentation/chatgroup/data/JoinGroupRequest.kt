package com.qyqy.ucoo.compose.presentation.chatgroup.data


import androidx.annotation.Keep
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Serializable
data class JoinGroupRequest(
    @SerialName("apply_id")
    val applyId: Int = 0,
    val status: Int = 0,//0 处理中 1已同意 2已拒绝
    @SerialName("user")
    val user: User = User()
) {
    @Keep
    @Serializable
    data class User(
        @SerialName("age")
        val age: Int = 0,
        @SerialName("avatar_frame")
        val avatarFrame: String = "",
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        @SerialName("country_flag")
        val countryFlag: String = "",
        @SerialName("gender")
        val gender: Int = 0,
        @SerialName("height")
        val height: Int = 0,
        @SerialName("level")
        val level: Int = 0,
//        @SerialName("medal")
//        val medal: Any = Any(),
//        @SerialName("medal_list")
//        val medalList: List<Any> = listOf(),
        @SerialName("nickname")
        val nickname: String = "",
        @SerialName("public_id")
        val publicId: String = "",
        @SerialName("userid")
        val userid: Int = 0
    )

    fun done(accept: Boolean): JoinGroupRequest {
        return JoinGroupRequest(applyId, if (accept) 1 else 2, user)
    }
}