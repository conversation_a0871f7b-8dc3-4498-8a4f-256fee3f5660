package com.qyqy.ucoo.compose.presentation.language

import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.hjq.language.MultiLanguages
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.restartApp
import com.qyqy.ucoo.utils.LogUtil
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Locale

@Composable
fun LanguageSettingsScreen(viewModel: LanguageSettingsViewModel = viewModel(), onBack: () -> Unit = {}) {
    LaunchedEffect(key1 = viewModel) {
        viewModel.loadSettings()
    }
    val scope = rememberCoroutineScope()
    val context = LocalContext.current
    UCOOScreen(title = stringResource(id = R.string.language_settings)) {
        Box(modifier = Modifier.fillMaxSize(1f), contentAlignment = Alignment.Center) {
            val list by viewModel.list.collectAsState()
            LazyColumn(modifier = Modifier.fillMaxSize(1f)) {
                items(list) {
                    val isSelected = viewModel.currentLanguageCode.collectAsState().value.startsWith(it.code)
                    Row(
                        modifier = Modifier
                            .clickable {
                                scope.launch {
                                    MultiLanguages.setAppLanguage(context, it.locale.also {
                                        LogUtil.d("set locale:$it")
                                    })
                                    viewModel.update(it.code)

                                }
                            }
                            .padding(10.dp)
                    ) {
                        AppText(text = it.label, color = Color.White)
                        Spacer(modifier = Modifier.weight(1f))
                        if (isSelected) {
                            Image(painter = painterResource(id = R.drawable.ic_checked_purple), contentDescription = "arrow")
                        }
                    }
                }
            }
            if (list.isEmpty()) {
                Loading()
            }
        }
    }
}

@Preview
@Composable
fun LanguageSettingsScreenPreview() {
    LanguageSettingsScreen()
}