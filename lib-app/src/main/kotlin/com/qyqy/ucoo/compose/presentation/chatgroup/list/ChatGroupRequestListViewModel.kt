package com.qyqy.ucoo.compose.presentation.chatgroup.list

import com.qyqy.ucoo.compose.presentation.chatgroup.ChatGroupApi
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupMember
import com.qyqy.ucoo.compose.presentation.chatgroup.data.JoinGroupRequest
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonPrimitive

/**
 *  @time 2024/6/25
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.compose.presentation.chatgroup.list
 */
class ChatGroupRequestListViewModel(private val chatGroupId: Int) : StateViewModelWithIntPage<JoinGroupRequest>() {
    protected val api = createApi<ChatGroupApi>()
    private var lastId = -1

    override suspend fun loadData(pageNum: Int): Result<List<JoinGroupRequest>> {
        val id = if (pageNum == firstPage) 0 else lastId
        if (pageNum != firstPage && lastId == 0) {
            return Result.success(emptyList())
        }

        val result = runApiCatching { api.getMemberRequestList(chatGroupId, id) }
        return if (result.isSuccess) {
            val jsonObject = result.getOrThrow()
            lastId = jsonObject["last_member_id"]?.jsonPrimitive?.intOrNull ?: 0

            val members = jsonObject["applies"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<JoinGroupRequest>>(it) } ?: listOf()
            Result.success(members)
        } else {
            Result.failure<List<JoinGroupRequest>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    fun requestDone(applyId: Int, accept: Boolean) {
        transformList {
            val newList = mutableListOf<JoinGroupRequest>()
            for (request in this) {
                if (request.applyId == applyId) {
                    newList.add(request.done(accept))
                } else {
                    newList.add(request)
                }
            }
            newList
        }
    }
}