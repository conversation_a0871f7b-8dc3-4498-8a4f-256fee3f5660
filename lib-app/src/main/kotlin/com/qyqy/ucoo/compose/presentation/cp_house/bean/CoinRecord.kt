package com.qyqy.ucoo.compose.presentation.cp_house.bean


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class CoinRecord(
    @SerialName("change_count")
    val changeCount: Int = 0,
    @SerialName("change_type")
    val changeType: Int = 0,
    @SerialName("create_timestamp")
    val createTimestamp: Long = 0,
    @SerialName("id")
    val id: Int = 0,
    @SerialName("scene")
    val scene: Int = 0,
    @SerialName("scene_name")
    val sceneName: String = "",
    val user: Usr? = null,
) {
    @Serializable
    data class Usr(
        @SerialName("nickname")
        val nickname: String = "",
        @SerialName("public_id")
        val publicId: String = "",
        @SerialName("userid")
        val userid: Int = 0
    )
}