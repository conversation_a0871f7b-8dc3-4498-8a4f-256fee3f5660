package com.qyqy.ucoo.compose.router

import androidx.navigation.NavHostController

interface IDestination {

    val route: String

    val popUpRoute: String
        get() = ""

    val launchSingleTop: Boolean
        get() = true

    val inclusive: Boolean
        get() = false

    val saveState: <PERSON>olean
        get() = false

    val restoreState: <PERSON>olean
        get() = false
}

fun NavHostController.navigateTo(destination: IDestination) =
    navigateTo(
        destination.route,
        destination.launchSingleTop,
        destination.popUpRoute.takeIf { it.isNotEmpty() },
        destination.inclusive,
        destination.saveState,
        destination.restoreState
    )


fun NavHostController.navigateTo(
    route: String,
    launchSingleTop: Boolean = true,
    popUpRoute: String? = null,
    inclusive: Boolean = false,
    saveState: Boolean = false,
    restoreState: Boolean = false,
) = this.navigate(route) {
    // Pop up to the start destination of the graph to
    // avoid building up a large stack of destinations
    // on the back stack as users select items
    if (popUpRoute != null) {
        popUpTo(popUpRoute) {
            this.inclusive = inclusive
            this.saveState = saveState
        }
    }
    // Avoid multiple copies of the same destination when
    // reselecting the same item
    this.launchSingleTop = launchSingleTop
    // Restore state when reselecting a previously selected item
    this.restoreState = restoreState
}

fun IDestination.copy(
    route: String? = null,
    popUpRoute: String? = null,
    launchSingleTop: Boolean? = null,
    inclusive: Boolean? = null,
    saveState: Boolean? = null,
    restoreState: Boolean? = null,
): IDestination {
    val origin = this
    return object : IDestination {
        override val route: String
            get() = route ?: origin.route

        override val popUpRoute: String
            get() = popUpRoute ?: origin.popUpRoute

        override val launchSingleTop: Boolean
            get() = launchSingleTop ?: origin.launchSingleTop

        override val inclusive: Boolean
            get() = inclusive ?: origin.inclusive

        override val saveState: Boolean
            get() = saveState ?: origin.saveState

        override val restoreState: Boolean
            get() = restoreState ?: origin.restoreState

    }
}
