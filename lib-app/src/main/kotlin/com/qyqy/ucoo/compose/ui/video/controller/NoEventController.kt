package com.qyqy.ucoo.compose.ui.video.controller

import android.content.Context
import android.view.MotionEvent
import site.xzwzz.video.player.controller.GestureVideoController

/**
 *  @time 2024/7/24
 *  <AUTHOR>
 *  @package com.qyqy.keya.widget.video.controller
 */
class NoEventController(context: Context) : GestureVideoController(context) {
    init {
        setGestureEnabled(false)
        setEnableInNormal(false)
    }

    override fun dispatchTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun onTouchEvent(event: MotionEvent?): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun onInterceptTouchEvent(ev: MotionEvent?): <PERSON><PERSON><PERSON> {
        return true
    }

//    override fun onSingleTapConfirmed(e: MotionEvent): Bo<PERSON>an {
//        if (!this.isLocked() && this.isInPlaybackState()) {
//            this.togglePlay()
//        }
//        return true
//    }

//    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Bo<PERSON>an {
//        return false
//    }
//
//    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
//        return false
//    }


    override fun onDoubleTap(e: MotionEvent): Boolean {
        return false
    }

    override fun destroy() {

    }

    override fun getLayoutId(): Int {
        return 0;
    }

}