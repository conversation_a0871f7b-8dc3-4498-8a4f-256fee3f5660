package com.qyqy.ucoo.compose.domain.usecase.room

import com.qyqy.ucoo.compose.data.RoomBackgroundItem
import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.im.room.RoomRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers

class FetchBackgroundListUseCase constructor(
    private val repository: RoomRepository = RoomRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Int, List<RoomBackgroundItem>>(ioDispatcher) {

    override suspend fun execute(parameters: Int): Result<List<RoomBackgroundItem>> {
        return repository.fetchRoomBackgroundList(parameters)
    }

}