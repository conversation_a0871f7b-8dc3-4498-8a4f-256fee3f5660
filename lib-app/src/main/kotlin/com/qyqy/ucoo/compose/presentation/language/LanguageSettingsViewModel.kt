package com.qyqy.ucoo.compose.presentation.language

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.hjq.language.MultiLanguages
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.lanCode
import com.qyqy.ucoo.restartApp
import com.qyqy.ucoo.sAppKV
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString

private const val KEY_STORE_LANGUAGE_SETTINGS = "store_language_settings"

class LanguageSettingsViewModel : ViewModel() {

    private val api = createApi<LanguageApi>()

    private val _currentLanguageCode = MutableStateFlow("")
    val currentLanguageCode = _currentLanguageCode.asStateFlow()

    private val _list = MutableStateFlow<List<LangEntity.Option>>(emptyList())
    val list = _list.asStateFlow()

    init {
        viewModelScope.launch {
            try {
                val json = sAppKV.getString(KEY_STORE_LANGUAGE_SETTINGS, "")
                if (json.isNotEmpty()) {
                    val l = sAppJson.decodeFromString<List<LangEntity.Option>>(json)
                    _list.emit(l)
                }
            } catch (e: Exception) {
                e.printStackTrace()
            }
            val lanCode = MultiLanguages.getAppLanguage().lanCode
            _currentLanguageCode.emit(lanCode)
        }
    }

    fun loadSettings() {
        viewModelScope.launch {
            runApiCatching {
                api.getSettings()
            }.onSuccess {
                _list.emit(it.options)
                sAppKV.putString(KEY_STORE_LANGUAGE_SETTINGS, sAppJson.encodeToString(it.options))
            }.toastError()
        }
    }

    fun update(code: String) {
        if (currentLanguageCode.value.startsWith(code)) {
            return
        }
        viewModelScope.launch {
            _currentLanguageCode.emit(code)
            runApiCatching {
                api.update(mapOf("system_language" to code))
            }
            delay(500)
            restartApp()
        }
    }
}