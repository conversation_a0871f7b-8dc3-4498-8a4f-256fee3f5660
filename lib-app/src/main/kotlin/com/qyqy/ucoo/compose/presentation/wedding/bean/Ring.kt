package com.qyqy.ucoo.compose.presentation.wedding.bean

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class Ring(
    @SerialName("days")
    val days: Int = 0,
    @SerialName("gain_type")
    val gainType: Int = 0,
    @SerialName("icon")
    val icon: String = "",
    @SerialName("id")
    val id: Int = 0,
    @SerialName("name")
    val name: String = "",
    @SerialName("order")
    val order: Int = 0,
    @SerialName("price")
    val price: Int = 0,
    @SerialName("t")
    val t: Int = 0
)