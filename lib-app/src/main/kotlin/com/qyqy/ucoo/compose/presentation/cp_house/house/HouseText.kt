package com.qyqy.ucoo.compose.presentation.cp_house.house

import androidx.annotation.StringRes
import androidx.compose.runtime.staticCompositionLocalOf
import com.qyqy.ucoo.R

class HouseText(
    @StringRes val titleTask: Int = R.string.亲密任务,
    @StringRes val tabDailyTask: Int = R.string.每日任务,
    @StringRes val tabNormalTask: Int = R.string.常规任务,
    @StringRes val houseCoin: Int = R.string.小屋币,
    @StringRes val formatHouseCoin: Int = R.string.format当前拥有,
    @StringRes val using: Int = R.string.使用中,
    @StringRes val use: Int = R.string.使用,
    @StringRes val cancelUse: Int = R.string.取消使用,
    @StringRes val dressUpCenter: Int = R.string.装扮中心,
    @StringRes val ruleDesc: Int = R.string.规则说明,
    @StringRes val coinRecord: Int = R.string.小屋币记录,
    @StringRes val hcIncome: Int = R.string.小屋币获取,
    @StringRes val hcOutcome: Int = R.string.小屋币消耗,
    @StringRes val treasureBox: Int = R.string.幸运宝箱,
    @StringRes val lotteryRecord: Int = R.string.抽奖记录,
    @StringRes val formatMyHouseCoin: Int = R.string.format_house_coin,
    @StringRes val boxRule: Int = R.string.宝箱规则,
    @StringRes val descPrize: Int = R.string.desc奖品,
    @StringRes val prizeResult: Int = R.string.恭喜你获得以下奖励,
    @StringRes val formatC: Int = R.string.format_抽,
    @StringRes val formatUCoin: Int = R.string.format_coin_ucoo,
    @StringRes val formatHCoin: Int = R.string.format_coin_house,
    @StringRes val formatDay: Int = R.string.format_day,
    @StringRes val costU: Int = R.string.cost_u,
    @StringRes val costH: Int = R.string.cost_h,
    @StringRes val nodata: Int = R.string.什么都没有,
    @StringRes val formatBenefitCar: Int = R.string.format_benefit_car,
    @StringRes val formatBenefitHouse: Int = R.string.format_benefit_house,
    @StringRes val formatDurationDay: Int = R.string.到期,
    @StringRes val durationForever: Int = R.string.永久有效,
    val coinTextArray: IntArray = intArrayOf(R.string.house_coin_text1, R.string.house_coin_text2),
    val houseRankEmpty: Int = R.string.house_rank_empty,
    val emptyTitle: Int = R.string.empty_rank_title,
    val emptyDesc: Int = R.string.empty_rank_desc,
    val emptyButton: Int = R.string.empty_rank_btn,
    val upNow: Int = R.string.立即上榜,
    val formatInteRank: Int = R.string.format_interank_,
    val formatNamePair: Int = R.string.format_name_pari,
    val noRank: Int = R.string.no_rank,
    val nameHidden: Int = R.string.name_hidden,
    val retry: Int = R.string.点击重试,
    val rankRule: Int = R.string.榜单说明,
    val checkMore:Int=R.string.查看更多
)

val LocalHouseText = staticCompositionLocalOf { HouseText() }


