package com.qyqy.ucoo.compose.presentation.chatgroup.data

object ChatGroup {

    object Relation {
        const val JOINED = 10
        const val APPLYING = 5
        const val NONE = 0
        const val UNKNOWN = -100
    }

    object Role {
        const val NONE = -1
        const val MEMBER = 0
        const val ADMIN = 5
        const val OWNER = 10
    }

    object Events {
        const val CHATGROUP_CREATED = "chatgroup_created"
        const val CHATGROUP_MEMBER_JOIN = "chatgroup_member_join"
        const val CHATGROUP_MEMBER_QUIT = "chatgroup_member_quit"
        const val CHATGROUP_MEMBER_KICKED_OUT = "chatgroup_member_kicked_out"
        const val CHATGROUP_MEMBER_APPLY_CNT_CHANGE = "chatgroup_member_apply_cnt_change"
        const val CHATGROUP_DISBANDED = "chatgroup_disbanded"
        const val CHATGROUP_NAME_CHANGE = "chatgroup_name_change"
    }
}

fun Int.isAdmin(): <PERSON><PERSON><PERSON> = this == ChatGroup.Role.ADMIN
fun Int.isOwner(): Bo<PERSON>an = this == ChatGroup.Role.OWNER