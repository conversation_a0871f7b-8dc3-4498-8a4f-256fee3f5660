package com.qyqy.ucoo.compose.data

import com.qyqy.ucoo.im.bean.Gift

sealed interface GiftWallItem {

    val type: Int

    val span: Int

}

class SpaceItem(val height: Int) : GiftWallItem {

    override val type: Int
        get() = 0

    override val span: Int
        get() = 8
}


data object TopRadiusItem : GiftWallItem {

    override val type: Int
        get() = 1

    override val span: Int
        get() = 8
}

data object BottomRadiusItem : GiftWallItem {

    override val type: Int
        get() = 2

    override val span: Int
        get() = 8
}

class SpanItem(override val span: Int, val isStart: Boolean = false) : GiftWallItem {
    override val type: Int
        get() = 3

}

class CategoryTitleItem(val title: String, val lightCount: Int = 0, val totalCount: Int = 0, val seriesId: Int = -1) : GiftWallItem {

    override val type: Int
        get() = 4

    override val span: Int
        get() = 8


    fun updateLightCount(newCount: Int): CategoryTitleItem {
        return CategoryTitleItem(title, lightCount + newCount, totalCount, seriesId)
    }
}

data class GiftItem(val gift: Gift, val count: Int, val direction: Int = -1, val seriesId: Int = -1) : GiftWallItem {

    fun updateLightCount(newCount: Int): GiftItem {
        return GiftItem(gift, count + newCount, direction, seriesId)
    }

    override val type: Int
        get() = 5

    override val span: Int
        get() = 2
}

data class CategoryGiftWall(val category: String, val items: List<GiftWallItem>, val lightCount: Int = 0, val totalCount: Int = 0)

data class NewCategoryGiftWall(val category: String, val items: List<SeriesItem>, val lightCount: Int = 0, val totalCount: Int = 0) {
    data class SeriesItem(val seriesName: String, val items: List<GiftItem>, val lightCount: Int = 0, val totalCount: Int = 0)
}