package com.qyqy.ucoo.compose.domain.usecase.profile


import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.compose.data.toCpRelationship
import com.qyqy.ucoo.compose.domain.SuspendUseCase
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewResult
import com.qyqy.ucoo.http.getBoolOrNull
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.ProfileExtra
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.user.UserRepository
import com.qyqy.ucoo.utils.UCOOTribeManager
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.decodeFromJsonElement

class GetUserProfileUseCase constructor(
    private val userRepository: UserRepository = UserRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : SuspendUseCase<String, ViewResult.UserProfileResult>(ioDispatcher) {
    override suspend fun execute(parameters: String): ViewResult.UserProfileResult {
        return userRepository.getUserProfileInfo(parameters.toInt()).getOrThrow().let { json ->
            val user = sAppJson.decodeFromJsonElement<AppUser>(json)
            if (user.isSelf) {
                user.tribe?.relationWithMe = 10
                UCOOTribeManager.updateMyTribe(user.tribe)
            }

            val cpExtraInfo = user.cpExtraInfo

            val showRelationship = json.getBoolOrNull("relationship_available").orDefault(false)

            val showCpZone = json.getBoolOrNull("show_cp_zone").orDefault(false)

            val cp = if (showCpZone) user.cp ?: user.publicCP else null

            val cpRelationship = user.toCpRelationship()

            ViewResult.UserProfileResult(
                user = user,
                userLabel = user.attractiveFlags,
                extra = ProfileExtra(
                    showRelationship = showRelationship,
                    showCpZone = cp != null,
                    isPublishCp = user.publicCP != null,
                    cpPublishSealUrl = cpExtraInfo?.levelInfo?.footerImgUrl.orEmpty(),
                    cpPublishLevelUrl = cpExtraInfo?.levelInfo?.normalImgUrl.orEmpty(),
                    hasNextLevel = cpExtraInfo?.levelInfo?.hasNextLevel ?: false,
                    cpRelationship = cpRelationship,
                    sound = if (user.isHighQuality == true) json.parseValue("sound_brand") else null,
                    certified = json.getBoolOrNull("have_certified").orDefault(false),
                    momentTotalCount = json.getIntOrNull("moment_total_count") ?: 0,
                ),
                momentImages = json.parseValue<List<MediaInfo>>("moment_images").orEmpty(),
            )
        }
    }

}