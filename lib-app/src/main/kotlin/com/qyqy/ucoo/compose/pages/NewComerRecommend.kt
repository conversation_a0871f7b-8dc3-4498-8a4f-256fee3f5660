package com.qyqy.ucoo.compose.pages

import android.os.Bundle
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Badge
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.data.NewbieRecommendUserItem
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.state.StateList
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.compose.vm.NewComerViewModel
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.im.newer.NewerItemProvider
import com.qyqy.ucoo.isBestMatchUser
import com.qyqy.ucoo.utils.statistic.ReportKey
import com.qyqy.ucoo.utils.statistic.StatisticReporter
import com.qyqy.ucoo.widget.custom.UserLevelView

object NewbieRecommendNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        NewbieRecommendScreen()
    }
}

@Composable
private fun NewbieRecommendScreen() {
    val vm = viewModel(modelClass = NewComerViewModel::class)
    LaunchedEffect(key1 = Unit) {
        vm.refresh()
    }
    val context = LocalContext.current
    UCOOScreen(title = stringResource(id = R.string.新人推荐)) {
        StateList(viewModel = vm) { items ->
            items(items) {
                ItemNewerRecommend(it, Modifier.click {
                    val user = it.user
                    context.startActivity(ChatActivity.createIntent(context, user))
                    if (isBestMatchUser(user.id)) {
                        StatisticReporter.onClick(ReportKey.message_list_cell_best_match)
                    } else {
                        StatisticReporter.onClick(ReportKey.message_list_cell_user_msg)
                    }
                    vm.clearBadge(user.userId)
                })
            }
        }
    }
}

@Composable
private fun ItemNewerRecommend(item: NewbieRecommendUserItem, modifier: Modifier = Modifier) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(84.dp)
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(contentAlignment = Alignment.Center, modifier = Modifier.padding(end = 4.dp)) {
            ComposeImage(model = item.user.avatarUrl, modifier = Modifier.size(56.dp).clip(CircleShape))
//            if(!item.user.avatarFrame.isNullOrBlank()){
//                ComposeImage(model = item.user.avatarFrame, modifier = Modifier.size(64.dp))
//            }
        }
        Column(
            modifier = Modifier
                .heightIn(48.dp)
                .weight(1f), verticalArrangement = Arrangement.SpaceBetween
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(item.user.nickname, fontSize = 14.sp, lineHeight = 14.sp, color = Color.White)
                AndroidView(
                    factory = {
                        UserLevelView(it)
                    }, modifier = Modifier
                        .height(20.dp)
                        .padding(start = 4.dp)
                ) {
                    it.setLevel(item.user.level)
                }
            }
            Text(buildAnnotatedString {
                append(item.recommendTime)
                append("  ")
                append(item.recommendAction)
            }, color = Color(0xFF939494), fontSize = 12.sp, lineHeight = 12.sp)
        }
        Column(
            modifier = Modifier
                .heightIn(48.dp),
            verticalArrangement = Arrangement.SpaceBetween,
            horizontalAlignment = Alignment.End
        ) {
            Text(item.onlineTime, fontSize = 12.sp, lineHeight = 12.sp, color = Color(0xFF00B42A))
//            #80FFFFFF
            if (item.isUnread) {
                Badge(
                    modifier = Modifier
                        .offset(0.dp, (-2).dp)
                        .alpha(1f)
                        .border(0.25.dp, Color(0xFFFFFFFF), CircleShape),
                    containerColor = Color(0xFFF76560),
                    contentColor = Color.White,
                ) {
                    Text(text = "1")
                }
            } else {
                Spacer(modifier = Modifier)
            }
        }
    }
}


@Composable
@Preview(showBackground = true)
private fun ItemNewComerPreview() {
    ItemNewerRecommend(
        item = NewbieRecommendUserItem(
            1, true, "在线", "进入了你所在的语音房", "1分钟前", userForPreview
        )
    )
}

@Composable
@Preview
private fun NewComerRecommandPreview() {
    NewbieRecommendScreen()
}