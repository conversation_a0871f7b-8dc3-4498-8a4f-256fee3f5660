package com.qyqy.ucoo.compose.presentation.wedding.bean


import com.qyqy.ucoo.compose.presentation.wedding.DateOption
import com.qyqy.ucoo.compose.presentation.wedding.TimeSlot
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BookingTimeEntity(
    @SerialName("booking_date")
    val bookingDate: String = "",
    @SerialName("booking_date_desc")
    val bookingDateDesc: String = "",
    @SerialName("timeranges")
    val timeranges: List<Timerange> = listOf()
) {
    @Serializable
    data class Timerange(
        @SerialName("end_time")
        val endTime: String = "",
        @SerialName("is_booked")
        val isBooked: Boolean = false,
        @SerialName("range_type")
        val rangeType: Int = 0,
        @SerialName("start_time")
        val startTime: String = ""
    )

    fun toDateOption() = DateOption(label = bookingDateDesc, bookingDate = bookingDate, timeSlots = timeranges.map {
        TimeSlot("${it.startTime} - ${it.endTime}", rangeType = it.rangeType, isAvailable = !it.isBooked)
    })
}