package com.qyqy.ucoo.compose.presentation.ff

import android.graphics.drawable.Drawable
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.toAppUser
import com.qyqy.ucoo.compose.composePlaceholderDrawable
import com.qyqy.ucoo.compose.preview_providers.UserInfoProvider
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.mine.UserProfileActivity


@Composable
fun UserItem(
    user: UserInfo,
    placeholderDrawable: Drawable = composePlaceholderDrawable(),
    nameColor: Color = Color.White,
    onAvatarClicked: ((UserInfo) -> Unit)? = null,
    afterNickName: @Composable RowScope.() -> Unit = {},
    endCompose: @Composable RowScope.() -> Unit = {},
) {
    val context = LocalContext.current
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .padding(16.dp, 12.dp)
            .fillMaxWidth(),
    ) {
        CircleComposeImage(
            model = user.avatarUrl,
            modifier = Modifier
                .size(56.dp, 56.dp)
                .clickable(onClick = {
                    onAvatarClicked?.invoke(user) ?: run {
                        context.startActivity(UserProfileActivity.createIntent(context, user.toAppUser()))
                    }
                }),
        )

        Column(
            verticalArrangement = Arrangement.Center,
            modifier = Modifier
                .padding(horizontal = 8.dp)
        ) {
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    text = user.nickname,
                    color = nameColor,
                    fontSize = 16.sp,
                    modifier = Modifier.requiredWidthIn(10.dp, 120.dp),
                    overflow = TextOverflow.Ellipsis,
                    maxLines = 1
                )
                afterNickName()
            }
            Spacer(modifier = Modifier.height(8.dp))
            AgeGender(age = user.age, isBoy = user.isBoy)
        }
        Spacer(modifier = Modifier.weight(1f, true))
        endCompose()
    }
}

@Preview(backgroundColor = 0xFF000000, showBackground = true, showSystemUi = false)
@Composable
fun UserItemPreview(@PreviewParameter(UserInfoProvider::class) user: UserInfo) {
    UserItem(user)
}

