package com.qyqy.ucoo.compose.state

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.LocalUserPartition
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.http.ApiException

@Composable
fun EmptyView(
    modifier: Modifier = Modifier,
    textRes: Int = if (LocalUserPartition.current.isUCOO) R.string.暂无推荐内容 else R.string.cupid_no_data,
    iconRes: Int = if (LocalUserPartition.current.isUCOO) R.drawable.ic_empty_for_conversation
    else R.drawable.cupid_empty,
) {
    BasicStateView(message = stringResource(id = textRes), modifier = modifier, iconRes = iconRes)
}

@Composable
fun ErrorView(
    throwable: Throwable?,
    modifier: Modifier = Modifier,
    iconRes: Int = if (LocalUserPartition.current.isUCOO) R.drawable.ic_empty_for_conversation
    else R.drawable.cupid_empty,
    retryAction: @Composable () -> Unit = {},
) {
    val errorMessage = if (throwable is ApiException) {
        throwable.errorMsg.orDefault(throwable.msg)
    } else {
        throwable?.message ?: stringResource(id = R.string.cpd网络错误)
    }
    BasicStateView(
        message = errorMessage,
        modifier = modifier, iconRes = iconRes,
        action = retryAction
    )
}

@Composable
fun BasicStateView(
    message: String,
    iconRes: Int,
    modifier: Modifier = Modifier,
    action: @Composable () -> Unit = {}
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center,
        modifier = modifier
    ) {
        Image(
            painter = painterResource(iconRes),
            contentDescription = "",
            modifier = Modifier.height(150.dp)
        )
        Spacer(modifier = Modifier.height(20.dp))
        AppText(
            text = message,
            modifier = Modifier.padding(horizontal = 15.dp),
            color = MaterialTheme.colorScheme.onSurface.copy(alpha = 0.5f),
            fontSize = 12.sp
        )
        action()
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun EmptyViewPreview() {
    AppTheme {
        Box(modifier = Modifier.fillMaxSize()) {
            EmptyView(modifier = Modifier.align(Alignment.Center))
        }
    }
}