package com.qyqy.ucoo.compose.presentation.game

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.UIStateContent
import com.qyqy.ucoo.compose.state.isError
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior



@Composable
fun MyDiamondPage(
    diamond: Int,
    uiState: UIState<GameList>,
    onAction: (UIAction) -> Unit = {},
) {
    Column(modifier = Modifier.fillMaxSize()) {
        val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
        AppTitleBar(title = stringResource(id = R.string.我的钻石), onBack = {
            onBackPressedDispatcher?.onBackPressed()
        }) {
            Text(
                text = stringResource(id = R.string.diamond_store),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp)
                    .clickable {
                        onAction(UIAction.OnMall)
                    },
                color = Color.White,
                fontSize = 14.sp
            )
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            DiamondBalanceCard(diamond = diamond, modifier = Modifier.padding(top = 24.dp))

            Text(
                text = uiState.data?.diamondAccount?.tooltips.orEmpty().ifEmpty {
                    stringResource(id = R.string.钻石有效期提示)
                },
                modifier = Modifier.padding(top = 12.dp),
                color = colorResource(id = R.color.white_alpha_30),
                fontSize = 12.sp
            )

            Row(
                modifier = Modifier
                    .padding(top = 40.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                HorizontalDivider(modifier = Modifier.weight(1f), thickness = 0.5.dp, color = Color(0x26FFFFFF))
                Text(
                    text = stringResource(id = R.string.参与下列游戏可获得钻石),
                    modifier = Modifier.widthIn(max = 240.dp),
                    color = Color.White,
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
                HorizontalDivider(modifier = Modifier.weight(1f), thickness = 0.5.dp, color = Color(0x26FFFFFF))
            }

            Spacer(modifier = Modifier.height(15.dp))

            UIStateContent(
                uiState = uiState,
                onEmptyOrError = {
                    if (it.isError) {
                        onAction(UIAction.Retry)
                    }
                }
            ) {
                val scrollState = rememberLazyGridState()
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    modifier = Modifier
                        .fillMaxSize()
                        .overScrollVertical(),
                    state = scrollState,
                    contentPadding = PaddingValues(top = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    flingBehavior = rememberOverscrollFlingBehavior { scrollState }
                ) {
                    items(value.items, key = {
                        it.id
                    }) {
                        Column(
                            modifier = Modifier
                                .aspectRatio(1f)
                                .clip(Shapes.small)
                                .background(Color(0xFF272829))
                                .clickable {
                                    onAction(UIAction.OnStartGame(it.id, it.link))
                                }
                        ) {
                            ComposeImage(
                                model = it.img,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .weight(1f)
                            )
                            Text(
                                text = it.name,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(40.dp)
                                    .wrapContentSize(Alignment.Center),
                                fontSize = 14.sp,
                                color = Color.White,
                                textAlign = TextAlign.Center
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun DiamondBalanceCard(diamond: Int, modifier: Modifier = Modifier, content: @Composable ColumnScope.() -> Unit = {}) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(2.858f)
            .paint(painter = painterResource(id = R.drawable.card_diamond_balance))
            .padding(start = 24.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp, Alignment.CenterVertically)
    ) {
        Text(text = stringResource(id = R.string.我的钻石余额), color = Color(0xFFFAEBFF), fontSize = 14.sp)
        Text(text = diamond.toString(), color = Color(0xFFFAEBFF), fontSize = 24.sp, fontFamily = D_DIN)
        content()
    }
}

@Preview
@Composable
private fun PreviewMyDiamondPage() {
    MyDiamondPage(
        1000, UIState.Data(
            GameList(
                items = listOf(
                    GameItem(1, "极速赛车"),
                    GameItem(2, "极速赛车"),
                    GameItem(3, "极速赛车"),
                    GameItem(4, "极速赛车"),
                    GameItem(5, "极速赛车"),
                )
            )
        )
    )
}
