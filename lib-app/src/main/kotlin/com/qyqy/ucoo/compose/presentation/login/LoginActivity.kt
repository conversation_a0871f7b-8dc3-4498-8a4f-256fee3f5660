package com.qyqy.ucoo.compose.presentation.login

import android.os.Build
import android.os.Bundle
import android.view.WindowManager
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.LocalUserPartition
import com.qyqy.ucoo.getUserPartition
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.credentials.CredentialManager
import androidx.credentials.GetCredentialRequest
import androidx.credentials.exceptions.GetCredentialCancellationException
import androidx.credentials.exceptions.GetCredentialException
import androidx.credentials.exceptions.NoCredentialException
import androidx.lifecycle.lifecycleScope
import androidx.navigation.compose.rememberNavController
import com.google.android.libraries.identity.googleid.GetGoogleIdOption
import com.google.android.libraries.identity.googleid.GetSignInWithGoogleOption
import com.overseas.common.ext.startActivity
import com.overseas.common.utils.EncryptionUtils
import com.qyqy.cupid.ui.CupidMainActivity
import com.qyqy.cupid.ui.login.LineLoginContract
import com.qyqy.ucoo.ABTest
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.INotificationSetting
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.compose.vm.login.LoginViewEffect
import com.qyqy.ucoo.compose.vm.login.LoginViewModel
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.home.HomeActivity
import com.qyqy.ucoo.login.third.showWarmTipDialog
import com.qyqy.ucoo.sAccountToken
import com.qyqy.ucoo.toast
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch


class LoginActivity : BaseActivity(), INotificationSetting {

    private val viewModel by viewModels<LoginViewModel>()

    ///////////////////////////////////////////////////////////////////////////
    // google
    ///////////////////////////////////////////////////////////////////////////

    private val signupWithGoogleOption by lazy(LazyThreadSafetyMode.NONE) {
        // 文档说当登录凭证找不到时用注册的方式
        GetGoogleIdOption.Builder()
            .setFilterByAuthorizedAccounts(false)
            .setServerClientId(BuildConfig.GOOGLE_WEB_CLIENT_ID)
            .build()
    }

    ///////////////////////////////////////////////////////////////////////////
    // google
    ///////////////////////////////////////////////////////////////////////////
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.Q) {
            window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_RESIZE)
        }
        setContent {
            MaterialTheme {
                val navController = rememberNavController()

                LaunchedEffect(key1 = Unit) {
                    viewModel.effectFlow.onEach {
                        when (it) {
                            is LoginViewEffect.Navigation -> {
                                navController.navigateTo(it.destination)
                            }

                            is LoginViewEffect.ReLogin -> {
                                navController.popBackStack(
                                    if (AppUserPartition.isCupid)
                                        CupidLoginDestination.Start.route
                                    else
                                        LoginDestination.Start.route, inclusive = false, saveState = false
                                )
                            }

                            is LoginViewEffect.Toast -> {
                                toast(it.msg)
                            }

                            is LoginViewEffect.DialogTip -> {
                                showWarmTipDialog(this@LoginActivity, it.msg, it.button, false)
                            }
                        }
                    }.launchIn(this)
                }

                Box(modifier = Modifier.fillMaxSize()) {
                    CompositionLocalProvider(LocalUserPartition provides remember {
                        AppUserPartition.current
                    }) {
                        LoginNavHost(loginActivity = this@LoginActivity, viewModel = viewModel, navController = navController, modifier = Modifier.fillMaxSize())
                        if (viewModel.showLoading) {
                            Box(modifier = Modifier.fillMaxSize()) {
                                Loading() {
                                    viewModel.showLoading = false
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onAccountTokenStatusChange(isInvalid: Boolean) {
        if (!isInvalid && sAccountToken?.finishFillInfo == true && sAccountToken?.needVerifyPhone == false) {
            if (sAccountToken?.getUserPartition()?.isCupid == true) {
                CupidMainActivity::class.java
            } else {
                HomeActivity::class.java
            }.startActivity()
            finish()
        }
    }

    override fun isNotificationEnable(): Boolean {
        return false
    }

    fun googleSignIn(signIn: Boolean = true) {
        val signInWithGoogleOption = GetSignInWithGoogleOption.Builder(BuildConfig.GOOGLE_WEB_CLIENT_ID)
            .setNonce(EncryptionUtils.generateRandomString())
            .build()

        val request: GetCredentialRequest = GetCredentialRequest.Builder()
            .addCredentialOption(if (signIn) signInWithGoogleOption else signupWithGoogleOption)
            .build()

        lifecycleScope.launch {
            try {
                val result = CredentialManager.create(this@LoginActivity).getCredential(
                    request = request,
                    context = this@LoginActivity,
                )
                viewModel.handleGoogleSignInResult(result)
            } catch (e: GetCredentialException) {
                handleFailure(signIn, e)
            } catch (e: Throwable) {
                val msg = "google signIn error(${e.javaClass.simpleName}): ${e.message}"
                Analytics.appReportEvent(DataPoint.eventBody(DataTrace.Event.Login.Error, ABTest.Device.LoginUI, extra = msg))
                toast(msg)
            }
        }
    }

    private fun handleFailure(signIn: Boolean, e: GetCredentialException) {
        if (e is GetCredentialCancellationException) {
            if (AppUserPartition.isUCOO) {
                toast(getString(R.string.登录已取消))
            } else{
                toast(getString(R.string.cpd登录已取消))
            }
            Analytics.appReportEvent(
                DataPoint.eventBody(
                    DataTrace.Event.Login.Error, ABTest.Device.LoginUI,
                    extra = "google signIn error: canceled"
                )
            )
        } else if (signIn && e is NoCredentialException) {
            googleSignIn(false)
        } else {
            val msg = "google signIn error(${e.javaClass.simpleName}): ${e.message}"
            Analytics.appReportEvent(DataPoint.eventBody(DataTrace.Event.Login.Error, ABTest.Device.LoginUI, extra = msg))
            toast(msg)
        }
    }

    //region LINE登录

    private val lineSignLauncher = registerForActivityResult(LineLoginContract()) { result ->
        viewModel.handleLineSignInResult(result)
    }

    fun lineSignIn() {
        lineSignLauncher.launch(Unit)
    }

    //endregion
}