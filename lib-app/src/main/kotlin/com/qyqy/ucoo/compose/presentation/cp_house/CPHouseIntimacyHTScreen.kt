package com.qyqy.ucoo.compose.presentation.cp_house

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.CPHouseIntimacyHistoryBean
import com.qyqy.ucoo.compose.router.UCOOPreviewTheme
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.compose.theme.colorDarkScreenBackground
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching

/**
 * CP小屋亲密度历史
 */
@Composable
fun CPHouseIntimacyHTScreen() {
    val backOwner = LocalOnBackPressedDispatcherOwner.current
    val viewModel = viewModel(modelClass = CPHouseIntimacyViewModel::class, factory = viewModelFactory {
        initializer {
            CPHouseIntimacyViewModel()
        }
    })
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding(),
        topBar = {
            AppTitleBar(title = stringResource(id = R.string.亲密度记录), onBack = {
                backOwner?.onBackPressedDispatcher?.onBackPressed()
            })
        },
        containerColor = colorDarkScreenBackground
    ) { pv ->
        val list by viewModel.dataState
        CPHouseIntimacyContent(isRefresh = viewModel.isRefreshing,
            modifier = Modifier.padding(pv),
            isLoaded = viewModel.isLoaded,
            list = list, onRefresh = { viewModel.refresh() }) {
            itemLoadMore(list.isNotEmpty() && viewModel.allowLoad, viewModel.isLoadingNow, viewModel.hasMore) {
                viewModel.loadMore()
            }
        }
    }
}

@Composable
private fun CPHouseIntimacyContent(
    isRefresh: Boolean,
    isLoaded: Boolean,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
    list: List<CPHouseIntimacyHistoryBean> = listOf(),
    loadMoreContent: LazyListScope.() -> Unit = {},
) {
    PullRefreshBox(
        modifier = modifier
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        isRefreshing = isRefresh,
        onRefresh = onRefresh
    ) {
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            if (isLoaded) {
                if (list.isEmpty()) {
                    item {
                        EmptyView(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(0.75f),
                            textRes = R.string.no_records
                        )
                    }
                } else {
                    items(list) {
                        CPHouseIntimacyItem(it)
                    }
                }
            }
//            itemLoadMore(list.isNotEmpty() && vm.allowLoad, vm.isLoadingNow, vm.hasMore) {
//                vm.loadMore()
//            }
            loadMoreContent(this@LazyColumn)
        }
    }
}

@Composable
private fun CPHouseIntimacyItem(data: CPHouseIntimacyHistoryBean) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 20.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(text = data.changeReason, fontSize = 14.sp, color = Color.White)
            if (data.userName.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                Text(text = data.userName, fontSize = 12.sp, color = colorWhite50Alpha)
            } else {
                Spacer(modifier = Modifier.height(4.dp))
            }
            Spacer(modifier = Modifier.height(8.dp))
            Text(text = data.readableTimeStamp, fontSize = 12.sp, color = colorWhite50Alpha)
        }
        Text(text = data.score, fontSize = 14.sp, color = Color.White)
    }
}

@Composable
@Preview
private fun COHouseIntimacyItemPreview() {
    UCOOPreviewTheme {
        CPHouseIntimacyContent(
            false, true, {}, modifier = Modifier.background(color = Color.Black), list = listOf(
                CPHouseIntimacyHistoryBean(0, "幼儿园班花", "日常任务签到", 10000, "十分钟前", "+10亲密度"),
                CPHouseIntimacyHistoryBean(0, "幼儿园班花", "日常任务签到", 10000, "十分钟前", "+10亲密度"),
                CPHouseIntimacyHistoryBean(0, "幼儿园班花", "日常任务签到", 10000, "十分钟前", "+10亲密度"),
                CPHouseIntimacyHistoryBean(0, "幼儿园班花", "日常任务签到", 10000, "十分钟前", "+10亲密度"),
                CPHouseIntimacyHistoryBean(0, "幼儿园班花", "日常任务签到", 10000, "十分钟前", "+10亲密度"),
            )
        )
    }
}

private class CPHouseIntimacyViewModel : LiStateViewModel<CPHouseIntimacyHistoryBean>() {
    private val api = createApi<CPHouseApi>()

    private var lastId = 0

    init {
        refresh()
    }

    override fun onStartRequest(isRefresh: Boolean) {
        lastId = if (isRefresh) {
            0
        } else {
            dataState.value.lastOrNull()?.id ?: -1
        }
    }

    override suspend fun fetch(): List<CPHouseIntimacyHistoryBean> {
        return runApiCatching { api.getCpIntimacyHistory(lastId) }
            .toastError()
            .getOrNull().orEmpty()
    }

}