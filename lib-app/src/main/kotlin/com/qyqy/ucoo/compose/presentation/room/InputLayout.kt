package com.qyqy.ucoo.compose.presentation.room

import android.view.KeyEvent
import androidx.activity.compose.BackHandler
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.verticalDrag
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.enableClick
import com.qyqy.ucoo.compose.presentation.config.LocalChatUIProvider
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CommonButton
import com.qyqy.ucoo.compose.vm.room.UIAction
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.emoji.EmojiBoard
import com.qyqy.ucoo.im.emoji.insertText
import com.qyqy.ucoo.im.inputpanel.audio.AudioDelegateHelper
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper

fun bottomBar(
    panelState: KeyboardPanelState,
    textFieldValue: MutableState<TextFieldValue>,
    onAction: (UIAction) -> Unit,
): ItemContent<ColumnScope> = {
    val emojiPanel = panelState.panels[0]
    val audioPanel = panelState.panels[1]

    val showSendBtn by remember {
        derivedStateOf {
            textFieldValue.value.text.isNotEmpty() && !panelState.isShowing(audioPanel)
        }
    }

    Row(
        modifier = Modifier.padding(horizontal = 16.dp), verticalAlignment = Alignment.Bottom
    ) {

        Box(
            modifier = Modifier
                .clip(RoundedCornerShape(20.dp))
                .weight(1f)
                .heightIn(min = 40.dp)
                .background(Color(0x4D6A6A6A)),
            contentAlignment = Alignment.CenterStart
        ) {
            val isAudioPanelShowing = panelState.isShowing(audioPanel)

            // 不能隐藏会抛异常
            AppBasicTextField(
                value = textFieldValue.value,
                onValueChange = {
                    textFieldValue.value = it
                },
                modifier = Modifier
                    .alpha(if (isAudioPanelShowing) 0f else 1f)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 10.dp)
                    .withInputField(panelState),
                textStyle = TextStyle.Default.copy(
                    color = Color.White, fontSize = 12.sp
                ),
                maxLines = 6,
                cursorBrush = SolidColor(Color.White),
                decorationBox = { innerTextField ->
                    innerTextField()
                    if (textFieldValue.value.text.isEmpty()) {
                        AppText(
                            text = stringResource(id = R.string.hint_tribe_hall_edit), fontSize = 12.sp, color = colorWhite50Alpha
                        )
                    }
                })

            val rootView = LocalView.current.rootView

            val cancelOffset = with(LocalDensity.current) {
                remember {
                    -135.dp.toPx()
                }
            }

            val launcher = rememberLauncherForActivityResult(ActivityResultContracts.RequestPermission()) {
                if (!it) {
                    toastRes(R.string.此操作需要授权访问录音的权限)
                }
            }

            if (isAudioPanelShowing) {
                val audioDelegate = remember(rootView) {
                    AudioDelegateHelper(rootView, onRequestPermission = {
                        launcher.launch(it)
                    }) {
                        onAction(UIAction.OnSendMessage(it))
                    }
                }

                DisposableEffect(key1 = audioDelegate) {
                    onDispose {
                        audioDelegate.stopRecord()
                    }
                }

                var textResId by remember {
                    mutableIntStateOf(R.string.rc_voice_press_to_input)
                }

                val msgAudioPlayer = LocalMsgAudioPlayer.current

                AppText(
                    text = stringResource(id = textResId),
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                        .wrapContentHeight()
                        .enableClick()
                        .pointerInput(key1 = Unit) {
                            awaitEachGesture {
                                val id = awaitFirstDown().id
                                if (audioDelegate.startRecord()) {
                                    msgAudioPlayer.cancel()
                                    textResId = R.string.rc_voice_release_to_send
                                    verticalDrag(id) {
                                        if (it.position.y < cancelOffset) {
                                            textResId = R.string.rc_voice_release_to_cancel
                                            audioDelegate.showRecordTips(true)
                                        } else {
                                            textResId = R.string.rc_voice_release_to_send
                                            audioDelegate.showRecordTips(false)
                                        }
                                    }
                                    textResId = R.string.rc_voice_press_to_input
                                    audioDelegate.stopRecord()
                                }
                            }
                        },
                    fontSize = 14.sp,
                    color = Color.White,
                    textAlign = TextAlign.Center,
                )
            }
        }

        Image(
            painter = if (panelState.isShowing(emojiPanel)) {
                painterResource(id = R.drawable.ic_chat_keyboard)
            } else {
                painterResource(id = R.drawable.ic_chat_emoji)
            },
            contentDescription = null,
            modifier = Modifier
                .padding(start = 8.dp, bottom = 4.dp)
                .size(30.dp)
                .clickable {
                    panelState.switchPanel(emojiPanel)
                },
            contentScale = ContentScale.Crop,
        )

        AnimatedVisibility(visible = showSendBtn) {
            CommonButton(
                modifier = Modifier
                    .padding(start = 8.dp, bottom = 4.dp)
                    .size(72.dp, 32.dp),
                gradient = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))),
                colors = ButtonDefaults.buttonColors(
                    Color.Transparent,
                    Color.White,
                    colorResource(id = R.color.white_alpha_20),
                    colorResource(id = R.color.white_alpha_50),
                ),
                shape = CircleShape,
                onClick = {
                    if (textFieldValue.value.text.isNotEmpty()) {
                        onAction(UIAction.OnSendText(textFieldValue.value.text))
                        textFieldValue.value = TextFieldValue()
                    }
                }) {
                Text(text = stringResource(id = R.string.send), fontSize = 12.sp, fontWeight = FontWeight.Medium)
            }
        }
    }
    val chatUIProvider = LocalChatUIProvider.current
    Row(
        modifier = Modifier
            .padding(vertical = 16.dp, horizontal = 44.dp)
            .fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
    ) {
        Image(
            painter = if (panelState.isShowing(audioPanel)) {
                painterResource(id = R.drawable.ic_chat_keyboard)
            } else {
                painterResource(id = R.drawable.ic_chat_voice_normal)
            },
            contentDescription = null,
            modifier = Modifier
                .size(24.dp)
                .clickable {
                    panelState.switchPanel(audioPanel)
                },
            contentScale = ContentScale.Crop,
        )

        val uploadImageHelper = rememberRequestAlbumPermissionHelper(context = LocalContext.current, needCrop = false) {
            val result = it.list.first()
            onAction(UIAction.OnSendMessage(MessageBundle.Image.create(result.uri, result.path, result.width, result.height)))
        }

        Image(
            painter = painterResource(id = R.drawable.ic_chat_album),
            contentDescription = null,
            modifier = Modifier
                .size(24.dp)
                .clickable {
                    uploadImageHelper.start()
                },
            contentScale = ContentScale.Crop,
        )

        Image(
            painter = painterResource(id = R.drawable.ic_room_game),
            contentDescription = null,
            modifier = Modifier
                .size(24.dp)
                .clickable {
                    onAction(UIAction.OnGameCenter)
                },
            contentScale = ContentScale.Crop,
        )

        if (chatUIProvider?.showBottomGiftIcon == true) {
            Image(
                painter = painterResource(id = R.drawable.ic_room_gift),
                contentDescription = null,
                modifier = Modifier
                    .size(24.dp)
                    .clickable {
                        onAction(UIAction.OnOpenGift)
                    },
                contentScale = ContentScale.Crop,
            )
        }

    }
}

fun panelContent(backgroundColor: Color, state: KeyboardPanelState, textFieldValue: MutableState<TextFieldValue>): @Composable BoxScope.() -> Unit = {
    val current = state.targetVisiblePanel
    if (current != null && !current.isKeyboard && current.autoHideEnable) {
        BackHandler {
            state.hidePanel(current)
        }
    }
    val composeView = LocalView.current
    for (panel in state.panels) {
        when (panel.key) {
            EmojiPanel -> {
                val alpha by remember {
                    derivedStateOf {
                        if (state.isAllowPanelShowing(panel)) 1f else 0f
                    }
                }
                EmojiBoard(
                    modifier = Modifier
                        .height(300.dp)
                        .alpha(alpha)
                        .background(backgroundColor)
                        .zIndex(if (state.targetVisiblePanel == panel) 1f else 0f)
                        .withPanelSize(state, panel)
                        .navigationBarsPadding(),
                    onDel = {
                        composeView.dispatchKeyEvent(KeyEvent(KeyEvent.ACTION_DOWN, KeyEvent.KEYCODE_DEL))
                    },
                    onAppend = {
                        textFieldValue.value = textFieldValue.value.insertText(it)
                    }
                )
            }
        }
    }
}