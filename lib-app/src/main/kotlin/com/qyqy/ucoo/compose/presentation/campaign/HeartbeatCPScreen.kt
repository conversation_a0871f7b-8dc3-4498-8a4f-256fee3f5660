package com.qyqy.ucoo.compose.presentation.campaign

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.CupidRouters
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isHighQuality
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.im.bean.CPRightPageInfo
import com.qyqy.ucoo.im.bean.Gift
import com.qyqy.ucoo.im.bean.Scene
import com.qyqy.ucoo.im.room.CpContract
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.launch

@Composable
fun HeartBeatCP(
    info: CPRightPageInfo,
    onClick: () -> Unit,
    onBack: () -> Unit = { ActivityLifecycle.topActivity?.finish() },
) {
    val gift = info.gift
    val buttonContainerHeight = 160.dp
    Box(
        modifier = Modifier
            .fillMaxSize(1f)
    ) {
        LazyColumn(
            modifier = Modifier
                .background(Color(0xFFFE8CB7))
//            .overScrollVertical()
        ) {
            item {
                Box(
                    modifier = Modifier
                        .fillMaxWidth(1f)
                        .aspectRatio(1125f / 1200)
                        .paint(
                            painter = painterResource(id = R.drawable.header_lover),
                            contentScale = ContentScale.Inside
                        )
                        .padding(top = 20.dp)
//                        .windowInsetsPadding(androidx.compose.foundation.layout.WindowInsets.systemBars)

                ) {
                    AppTitleBar(
                        title = stringResource(id = R.string.title_cp_page),
                        onBack = onBack
                    )
                    Row(
                        modifier = Modifier
                            .fillMaxWidth(1f)
                            .padding(top = 190.dp)
                    ) {
                        Spacer(modifier = Modifier.weight(1f))
                        UserItem(user = sUser)
                        Spacer(modifier = Modifier.weight(2f))
                        UserItem(user = info.targetUser)
                        Spacer(modifier = Modifier.weight(1f))
                    }
                }
            }
            if (info.cpRights.isNotEmpty()) {
                item {
                    Spacer(modifier = Modifier.height(16.dp))
                }
                item {
                    RewardCard(
                        modifier = Modifier
                            .aspectRatio(1452f / 788)
                            .paint(painterResource(id = R.drawable.frame_love_section), contentScale = ContentScale.Crop)
                    ) {
                        info.cpRights.take(4).forEachIndexed { index, cpRight ->
                            if (index > 0) {
                                Spacer(modifier = Modifier.width(8.dp))
                            }
                            RewardItem(modifier = Modifier.weight(1f), title1 = cpRight.text, cpRight.valueCoinCnt, cpRight.icon)
                        }
                    }
                }
            }
            //祝福礼包
            if (info.giftPacketVisible) {
                item {
                    Spacer(modifier = Modifier.height(15.dp))
                }
                item {
                    RewardCard(
                        title = stringResource(id = R.string.bless_gift_packet),
                        desc = if (info.giftPackageValueCoinCnt > 0)
                            stringResource(
                                id = R.string.format_gift_value,
                                info.giftPackageValueCoinCnt
                            )
                        else "",
                        modifier = Modifier
                            .aspectRatio(1452f / 788)
                            .paint(painterResource(id = R.drawable.frame_love_section), contentScale = ContentScale.Crop)
                    ) {
                        val list = info.giftPackageRights.take(4)
                        val size = list.size
                        if (size > 3) {
                            list.forEachIndexed { index, cpRight ->
                                if (index > 0) {
                                    Spacer(modifier = Modifier.width(8.dp))
                                }
                                RewardItem(
                                    modifier = Modifier.weight(1f),
                                    title1 = cpRight.text,
                                    cpRight.valueCoinCnt,
                                    cpRight.icon
                                )
                            }
                        } else {
                            list.forEachIndexed { index, cpRight ->
                                if (index == 0) {
                                    Spacer(modifier = Modifier.weight(1f))
                                }
                                RewardItem(
                                    modifier = Modifier.width(78.dp),
                                    title1 = cpRight.text,
                                    cpRight.valueCoinCnt,
                                    cpRight.icon
                                )
                                Spacer(modifier = Modifier.weight(1f))
                            }
                        }

                    }
                }
            }

            item {
                Spacer(modifier = Modifier.height(16.dp))
            }
            item {
                RewardCard(
                    modifier = Modifier
                        .aspectRatio(1452f / 788)
                        .paint(painterResource(id = R.drawable.frame_love_section), contentScale = ContentScale.Crop),
                    title = stringResource(id = R.string.text_send_confession_gift)
                ) {
                    Spacer(modifier = Modifier.weight(124f))
                    val cross = sUser.isHighQuality || gift.crossOutPrice > 0
                    RewardItem(
                        modifier = Modifier
                            .weight(87f)
                            .requiredWidthIn(max = 88.dp),
                        title1 = if (sUser.isHighQuality) {
                            stringResource(id = R.string.free)
                        } else {
                            gift.priceLabel.ifEmpty { gift.name }
                        },
                        if (gift.crossOutPrice > 0) gift.crossOutPrice else gift.price,
                        gift.icon,
                        cross
                    )
                    Spacer(modifier = Modifier.weight(124f))
                }
            }

            item {
                Spacer(modifier = Modifier.height(buttonContainerHeight))
            }
        }
        Box(
            modifier = Modifier
                .fillMaxWidth(1f)
                .height(buttonContainerHeight)
                .background(Brush.verticalGradient(listOf(Color.Transparent, Color(0xFFFF5696))))
                .align(Alignment.BottomCenter),
            contentAlignment = Alignment.Center
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth(1f)
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth(1f)
                        .padding(horizontal = 32.dp)
                        .aspectRatio(933f / 288)
                        .paint(painterResource(id = R.drawable.frame_button))
                        .noEffectClickable(onClick = onClick),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.Center
                ) {
                    val buttonText = if (!info.inviteCode.isNullOrEmpty()) {
                        stringResource(id = R.string.cp_send_gift)
                    } else {
                        stringResource(id = R.string.send_confession_gift)
                    }
                    AppText(
                        text = buttonText,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.ExtraBold,
                        color = Color.White,
                        lineHeight = 18.sp
                    )
                    if (sUser.isHighQuality) {
                        AppText(
                            text = stringResource(id = R.string.free),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Normal,
                            color = Color.White
                        )
                    } else {
                        val priceLabel = gift.priceLabel
                        if (priceLabel.isNotEmpty()) {
                            Text(text = buildAnnotatedString {
                                if (priceLabel.isNotEmpty()) {
                                    append(priceLabel)
                                }
                                append(stringResource(id = R.string.format_金币, gift.price))
                                val crossOutPrice = gift.crossOutPrice
                                if (crossOutPrice > 0) {
                                    withStyle(SpanStyle(textDecoration = TextDecoration.LineThrough)) {
                                        append("($crossOutPrice)")
                                    }
                                }
                            }, fontSize = 12.sp, fontWeight = FontWeight.Normal, color = Color.White)
                        }
                    }
                }
                AppText(
                    modifier = Modifier
                        .fillMaxWidth(1f)
                        .padding(bottom = 30.dp),
                    textAlign = TextAlign.Center,
                    text = stringResource(id = R.string.send_gift_to_cp_auto),
                    fontSize = 10.sp,
                    color = colorWhite50Alpha
                )
            }
        }
    }

}

@Composable
private fun RewardCard(
    modifier: Modifier = Modifier,
    title: String = stringResource(id = R.string.unlock_right),
    desc: String = "",
    content: @Composable RowScope.() -> Unit,
) {
    val density = LocalDensity.current
    Column(
        modifier = modifier
            .fillMaxWidth(1f)
            .padding(start = 6.dp, end = 6.dp),
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth(1f)
                .height(50.dp),
            contentAlignment = Alignment.TopCenter
        ) {
            AppText(
                text = buildAnnotatedString {
                    append(title)
                    if (desc.isNotEmpty()) {
                        withStyle(SpanStyle(fontSize = 12.sp)) {
                            append(desc)
                        }
                    }
                },
                fontWeight = FontWeight.ExtraBold,
                fontSize = 18.sp,
                color = Color(0xFFA1161A),
                style = TextStyle(
                    shadow = density.run {
                        Shadow(Color(0x40000000), Offset(0f, 0.5.dp.toPx()), blurRadius = 1.5.dp.toPx())
                    },
                    brush = Brush.verticalGradient(listOf(Color(0xFFE84B4F), Color(0xFF8E090C)))
                ),
                modifier = Modifier.padding(top = 6.dp)
            )
        }
        Row(
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(horizontal = 8.dp),
            content = content
        )
    }
}


@Composable
fun RewardItem(
    modifier: Modifier = Modifier,
    title1: String? = "CP头像框",
    coinCount: Int,
    icon: String,
    cross: Boolean = false,
) {
    Column(modifier = modifier) {
        ComposeImage(
            model = icon, contentScale = ContentScale.Inside, modifier = Modifier
                .fillMaxWidth(1f)
                .aspectRatio(1f)
                .background(Color(0xFFFFEEEF), Shapes.small)
        )
        Spacer(modifier = Modifier.height(12.dp))
        if (!title1.isNullOrEmpty()) {
            Text(
                fontWeight = FontWeight.Bold,
                text = title1,
                fontSize = 11.sp,
                color = Color(0xFFA1161A),
                textAlign = TextAlign.Center,
                modifier = Modifier.fillMaxWidth(1f)
            )
        }
        if (coinCount > 0) {
            if (cross) {
                AppText(
                    text = buildAnnotatedString {
                        withStyle(SpanStyle(textDecoration = TextDecoration.LineThrough)) {
                            append(stringResource(id = R.string.format_金币, coinCount))
                        }
                    },
                    fontWeight = FontWeight.Bold,
                    fontSize = 11.sp,
                    color = Color(0xFFA1161A),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(1f)
                )
            } else {
                AppText(
                    text = stringResource(id = R.string.format_金币, coinCount),
                    fontWeight = FontWeight.Bold,
                    fontSize = 11.sp,
                    color = Color(0xFFA1161A),
                    textAlign = TextAlign.Center,
                    modifier = Modifier.fillMaxWidth(1f)
                )
            }

        }
    }
}


@Composable
private fun UserItem(user: User) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        CircleComposeImage(
            model = user.avatarUrl, modifier = Modifier
                .size(64.dp)
                .border(2.dp, Color(0xFFFF9BC5), CircleShape)
        )
        Spacer(modifier = Modifier.height(2.dp))
        Box(
            modifier = Modifier
                .height(24.dp)
                .requiredWidthIn(min = 80.dp)
                .background(
                    Brush.horizontalGradient(
                        listOf(
                            Color(0x00FF3535),
                            Color(0xFFFF3535),
                            Color(0xFFFF3535),
                            Color(0x00FF3535)
                        )
                    )
                )
                .padding(horizontal = 5.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(text = user.nickname, fontSize = 12.sp, color = Color.White)
        }
    }
}

@Composable
fun HeartBeatCPScreen(info: CPRightPageInfo, onBack: () -> Unit, toC2c: () -> Unit) {
    LoadingLayout {

        val userRepository = UserManager.userRepository

        val scope = rememberCoroutineScope()

        val loadingState = LocalContentLoading.current

        HeartBeatCP(info, {
            scope.launch {
                loadingState.runWithLoading {
                    val inviteCode = info.inviteCode
                    if (inviteCode.isNullOrEmpty()) {
                        userRepository
                            .inviteHaveCp(info.targetUser.id, Scene.NONE, 0)
                            .onSuccess {
                                toC2c()
                            }
                            .toastError()
                    } else {
                        userRepository
                            .agreeHaveCp(inviteCode, Scene.NONE, 0)
                            .onSuccess {
                                toC2c()
                            }
                            .toastError()
                    }
                }
            }
        }, onBack)
    }
}


@Preview
@Composable
private fun Preview() {
    HeartBeatCP(CPRightPageInfo(gift = Gift()), {})
}