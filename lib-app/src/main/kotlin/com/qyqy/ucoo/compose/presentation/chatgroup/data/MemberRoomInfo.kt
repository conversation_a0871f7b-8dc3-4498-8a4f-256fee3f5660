package com.qyqy.ucoo.compose.presentation.chatgroup.data


import android.os.Parcelable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Keep
@Parcelize
@Serializable
data class MemberRoomInfo(
    @SerialName("age")
    val age: Int = 0,
    @SerialName("avatar_frame")
    val avatarFrame: String = "",
    @SerialName("avatar_url")
    val avatarUrl: String = "",
    @SerialName("current_room_id")
    val currentRoomId: String = "",
    @SerialName("gender")
    val gender: Int = 0,
    @Serial<PERSON>ame("nickname")
    val nickname: String = "",
    @SerialName("public_id")
    val publicId: Int = 0,
    @SerialName("userid")
    val userid: Int = 0,
    @SerialName("room")
    val room: SimpleRoomInfo? = null,
) : Parcelable

@Parcelize
@Serializable
data class SimpleRoomInfo(
    val id: Int = 0,
    val title: String = "",
) : Parcelable