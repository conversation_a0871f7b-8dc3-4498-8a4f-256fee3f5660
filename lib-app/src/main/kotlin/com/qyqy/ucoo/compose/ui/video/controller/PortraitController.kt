package com.qyqy.ucoo.compose.ui.video.controller

import android.content.Context
import android.util.AttributeSet
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import android.widget.ImageView
import androidx.annotation.AttrRes
import site.xzwzz.video.player.controller.BaseVideoController

class PortraitController : BaseVideoController, GestureDetector.OnGestureListener, GestureDetector.OnDoubleTapListener, View.OnTouchListener {
    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, @AttrRes defStyleAttr: Int) : super(context, attrs, defStyleAttr)

    lateinit var thumbImageView: ImageView
        private set
    private lateinit var mGestureDetector: GestureDetector

    override fun initView(context: Context) {
        super.initView(context)

        this.mGestureDetector = GestureDetector(context, this)
        setOnTouchListener(this)

        //根据屏幕方向自动进入/退出全屏
        setEnableOrientation(false)
        //先移除多有的视图view
        removeAllControlComponent()

        //添加视图到界面
        addDefaultControlComponent()
    }

    private fun addDefaultControlComponent() {
        val vodControlView = CustomBottomView(context).apply {
            //是否显示底部进度条。默认显示
            showBottomProgress(false)
        }
        thumbImageView = vodControlView.thumb
        addControlComponent(vodControlView)

//        //添加与加载视图界面view，准备播放界面
//        val prepareView = PortraitControllView(context)
//        thumbImageView = prepareView.thumb
//        prepareView.setClickStart()
//        this.addControlComponent(prepareView)
    }

    override fun destroy() = Unit

    override fun getLayoutId(): Int = 0

    override fun onDown(e: MotionEvent): Boolean {
        return true
    }

    override fun onShowPress(e: MotionEvent) = Unit

    override fun onSingleTapUp(e: MotionEvent): Boolean {
        return false;
    }

    override fun onScroll(e1: MotionEvent?, e2: MotionEvent, distanceX: Float, distanceY: Float): Boolean {
        return false
    }

    override fun onLongPress(e: MotionEvent) = Unit

    override fun onFling(e1: MotionEvent?, e2: MotionEvent, velocityX: Float, velocityY: Float): Boolean {
        return false
    }

    override fun onSingleTapConfirmed(e: MotionEvent): Boolean {
        mControlWrapper.togglePlay()
        if (mControlWrapper.isPlaying()) {
            hide()
        }else{
            show()
        }
        return true
    }

    override fun onDoubleTap(e: MotionEvent): Boolean {
        return true
    }

    override fun onDoubleTapEvent(e: MotionEvent): Boolean {
        return true
    }

    override fun onTouch(v: View, event: MotionEvent): Boolean {
        return mGestureDetector.onTouchEvent(event)
    }
}