package com.qyqy.ucoo.compose.presentation.room

import android.app.Activity
import android.content.Context
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.ext.asLifecycleOwner
import com.overseas.common.ext.launch
import com.overseas.common.utils.dpF
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.data.ClickItem
import com.qyqy.ucoo.compose.data.Interactive
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.state.Empty
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.toUIData
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.SimpleComposeDialog
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.im.room.game.GameInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import kotlin.random.Random

data class Scene(
    val type: String,
    val id: String,
)

@Composable
fun GamaPanelPage(scene: Scene?, showState: MutableState<Boolean>, sendMessage: (MessageBundle) -> Unit) {

    if (!showState.value) {
        return
    }

    val context = LocalContext.current

    var isActiveClose by remember { mutableStateOf(false) }

    fun sendMessage(type: Int, result: String) {
        sendMessage(
            MessageBundle.Custom.create(
                cmd = MsgEventCmd.INTERACTIVE_MSG,
                data = buildJsonObject {
                    put("type", type)
                    put("result", result)
                },
                summary = context.getString(R.string._互动表情_)
            )
        )
        isActiveClose = true
    }

    val list = buildList {

        add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_touzi, R.string.骰子)) {
            sendMessage(1, Random.nextInt(1, 7).toString())
        })

        add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_caiquan, R.string.猜拳)) {
            sendMessage(5, Random.nextInt(1, 4).toString())
        })

        add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_number_1, R.string.幸运数字)) {
            sendMessage(2, Random.nextInt(10).toString())
        })

        add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_number_3, R.string.幸运数字)) {
            sendMessage(3, "${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}")
        })

        add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_number_5, R.string.幸运数字)) {
            sendMessage(4, "${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}")
        })
    }

    GamaPanel(isActiveClose, list, scene) {
        showState.value = false
    }
}

@Composable
private fun GamaPanel(isActiveClose: Boolean, gridList: List<ClickItem<Interactive>>, scene: Scene?, onDismiss: () -> Unit = {}) {
    AnimatedDialog(isActiveClose = isActiveClose, onDismiss = onDismiss) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .background(Color(0xFF222222), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .navigationBarsPadding()
        ) {
            Page(gridList, scene)
        }
    }
}



@Composable
private fun Item(icon: Any, name: String, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ComposeImage(
            model = icon,
            modifier = Modifier
                .size(64.dp, 64.dp)
                .clip(Shapes.medium),
            contentScale = ContentScale.Crop
        )
        AutoSizeText(
            text = name,
            modifier = Modifier.padding(top = 12.dp),
            fontSize = 14.sp,
            color = Color.White,
            maxLines = 1
        )
    }
}

@Composable
private fun Page(list: List<ClickItem<Interactive>>, scene: Scene?) {

    var gameUiState by remember {
        mutableStateOf<UIState<List<ClickItem<Interactive>>>>(UIState.Loading(R.string.加载中))
    }

    val repository = remember {
        RoomRepository()
    }

    val context = LocalContext.current

    if (scene != null) {
        if (gameUiState.isLoading) {
            LaunchedEffect(key1 = Unit) {
                getSudGameList(context, repository, scene).onSuccess {
                    gameUiState = it.toUIData()
                }.onFailure {
                    gameUiState = UIState.Error(Empty(R.string.加载失败点击重试, R.drawable.ic_empty_for_fans), throwable = it)
                }
            }
        }
    }

    LazyVerticalGrid(
        columns = GridCells.Fixed(4),
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize()
            .heightIn(max = 600.dp)
            .background(Color(0xFF222222), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(bottom = 10.dp)
            .navigationBarsPadding(),
        contentPadding = PaddingValues(start = 24.dp, top = 24.dp, end = 24.dp, bottom = 10.dp),
        horizontalArrangement = Arrangement.spacedBy(23.6667.dp),
        verticalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        if (scene != null) {
            interactiveSudGameItems(gameUiState) {
                gameUiState = UIState.Loading(R.string.加载中)
            }
        }

        interactiveItems(list)
    }
}

private fun LazyGridScope.interactiveSudGameItems(uiState: UIState<List<ClickItem<Interactive>>>, onRetry: () -> Unit) {
    when (uiState) {
        is UIState.Loading -> {
            item(key = "loading", span = { GridItemSpan(4) }, contentType = -1) {
                Row(
                    modifier = Modifier
                        .padding(bottom = 10.dp)
                        .fillMaxWidth(),
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(5.dp, Alignment.CenterHorizontally),
                ) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        color = colorResource(id = R.color.white),
                        strokeWidth = 1.5.dp
                    )
                    Text(
                        text = uiState.text,
                        color = colorResource(id = R.color.white),
                        fontSize = 14.sp
                    )
                }
            }
        }

        is UIState.Error -> {
            item(key = "error", span = { GridItemSpan(4) }, contentType = -2) {
                Box(
                    modifier = Modifier
                        .padding(bottom = 10.dp)
                        .fillMaxWidth()
                        .clickable(onClick = onRetry),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = uiState.text.orEmpty(),
                        color = colorResource(id = R.color.red_300),
                        fontSize = 14.sp
                    )
                }
            }
        }

        is UIState.Data -> {
            interactiveItems(uiState.value)
        }
    }
}

private fun LazyGridScope.interactiveItems(list: List<ClickItem<Interactive>>) {
    items(items = list, key = {
        it.item.id
    }, span = {
        GridItemSpan(if (it.item is Interactive.Divider || it.item is Interactive.Title) 4 else 1)
    }, contentType = {
        when (it.item) {
            is Interactive.Title -> 0
            is Interactive.Divider -> 1
            else -> 2
        }
    }) {
        when (val item = it.item) {
            is Interactive.Title -> {
                AppText(
                    text = item.content,
                    modifier = Modifier.padding(bottom = 10.dp),
                    fontSize = 16.sp,
                    color = Color.White,
                )
            }

            is Interactive.Divider -> {
                HorizontalDivider(modifier = Modifier.padding(vertical = 10.dp), thickness = 1.dp, color = Color(0x1AFFFFFF))
            }

            else -> {
                val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
                Item(icon = item.icon, name = item.name, modifier = Modifier.noEffectClickable {
                    if (item.dismissAfterClick) {
                        onBackPressedDispatcher?.onBackPressed()
                    }
                    it.onClick(item)
                })
            }
        }
    }
}

private suspend fun getSudGameList(context: Context, repository: RoomRepository, scene: Scene) = withContext(Dispatchers.IO) {
    repository.fetchSudGameList(null).mapCatching { game ->

        val activity = context.asActivity

        buildList {
            game.competitiveList?.takeIf {
                it.isNotEmpty()
            }?.also {
                add(ClickItem(Interactive.Title(buildAnnotatedString {
                    append(context.getString(R.string.竞技游戏))
                    withStyle(SpanStyle(fontSize = 12.sp, color = Color(app.getColor(R.color.white_alpha_50)))) {
                        append(context.getString(R.string.可获得钻石奖励))
                    }
                })))

                it.forEach { info ->
                    add(ClickItem(Interactive.SudGame(info)) {
                        activity?.apply {
                            if (info.type == 1) {
                                switchGame(this, repository, info, scene)
                            } else if (info.link.isNotEmpty()) {
                                AppLinkManager.open(this, info.link, info.container)
                            }
                        }
                    })
                }

                add(ClickItem(Interactive.Divider("0")))
            }

            game.interactiveList?.takeIf {
                it.isNotEmpty()
            }?.also {
                add(ClickItem(Interactive.Title(buildAnnotatedString {
                    append(context.getString(R.string.互动游戏))
                })))

                it.forEach { info ->
                    add(ClickItem(Interactive.SudGame(info)) {
                        activity?.apply {
                            if (info.type == 1) {
                                switchGame(this, repository, info, scene)
                            } else if (info.link.isNotEmpty()) {
                                AppLinkManager.open(this, info.link, info.container)
                            }
                        }
                    })
                }

                add(ClickItem(Interactive.Divider("1")))
            }
        }
    }
}

private fun switchGame(context: Activity, repository: RoomRepository, info: GameInfo, scene: Scene) {

    fun sendSwitchGameReq(mode: Int = 1) {
        context.asLifecycleOwner.launch {
            repository.startSudGameByScene(scene, info.id, mode).toastError()
        }
    }

    if (!info.modeOptions.isNullOrEmpty()) {
        SimpleComposeDialog(context, 270.dpF.toInt()) { dialog ->
            GameModeDialogContent(info.modeOptions, {
                dialog.dismiss()
            }) {
                sendSwitchGameReq(it.mode)
                dialog.dismiss()
            }
        }.show()
    } else {
        sendSwitchGameReq()
    }

}
