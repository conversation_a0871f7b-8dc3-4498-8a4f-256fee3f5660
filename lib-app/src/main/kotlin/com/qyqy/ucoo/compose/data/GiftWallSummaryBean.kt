package com.qyqy.ucoo.compose.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import com.qyqy.ucoo.compose.ui.ITab
import kotlinx.parcelize.IgnoredOnParcel

/**
{
"total_cnt": 823,
"star_cnt": 10,
"tabs": [{
"name": "XX礼物",
"total_cnt": 50,
"star_cnt": 1,
"t": 1
}]
}
 */
@Keep
@Serializable
data class GiftWallSummaryBean(
    @SerialName("star_cnt")
    val starCnt: Int = 0,
    @SerialName("tabs")
    val tabs: List<Tab> = listOf(),
    @SerialName("total_cnt")
    val totalCnt: Int = 0
) {
    @Keep
    @Serializable
    data class Tab(
        @SerialName("name")
        override val name: String = "",
        @SerialName("star_cnt")
        val starCnt: Int = 0,
        @SerialName("t")
        val t: Int = 0,
        @SerialName("total_cnt")
        val totalCnt: Int = 0,
    ):ITab

    fun updateTabCount(tabId: Int, count: Int): GiftWallSummaryBean {
        return copy(starCnt = starCnt + 1, tabs = tabs.toMutableList().apply {
            (find { it.t == tabId })?.let {
                val pos = indexOf(it)
                set(pos, it.copy(starCnt = it.starCnt + 1))
            }
        })
    }
}