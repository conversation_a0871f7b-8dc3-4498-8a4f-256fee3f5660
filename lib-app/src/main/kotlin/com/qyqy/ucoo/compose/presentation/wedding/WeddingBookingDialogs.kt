package com.qyqy.ucoo.compose.presentation.wedding

import androidx.annotation.DrawableRes
import androidx.compose.animation.core.spring
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Add
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastFirstOrNull
import androidx.core.content.ContextCompat
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.getShadowColor
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.wedding.bean.LightUser
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingRoomType
import com.qyqy.ucoo.compose.theme.MI_SANS
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.rightFadingEdge
import com.qyqy.ucoo.utils.OnClick

data class WeddingTheme(
    val primaryColor: Color,
    val secondaryColor: Color,
    val titleColor: Color,
    val tintColor: Color,
    @DrawableRes val bgRes: Int,
    @DrawableRes val typeRes: Int,
    @DrawableRes val buttonRes: Int,
    @DrawableRes val closeRes: Int,
    @DrawableRes val smallBgRes: Int,
    @DrawableRes val checkRes: Int = R.drawable.ic_wedding_booking_red_checked,
    @DrawableRes val switchRes: Int = R.drawable.ic_wedding_booking_red_change,
) {
    companion object {

        fun getByType(roomType: Int) = when (roomType) {
            WeddingRoomType.TIME_PRINT_PURPLE -> WeddingTheme(
                primaryColor = Color(0xFF7A59D2),
                secondaryColor = Color(0xFFD0C1F8),
                titleColor = Color(0xFFD0C1F8),
                tintColor = Color(0xFFB59EF0),
                bgRes = R.drawable.bg_wedding_booking_purple,
                typeRes = R.drawable.bg_wedding_booking_purple_type,
                buttonRes = R.drawable.wd_btn_520,
                closeRes = R.drawable.ic_wedding_booking_purple_close,
                smallBgRes = R.drawable.bg_booking_purple,
                checkRes = R.drawable.ic_purple_check,
                switchRes = R.drawable.ic_purple_change,
            )

            WeddingRoomType.STAR_PROMISE_BLUE -> WeddingTheme(
                primaryColor = Color(0xFF7A59D2),
                secondaryColor = Color(0xFFD0C1F8),
                titleColor = Color(0xFFD0C1F8),
                tintColor = Color(0xFFA48BE9),
                bgRes = R.drawable.bg_wedding_booking_blue,
                typeRes = R.drawable.bg_wedding_booking_blue_type,
                buttonRes = R.drawable.wd_btn_888,
                closeRes = R.drawable.ic_wedding_booking_blue_close,
                smallBgRes = R.drawable.bg_booking_blue,
                checkRes = R.drawable.ic_blue_check,
                switchRes = R.drawable.ic_blue_change,
            )

            else -> WeddingTheme(
                primaryColor = Color(0xFFD5608F),
                secondaryColor = Color(0xFFFFDBEE),
                titleColor = Color(0xFFFBBBDD),
                tintColor = Color(0xFFF5BCDD),
                bgRes = R.drawable.bg_wedding_booking_red,
                typeRes = R.drawable.bg_wedding_booking_red_type,
                buttonRes = R.drawable.btn_wedding_booking_red,
                closeRes = R.drawable.ic_wedding_booking_red_close,
                smallBgRes = R.drawable.bg_booking_red,
                checkRes = R.drawable.ic_wedding_booking_red_checked,
                switchRes = R.drawable.ic_wedding_booking_red_change,
            )
        }
    }
}


data class WeddingInfo(
    val showCloseBtn: Boolean,
    val theme: WeddingTheme,
)

/**
 * [weddingType] 婚礼类型:臻爱永恒
 * [title] 标题：婚礼预约
 * [buttonText] 按钮文案，提交预约
 *
 */
@Composable
fun WeddingBookingContent(
    weddingType: String,
    title: String,
    roomId: String,
    info: WeddingInfo,
    leftUser: User,
    rightUser: User?,
    onDismissRequest: () -> Unit,
    onSubmit: () -> Unit,
    onSelectGuest: () -> Unit = {},
    onTimeSelect: OnClick = {},
    ringUrl: String? = null,
    submitEnable: Boolean = false,
    canChange: Boolean = false,
    buttonText: String = "提交预约",
    posting: Boolean = false,
    bookingTime: String? = null,
    textDisplayUI: @Composable () -> Unit = {
        // Information Fields
        InfoField(
            label = stringResource(R.string.wedding_room_id),
            value = roomId,
            theme = info.theme,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        InfoField(
            label = stringResource(R.string.wedding_room_type),
            value = weddingType,
            theme = info.theme,
            modifier = Modifier.padding(bottom = 16.dp)
        )

        InfoField(
            label = stringResource(R.string.wedding_time),
            value = bookingTime ?: stringResource(R.string.click_select),
            theme = info.theme,
            modifier = Modifier.padding(bottom = 24.dp),
            isBoldText = bookingTime == null,
            onClick = onTimeSelect,
        )
    }
) {
    val theme = info.theme
    val context = LocalContext.current
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Main card with fancy border

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .paint(
                    painter = rememberDrawablePainter(drawable = ContextCompat.getDrawable(context, theme.bgRes)),
                    contentScale = ContentScale.FillBounds
                )
                .padding(horizontal = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Header - "臻爱永恒" with wings
            Box(
                modifier = Modifier
                    .size(163.dp, 60.dp)
                    .paint(
                        painter = painterResource(theme.typeRes),
                        contentScale = ContentScale.FillBounds
                    ),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = weddingType,
                    modifier = Modifier.padding(top = if (theme.bgRes == R.drawable.bg_wedding_booking_red) 10.dp else 0.dp),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        fontFamily = FontFamily.MI_SANS,
                        fontWeight = FontWeight.Normal,
                        color = Color.White,
                        shadow = Shadow(
                            color = theme.primaryColor.getShadowColor(),
                            offset = Offset(3f, 3f),
                            blurRadius = 2f
                        ),
                    )
                )
            }

            // Title - "婚礼预约"
            Text(
                text = title,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = FontFamily.MI_SANS,
                color = theme.tintColor,
                modifier = Modifier.padding(vertical = 16.dp)
            )


            // Profile selection area
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 24.dp),
                horizontalArrangement = Arrangement.Center
            ) {
                // Left profile
                ProfileCircle(
                    user = leftUser,
                    theme = theme,
                    clickable = false,
                )

                Box(
                    modifier = Modifier
                        .width(56.dp)
                        .height(66.dp),
                    contentAlignment = Alignment.Center
                ) {
                    if (!ringUrl.isNullOrEmpty()) {
                        ComposeImage(ringUrl, modifier = Modifier.size(48.dp))
                    }
                }

                // Right profile (add button)
                ProfileCircle(
                    user = rightUser,
                    theme = theme,
                    clickable = true,
                    onClick = {
                        if (canChange) {
                            onSelectGuest()
                        }
                    },
                    onExtraContent = {
                        if (rightUser != null) {
                            Image(
                                painter = painterResource(if (canChange) theme.switchRes else theme.checkRes),
                                contentDescription = null,
                                modifier = Modifier.align(Alignment.BottomEnd)
                            )
                        }
                    }
                )
            }

            textDisplayUI()
            val grayFilter = remember {
                ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
            }
            Box(
                modifier = Modifier
                    .padding(bottom = 20.dp)
                    .size(130.dp, 58.dp)
                    .graphicsLayer {
                        alpha = if (submitEnable) 1f else
                            0.4f
                    }
                    .paint(
                        painter = painterResource(theme.buttonRes),
                        colorFilter = if (submitEnable) null else grayFilter
                    )
                    .noEffectClickable(onClick = onSubmit, enabled = submitEnable && !posting),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = buttonText,
                    style = TextStyle(
                        fontFamily = FontFamily.MI_SANS,
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        shadow = Shadow(
                            color = theme.primaryColor.getShadowColor(),
                            offset = Offset(3f, 3f),
                            blurRadius = 2f
                        ),
                    ),
                )

                if (posting) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .padding(end = 8.dp)
                            .size(20.dp)
                            .align(Alignment.CenterEnd),
                        color = Color.White,
                        strokeWidth = 4.dp
                    )
                }
            }
        }

        if (info.showCloseBtn) {
            // Close button at the bottom
            IconButton(
                onClick = onDismissRequest,
                modifier = Modifier
                    .padding(top = 26.dp)
                    .size(30.dp)
                    .clip(CircleShape)
            ) {
                Image(
                    painter = painterResource(theme.closeRes),
                    contentDescription = "Close",
                )
            }
        }
    }
}

@Composable
fun GuestSelectionContent(
    info: WeddingInfo,
    users: List<LightUser>,
    selectedUser: LightUser?,
    label: String = "",
    title: String = stringResource(R.string.选择婚礼预约人),
    buttonText: String = stringResource(id = R.string.rc_confirm),
    onDismissRequest: () -> Unit,
    onSubmit: (LightUser) -> Unit,
) {
    val theme = info.theme
    val context = LocalContext.current
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Main card with fancy border
        Column(
            modifier = Modifier
                .size(281.dp, 339.dp)
                .paint(
                    painter = rememberDrawablePainter(drawable = ContextCompat.getDrawable(context, theme.smallBgRes)),
                    contentScale = ContentScale.FillBounds
                )
                .padding(top = 50.dp, bottom = 22.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {

            // Title - "选择婚礼预约人"
            Text(
                text = title,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = FontFamily.MI_SANS,
                color = theme.titleColor,
                modifier = Modifier.padding(top = 8.dp)
            )

            val listState = rememberLazyListState()
            var sel by remember {
                mutableStateOf(selectedUser ?: users.firstOrNull())
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .rightFadingEdge(
                        color = Color(0x20FFFBF4),
                        width = 80.dp,
                        spec = spring(),
                        isVisible = listState.canScrollForward,
                    )
            ) {
                LazyRow(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp),
                    state = listState,
                    horizontalArrangement = Arrangement.spacedBy(20.dp, Alignment.CenterHorizontally),
                    contentPadding = PaddingValues(horizontal = 10.dp)
                ) {
                    items(users) {
                        ProfileCircle(
                            user = it.user,
                            theme = theme,
                            clickable = true,
                            onExtraContent = {
                                if (it.user.id == sel?.user?.id) {
                                    Image(
                                        painter = painterResource(theme.checkRes),
                                        contentDescription = null,
                                        modifier = Modifier
                                            .align(Alignment.BottomEnd)
                                            .padding(2.dp)
                                            .size(18.dp)
                                    )
                                }
                            },
                            onClick = {
                                sel = it
                            }
                        )
                    }
                }
            }
            Box(
                modifier = Modifier
                    .size(130.dp, 58.dp)
                    .paint(painter = painterResource(theme.buttonRes))
                    .noEffectClickable(onClick = {
                        val u = sel
                        if (u != null) {
                            onSubmit(u)
                        }
                    }),
                contentAlignment = Alignment.Center,
            ) {
                Text(
                    text = buttonText,
                    style = TextStyle(
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Bold,
                        shadow = Shadow(
                            color = theme.primaryColor.getShadowColor(),
                            offset = Offset(3f, 3f),
                            blurRadius = 2f
                        ),
                    ),
                )
            }
        }

        if (info.showCloseBtn) {
            // Close button at the bottom
            IconButton(
                onClick = onDismissRequest,
                modifier = Modifier
                    .padding(top = 26.dp)
                    .size(30.dp)
                    .clip(CircleShape)
            ) {
                Image(
                    painter = painterResource(theme.closeRes),
                    contentDescription = "Close",
                )
            }
        }
    }
}

@Composable
private fun ProfileCircle(
    user: User?,
    theme: WeddingTheme,
    clickable: Boolean,
    onExtraContent: @Composable BoxScope.() -> Unit = {},
    onClick: () -> Unit = {}
) {
    Column(
        modifier = Modifier.width(66.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Profile circle
        Box(
            modifier = Modifier
                .size(66.dp)
                .background(color = theme.secondaryColor, shape = CircleShape)
                .noEffectClickable(enabled = clickable, onClick = onClick),
            contentAlignment = Alignment.Center
        ) {
            if (user == null) {
                Icon(
                    Icons.Rounded.Add,
                    contentDescription = "Add",
                    tint = theme.tintColor,
                    modifier = Modifier.size(24.dp)
                )
            } else {
                CircleComposeImage(user.avatarUrl, modifier = Modifier.size(58.dp))
            }

            onExtraContent()
        }

        // Label
        Text(
            text = user?.nickname ?: "选择婚礼预约人",
            fontSize = 12.sp,
            lineHeight = 12.sp,
            color = theme.primaryColor,
            modifier = Modifier
                .padding(top = 8.dp),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }
}

@Composable
fun InfoField(
    label: String,
    value: String,
    theme: WeddingTheme,
    modifier: Modifier = Modifier,
    isBoldText: Boolean = false,
    onClick: () -> Unit = {},
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .noEffectClickable(onClick = onClick),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 14.sp,
            color = theme.primaryColor,
            fontWeight = FontWeight.Medium
        )

        Text(
            text = value,
            fontSize = 14.sp,
            color = theme.primaryColor,
            style = TextStyle(
                fontWeight = if (isBoldText) FontWeight.ExtraBold else FontWeight.Medium,
//                shadow = if (isBoldText) Shadow(color = theme.primaryColor, blurRadius = 4.dp.value) else null,
            )
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun WeddingBookingRedContentPreview() {
    Box(modifier = Modifier.padding(horizontal = 18.dp, vertical = 100.dp)) {
        WeddingBookingContent(
            weddingType = "臻爱永恒",
            title = "婚礼预约",
            roomId = "1314",
            info = WeddingInfo(
                showCloseBtn = true,
                theme = WeddingTheme.getByType(WeddingRoomType.LOVE_FOREVER_RED)
            ),
            leftUser = userForPreview,
            rightUser = null,
            onDismissRequest = {},
            onSubmit = {},
            onSelectGuest = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun WeddingBookingBlueContentPreview() {
    Box(modifier = Modifier.padding(horizontal = 18.dp, vertical = 100.dp)) {
        WeddingBookingContent(
            weddingType = "星辰之诺",
            title = "婚礼预约",
            roomId = "1314",
            info = WeddingInfo(
                showCloseBtn = true,
                theme = WeddingTheme.getByType(WeddingRoomType.STAR_PROMISE_BLUE)
            ),
            leftUser = userForPreview,
            rightUser = null,
            onDismissRequest = {},
            onSubmit = {},
            onSelectGuest = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun WeddingBookingPurpleContentPreview() {
    Box(modifier = Modifier.padding(horizontal = 18.dp, vertical = 100.dp)) {
        WeddingBookingContent(
            weddingType = "时光印记",
            title = "婚礼预约",
            roomId = "1314",
            info = WeddingInfo(
                showCloseBtn = true,
                theme = WeddingTheme.getByType(WeddingRoomType.TIME_PRINT_PURPLE)
            ),
            leftUser = userForPreview.copy(nickname = "哈哈哈啊哈哈哈哈哈哈哈啊哈哈哈啊哈哈哈哈哈哈哈啊"),
            rightUser = null,
            onDismissRequest = {},
            onSubmit = {},
            onSelectGuest = {},
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun GuestSelectionContentPreview() {
    Box(contentAlignment = Alignment.Center) {
        GuestSelectionContent(
            info = WeddingInfo(
                showCloseBtn = true,
                theme = WeddingTheme.getByType(WeddingRoomType.LOVE_FOREVER_RED)
            ),
            users = listOf(
                LightUser(userForPreview.copy(id = "222", nickname = "哈哈哈"), 0), LightUser(userForPreview, 0), LightUser(userForPreview, 0)
            ),
            selectedUser = LightUser(userForPreview.copy(id = "222"), 0),
            onDismissRequest = {},
            onSubmit = { _ -> },
        )
    }
}


data class DateOption(
    val label: String,
    val timeSlots: List<TimeSlot>,
    val bookingDate: String = "",
)

data class TimeSlot(
    val timeRange: String,
    val isAvailable: Boolean = true,
    val rangeType: Int = 1,
)

@Composable
fun WeddingTimeSelectionContent(
    theme: WeddingTheme,
    dateOptions: List<DateOption>,
    onDismissRequest: () -> Unit,
    selectedTime: Pair<String, TimeSlot?>? = null,
    cancelText: String = stringResource(id = R.string.rc_cancel),
    confirmText: String = stringResource(id = R.string.rc_confirm),
    onConfirm: (Pair<String, TimeSlot?>) -> Unit
) {
    var select by remember {
        mutableStateOf(selectedTime ?: dateOptions.firstOrNull()?.let {
            Pair(it.bookingDate, it.timeSlots.firstOrNull())
        })
    }

    val timeSlots by remember(select) {
        derivedStateOf { dateOptions.firstOrNull { it.bookingDate == select?.first }?.timeSlots.orEmpty() }
    }

    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        shape = RoundedCornerShape(16.dp),
        color = Color.White
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .navigationPadding(minimumPadding = 20.dp)
        ) {
            // 顶部操作栏
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp)
            ) {
                Text(
                    text = cancelText,
                    fontSize = 14.sp,
                    color = Color(0xFF50596B),
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier
                        .align(Alignment.CenterStart)
                        .clickable { onDismissRequest() }
                )

                Text(
                    text = confirmText,
                    fontSize = 14.sp,
                    color = Color(0xFF50596B),
                    fontWeight = FontWeight.SemiBold,
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .clickable(enabled = select != null) {
                            select?.also {
                                onConfirm(it)
                            }
                        }
                )
            }

            // 主内容区域 - 左右分栏
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(300.dp) // 固定高度以确保可滚动
            ) {
                // 左侧日期列表
                LazyColumn(
                    modifier = Modifier
                        .weight(1.5f)
                        .fillMaxHeight()
                ) {
                    itemsIndexed(dateOptions) { index, dateOption ->
                        DateItem(
                            theme = theme,
                            date = dateOption.label,
                            isSelected = dateOption.bookingDate == select?.first,
                            onClick = {
                                select = Pair(dateOption.bookingDate, dateOption.timeSlots.fastFirstOrNull { it.isAvailable })
                            }
                        )
                    }
                }


                // 右侧时间列表
                LazyColumn(
                    modifier = Modifier
                        .weight(3f)
                        .fillMaxHeight()
                ) {
                    itemsIndexed(timeSlots) { index, timeSlot ->
                        TimeItem(
                            theme = theme,
                            timeSlot = timeSlot,
                            isSelected = timeSlot.rangeType == select?.second?.rangeType,
                            onClick = {
                                if (timeSlot.isAvailable) {
                                    select = select?.copy(second = timeSlot)
                                }
                            }
                        )
                    }
                }
            }

        }
    }
}

@Composable
private fun DateItem(
    theme: WeddingTheme,
    date: String,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val density = LocalDensity.current
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .clickable(onClick = onClick)
            .drawBehind {
                if (isSelected) {
                    val drawSize = with(density) {
                        DpSize(9.dp, 14.dp).toSize()
                    }
                    drawRoundRect(
                        color = theme.primaryColor,
                        topLeft = Offset(
                            0f,
                            size.height
                                .minus(drawSize.height)
                                .div(2)
                        ),
                        size = drawSize,
                    )
                }
            },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = date,
            fontSize = 16.sp,
            color = if (isSelected) theme.primaryColor else Color(0xFFAEAEAE),
            fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun TimeItem(
    theme: WeddingTheme,
    timeSlot: TimeSlot,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(48.dp)
            .clickable(enabled = timeSlot.isAvailable, onClick = onClick)
            .padding(horizontal = 12.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = timeSlot.timeRange,
            fontSize = 16.sp,
            color = when {
                isSelected -> theme.primaryColor
                else -> Color(0xFFAEAEAE)
            },
            fontWeight = FontWeight.Medium
        )

        if (!timeSlot.isAvailable) {
            Icon(
                imageVector = IcWdBooked,
                contentDescription = "Selected",
                tint = theme.tintColor,
                modifier = Modifier.size(28.dp)
            )
        } else if (isSelected) {
            Image(
                painter = painterResource(theme.checkRes),
                contentDescription = null,
                modifier = Modifier.size(22.dp)
            )
        }
    }
}

@Composable
@Preview(showBackground = true)
private fun WeddingTimeSelectionContentPreview() {
    WeddingTimeSelectionContent(
        theme = WeddingTheme.getByType(WeddingRoomType.STAR_PROMISE_BLUE),
        dateOptions = listOf(
            DateOption(
                "今天",
                listOf(
                    TimeSlot("17:00 - 20:00"),
                    TimeSlot("20:00 - 23:00")
                ),
            ),
            DateOption(
                "明天", listOf(
                    TimeSlot("17:00 - 20:00", false),
                    TimeSlot("20:00 - 23:00")
                ), bookingDate = "03-21"
            ),
            DateOption(
                "后天", listOf(
                    TimeSlot("17:00 - 20:00"),
                    TimeSlot("20:00 - 23:00")
                )
            ),
            // 添加更多日期选项用于演示滚动效果
            DateOption(
                "03-21", listOf(
                    TimeSlot("17:00 - 20:00"),
                    TimeSlot("20:00 - 23:00")
                )
            ),
            DateOption(
                "03-22", listOf(
                    TimeSlot("17:00 - 20:00"),
                    TimeSlot("20:00 - 23:00")
                )
            ),
            DateOption(
                "03-23", listOf(
                    TimeSlot("17:00 - 20:00"),
                    TimeSlot("20:00 - 23:00")
                )
            )
        ),
        selectedTime = "03-21" to TimeSlot(""),
        onDismissRequest = {},
        onConfirm = { _ -> }
    )
}

@Composable
fun WeddingPreBooking(
    weddingType: String,
    title: String,
    message: String,
    theme: WeddingTheme,
    leftButtonText: String,
    rightButtonText: String,
    onLeftClick: OnClick = {},
    onRightClick: OnClick = {},
    onClose: OnClick = {}
) {
    Column(
        modifier = Modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // Main card with fancy border
        val context = LocalContext.current
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(339f / 307)
                .paint(
                    painter = rememberDrawablePainter(drawable = ContextCompat.getDrawable(context, theme.bgRes)),
                    contentScale = ContentScale.FillBounds
                )
                .padding(horizontal = 32.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(163.dp, 60.dp)
//                    .paint(
//                        painter = painterResource(theme.typeRes),
//                        contentScale = ContentScale.FillBounds
//                    )
                ,
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = weddingType,
                    modifier = Modifier.padding(top = if (theme.bgRes == R.drawable.bg_wedding_booking_red) 10.dp else 0.dp),
                    style = TextStyle(
                        fontSize = 14.sp,
                        lineHeight = 14.sp,
                        fontFamily = FontFamily.MI_SANS,
                        fontWeight = FontWeight.Normal,
                        color = Color.White,
                        shadow = Shadow(
                            color = theme.primaryColor.getShadowColor(),
                            offset = Offset(3f, 3f),
                            blurRadius = 2f
                        ),
                    )
                )
            }

            // Title - "婚礼预约"
            Text(
                text = title,
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                fontFamily = FontFamily.MI_SANS,
                color = theme.tintColor,
                modifier = Modifier.padding(top = 24.dp)
            )

            Spacer(modifier = Modifier.weight(1f))
            AppText(
                text = message, color = theme.primaryColor, fontWeight = FontWeight.Bold,
                modifier = Modifier
                    .padding(horizontal = 20.dp),
                fontSize = 16.sp,
                lineHeight = 24.sp
            )

            Spacer(modifier = Modifier.weight(1f))
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 2.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(124.dp, 58.dp)
                        .click(onClick = onLeftClick), contentAlignment = Alignment.Center
                ) {
                    Image(painter = painterResource(id = theme.buttonRes), contentDescription = "left")
                    AppText(
                        text = leftButtonText, color = Color.White, fontWeight = FontWeight.Bold, fontFamily = FontFamily.MI_SANS, style = TextStyle(
                            shadow = Shadow(
                                color = Color.Gray.getShadowColor(),
                                offset = Offset(3f, 3f),
                                blurRadius = 2f
                            )
                        )
                    )
                }

                Box(
                    modifier = Modifier
                        .size(124.dp, 58.dp)
                        .click(onClick = onRightClick), contentAlignment = Alignment.Center
                ) {
                    Image(painter = painterResource(id = R.drawable.btn_yellow), contentDescription = "right")
                    AppText(
                        text = rightButtonText,
                        color = Color(0xFFAD5858),
                        fontWeight = FontWeight.Bold,
                        fontFamily = FontFamily.MI_SANS,
                        style = TextStyle(
                            shadow = Shadow(
                                color = Color.Gray.getShadowColor(),
                                offset = Offset(1f, 1f),
                                blurRadius = 1f
                            )
                        )
                    )
                }
            }
            Spacer(modifier = Modifier.weight(1f))
        }

        IconButton(
            onClick = onClose,
            modifier = Modifier
                .padding(top = 26.dp)
                .size(30.dp)
                .clip(CircleShape)
        ) {
            Image(
                painter = painterResource(theme.closeRes),
                contentDescription = "Close",
            )
        }

    }
}

@Preview
@Composable
private fun PreBookingPreview() {
    WeddingPreBooking(
        weddingType = "时光印记",
        title = "专属婚礼礼遇",
        message = "您当前拥有1次预约婚礼机会！抢先预约婚礼，或继续选购戒指",
        theme = WeddingTheme.getByType(WeddingRoomType.STAR_PROMISE_BLUE),
        leftButtonText = "继续购买戒指",
        rightButtonText = "立即预约婚礼"
    )

}

@Composable
fun WeddingAlert(
    theme: WeddingTheme,
    roomTypeDesc: String,
    title: String,
    message: String,
    buttonText: String,
    onClose: OnClick,
    onClick: () -> Unit
) {
    Column(modifier = Modifier.fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally) {
        WeddingCard(
            theme, roomTypeDesc, modifier = Modifier.fillMaxWidth()
        ) {
            Text(
                text = title,
                fontSize = 24.sp,
                fontFamily = FontFamily.MI_SANS,
                color = theme.titleColor,
                modifier = Modifier.padding(top = 32.dp)
            )
            Spacer(modifier = Modifier.height(24.dp))
            AppText(
                text = message,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 10.dp)
                    .padding(horizontal = 26.dp),
                color = theme.primaryColor,
                lineHeight = 24.sp,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(20.dp))
            Box(
                modifier = Modifier
                    .size(124.dp, 58.dp)
                    .click(onClick = onClick), contentAlignment = Alignment.Center
            ) {
                Image(painter = painterResource(id = theme.buttonRes), contentDescription = "left")
                AppText(
                    text = buttonText,
                    color = Color.White,
                    fontFamily = FontFamily.MI_SANS,
                    style = TextStyle(
                        shadow = Shadow(
                            color = Color.Gray.getShadowColor(),
                            offset = Offset(3f, 3f),
                            blurRadius = 2f
                        )
                    )
                )
            }
            Spacer(modifier = Modifier.height(30.dp))
        }
        IconButton(
            onClick = onClose,
            modifier = Modifier
                .padding(top = 26.dp)
                .size(30.dp)
                .clip(CircleShape)
        ) {
            Image(
                painter = painterResource(theme.closeRes),
                contentDescription = "Close",
            )
        }
    }
}


@Preview
@Composable
private fun WeddingStartPreview() {
    Box(modifier = Modifier.fillMaxWidth().background(Color.LightGray), contentAlignment = Alignment.Center) {
        Box(modifier = Modifier.fillMaxWidth(340/375f)) {
            WeddingAlert(
                theme = WeddingTheme.getByType(WeddingRoomType.LOVE_FOREVER_RED),
                roomTypeDesc = "臻爱官宣",
                title = "婚礼开始提醒",
                message = "您的戒指点亮用户\"用户昵称\"已成功预约\"2025年3月8日19:00\"婚礼仪式，婚礼房间号\"520\"，让重要时刻不容错过！",
                buttonText = "进入房间",
                onClose = {}
            ) {

            }
        }
    }
}