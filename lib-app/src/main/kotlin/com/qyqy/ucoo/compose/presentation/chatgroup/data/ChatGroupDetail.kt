package com.qyqy.ucoo.compose.presentation.chatgroup.data


import com.qyqy.ucoo.account.AppUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChatGroupDetail(
    @SerialName("avatar_url")
    val avatarUrl: String = "",
    @SerialName("first_page_members")
    val firstPageMembers: List<ChatGroupMember> = listOf(),
    @SerialName("i_enable_dont_disturb")
    val iEnableDontDisturb: Boolean = false,
    val intro: String = "",
    @SerialName("member_apply_wait_cnt")
    val memberApplyWaitCnt: Int = 0,
    @SerialName("member_cnt")
    val memberCnt: Int = 0,
    val name: String = "",
    @SerialName("online_member_cnt")
    val onlineMemberCnt: Int = 0,
    @SerialName("public_id")
    val publicId: String = "",
    @SerialName("rc_group_id")
    val rcGroupId: String = "",
    @SerialName("relation_with_me")
    val relationWithMe: Int = ChatGroup.Relation.UNKNOWN,
    @SerialName("is_need_review")
    val needReview: Boolean = false,
    val role: Int = 0,
    @SerialName("members_room_infos")
    val membersRoomInfos: List<MemberRoomInfo> = emptyList(),
) {

    val ownerIds = firstPageMembers.filter { it.role == ChatGroup.Role.OWNER }.map {
        it.user.id
    }

    val adminIds = firstPageMembers.filter { it.role == ChatGroup.Role.ADMIN }.map {
        it.user.id
    }

    val memberApplyWaitCount: Int
        get() = if (role <= ChatGroup.Role.MEMBER) {
            0
        } else {
            memberApplyWaitCnt
        }

    fun getUserRole(uid: String): Int {
        if (ownerIds.contains(uid)) {
            return ChatGroup.Role.OWNER
        }
        if (adminIds.contains(uid)) {
            return ChatGroup.Role.ADMIN
        }
        return ChatGroup.Role.MEMBER
    }

    val chatGroupId: Int
        get() = rcGroupId.replace("chatgroup", "").toIntOrNull() ?: 0
    val isEmpty: Boolean
        get() = memberCnt == 0 && rcGroupId.isEmpty()

    fun toBrief() = ChatGroupBrief(
        id = chatGroupId,
        avatarUrl = avatarUrl,
        iEnableDontDisturb = iEnableDontDisturb,
        intro = intro,
        memberCnt = memberCnt,
        name = name,
        publicId = publicId,
        rcGroupId = rcGroupId,
        relationWithMe = relationWithMe,
        role = role,
        membersRoomInfos = membersRoomInfos,
    )
}

@Serializable
data class ChatGroupMember(
    @SerialName("member_id")
    val member_id: Int,
    @SerialName("is_online")
    val is_online: Boolean,
    @SerialName("role")
    val role: Int,//0普通成员  5:管理员   10:群主
    @SerialName("user")
    val user: AppUser,
)