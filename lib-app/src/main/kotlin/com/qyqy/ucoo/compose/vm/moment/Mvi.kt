package com.qyqy.ucoo.compose.vm.moment

import com.qyqy.ucoo.compose.data.MomentPreviewItem
import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.compose.ui.LoadResult
import com.qyqy.ucoo.compose.ui.PagingScope
import com.qyqy.ucoo.moment.Moment

interface MomentMvi {

    sealed interface ViewEvent : MviViewModel.MviEvent {

        data class RefreshMoments(val userId: String?) : ViewEvent

        data class LoadMoments(val userId: String?, val key: Int, val pagingScope: PagingScope<Int>) : ViewEvent

        data class ToggleLikeMoment(val moment: Moment) : ViewEvent

        data class DeleteMoment(val moment: Moment) : ViewEvent

        data class PreviewMomentImage(val moment: Moment, val position: Int) : ViewEvent
    }

    sealed interface ViewResult : MviViewModel.MviViewResult {
        data class ErrorResult(val toastError: String? = null) : ViewResult

        data class MomentsResult(val userId: String?, val append: Boolean, val list: List<Moment>) : ViewResult

        /**
         * pagingScope == null，表示刷新
         */
        data class LoadPageResult(val result: LoadResult<Int>, val pagingScope: PagingScope<Int>?) : ViewResult

        data class LikeMomentResult(val moment: Moment, val liked: Boolean) : ViewResult

        data class DeleteMomentResult(val moment: Moment) : ViewResult

        data class PreviewMomentResult(val list: List<MomentPreviewItem>, val start: Int) : ViewResult
    }

    data class ViewState(
        val userId: String?,
        val list: List<Moment>,
    ) : MviViewModel.MviViewState


    interface ViewEffect : MviViewModel.MviSideEffect {

        data class Toast(val msg: String) : ViewEffect

        data class LoadPageEnd(val result: LoadResult<Int>, val pagingScope: PagingScope<Int>?) : ViewEffect

        data class PreviewImage(val list: List<MomentPreviewItem>, val start: Int) : ViewEffect
    }
}

