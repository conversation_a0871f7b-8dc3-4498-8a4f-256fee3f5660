package com.qyqy.ucoo.compose.domain.usecase.room

import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.im.room.RoomRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers

class SetBackgroundUseCase constructor(
    private val repository: RoomRepository = RoomRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Pair<Int, Int>, Any>(ioDispatcher) {

    override suspend fun execute(parameters: Pair<Int, Int>): Result<Any> {
        return repository.setRoomBackground(parameters.first, parameters.second)
    }

}