package com.qyqy.ucoo.compose.presentation.wedding

import android.content.Context
import android.os.Bundle
import androidx.collection.mutableScatterMapOf
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.NavArgument
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.ifEmpty
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.router.IDestination
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.core.Const

val LocalWeddingPageFrom = staticCompositionLocalOf<Int> { 0 }

object WeddingNavigator : ScreenNavigator {
    private const val KEY_ONLY_SELF = "only_self"

    sealed class WeddingDest : IDestination {
        /**
         * 婚礼殿堂
         */
        data object Main : WeddingDest() {
            override val route: String = "wedding_main"
        }

        /**
         * 婚礼纪念墙
         */
        data object WeddingWall : WeddingDest() {
            override val route: String = "wedding_wall"
        }

        /**
         * 婚礼预告
         */
        data object WeddingPreview : WeddingDest() {
            override val route: String = "wedding_preview"
        }
    }

    /**
     * @param context
     * @param destination
     * @param from
     * - 1.**房间banner；
     * 2.语音房用户卡-戒指；
     * 3.用户主页(App主tab 个人中心)-戒指；
     * - 4.礼物栏banner；
     * - 5.IM消息；
     * - 6.房间公屏消息；
     * 7:语音房用户卡-cp卡；
     * 8: 用户主页(App主tab 个人中心)-cp卡
     *
     */
    fun navigate(
        context: Context,
        destination: WeddingDest = WeddingDest.Main,
        from: Int = 0
    ) {
        this.navigate(context) {
            putExtra(Const.KEY_ID, destination.route)
            putExtra(Const.KEY_FROM, from)
        }
    }

    fun startWeddingWall(context: Context, onlySelf: Boolean = false) {
        navigate(context) {
            putExtra(Const.KEY_ID, WeddingDest.WeddingWall.route)
            putExtra(KEY_ONLY_SELF, onlySelf)
        }
    }

    fun startWeddingBooking(context: Context, onlySelf: Boolean = false) {
        navigate(context) {
            putExtra(Const.KEY_ID, WeddingDest.WeddingPreview.route)
            putExtra(KEY_ONLY_SELF, onlySelf)
        }
    }

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val navController = rememberNavController()
        val rootArguments = mutableScatterMapOf<String, NavArgument>()
        val scope = rememberCoroutineScope()
        val appNavController = remember {
            AppNavController(navController, rootArguments, scope)
        }
        val from = bundle.getInt(Const.KEY_FROM, 0)
        val startDestination = activity.intent.getStringExtra(Const.KEY_ID).ifEmpty(WeddingDest.Main.route)
        AppTheme {
            LoadingLayout(modifier = Modifier.fillMaxSize()) {
                CompositionLocalProvider(
                    LocalUCOONavController provides navController,
                    LocalAppNavController provides appNavController,
                    LocalWeddingPageFrom provides from
                ) {
                    NavHost(
                        navController, startDestination = startDestination,
                        enterTransition = {
                            slideInHorizontally(initialOffsetX = { it })
                        },
                        exitTransition = {
                            slideOutHorizontally(targetOffsetX = { -it })
                        },
                        popEnterTransition = {
                            slideInHorizontally(initialOffsetX = { -it })
                        },
                        popExitTransition = {
                            slideOutHorizontally(targetOffsetX = { it })
                        },
                    ) {
                        composable(WeddingDest.Main.route) {
                            WeddingMainScreen()
                        }

                        composable(WeddingDest.WeddingWall.route) {
                            WeddingWallScreen(onlySelf = activity.intent.getBooleanExtra(KEY_ONLY_SELF, false))
                        }

                        composable(WeddingDest.WeddingPreview.route) {
                            WeddingPreviewScreen(onlySelf = activity.intent.getBooleanExtra(KEY_ONLY_SELF, false))
                        }
                    }
                }
            }
        }
    }
}