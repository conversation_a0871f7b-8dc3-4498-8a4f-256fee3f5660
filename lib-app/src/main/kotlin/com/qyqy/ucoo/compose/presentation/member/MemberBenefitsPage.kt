package com.qyqy.ucoo.compose.presentation.member

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.key
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layout
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.user.MemberRightItem
import com.qyqy.ucoo.user.vip.BonusItem
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map


@Composable
fun GoldCoinRewardCard(
    list: List<BonusItem>,
    onClick: (Int) -> Unit,
    modifier: Modifier = Modifier,
    itemMaxCountOnEachPage: Int = 3,
) {
    BoxWithConstraints(
        modifier = modifier
            .fillMaxWidth()
            .clip(Shapes.corner12)
            .background(MemberCenter.Color.Background1),
    ) {
        val itemWidth = constraints.maxWidth.div(itemMaxCountOnEachPage)
        val scrollState = rememberScrollState()
        LaunchedEffect(scrollState) {
            snapshotFlow { scrollState.isScrollInProgress to scrollState.value }
                .distinctUntilChangedBy {
                    it.first
                }
                .filter {
                    it.first.not()
                }
                .map {
                    it.second
                }
                .collectLatest { value ->
                    val offset = value.rem(itemWidth)
                    if (offset > 0) {
                        val index = value.div(itemWidth)
                        if (offset >= itemWidth.div(2)) {
                            scrollState.animateScrollTo(itemWidth.times(index.plus(1)))
                        } else {
                            scrollState.animateScrollTo(itemWidth.times(index))
                        }
                    }
                }
        }

        Row(
            modifier = Modifier
                .horizontalScroll(scrollState, enabled = list.size > itemMaxCountOnEachPage),
        ) {
            val itemModifier = Modifier
                .fillMaxHeight()
                .layout { measurable, constraints ->
                    val placeable = measurable.measure(
                        constraints.copy(
                            minWidth = itemWidth,
                            maxWidth = itemWidth
                        )
                    )
                    layout(placeable.width, placeable.height) {
                        placeable.placeRelative(0, 0)
                    }
                }

            for (item in list) {
                key(item.bonusLabel) {
                    Column(
                        modifier = itemModifier,
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center,
                    ) {

                        Image(painter = painterResource(id = R.drawable.ic_u_coin), contentDescription = null)

                        Spacer(modifier = Modifier.height(2.dp))

                        Text(item.bonusLabel, color = MemberCenter.Color.Text3, fontSize = 12.sp)

                        Spacer(modifier = Modifier.height(6.dp))

                        when (item.bonusStatus) {
                            0 -> {
                                Text(
                                    text = item.bonusStatusLabel,
                                    modifier = Modifier
                                        .height(22.dp)
                                        .widthIn(min = 74.dp)
                                        .clip(CircleShape)
                                        .background(MemberCenter.Color.Background3)
                                        .border(0.5.dp, MemberCenter.Color.Text3, CircleShape)
                                        .wrapContentHeight()
                                        .padding(horizontal = 12.dp),
                                    fontSize = 10.sp,
                                    color = MemberCenter.Color.Text3
                                )
                            }

                            5 -> {
                                Text(
                                    text = item.bonusStatusLabel,
                                    modifier = Modifier
                                        .height(22.dp)
                                        .widthIn(min = 74.dp)
                                        .clip(CircleShape)
                                        .background(Brush.horizontalGradient(listOf(MemberCenter.Color.Background4, MemberCenter.Color.Background5)))
                                        .wrapContentHeight()
                                        .clickable {
                                            onClick(item.bonusIndex)
                                        }
                                        .padding(horizontal = 12.dp),
                                    fontSize = 10.sp,
                                    color = MemberCenter.Color.Text4,
                                    textAlign = TextAlign.Center,
                                )
                            }

                            else -> {
                                Text(
                                    text = item.bonusStatusLabel,
                                    modifier = Modifier
                                        .height(22.dp)
                                        .widthIn(min = 74.dp)
                                        .clip(CircleShape)
                                        .background(MemberCenter.Color.Background6)
                                        .wrapContentHeight()
                                        .padding(horizontal = 12.dp),
                                    fontSize = 10.sp,
                                    color = MemberCenter.Color.Text5,
                                    textAlign = TextAlign.Center,
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun GoldCoinRewardCardPreview() {
    GoldCoinRewardCard(
        listOf(
            BonusItem(1, "50金币", 10, "已领取"),
            BonusItem(1, "100金币", 5, "领取"),
            BonusItem(1, "150金币", 0, "24h后可领取"),
        ), {}, modifier = Modifier.height(90.dp)
    )
}


@Composable
fun MemberBenefitsLabel(labelUrl: String, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.fillMaxWidth()
    ) {
        ComposeImage(
            model = labelUrl,
            contentDescription = null,
            modifier = Modifier.height(32.dp),
            contentScale = ContentScale.FillHeight,
            alignment = Alignment.CenterStart
        )
        Text(
            text = stringResource(id = R.string.享高效交友体验),
            modifier = Modifier.align(Alignment.CenterEnd),
            color = MemberCenter.Color.Text7,
            fontSize = 12.sp
        )
    }
}


@Composable
fun MemberBenefitsCard(
    list: List<MemberRightItem>,
    modifier: Modifier = Modifier,
) {
    VerticalGrid(
        columns = 3,
        modifier = modifier.fillMaxWidth(),
        verticalSpace = 24.dp,
    ) {
        list.forEach {
            key(it.name) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ComposeImage(model = it.icon, modifier = Modifier.size(56.dp), preview = painterResource(id = R.drawable.ic_google_pay))

                    Spacer(modifier = Modifier.height(10.dp))

                    Text(text = it.name, color = MemberCenter.Color.Text6, fontSize = 14.sp)
                }
            }
        }
    }
}

@Preview
@Composable
fun MemberBenefitsCardPreview() {
    MemberBenefitsCard(
        listOf(
            MemberRightItem("", "无限免费畅聊", ""),
            MemberRightItem("", "无限加好友", ""),
            MemberRightItem("", "查看谁喜欢我", ""),
            MemberRightItem("", "尊享会员勋章", ""),
            MemberRightItem("", "3min免费语音/天", ""),
            MemberRightItem("", "创建/加入部落,", ""),
        )
    )
}