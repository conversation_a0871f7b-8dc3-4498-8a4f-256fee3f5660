package com.qyqy.ucoo.compose.presentation.ff

import android.os.Bundle
import androidx.activity.compose.setContent
import com.qyqy.ucoo.base.BaseActivity

/**
 * # 亲友关系设置
 */
class FamilySettingActivity : BaseActivity() {

    private var removed = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            FFRelationSettingScreen(onRemoved = {
                removed = true
            })
        }
    }

    override fun finish() {
        setResult(if (removed) RESULT_OK else RESULT_CANCELED)
        super.finish()
    }
}