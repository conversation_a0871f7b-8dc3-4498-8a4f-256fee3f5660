

package com.qyqy.ucoo.compose.presentation.chatgroup.list

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.getSampleImageUrl
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.theme.colorWhite80Alpha
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage


@Composable
fun ChatGroupItem(
    modifier: Modifier = Modifier,
    avatar: String,
    title: String,
    desc: String,
    memberAvatars: List<String>,
    memberCount: Int,
    relationWithMe: Int = 0,
    onJoinGroup: () -> Unit = {},
    onOpenGroup: () -> Unit = {}
) {
    Column(
        modifier = modifier
            .paint(painterResource(id = R.drawable.bg_group_item), contentScale = ContentScale.FillWidth)
            .padding(horizontal = 12.dp)
    ) {
        Row(modifier = Modifier.padding(top = 16.dp, bottom = 12.dp), verticalAlignment = Alignment.CenterVertically) {
            CircleComposeImage(
                model = avatar, modifier = Modifier
                    .size(80.dp)
                    .background(Color(0x40FFFFFF), CircleShape)
                    .padding(1.dp)
            )
            Column(
                modifier = Modifier
                    .padding(start = 12.dp)
                    .weight(1f)
            ) {
                AppText(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    maxLines = 2,
                    overflow = TextOverflow.Ellipsis,
                    color = Color.White
                )
                Spacer(modifier = Modifier.height(16.dp))
                Row {
                    memberAvatars.forEachIndexed { index, avatar ->
                        CircleComposeImage(
                            model = avatar, modifier = Modifier
                                .size(28.dp)
                                .background(Color(0xFF5DECFF), CircleShape)
                                .padding(0.5.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                    }
                    Box(
                        modifier = Modifier
                            .size(28.dp)
                            .background(Color(0x26FFFFFF), CircleShape),
                        contentAlignment = Alignment.Center
                    ) {
                        AppText(text = memberCount.toString(), fontSize = 12.sp, color = colorWhite80Alpha)
                    }
                }
            }
        }
        HorizontalDivider(color = Color(0x14FFFFFF), thickness = 0.5.dp, modifier = Modifier.height(0.5.dp))
        Row(modifier = Modifier.height(54.dp), verticalAlignment = Alignment.CenterVertically) {
            AppText(
                text = desc,
                fontSize = 12.sp,
                color = Color(0xA6FFFFFF),
                maxLines = 2,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 7.dp)
            )
            AppButton(
                text = stringResource(
                    id = when (relationWithMe) {
                        ChatGroup.Relation.NONE -> R.string.join_chatgroup
                        ChatGroup.Relation.APPLYING -> R.string.applying
                        ChatGroup.Relation.JOINED -> R.string.joined
                        else -> R.string.new_one
                    }
                ),
                modifier = Modifier
                    .widthIn(min = 80.dp)
                    .background(Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))), RoundedCornerShape(50))
                    .clip(RoundedCornerShape(50)),
                fontSize = 12.sp,
                color = Color.White,
                paddingValues = PaddingValues(horizontal = 12.dp, 9.dp),
                onClick = {
                    when (relationWithMe) {
                        ChatGroup.Relation.JOINED -> onOpenGroup()
                        ChatGroup.Relation.NONE -> onJoinGroup()
                        else -> {}
                    }
                }
            )
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF050505)
@Composable
fun ChatGroupItemPreview() {
    ChatGroupItem(
        Modifier, getSampleImageUrl(300, 300), "群组名称", "独身の友達独身の友達もグループへの参加を申請できます", buildList {
            repeat(4) {
                add(getSampleImageUrl(300, 300))
            }
        }, 100, 10
    )
}