package com.qyqy.ucoo.compose.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import com.qyqy.ucoo.account.AppUser
import kotlin.random.Random

/**
{
        "intimacy_score": 100,  
        "next_level_intimacy_score": 10000, 
        "female_user": {
            "userid": 2192,
            "public_id": "102805",
            "nickname": "男0001名字超长😀📱😀😀📱📱",
            "avatar_url": "https://s.test.ucoofun.com/aacecN?x-oss-process=image/format,webp",
            "gender": 1,
            "age": 25,
            "height": 167,
            "avatar_frame": "",
            "level": 45,
            "exp_level_info": {
                "charm_level": 1,
                "wealth_level": 1
            }
        },
        "male_user": {
            "userid": 2192,
            "public_id": "102805",
            "nickname": "男0001名字超长😀📱😀😀📱📱",
            "avatar_url": "https://s.test.ucoofun.com/aacecN?x-oss-process=image/format,webp",
            "gender": 1,
            "age": 25,
            "height": 167,
            "avatar_frame": "",
            "level": 45,
            "exp_level_info": {
                "charm_level": 1,
                "wealth_level": 1
            }
        },
        "history": [  
            {
                "date": "2021-01-01",
                "content": "xxxxxx"
            }
        ],
        "togather_days": 10,
        "house": {
            "name": "xxx",
            "icon": "xxxx",
            "effect_file": "xxx",
            "profit": 100
             }
    }
*/
@Keep
@Serializable
data class CPMemoriesBean(
    @SerialName("female_user")
    val femaleUser: AppUser = AppUser(),
    @SerialName("male_user")
    val maleUser:  AppUser = AppUser(),
    @SerialName("history")
    val history: List<History> = listOf(),
    @SerialName("house")
    val house: CPHouseFullInfo.CPHouseSimplePropItem = CPHouseFullInfo.CPHouseSimplePropItem(),
    @SerialName("intimacy_score")
    val intimacyScore: Int = 0,
    @SerialName("next_level_intimacy_score")
    val nextLevelIntimacyScore: Int = 0,
    @SerialName("together_days")
    val togetherDays: Int = 0
) {

    @Keep
    @Serializable
    data class History(
        @SerialName("content")
        val content: String = "",
        @SerialName("create_timestamp")
        val create_timestamp: Long = 0,
        var isLeft :Boolean = false
    )
}