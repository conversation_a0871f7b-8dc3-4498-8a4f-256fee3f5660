@file:OptIn(ExperimentalFoundationApi::class)

package com.qyqy.ucoo.compose.ui

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.LifecyclePauseOrDisposeEffectResult
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.LifecycleStartEffect
import androidx.lifecycle.compose.LifecycleStopOrDisposeEffectResult
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

@Composable
@OptIn(ExperimentalFoundationApi::class)
fun rememberLoopPagerState(pageCount: Int, initialPage: Int = 0) =
    rememberPagerState(initialPage = initialPage) {
        pageCount
    }
//    rememberPagerState(initialPage = (Int.MAX_VALUE / 2).let { it - it % pageCount }) {
//        Int.MAX_VALUE
//    }

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun LoopHorizontalPager(
    modifier: Modifier = Modifier,
    state: PagerState,
    pageCreator: @Composable PagerScope.(Int) -> Unit,
    interval: Long = 5000L
) {
    if (state.pageCount > 1) {
        val scope = rememberCoroutineScope()
        LifecycleResumeEffect(key1 = state.pageCount) {
            val job = scope.launch(Dispatchers.Main) {
                while (isActive) {
                    delay(interval)
                    state.animateScrollToPage((state.currentPage + 1) % state.pageCount)
                }
            }
            object : LifecyclePauseOrDisposeEffectResult {
                override fun runPauseOrOnDisposeEffect() {
                    job.cancel()
                }
            }
        }
    }
    HorizontalPager(modifier = modifier, state = state) { page ->
        pageCreator(page)
    }
}

@Composable
fun CircleIndicator(
    modifier: Modifier = Modifier,
    count: Int,
    currentIndex: Int,
    size: Dp = 5.dp,
    spacing: Dp = 10.dp,
    colorSelected: Color = Color.White,
    colorUnSelected: Color = colorWhite50Alpha
) {
    Row(modifier = modifier) {
        repeat(count) {
            val color = if (it == currentIndex) colorSelected else colorUnSelected
            Box(
                modifier = Modifier
                    .size(size)
                    .background(color, RoundedCornerShape(50))
            ) {

            }
            if (it < count - 1) {
                Spacer(modifier = Modifier.width(spacing))
            }
        }
    }
}

@Composable
fun rememberViewPagerState(initialPage: Int, pageCount: Int): PagerState {
    val loopPageCount = if (pageCount > 1) pageCount.times(3) else pageCount
    val loopInitialPage = if (pageCount > 1) initialPage.plus(pageCount) else initialPage
    return rememberPagerState(loopInitialPage) {
        loopPageCount
    }
}

fun mapLoopViewPagerPage(page: Int, realCount: Int): Int {
    return if (realCount > 0) page.rem(realCount) else 0
}

@Composable
fun LoopViewPagerEffect(realCount: Int, pagerState: PagerState, interval: Long = 5000L) {
    if (realCount > 1) {
        val scope = rememberCoroutineScope()
        LifecycleStartEffect(key1 = pagerState) {
            val job = scope.launch(Dispatchers.Main) {
                while (this.isActive) {
                    try {
                        delay(interval)
                        pagerState.animateScrollToPage(pagerState.currentPage.plus(1).rem(pagerState.pageCount))
                    } catch (e: Exception) {
                        e.printStackTrace()
                    }
                }
            }
            object : LifecycleStopOrDisposeEffectResult {
                override fun runStopOrDisposeEffect() {
                    job.cancel()
                }
            }
        }
    }
}


@Composable
fun LoopAdjustEffect(realCount: Int, pageCount: Int, currentPage: Int, scrollToPage: (Int) -> Unit) {
    val scope = rememberCoroutineScope()
    if (realCount > 1) {
        SideEffect {
            scope.launch {
                when (currentPage) {
                    0 -> scrollToPage(realCount)
                    pageCount - 1 -> scrollToPage(realCount - 1 + realCount)
                }
            }
        }
    }
}
