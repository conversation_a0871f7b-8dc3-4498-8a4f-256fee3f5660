package com.qyqy.ucoo.compose.ui.photo

import android.content.Context
import androidx.compose.animation.core.AnimationVector4D
import androidx.compose.animation.core.TwoWayConverter
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.layout.positionInRoot
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.qyqy.ucoo.utils.LogUtil
import site.xzwzz.video.XPlayer

data class PhotoTransform(
    val size: DpSize,
    val position: Offset,
) {
    companion object {
        val PhotoTransformToVector: TwoWayConverter<PhotoTransform, AnimationVector4D> =
            TwoWayConverter(
                convertToVector = {
                    AnimationVector4D(
                        it.size.width.value,
                        it.size.height.value,
                        it.position.x,
                        it.position.y
                    )
                },
                convertFromVector = {
                    PhotoTransform(
                        DpSize(it.v1.dp, it.v2.dp),
                        Offset(it.v3, it.v4)
                    )
                }
            )
    }
}


fun Modifier.previewKeyModifier(previewState: PhotoPreviewState?, key: String): Modifier = if (previewState == null) {
    this
} else {
//    composed {
//        DisposableEffect(key) {
//            if (previewState.previewItems[key] == null) {
//                previewState.previewItems[key] = PreviewKeyItem()
//            }
//            onDispose {
//                previewState.previewItems.remove(key)
//            }
//        }
//        onGloballyPositioned {
//            previewState.previewItems[key]?.apply {
//                size = it.size
//                position = it.positionInRoot()
//            }
//        }
//    }.graphicsLayer {
//        alpha = if (previewState.transitionAnimKey == key) {
//            0f
//        } else {
//            1f
//        }
//    }
    this
}

internal class PreviewKeyItem {

    var size by mutableStateOf(IntSize.Zero)

    var position by mutableStateOf(Offset.Zero)

}

val LocalPhotoPreviewState = staticCompositionLocalOf<PhotoPreviewState?> {
    null
}

@Composable
fun rememberPhotoPreviewState(
    gestureState: PhotoGestureState = rememberPhotoGestureState(),
    onGestureClose: Density.(Int, Float, Float) -> Boolean = { _, _, _ -> false },
    previewList: () -> List<IPhotoPreviewModel> = { emptyList() },
): PhotoPreviewState {
    return rememberSaveable(
        saver = PhotoPreviewState.Saver(
            gestureState = gestureState,
            onGestureClose = onGestureClose
        )
    ) {
        PhotoPreviewState(
            gestureState = gestureState,
            onGestureClose = onGestureClose,
            updatedPreviewList = previewList,
        )
    }.apply {
        previewListState.value = previewList
    }
}

@Stable
class PhotoPreviewState internal constructor(
    internal val gestureState: PhotoGestureState,
    private val onGestureClose: Density.(Int, Float, Float) -> Boolean = { _, _, _ -> false },
    updatedPreviewList: () -> List<IPhotoPreviewModel>,
) {

    companion object {

        fun Saver(
            gestureState: PhotoGestureState,
            onGestureClose: Density.(Int, Float, Float) -> Boolean,
        ): Saver<PhotoPreviewState, *> = Saver(
            save = {
                listOf(it.state, it.previewList)
            },
            restore = {
                PhotoPreviewState(
                    gestureState = gestureState,
                    onGestureClose = onGestureClose,
                    updatedPreviewList = {
                        it[1] as List<IPhotoPreviewModel>
                    }
                ).apply {
                    state = if (it[0] as Int == 1 || it[0] as Int == 4) {
                        4
                    } else {
                        0
                    }
                }
            }
        )
    }

//    internal val previewItems = mutableMapOf<String, PreviewKeyItem>()

    internal var state by mutableIntStateOf(0)
        private set

    private var initialStartPage: Int = 0

    private var initialPageOffsetFraction: Float = 0f

    internal var closeOffsetMode by mutableStateOf(false)

    internal var transitionAnimKey by mutableStateOf<String?>(null)

    val isPreviewing: Boolean by derivedStateOf {
        state > 0
    }

    val isAnimPreviewing: Boolean by derivedStateOf {
        state in 1..3
    }

    /**
     * 预览已结束
     */
    val previewIsClosed: Boolean
        get() = state == 0

    /**
     * 进入预览动画
     */
    val inPreviewing: Boolean
        get() = state == 1

    /**
     * 中止进入预览动画并退出预览
     */
    val abortInPreviewing: Boolean
        get() = state == 2

    /**
     * 退出预览动画
     */
    val outPreviewing: Boolean
        get() = state == 3

    /**
     * 预览就绪，可以进行手势控制了，此时转场动画已结束
     */
    val previewIsReady: Boolean
        get() = state == 4


    internal var previewListState = mutableStateOf(updatedPreviewList)

    private val previewList: List<IPhotoPreviewModel>
        get() = previewListState.value()

    @OptIn(ExperimentalFoundationApi::class)
    private var pagerState: PagerState? = null

    @OptIn(ExperimentalFoundationApi::class)
    val currentPage: Int
        get() = pagerState?.currentPage ?: -1

    @OptIn(ExperimentalFoundationApi::class)
    val currentPreview: IPhotoPreviewModel?
        get() = pagerState?.currentPage?.let {
            previewList.getOrNull(it)
        }

    fun startPreview(index: Int, pageOffsetFraction: Float = 0f) {
        LogUtil.d("start preview :$state")
        if (state == 0 && index >= 0 && index < previewList.size) {
            closeOffsetMode = false
            initialStartPage = index
            initialPageOffsetFraction = pageOffsetFraction
            state = 1
        }
    }

    fun startPreview(previewKey: String, pageOffsetFraction: Float = 0f) {
        if (state == 0) {
            val index = previewList.indexOfLast {
                it.key == previewKey
            }
            if (index >= 0) {
                closeOffsetMode = false
                initialStartPage = index
                initialPageOffsetFraction = pageOffsetFraction
                state = 1
            }
        }
    }

    fun endPreview() {
        if (inPreviewing) {
            state = 2
        } else if (previewIsReady) {
            state = 3
        }
    }

    @OptIn(ExperimentalFoundationApi::class)
    @Composable
    internal fun rememberPagerState(): PagerState {
        check(isPreviewing)
        return rememberPagerState(initialStartPage, initialPageOffsetFraction) {
            previewList.size
        }.also {
            pagerState = it
        }
    }

    @OptIn(ExperimentalFoundationApi::class)
    internal fun previewClosed() {
        state = 0
        pagerState = null
    }

    internal fun previewReady() {
        state = 4
    }

    internal fun invokeGestureClose(density: Density, page: Int, offsetY: Float, velocityY: Float): Boolean {
        return with(density) {
            onGestureClose(page, offsetY, velocityY)
        }.also { end ->
            if (end) {
                endPreview()
            }
        }
    }

    internal fun getPreviewModelByPage(page: Int): IPhotoPreviewModel? {
        return previewList.getOrNull(page)
    }

    internal fun getPreviewKeyByPage(page: Int): String? {
        return getPreviewModelByPage(page)?.key
    }

//    internal fun getPreviewKeyItem(key: String): PreviewKeyItem? {
//        return previewItems[key]
//    }
}