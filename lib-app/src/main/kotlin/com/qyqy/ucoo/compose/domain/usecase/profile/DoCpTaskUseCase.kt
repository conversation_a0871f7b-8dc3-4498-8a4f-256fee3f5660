package com.qyqy.ucoo.compose.domain.usecase.profile

import com.qyqy.ucoo.compose.data.TaskResult
import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers

class DoCpTaskUseCase constructor(
    private val userRepository: UserRepository = UserRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<String, TaskResult>(ioDispatcher) {
    override suspend fun execute(parameters: String): Result<TaskResult> {
        return userRepository.routerLinkByTaskId(parameters)
    }

}