package com.qyqy.ucoo.compose.ui

import android.content.Context
import android.view.View
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.qyqy.ucoo.account.T
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.widget.avatar.AvatarView

@Composable
fun <V : View, T> ComposeAndroidView(
    provider: () -> T,
    factory: (Context) -> V,
    modifier: Modifier = Modifier,
    update: (V, T) -> Unit = { _, _ -> }
) {
    // Adds view to Compose
    AndroidView(
        modifier = modifier, // Occupy the max size in the Compose UI tree
        factory = factory,
        update = { view ->
            // View's been inflated or state read in this block has been updated
            // Add logic here if necessary
            update(view, provider())
        }
    )
}

@Composable
fun ComposeAvatarView(
    user: User,
    modifier: Modifier = Modifier,
    strokeIfEmptyFrame: Boolean = false,
    update: (AvatarView, User) -> Unit = { _, _ -> }
) {
    ComposeAndroidView({
        user
    }, {
        AvatarView(it)
    },
        modifier
    ) { view, data ->
        if (!data.isInvalid()) {
            view.bindUser(data, strokeIfEmptyFrame)
        }
        update(view, data)
    }
}

