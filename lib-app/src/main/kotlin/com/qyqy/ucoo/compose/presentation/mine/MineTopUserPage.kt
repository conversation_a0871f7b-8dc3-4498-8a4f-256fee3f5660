@file:OptIn(ExperimentalLayoutApi::class)

package com.qyqy.ucoo.compose.presentation.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.graphics.toColorInt
import androidx.fragment.app.Fragment
import androidx.fragment.app.findFragment
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.colorList
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.member.MemberCenterScreenNavigator
import com.qyqy.ucoo.compose.presentation.wedding.WeddingNavigator
import com.qyqy.ucoo.compose.takeUnlessEditMode
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.mine.FriendsActivity
import com.qyqy.ucoo.mine.UserProfileActivity

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun MineTopUserPage(user: User, modifier: Modifier = Modifier) {
    val context = LocalContext.current
    val fragment = LocalView.current.takeUnlessEditMode?.findFragment<Fragment>()
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(280.dp)
    ) {
        ComposeImage(
            model = user.avatarUrl,
            modifier = Modifier
                .fillMaxSize()
                .drawWithContent {
                    drawContent()
                    drawRect(Brush.verticalGradient(listOf(Color(0x66000000), Color(0xBF000000))))
                },
        )
        Row(
            Modifier
                .align(Alignment.BottomStart),
            verticalAlignment = Alignment.Bottom
        ) {
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 16.dp, bottom = 16.dp, end = 8.dp)
            ) {
                val density = LocalDensity.current
                FlowRow(
                    modifier = Modifier
                        .fillMaxWidth(),
                    horizontalArrangement = Arrangement.spacedBy(2.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp),
                ) {
                    Text(
                        text = user.nickname,
                        modifier = Modifier.clickable {
                            context.startActivity(UserProfileActivity.createIntent(context, user))
                        },
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        style = TextStyle(
                            brush = user.colorList?.run {
                                Brush.horizontalGradient(map { Color(it.toColorInt()) })
                            }
                        )
                    )
                    Image(
                        modifier = Modifier
                            .height(24.dp)
                            .padding(start = 8.dp)
                            .clickable {
                                MemberCenterScreenNavigator.navigate(fragment!!, "mine")
                            },
                        painter = painterResource(id = if (user.isVip) R.drawable.ic_mine_vip_enable else R.drawable.ic_mine_vip_un_enable),
                        contentDescription = null,
                        contentScale = ContentScale.FillHeight,
                    )
                    user.characteristicMedalList.forEach {
                        Box(
                            modifier = Modifier
                                .align(Alignment.CenterVertically)
                                .height(24.dp),
                            contentAlignment = Alignment.BottomStart
                        ) {
                            ComposeImage(
                                model = it.icon, modifier = Modifier.size(
                                    (it.width / density.density).dp, (Math.min(it.height / density.density, 24f)).dp
                                ), contentScale = ContentScale.FillHeight
                            )
                        }
                    }
                }

                Row(modifier = Modifier.padding(top = 17.dp)) {

                    AppText(
                        text = stringResource(id = R.string.id_num, user.publicId),
                        color = colorResource(id = R.color.white_alpha_50),
                        fontSize = 14.sp,
                    )

                    AppText(
                        text = buildAnnotatedString {
                            append(stringResource(id = R.string.关注))
                            append(" ")
                            withStyle(style = SpanStyle(color = Color.White)) {
                                append(user.followCount.toString())
                            }
                        },
                        modifier = Modifier
                            .padding(start = 10.dp)
                            .clickable {
                                context.startActivity(FriendsActivity.createIntent(context, 1))
                            },
                        color = colorResource(id = R.color.white_alpha_50),
                        fontSize = 14.sp,
                    )

                    AppText(
                        text = buildAnnotatedString {
                            append(stringResource(id = R.string.粉丝))
                            append(" ")
                            withStyle(style = SpanStyle(color = Color.White)) {
                                append(user.fansCount.toString())
                            }
                        },
                        modifier = Modifier
                            .padding(start = 10.dp)
                            .clickable {
                                context.startActivity(FriendsActivity.createIntent(context, 2))
                            },
                        color = colorResource(id = R.color.white_alpha_50),
                        fontSize = 14.sp,
                    )
                }
            }

            //2.41.0 婚戒相关
            user.ringInfo?.let { info ->
                Column(
                    modifier = Modifier
                        .padding(bottom = 16.dp, end = 16.dp)
                        .background(
                            color = Color(0x4D000000),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(4.dp)
                        .click {
                            WeddingNavigator.navigate(context,from = 3)
                        }
                    ,
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ComposeImage(model = info.effect_file, modifier = Modifier.size(48.dp), contentScale = ContentScale.FillWidth)
                    Text(
                        info.name,
                        color = Color(0xFFFFE589),
                        fontSize = 10.sp,
                        lineHeight = 10.sp,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.widthIn(48.dp, 64.dp)
                    )
                }
            }
        }
    }
}


@Preview
@Composable
fun PreviewMineTopUserPage() {
    MineTopUserPage(userForPreview)
}