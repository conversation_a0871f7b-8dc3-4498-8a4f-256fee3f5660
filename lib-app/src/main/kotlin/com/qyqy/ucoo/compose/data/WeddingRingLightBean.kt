package com.qyqy.ucoo.compose.data

import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.account.WeddingRing
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class WeddingRingLightBean(
    @SerialName("sender")
    val sender: AppUser = INVALID_USER,
    @SerialName("receiver")
    val receiver: AppUser = INVALID_USER,
    @SerialName("ring")
    val ring: WeddingRing = WeddingRing(),
    @SerialName("content")
    val content: String = "",
    @SerialName("jump_link")
    val jumpLink: String = "",
)
