package com.qyqy.ucoo.compose.ui

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp


@Composable
fun VerticalGrid(
    modifier: Modifier = Modifier,
    columns: Int = 2,
    horizontalSpace: Dp = 0.dp,
    verticalSpace: Dp = 0.dp,
    wrapRowHeight: Boolean = false,
    content: @Composable () -> Unit
) {
    val horizontalSpacePx = with(LocalDensity.current) {
        horizontalSpace.roundToPx()
    }
    val verticalSpacePx = with(LocalDensity.current) {
        verticalSpace.roundToPx()
    }
    Layout(
        content = content,
        modifier = modifier
    ) { measurables, constraints ->
        check(columns > 0)
        val itemWidth = if (columns > 1) {
            constraints.maxWidth.minus(horizontalSpacePx.times(columns.minus(1))) / columns
        } else {
            constraints.maxWidth / columns
        }.toInt().coerceAtLeast(0)
        // Keep given height constraints, but set an exact width
        val itemConstraints = constraints.copy(
            minWidth = itemWidth,
            maxWidth = itemWidth,
            minHeight = 0,
        )
        // Measure each item with these constraints
        val placeables = measurables.map { it.measure(itemConstraints) }
        // Track each columns height so we can calculate the overall height
        val columnHeights = Array(columns) { 0 }
        val rowHeights = Array(placeables.lastIndex.div(columns).plus(1)) {
            0
        }
        placeables.forEachIndexed { index, placeable ->
            val column = index % columns
            val row = index / columns
            rowHeights[row] = rowHeights[row].coerceAtLeast(placeable.height)
            if (row > 0) {
                columnHeights[column] += placeable.height.plus(verticalSpacePx)
            } else {
                columnHeights[column] += placeable.height
            }
        }
        val height = (columnHeights.maxOrNull() ?: constraints.minHeight)
            .coerceIn(constraints.minHeight, constraints.maxHeight)
        layout(
            width = constraints.maxWidth,
            height = height,
        ) {
            // Track the Y co-ord per column we have placed up to
            val columnY = Array(columns) { 0 }
            var startX = 0
            placeables.forEachIndexed { index, placeable ->
                val column = index % columns
                val row = index / columns
                if (column == 0) {
                    startX = 0
                }
                placeable.placeRelative(
                    x = startX,
                    y = columnY[column]
                )
                startX += itemWidth.plus(horizontalSpacePx)
                if (wrapRowHeight) {
                    columnY[column] += rowHeights[row].plus(verticalSpacePx)
                } else {
                    columnY[column] += placeable.height.plus(verticalSpacePx)
                }
            }
        }
    }
}