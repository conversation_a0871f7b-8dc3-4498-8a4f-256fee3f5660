package com.qyqy.ucoo.compose.vm.relationship

import com.qyqy.ucoo.compose.data.CpGraph
import com.qyqy.ucoo.compose.data.LabelItem
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.data.RelationshipGraph
import com.qyqy.ucoo.compose.state.MviViewModel
import kotlinx.collections.immutable.persistentListOf

interface RelationshipMvi {

    sealed interface ViewEvent : MviViewModel.MviEvent {

        data class GetFamilyRelationInfo(val userId: String, val cpGraph: CpGraph? = null) : ViewEvent

        data class ResetFamilyRelationInfo(val cpGraph: CpGraph) : ViewEvent

        data class BuySeats(val count: Int) : ViewEvent
    }

    sealed interface ViewResult : MviViewModel.MviViewResult {
        data class ErrorResult(val toastError: String? = null) : ViewResult

        data class CpGraphResult(
            val cpGraph: CpGraph,
        ) : ViewResult

        data class ResetGraphResult(
            val cpGraph: CpGraph,
        ) : ViewResult

        data class FamilyConfigInfoResult(
            val emptyCount: Int = 6,
            val totalCount: Int = emptyCount,
            val maxCount: Int = Int.MAX_VALUE,
            val pricePerLabel: Int = 5000,
            val ruleLink: String = "",
        ) : ViewResult

        data class FamilyLabelListResult(val labelList: List<Relationship.Label.Normal> = persistentListOf()) : ViewResult

        data class FamilyTagListResult(val labelTagList: List<LabelItem> = persistentListOf()) : ViewResult

        data class BuySeatsResult(val buyCount: Int, val totalCount: Int) : ViewResult
    }

    data class ViewState(
        val userId: String,
        val relationshipGraph: RelationshipGraph,
    ) : MviViewModel.MviViewState


    interface ViewEffect : MviViewModel.MviSideEffect {

        data class Toast(val msg: String) : ViewEffect

    }
}


