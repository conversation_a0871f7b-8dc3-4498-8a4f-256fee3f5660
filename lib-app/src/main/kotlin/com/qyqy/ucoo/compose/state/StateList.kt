package com.qyqy.ucoo.compose.state

import android.util.Log
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridItemScope
import androidx.compose.foundation.lazy.grid.LazyGridItemSpanScope
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.ucoo.compose.state.StateLayoutDefaults.defaultEmpty
import com.qyqy.ucoo.compose.state.StateLayoutDefaults.defaultError
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import kotlinx.coroutines.CoroutineScope

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun <Page, T> StateListView(
    viewModel: StateViewModel<Page, T>,
    keyProvider: ((Int, T) -> Any)? = null,
    listItem: @Composable (item: T, index: Int, coroutineScope: CoroutineScope, loadingState: MutableState<Boolean>) -> Unit,
) {
    val coroutineScope = rememberCoroutineScope()
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    StateLayout(viewModel.contentState, isRefreshing, onRefresh = {
        viewModel.refresh()
    }) {
        val loadingState = LocalContentLoading.current
        LazyColumn(content = {
            itemsIndexed(items = list, key = keyProvider) { index, item ->
                Box(modifier = Modifier.animateItemPlacement()) {
                    listItem(item, index, coroutineScope, loadingState)
                }
            }
            item {
                StateLayoutDefaults.LoadMoreIndicator(viewModel)
            }
        })
    }
}

@Composable
fun <Page, T> StateGrid(
    viewModel: StateViewModel<Page, T>,
    spanCount: Int = 2,
    span: (LazyGridItemSpanScope.(item: T) -> GridItemSpan)? = null,
    key: ((T) -> Any)? = null,
    modifier: Modifier = Modifier,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    itemContent: @Composable LazyGridItemScope.(item: T) -> Unit
) {
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    StateLayout(viewModel.contentState, isRefreshing, onRefresh = {
        viewModel.refresh()
    }) {
        LazyVerticalGrid(columns = GridCells.Fixed(spanCount), content = {
            items(list, key, span, itemContent = itemContent)
            item(span = { GridItemSpan(spanCount) }) {
                StateLayoutDefaults.LoadMoreIndicator(viewModel)
            }
        }, horizontalArrangement = horizontalArrangement, verticalArrangement = verticalArrangement, modifier = modifier)
    }
}

@Composable
fun <T> StateListView(
    viewModel: StateViewModelWithIntPage<T>,
    keyProvider: ((Int, T) -> Any)? = null,
    listItem: @Composable (item: T, index: Int, coroutineScope: CoroutineScope, loadingState: MutableState<Boolean>) -> Unit,
) {
    StateListView<Int, T>(viewModel, keyProvider, listItem)
}


//region new


@Composable
fun <Page, T> StateGridWidget(
    viewModel: StateViewModel<Page, T>,
    modifier: Modifier = Modifier,
    spanCount: Int = 2,
    enableRefresh: Boolean = true,
    enableLoadMore: Boolean = true,
    enableLoadMoreView: Boolean = true,
    showEmpty: Boolean = true,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    bottomLoading: @Composable ((StateViewModel<Page, T>) -> Unit)? = null,
    content: LazyGridScope.(List<T>) -> Unit
) {
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    StateLayout(viewModel.contentState, isRefreshing, customEmpty = showEmpty, onRefresh = {
        if (enableRefresh) {
            viewModel.refresh()
        }
    }) {
        Column {
            LazyVerticalGrid(
                columns = GridCells.Fixed(spanCount),
                horizontalArrangement = horizontalArrangement,
                verticalArrangement = verticalArrangement,
                modifier = modifier
            ) {
                content(list)
                if (enableLoadMoreView) {
                    item(span = { GridItemSpan(spanCount) }) {
                        (bottomLoading?.invoke(viewModel)) ?: StateLayoutDefaults.LoadMoreIndicator(viewModel)
                    }
                }
            }
        }
    }
}


@Composable
fun <Page, T> StateList(
    viewModel: StateViewModel<Page, T>,
    modifier: Modifier = Modifier,
    enableRefresh: Boolean = true,
    enableLoadMore: Boolean = true,
    showEmpty: Boolean = true,
    verticalArrangement: Arrangement.Vertical = Arrangement.Top,
    bottomLoading: @Composable ((StateViewModel<Page, T>) -> Unit)? = null,
    content: LazyListScope.(List<T>) -> Unit
) {
    val list by viewModel.listFlow.collectAsStateWithLifecycle()
    val isRefreshing by viewModel.isRefreshingFlow.collectAsState()
    StateLayout(viewModel.contentState, isRefreshing, customEmpty = showEmpty, onRefresh = {
        if (enableRefresh) {
            viewModel.refresh()
        }
    }) {
        Column {
            LazyColumn(
                verticalArrangement = verticalArrangement,
                modifier = modifier
            ) {
                content(list)
                item() {
                    (bottomLoading?.invoke(viewModel)) ?: StateLayoutDefaults.LoadMoreIndicator(viewModel)
                }
            }
        }
    }
}

//endregion