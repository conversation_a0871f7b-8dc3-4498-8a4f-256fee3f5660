package com.qyqy.ucoo.compose.router

import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.lifecycle.ViewModelStoreOwner
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController

val LocalUCOONavController = staticCompositionLocalOf<NavHostController> {
    error("navController is null")
}
val LocalNavigationViewModelOwner = staticCompositionLocalOf<ViewModelStoreOwner> {
    error("viewModelStoreOwner is null")
}


@Composable
fun UCOOPreviewTheme(content: @Composable () -> Unit) {
    val navController = rememberNavController()
    CompositionLocalProvider(
        LocalUCOONavController provides navController,
    ) {
        content()
    }
}