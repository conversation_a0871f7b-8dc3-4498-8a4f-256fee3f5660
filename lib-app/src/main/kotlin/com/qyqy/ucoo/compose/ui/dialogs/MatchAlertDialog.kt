package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Button
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.overseas.common.ext.setMatchParentLayoutParams
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorSecondBlack
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.utils.web.AppBridgeWebView
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

@Composable
fun MatchAlert(title: String, buttonText: String, link: String, onClick: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .background(colorSecondBlack, Shapes.small)
            .padding(12.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AppText(
            text = title,
            fontSize = 17.sp,
            color = Color.White,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth(1f)
        )
        Spacer(modifier = Modifier.height(4.dp))

        var loading by remember("web") {
            mutableStateOf(false)
        }
        val scope = rememberCoroutineScope()
        Box(
            modifier = Modifier
                .height(210.dp)
                .fillMaxWidth(1f)
        ) {
            AndroidView(
                factory = {
                    AppBridgeWebView(it).also { web ->
                        web.setBackgroundColor(android.graphics.Color.TRANSPARENT)
                        scope.launch {
                            web.flowLoading.collectLatest { l ->
                                loading = l
                            }
                        }
                        web.setMatchParentLayoutParams()
                    }
                },
                update = { web ->
                    web.loadUrl(link)
                },
                modifier = Modifier.fillMaxSize(1f)
            )
            Spacer(
                modifier = Modifier
                    .height(30.dp)
                    .fillMaxWidth(1f)
                    .background(Brush.verticalGradient(colors = listOf(Color(0x00222222), Color(0xFF222222))))
                    .align(Alignment.BottomCenter)
            )
            if (loading) {
                CircularProgressIndicator(color = Color.White, modifier = Modifier.align(Alignment.Center))
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
        Button(
            onClick = onClick,
            modifier = Modifier
                .height(36.dp)
                .widthIn(160.dp),
        ) {
            AppText(
                text = buttonText,
                fontSize = 16.sp,
                color = Color.White,
                lineHeight = 16.sp,
                modifier = Modifier
                    .align(Alignment.CenterVertically),
                textAlign = TextAlign.Center
            )
        }
    }
}


