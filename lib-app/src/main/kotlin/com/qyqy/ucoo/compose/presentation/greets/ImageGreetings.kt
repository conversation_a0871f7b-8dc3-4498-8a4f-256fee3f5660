package com.qyqy.ucoo.compose.presentation.greets

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.utils.id2String
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.voice.GreetTips
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.ConfirmDialog
import kotlinx.coroutines.launch


@Composable
fun ImageGreetings(
    activity: BaseActivity,
    buttonText: String = "Hello",
    vm: GreetViewModel = viewModel()
) {
    val loading by vm.refresh.collectAsState()
    if (loading) {
        Box(modifier = Modifier.fillMaxSize(1f), contentAlignment = Alignment.Center) {
            CircularProgressIndicator()
        }
        return
    }
    val greetings = vm.greetingsFlow.collectAsState()
    val greetingInfo by remember {
        derivedStateOf { greetings.value.tabs.firstOrNull { it.contentType == GreetItem.TYPE_IMAGE } ?: Greetings.Tab() }
    }
    val list = greetingInfo.contentList
    val context = LocalContext.current
    GreetListPage(buttonText = buttonText, onClick = {
        vm.addImageItem { activity.selectPhotos() }
    }, isEmpty = list.isEmpty()) {
        LazyVerticalGrid(columns = GridCells.Fixed(2),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp),
            modifier = Modifier
                .fillMaxSize(1f)
                .padding(12.dp),
            content = {
                items(list) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth(1f)
                            .aspectRatio(165f / 220)
                            .clip(RoundedCornerShape(8.dp))
                    ) {
                        ComposeImage(model = it.content, modifier = Modifier.fillMaxSize())
                        Image(
                            painter = painterResource(id = R.drawable.ic_delete_item),
                            contentDescription = "delete",
                            modifier = Modifier
                                .size(20.dp)
                                .align(Alignment.TopEnd)
                                .clickable(onClick = {
                                    ConfirmDialog(context, content = id2String(R.string.confrim_to_delete)) {
                                        vm.deleteItem(it)
                                    }.show()
                                })
                        )
                    }
                }
                item(span = { GridItemSpan(2) }) {
                    GreetTips(tips = greetingInfo.tips)
                }
            })
    }
}
