package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorTheme
import com.qyqy.ucoo.compose.ui.AppButton

@Composable
fun AlertContent(
    centerModifier: Modifier = Modifier
        .widthIn(160.dp)
        .padding(horizontal = 20.dp)
        .background(colorTheme, RoundedCornerShape(50)),
    title: String = "",
    content: String = "",
    centerButtonText: String = "",
    buttonCancelText: String = app.getString(R.string.rc_cancel),
    buttonConfirm: String = app.getString(R.string.rc_confirm),
    onCenterClick: () -> Unit = {},
    onCancel: () -> Unit = {},
    onConfirm: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .width(270.dp)
            .background(Color(0xFF222222), Shapes.small)
            .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (title.isNotEmpty()) {
            Text(
                modifier = Modifier
                    .padding(horizontal = 16.dp),
                text = title,
                color = Color.White,
                fontSize = 16.sp
            )
        }
        if (content.isNotEmpty()) {
            Text(
                modifier = Modifier
                    .padding(top = 12.dp)
                    .padding(horizontal = 16.dp),
                text = content,
                color = colorResource(id = R.color.white_alpha_50),
                fontSize = 15.sp
            )
        }
        if (centerButtonText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(20.dp))
            AppButton(
                text = centerButtonText, modifier = centerModifier,
                onClick = onCenterClick
            )
        } else {
            Row(
                modifier = Modifier
                    .padding(top = 30.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                val sizeModifier = Modifier.size(113.dp, 36.dp)
                Button(
                    modifier = sizeModifier,
                    onClick = onCancel,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF464646)),
                ) {
                    Text(text = buttonCancelText, color = Color(0xFFA3A3A3))
                }
                Button(
                    modifier = sizeModifier,
                    onClick = onConfirm,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF945EFF))
                ) {
                    Text(text = buttonConfirm, color = Color.White)
                }
            }
        }
    }
}

@Composable
fun AlertContent(
    centerModifier: Modifier = Modifier
        .widthIn(160.dp)
        .padding(horizontal = 20.dp)
        .background(colorTheme, RoundedCornerShape(50)),
    title: AnnotatedString = buildAnnotatedString { },
    content: AnnotatedString = buildAnnotatedString { },
    centerButtonText: String = "",
    buttonCancelText: String = app.getString(R.string.rc_cancel),
    buttonConfirm: String = app.getString(R.string.rc_confirm),
    onCenterClick: () -> Unit = {},
    onCancel: () -> Unit = {},
    onConfirm: () -> Unit = {}
) {
    Column(
        modifier = Modifier
            .width(270.dp)
            .background(Color(0xFF222222), Shapes.small)
            .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (title.isNotEmpty()) {
            Text(
                modifier = Modifier
                    .padding(horizontal = 16.dp),
                text = title,
                color = Color.White,
                fontSize = 16.sp
            )
        }
        if (content.isNotEmpty()) {
            Text(
                modifier = Modifier
                    .padding(top = 12.dp)
                    .padding(horizontal = 16.dp),
                text = content,
                color = colorResource(id = R.color.white_alpha_50),
                fontSize = 15.sp
            )
        }
        if (centerButtonText.isNotEmpty()) {
            Spacer(modifier = Modifier.height(20.dp))
            AppButton(
                text = centerButtonText, modifier = centerModifier,
                onClick = onCenterClick
            )
        } else {
            Row(
                modifier = Modifier
                    .padding(top = 30.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                val sizeModifier = Modifier.size(113.dp, 36.dp)
                Button(
                    modifier = sizeModifier,
                    onClick = onCancel,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF464646)),
                ) {
                    Text(text = buttonCancelText, color = Color(0xFFA3A3A3))
                }
                Button(
                    modifier = sizeModifier,
                    onClick = onConfirm,
                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF945EFF))
                ) {
                    Text(text = buttonConfirm, color = Color.White)
                }
            }
        }
    }
}

@Preview
@Composable
fun Alert1Preview() {
    AlertContent(
        onCancel = { },
        content = "我是正文多行内容居中对齐我是正文多行内容居中对齐",
        centerButtonText = "去上传",
        buttonCancelText = "",
        buttonConfirm = ""
    ) {

    }
}

@Preview
@Composable
fun Alert2Preview() {
    AlertContent(
        onCancel = { },
        title = "提示",
        content = "我是正文多行内容居中对齐我是正文多行内容居中对齐",
        centerButtonText = "",
        buttonCancelText = "取消",
        buttonConfirm = "确认"
    ) {

    }
}