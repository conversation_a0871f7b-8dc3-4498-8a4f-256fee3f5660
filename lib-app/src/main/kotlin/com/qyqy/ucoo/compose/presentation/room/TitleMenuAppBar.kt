package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CommonButton
import com.qyqy.ucoo.compose.ui.TitleMenuAppBarScaffold


class TitleMenuAppBarWidget(
    private val title: String,
    private val subTitle: String,
) : IComposeWidget<ColumnScope> {

    @Composable
    override fun ColumnScope.Content() {
        TitleMenuAppBarScaffold(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp, vertical = 12.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            back = {
                Image(
                    painter = painterResource(id = R.drawable.ic_navigation_back),
                    contentDescription = "back",
                    modifier = Modifier
                        .size(24.dp)
                        .padding(vertical = 2.dp),
                    contentScale = ContentScale.Fit,
                )
            },
            title = {
                AppText(text = title, color = Color.White)
            },
            subTitle = {
                AppText(text = subTitle, color = Color.White)
            },
            menu = {
                Image(
                    painter = painterResource(id = R.drawable.ic_party_share),
                    contentDescription = "share",
                    modifier = Modifier
                        .size(28.dp)
                        .background(Color(0x4D000000), shape = CircleShape)
                        .padding(5.dp),
                    contentScale = ContentScale.Crop,
                )

                Image(
                    painter = painterResource(id = R.drawable.ic_more_menu),
                    contentDescription = "menu",
                    modifier = Modifier
                        .size(28.dp)
                        .background(Color(0x4D000000), shape = CircleShape)
                        .padding(5.dp),
                    contentScale = ContentScale.Crop,
                )
            },
            extension = {
                CommonButton(
                    modifier = Modifier.size(60.dp, 28.dp),
                    gradient = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))),
                    shape = CircleShape,
                ) {
                    Text(text = "关注", color = Color.White, fontSize = 12.sp, fontWeight = FontWeight.Medium)
                }
            }
        )
    }
}