package com.qyqy.ucoo.compose.vm.login

import com.qyqy.ucoo.compose.presentation.login.LoginDestination
import com.qyqy.ucoo.compose.state.MviViewModel

//
//interface LoginMvi {
//
//    sealed interface ViewEvent : MviViewModel.MviEvent {
//
//        /**
//         * 发送验证码
//         */
//        data class SendPhoneCode(val areaCode: String, val number: String) : ViewEvent
//
//        /**
//         * 验证验证码
//         */
//        data class VerifyPhoneCode(val code: String) : ViewEvent
//
//        /**
//         * 确认用户信息
//         */
//        data class ConfirmUserInfo(val info: UserInfo) : ViewEvent
//
//        /**
//         * 确认额外信息
//         */
//        data class ConfirmExtraInfo(val location: SelectItem, val life: SelectItem, val inviteCode: String?) : ViewEvent
//
//        /**
//         * 注册用户
//         */
//        data class RegisterAccount(val avatar: String?) : ViewEvent
//    }
//
//    sealed interface ViewResult : MviViewModel.MviViewResult {
//
//        object NoOp : ViewResult
//
//        data class ToastResult(val content: String) : ViewResult
//
//        data class NavigationResult(val router: String) : ViewResult
//
//        data class SendPhoneCodeResult(val areaCode: String, val number: String) : ViewResult
//
//        data class VerifyPhoneCodeResult(val code: String) : ViewResult
//
//        data class ConfirmUserInfoResult(val info: UserInfo) : ViewResult
//
//    }
//
//    @Parcelize
//    data class ViewState(
//        val config: LoginConfigs = LoginConfigs(
//            hasFacebookLogin = true,
//            hasFastLogin = true,
//            hasGoogleLogin = true,
//            hasPhoneLogin = false,
//            userFromChina = false,
//        ),
//        val phoneInfo: PhoneInfo = PhoneInfo(),
//        val userInfo: UserInfo = UserInfo(),
//        val extraInfo: ExtraInfo = ExtraInfo(),
//    ) : MviViewModel.MviViewState, Parcelable
//
//    interface ViewEffect : MviViewModel.MviSideEffect {
//
//        data class Toast(val msg: String) : ViewEffect
//
//        data class Navigation(val router: String) : ViewEffect
//
//    }
//}
//
//
//@Parcelize
//data class PhoneInfo(
//    val country: String = "中国",
//    val countryCode: String = "CN",
//    val areaCode: String = "86",
//    val number: String = "",
//    val code: String = "",
//    val isBindPhone: Boolean = false,
//    val lastSendCodeTimestamp: Long = 0,
//    val displayLeftSeconds: Int = 0,
//) : Parcelable {
//
//    val leftResendMs: Long
//        get() = RESEND_INTERVAL_MS.minus(SystemClock.elapsedRealtime().minus(lastSendCodeTimestamp))
//
//}
//
//@Parcelize
//data class UserInfo(
//    val name: String = "",
//    val isBoy: Boolean = false,
//    val birther: String = "",
//) : Parcelable
//
//@Parcelize
//data class ExtraInfo(
//    val locationList: List<SelectItem> = emptyList(),
//    val selectedLocation: SelectItem? = null,
//    val lifeList: List<SelectItem> = emptyList(),
//    val selectedLife: SelectItem? = null,
//    val inviteCode: String? = null,
//) : Parcelable
//
//@Parcelize
//data class SelectItem(
//    val id: Int,
//    val text: String,
//) : Parcelable
//
//private const val RESEND_INTERVAL_MS = 60000


interface LoginViewEffect : MviViewModel.MviSideEffect {

    data class Toast(val msg: String) : LoginViewEffect

    object ReLogin : LoginViewEffect

    data class Navigation(val destination: LoginDestination) : LoginViewEffect

    data class DialogTip(val msg: String, val button: String) : LoginViewEffect

}
