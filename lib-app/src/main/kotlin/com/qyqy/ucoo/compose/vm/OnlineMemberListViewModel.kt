package com.qyqy.ucoo.compose.vm

import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import com.qyqy.ucoo.tribe.model.TribeApi

class OnlineMemberListViewModel(val tribeId: String) : LiStateViewModel<TribeMemberItem>() {
    private val api = createApi<TribeApi>()
//    var firstPageLastItem: TribeMemberItem? = null
    private var lastItem: TribeMemberItem? = null

    var orderType: Int = 0
        set(value) {
            field = value
            refresh()
        }

    override fun onStartRequest(isRefresh: Boolean) {
        super.onStartRequest(isRefresh)
        lastItem = if (isRefresh) null else dataState.value.lastOrNull()
    }

    override suspend fun fetch(): List<TribeMemberItem> {
        val reqId = lastItem?.member_id ?: 0
//        val memberId = dataState.value.lastOrNull()?.member_id ?: 0
        return runApiCatching {
            api.getMemberListV2(
                reqId,
                orderType
            )
        }.getOrNull()?.members.orEmpty()
    }
}