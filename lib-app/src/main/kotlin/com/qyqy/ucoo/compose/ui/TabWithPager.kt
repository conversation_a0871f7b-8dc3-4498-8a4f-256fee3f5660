package com.qyqy.ucoo.compose.ui

import android.annotation.SuppressLint
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.PagerDefaults
import androidx.compose.foundation.pager.PagerScope
import androidx.compose.foundation.pager.PagerState
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.qyqy.ucoo.compose.orDefault

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun TabWithPager(
    tabs: List<ITab>,
    pagerState: PagerState = rememberPagerState {
        tabs.size
    },
    @SuppressLint("ModifierParameter") tabModifier: Modifier = Modifier,
    key: ((ITab) -> Any) = { it.name },
    pagerModifier: Modifier = Modifier,
    beyondBoundsPageCount: Int = PagerDefaults.BeyondViewportPageCount,
    content: @Composable PagerScope.(page: Int) -> Unit,
) {
    AppTabRow(
        tabs = tabs,
        pagerState = pagerState,
        modifier = tabModifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth()
            .then(tabModifier)
    )

    HorizontalPager(
        state = pagerState,
        verticalAlignment = Alignment.Top,
        modifier = pagerModifier,
        key = {
            tabs.getOrNull(it)?.let(key).orDefault(it)
        },
        beyondViewportPageCount = beyondBoundsPageCount,
        pageContent = content
    )
}