package com.qyqy.ucoo.compose.presentation.member

import android.os.Bundle
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.show
import com.qyqy.ucoo.compose.MemberSubscriptionStyleLabel
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.obtainIntent
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.rememberRefWithPrevious
import com.qyqy.ucoo.compose.rememberSaveableRefWithPrevious
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CommonButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.isPlayChannel
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.user.ActivateOption
import com.qyqy.ucoo.user.VipInfo
import com.qyqy.ucoo.user.WalletContract
import com.qyqy.ucoo.user.WalletViewModel
import com.qyqy.ucoo.utils.GoogleBillingHelper
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import com.qyqy.ucoo.utils.web.addTitleByUrl
import com.qyqy.ucoo.utils.web.cacheEnableByUrl
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import java.text.SimpleDateFormat
import java.util.Locale

sealed interface MemberCenter {

    object Color {

        val Text1 = Color(0xFFB57514)

        val Text2 = Color(0xFFAB997E)

        val Text3 = Color(0xFFFFE9CE)

        val Text4 = Color(0xFF593A0C)

        val Text5 = Color(0xA6FFFFFF)

        val Text6 = Color(0xFFF3DAB3)

        val Text7 = Color(0x4DFFFFFF)


        val Background1 = Color(0x26FFFFFF)

        val Background2 = Color(0xFF333333)

        val Background3 = Color(0x1AFFFFFF)

        val Background4 = Color(0xFFF2DBB5)

        val Background5 = Color(0xFFEDB664)

        val Background6 = Color(0xFF6A6A6A)

        val Background7 = Color(0x14FFFFFF)
    }
}

object MemberCenterScreenNavigator : ScreenNavigator {
    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        MemberCenterScreen()
    }

    fun navigate(
        activity: FragmentActivity,
        from: String = "",
        exposureName: String = "",
        clickName: String = ""
    ) {
        //2.35.0 都改成全屏
        val newMemberPage = GoogleBillingHelper.vipInfoFlow.value.getOrNull()?.payAB == 23
        activity.startActivity(obtainIntent(activity) {
            putExtra("view_event_name", exposureName)
            putExtra("action_event_name", clickName)
        })
//        if (newMemberPage) {
//            activity.startActivity(obtainIntent(activity) {
//                putExtra("view_event_name", exposureName)
//                putExtra("action_event_name", clickName)
//            })
//        } else {
//            VipCenterFragment.newInstance(from, exposureName, clickName).show(activity.supportFragmentManager, "VipCenterFragment")
//        }
    }

    fun navigate(
        fragment: Fragment,
        from: String = "",
        exposureName: String = "",
        clickName: String = ""
    ) {
        //2.35.0 都改成全屏
        val newMemberPage = GoogleBillingHelper.vipInfoFlow.value.getOrNull()?.payAB == 23
        fragment.context?.also {
            fragment.startActivity(obtainIntent(it) {
                putExtra("view_event_name", exposureName)
                putExtra("action_event_name", clickName)
            })
        }
//        if (newMemberPage) {
//            fragment.context?.also {
//                fragment.startActivity(obtainIntent(it) {
//                    putExtra("view_event_name", exposureName)
//                    putExtra("action_event_name", clickName)
//                })
//            }
//        } else {
//            VipCenterFragment.newInstance(from, exposureName, clickName).show(fragment.childFragmentManager, "VipCenterFragment")
//        }
    }
}

@Composable
fun MemberCenterScreen() {
    val viewModel = viewModel<WalletViewModel>()

    val user by sUserFlow.collectAsStateWithLifecycle()

    val vipInfo by GoogleBillingHelper.vipInfoFlow.filter {
        it.isSuccess
    }.map {
        it.get()
    }.onEach {
        it.bonusList?.forEachIndexed { index, bonusItem ->
            bonusItem.bonusIndex = index.plus(1)
        }
    }.collectAsStateWithLifecycle(null)

    LaunchedEffect(key1 = Unit) {
        viewModel.sendEvent(WalletContract.Event.FetchVipConfig)

        viewModel.effect.onEach {
            it.show()
        }.launchIn(this)
    }

    val activity = LocalContext.current.asActivity!!

    MemberCenterPage(user, vipInfo, { bonusIndex ->
        viewModel.sendEvent(WalletContract.Event.DrawGold(bonusIndex))
    }) { fkChannel, productId, fkLink ->
        viewModel.sendEvent(WalletContract.Event.Buy(activity, fkChannel, productId, fkLink, 2))
    }
}

@Composable
fun PayWay(icon: String, title: String, selected: Boolean, onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .click(onClick = onClick)
            .padding(horizontal = 16.dp, vertical = 10.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ComposeImage(model = icon, modifier = Modifier.size(24.dp))
        Spacer(modifier = Modifier.width(8.dp))
        Text(text = title, color = Color.White, modifier = Modifier.weight(1f))
        Image(
            painter = painterResource(id = if (selected) R.drawable.ic_m_s else R.drawable.ic_m_us),
            contentDescription = "sel",
            modifier = Modifier.size(24.dp)
        )
    }
}

@Composable
fun MemberCenterPage(
    user: User,
    vipInfo: VipInfo?,
    onDrawGoldCoin: (Int) -> Unit = {},
    onBuy: (Int, String, String) -> Unit = { _, _, _ -> },
) {
    val context = LocalContext.current

    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

    Column(modifier = Modifier.fillMaxSize()) {

        AppTitleBar(
            title = stringResource(id = R.string.开通UCOO会员),
            modifier = Modifier.statusBarsPadding(),
            onBack = {
                onBackPressedDispatcher?.onBackPressed()
            })

        if (vipInfo == null) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(modifier = Modifier.requiredSize(48.dp))
            }
            return
        }

        // 当前选中的付款方式
        var selectedFkOption by rememberSaveableRefWithPrevious<VipInfo.Good?>(vipInfo.goods) { prev ->
            if (isPlayChannel) {
                mutableStateOf(vipInfo.goods.find {
                    it.fkChannel == 1
                })
            } else {
                if (prev == null) {
                    mutableStateOf(vipInfo.goods.firstOrNull { it.fkChannel == 1 }
                        ?: vipInfo.goods.firstOrNull())
                } else {
                    mutableStateOf(vipInfo.goods.find {
                        it.fkChannel == prev.fkChannel
                    } ?: vipInfo.goods.firstOrNull())
                }
            }
        }

        // 当前选中的付款方式对应的商品
        val goodList by remember {
            derivedStateOf {
                selectedFkOption?.activateOptions.orEmpty()
            }
        }

        // 当前选中的商品
        var selectedMemberOption by rememberRefWithPrevious<ActivateOption?>(goodList) { prev ->
            if (prev == null) {
                mutableStateOf(goodList.firstOrNull())
            } else {
                mutableStateOf(goodList.find {
                    it.equity == prev.equity
                } ?: goodList.firstOrNull())
            }
        }

        val scrollState = rememberScrollState()
        Column(
            modifier = Modifier
                .weight(1f)
                .overScrollVertical()
                .verticalScroll(
                    scrollState,
                    flingBehavior = rememberOverscrollFlingBehavior { scrollState }),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            if (vipInfo.isVip) {
                val formatDate = remember(vipInfo.expireTimestamp) {
                    SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(
                        vipInfo.expireTimestamp.times(1000L)
                    )
                }
                ActivatedMemberCard(
                    user = user,
                    modifier = Modifier.padding(horizontal = 16.dp),
                    activeDate = stringResource(R.string.format_date_to, formatDate),
                )
            } else {
                NotActiveMemberCard(
                    memberCount = vipInfo.membership,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )
            }

            val bonusList = vipInfo.bonusList
            if (!bonusList.isNullOrEmpty()) {
                GoldCoinRewardCard(
                    list = bonusList, onClick = { bonusIndex ->
                        onDrawGoldCoin(bonusIndex)
                    }, modifier = Modifier
                        .height(90.dp)
                        .padding(horizontal = 16.dp)
                )
            }

            MemberPriceOptionList(selectedMemberOption, goodList, {
                selectedMemberOption = it
            })

            // 会员特权
            MemberBenefitsLabel(
                labelUrl = vipInfo.benefitsIcon,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            MemberBenefitsCard(list = vipInfo.memberRightsInfos)

            if (vipInfo.goods.size>1) {
                MemberSubscriptionStyleLabel(modifier = Modifier.padding(horizontal = 16.dp))

                Column {
                    val payWays = vipInfo.goods
                    payWays.forEach {
                        PayWay(icon = it.icon, title = it.name, selected = it == selectedFkOption) {
                            selectedFkOption = it
                        }
                    }
                }
            }

//            MemberSubscriptionStyleCard(
//                selectedFkOption = selectedFkOption,
//                list = vipInfo.goods,
//                onSelectChange = {
//                    selectedFkOption = it
//                },
//                modifier = Modifier.padding(horizontal = 16.dp),
//            )

            Spacer(modifier = Modifier.height(36.dp))
        }

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(MemberCenter.Color.Background2)
                .padding(16.dp)
                .navigationBarsPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CommonButton(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                gradient = Brush.horizontalGradient(
                    listOf(
                        MemberCenter.Color.Background4,
                        MemberCenter.Color.Background5
                    )
                ),
                shape = CircleShape,
                enabled = selectedMemberOption != null,
                onClick = {
                    selectedMemberOption?.also {
                        selectedFkOption?.fkChannel?.also { fkChannel ->
                            onBuy(fkChannel, it.productId, it.fkLink)
                        }
                    }
                },
            ) {
                Text(
                    text = buildString {
                        append(stringResource(id = if (user.isVip) R.string.continue_buy else R.string.立即开通会员))
                        if (selectedMemberOption != null) {
                            append("(${selectedMemberOption?.transformPrice?.replace(" ", "")})")
                        }
                    },
                    color = MemberCenter.Color.Text4,
                    fontSize = 16.sp
                )
            }

            val annotatedText = buildAnnotatedString {

                append(stringResource(id = R.string.become_means_agree))

                pushStringAnnotation(tag = "UCOO会员协议", annotation = "")
                append(stringResource(id = R.string.ucoo_link))
                pop()

                appendLine()
                append(stringResource(id = R.string.和))

                pushStringAnnotation(tag = "连续订阅会员服务协议", annotation = "")
                append(stringResource(id = R.string.text_buy))
                pop()
            }

            ClickableText(
                text = annotatedText,
                modifier = Modifier.padding(vertical = 6.dp),
                style = TextStyle.Default.copy(
                    color = colorResource(id = R.color.white_alpha_50),
                    fontSize = 12.sp,
                    textAlign = TextAlign.Center
                ),
            ) { offset ->
                // We check if there is an *URL* annotation attached to the text
                // at the clicked position
                annotatedText.getStringAnnotations(
                    tag = "UCOO会员协议",
                    start = offset,
                    end = offset
                ).firstOrNull()?.also { _ ->
                    context.startActivity(
                        JsBridgeWebActivity.newIntentFromLogin(
                            context,
                            Const.Url.VIP_AGREEMENT_URL.addTitleByUrl("UCOO会员协议")
                                .cacheEnableByUrl(false)
                        )
                    )
                }

                annotatedText.getStringAnnotations(
                    tag = "连续订阅会员服务协议",
                    start = offset,
                    end = offset
                ).firstOrNull()?.also { _ ->
                    context.startActivity(
                        JsBridgeWebActivity.newIntentFromLogin(
                            context,
                            Const.Url.VIP_AGREEMENT_URL.addTitleByUrl("连续订阅会员服务协议")
                                .cacheEnableByUrl(false)
                        )
                    )
                }
            }
        }
    }
}

@Preview
@Composable
fun MemberCenterPagePreview() {
    MemberCenterPage(userForPreview, null)
}
