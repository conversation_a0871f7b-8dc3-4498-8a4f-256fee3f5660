@file:OptIn(ExperimentalFoundationApi::class)

package com.qyqy.ucoo.compose.presentation.moment

import android.content.Intent
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.ParagraphStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.data.MomentPreviewItem
import com.qyqy.ucoo.compose.isSelfOnCompose
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppPhotoPreviewerInDialog
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.compose.ui.lazy.rememberFullyVisibleIndices
import com.qyqy.ucoo.compose.ui.photo.PhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoGestureState
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoPreviewState
import com.qyqy.ucoo.compose.updateIfExist
import com.qyqy.ucoo.compose.vm.moment.MomentMvi
import com.qyqy.ucoo.compose.vm.moment.MomentViewModel
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.moment.Moment
import com.qyqy.ucoo.moment.Topic
import com.qyqy.ucoo.moment.openTopic
import com.qyqy.ucoo.moment.publish.PublishMomentActivity
import com.qyqy.ucoo.setting.ReportActivity
import com.qyqy.ucoo.toast
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import java.text.SimpleDateFormat
import java.util.Locale
import kotlin.math.abs

@Composable
fun MomentListPageRouter(user: User) {
    val viewModel = MomentViewModel.rememberViewModel(user.id)
    UCOOScreen(title = user.nickname) {
        MomentListPage(user, viewModel)
    }
}

@Composable
private fun MomentListPage(user: User, viewModel: MomentViewModel) {

    var previewList by rememberSaveable {
        mutableStateOf(emptyList<MomentPreviewItem>())
    }

    val gestureState = with(LocalDensity.current) {
        rememberPhotoGestureState(
            extraHorizontalOffset = 80.dp.toPx(),
            extraVerticalOffset = 100.dp.toPx(),
        )
    }

    val previewState = rememberPhotoPreviewState(
        gestureState = gestureState,
        onGestureClose = { _, offsetY, velocityY ->
            abs(velocityY) > 1000 || abs(offsetY) > 60.dp.toPx()
        },
    ) {
        previewList
    }

    val context = LocalContext.current

    LaunchedEffect(Unit) {
        viewModel.effects.onEach { effect ->
            when (effect) {
                is MomentMvi.ViewEffect.Toast -> {
                    toast(effect.msg)
                }

                is MomentMvi.ViewEffect.PreviewImage -> {
                    previewList = effect.list
                    previewState.startPreview(effect.start)
                }
            }
        }.launchIn(this)
    }

    val lazyListState = rememberLazyListState()
    val uiState = viewModel.states.collectAsStateWithLifecycle().value
    val loaded by viewModel.loaded
    MomentListPage(user, uiState.list, previewState, lazyListState,
        loaded = loaded,
        onTopicClicked = {
            with(context) {
                openTopic(it)
            }
        },
        onStartChat = {
            ChatActivity.startChatWithUser(context, it)
        },
        onGoPublishPage = {
            context.startActivity(Intent(context, PublishMomentActivity::class.java))
        },
        onToggleLike = {
            viewModel.processEvent(MomentMvi.ViewEvent.ToggleLikeMoment(it))
        }, onDeleteMoment = {
            viewModel.processEvent(MomentMvi.ViewEvent.DeleteMoment(it))
        }, onReportMoment = {
            context.startActivity(ReportActivity.newSccusateIntent(context, ReportActivity.TYPE_MOMENT, it.id.toString()))
        }
    ) { moment, position ->
        viewModel.processEvent(MomentMvi.ViewEvent.PreviewMomentImage(moment, position))
    }
    viewModel.attach(lazyListState)
}


@Composable
fun MomentListPage(
    user: User,
    list: List<Moment>,
    previewState: PhotoPreviewState = rememberPhotoPreviewState(),
    lazyListState: LazyListState = rememberLazyListState(),
    loaded: Boolean = false,
    onStartChat: (User) -> Unit = {},
    onGoPublishPage: () -> Unit = {},
    onToggleLike: (Moment) -> Unit = {},
    onDeleteMoment: (Moment) -> Unit = {},
    onReportMoment: (Moment) -> Unit = {},
    onTopicClicked: (Topic) -> Unit = {},
    onPreviewImageMoment: (Moment, Int) -> Unit = { _, _ -> },
) {

    val fullyVisibleIndices by rememberFullyVisibleIndices(lazyListState)

    if (previewState.previewIsReady) {
        LaunchedEffect(Unit) {
            snapshotFlow {
                previewState.currentPreview
            }.filterNotNull().map {
                (it as MomentPreviewItem).indexOfMoment.times(2)
            }.filter {
                it !in fullyVisibleIndices
            }.collectLatest {
                lazyListState.scrollToItem(it)
            }
        }
    }

    LazyColumn(
        modifier = Modifier.fillMaxSize(),
        state = lazyListState
    ) {
        if (loaded) {
            if (list.isEmpty()) {
                item(contentType = 2) {
                    Column(
                        modifier = Modifier.fillParentMaxSize(),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.spacedBy(10.dp, Alignment.CenterVertically)
                    ) {
                        Image(painter = painterResource(id = R.drawable.ic_empty_for_moment), contentDescription = null)
                        Text(
                            text = stringResource(
                                id = if (user != null) {
                                    when {
                                        user.isSelfOnCompose -> R.string.empty_moment
                                        user.isBoy -> R.string.he_has_not_publish_m
                                        else -> R.string.her_has_not_publish_m
                                    }
                                } else {
                                    R.string.empty_moment
                                }
                            ), fontSize = 12.sp, color = colorResource(id = R.color.white_alpha_30)
                        )
                        if (user != null && user.isSelfOnCompose) {
                            Button(
                                onClick = { onGoPublishPage() },
                                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF383838))
                            ) {
                                Text(text = stringResource(id = R.string.publish_moment), color = Color.White)
                            }
                        }
                        Spacer(modifier = Modifier.height(30.dp))
                    }
                }
            } else {
                list.forEachIndexed { index, moment ->
                    item(key = moment.id, contentType = 0) {
                        MomentItemOnUser(
                            user = user,
                            moment = moment,
                            previewState = previewState,
                            onToggleLike = onToggleLike,
                            onDeleteMoment = onDeleteMoment,
                            onReportMoment = onReportMoment,
                            onTopicClicked = onTopicClicked,
                            onPreviewImageMoment = onPreviewImageMoment,
                        )
                    }
                    if (index < list.lastIndex) {
                        item(contentType = 1) {
                            HorizontalDivider(thickness = 4.dp, color = Color(0xFF131414))
                        }
                    }
                }
                item {
                    Spacer(modifier = Modifier.height(25.dp))
                }
            }
        }
    }

    AppPhotoPreviewerInDialog(previewState)
}
//
//@Composable
//fun MomentItemOnFeed(
//    moment: Moment,
//    previewState: PhotoPreviewState,
//    modifier: Modifier = Modifier,
//    onStartChat: (User) -> Unit = {},
//    onToggleLike: (Moment) -> Unit = {},
//    onDeleteMoment: (Moment) -> Unit = {},
//    onReportMoment: (Moment) -> Unit = {},
//    onPreviewImageMoment: (Moment, Int) -> Unit = { _, _ -> },
//) {
//    Column(
//        modifier = modifier
//            .padding(horizontal = 16.dp, vertical = 20.dp)
//            .fillMaxWidth()
//    ) {
//        MomentTitleItem(
//            moment,
//            onStartChat = onStartChat,
//            onDeleteMoment = onDeleteMoment,
//            onReportMoment = onReportMoment,
//        )
//        Spacer(modifier = Modifier.height(12.dp))
//        MomentContentItem(
//            profileType = -1,
//            moment = moment,
//            modifier = Modifier
//                .padding(start = 56.dp)
//                .fillMaxWidth(),
//            previewState = previewState,
//            showMenu = false,
//            onToggleLike = onToggleLike,
//            onPreviewImageMoment = onPreviewImageMoment,
//        )
//    }
//}

@Composable
fun MomentItemOnUser(
    user: User,
    moment: Moment,
    previewState: PhotoPreviewState,
    modifier: Modifier = Modifier,
    onToggleLike: (Moment) -> Unit = {},
    onDeleteMoment: (Moment) -> Unit = {},
    onReportMoment: (Moment) -> Unit = {},
    onTopicClicked: (Topic) -> Unit = {},
    onPreviewImageMoment: (Moment, Int) -> Unit = { _, _ -> },
) {
    Row(
        modifier = modifier
            .padding(horizontal = 16.dp, vertical = 20.dp)
            .fillMaxWidth()
    ) {
        Spacer(modifier = Modifier.width(8.dp))
        Text(text = buildAnnotatedString {
            val formatTime = moment.formatTime
            append("${formatTime[0]}/ ")
            withStyle(style = SpanStyle(fontSize = 18.sp, fontWeight = FontWeight.Bold)) {
                append(formatTime[1])
            }
            val color = colorResource(id = R.color.white_alpha_50)
            withStyle(style = ParagraphStyle()) {
                withStyle(style = SpanStyle(color = color)) {
                    append(formatTime[2])
                }
            }
        }, color = Color.White, fontSize = 12.sp)
        MomentContentItem(
            user = user,
            moment = moment,
            modifier = Modifier
                .padding(start = 24.dp)
                .fillMaxWidth(),
            previewState = previewState,
            showMenu = true,
            onToggleLike = onToggleLike,
            onDeleteMoment = onDeleteMoment,
            onReportMoment = onReportMoment,
            onTopicClicked = onTopicClicked,
            onPreviewImageMoment = onPreviewImageMoment,
        )
    }
}


@Preview(showBackground = true, backgroundColor = 0xFF1C1D1D)
@Composable
fun PreviewMomentListPage() {
    val list = remember {
        mutableStateListOf(
            Moment(
                0, false, 0, false, listOf(
                    MediaInfo(
                        "https://media.ucoofun.com/mobileclient/ios/2023-06-10/image_3157171378.png",
                        828, 620,
                    ),
                    MediaInfo(
                        "https://media.ucoofun.com/mobileclient/ios/2023-06-10/image_3157171378.png",
                        828, 620,
                    ),
                ), null, 1, "美国·洛杉矶", text = "用心感受生活，用笔记录美好，留下值得回味的瞬间❤️", user = userForPreview
            ),
        ).onEach {
            it.format(SimpleDateFormat("dd-MMM-HH:mm", Locale.getDefault()), System.currentTimeMillis())
        }
    }
    MomentListPage(userForPreview, list, onToggleLike = {
        list.updateIfExist(it, true) { _ ->
            val like = it.iHaveLiked
            it.copy(i_have_liked = !like)
        }
    })
}