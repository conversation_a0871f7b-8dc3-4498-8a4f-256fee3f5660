package com.qyqy.ucoo.compose.presentation.login

import android.content.Context
import android.content.Intent
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.login.LoginChannel
import com.qyqy.ucoo.login.LoginChannelType
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.setting.DebugActivity
import com.qyqy.ucoo.setting.LoginConfigs
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import com.qyqy.ucoo.utils.web.addTitleByUrl
import com.qyqy.ucoo.utils.web.cacheEnableByUrl

@Composable
fun StartLoginScreen(configs: LoginConfigs, onFeedback: () -> Unit = { }, onStartLogin: (@LoginChannelType Int) -> Unit = {}) {
    val context = LocalContext.current
    var backEnable by remember {
        mutableStateOf(true)
    }
    BackHandler(enabled = backEnable) {
        val homeIntent = Intent(Intent.ACTION_MAIN)
        homeIntent.addCategory(Intent.CATEGORY_HOME)
        homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
        try {
            context.startActivity(homeIntent)
        } catch (e: Exception) {
            backEnable = false
        }
    }
    val isZn by remember {
        derivedStateOf {
            "zh" == context.resources.currentLocale().language
        }
    }
    val bgResId = if (isZn) R.drawable.background_login_ucoo else R.drawable.bg_ucoo_page
    Column(
        modifier = Modifier.paint(
            painter = painterResource(id = bgResId), contentScale = ContentScale.Crop
        ), horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .fillMaxHeight(0.574f)
                .clickable(enabled = !isRelease) {
                    context.startActivity(Intent(context, DebugActivity::class.java))
                }) {
            Column(
                verticalArrangement = Arrangement.Center,
                horizontalAlignment = Alignment.CenterHorizontally,
                modifier = Modifier
                    .fillMaxWidth(1f)
                    .align(Alignment.Center)
                    .padding(horizontal = 20.dp)
            ) {
                AnimatedVisibility(visible = isZn.not() && configs.slogan.isNotEmpty()) {
                    Text(
                        text = configs.slogan,
                        fontSize = 16.sp,
                        fontFamily = FontFamily.Serif,
                        color = Color.White,
                        modifier = Modifier.padding(top = 95.dp),
                    )
                }
            }
        }
        val onlyOneLoginWay = configs.onlyOneLoginWay
        if (onlyOneLoginWay) {
            if (configs.hasFastLogin) {
                FastLoginWay(configs, onStartLogin)
            } else {
                AnimatedVisibility(
                    visible = true, modifier = Modifier.padding(
                        horizontal = 56.dp
                    )
                ) {
                    Button(
                        onClick = {
                            val channelType = when {
                                configs.hasFacebookLogin -> LoginChannel.FACEBOOK2
                                configs.hasGoogleLogin -> LoginChannel.GOOGLE
                                else -> LoginChannel.PHONE
                            }
                            onStartLogin(channelType)
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(56.dp),
                        shape = CircleShape,
                        colors = ButtonDefaults.buttonColors(
                            containerColor = Color(0xFFF4EFFF),
                            contentColor = Color(0xFF1D2129),
                        ),
                    ) {
                        val res = when {
                            configs.hasFacebookLogin -> R.drawable.icon_facebook to R.string.login_way_facebook
                            configs.hasGoogleLogin -> R.drawable.icon_google to R.string.login_way_google
                            else -> R.drawable.icon_phone to R.string.login_way_phone
                        }
                        Image(
                            painter = painterResource(id = res.first), contentDescription = "icon", modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = stringResource(id = res.second), fontSize = 16.sp, fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        } else {
            FastLoginWay(configs, onStartLogin)
            Spacer(modifier = Modifier.fillMaxHeight(0.3f))
            AnimatedVisibility(
                visible = configs.hasPhoneLogin or configs.hasFacebookLogin or configs.hasGoogleLogin, modifier = Modifier.padding(horizontal = 56.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.spacedBy(8.dp), verticalAlignment = Alignment.CenterVertically
                ) {
                    HorizontalDivider(
                        modifier = Modifier.weight(1f), color = colorResource(id = R.color.white_alpha_50), thickness = 0.5.dp
                    )
                    Text(
                        text = stringResource(id = R.string.other_login_methods), fontSize = 12.sp, color = colorResource(id = R.color.white_alpha_50)
                    )
                    HorizontalDivider(
                        modifier = Modifier.weight(1f), color = colorResource(id = R.color.white_alpha_50), thickness = 0.5.dp
                    )
                }
            }

            Row(
                modifier = Modifier.padding(top = 15.dp), horizontalArrangement = Arrangement.spacedBy(40.dp)
            ) {
                AnimatedVisibility(
                    visible = configs.hasPhoneLogin,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_phone_login), contentDescription = null, modifier = Modifier
                            .size(40.dp)
                            .clickable {
                                onStartLogin(LoginChannel.PHONE)
                            })
                }

                AnimatedVisibility(
                    visible = configs.hasFacebookLogin,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_facebook_login), contentDescription = null, modifier = Modifier
                            .size(40.dp)
                            .clickable {
                                onStartLogin(LoginChannel.FACEBOOK2)
                            })
                }

                AnimatedVisibility(
                    visible = configs.hasGoogleLogin,
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_google_login), contentDescription = null, modifier = Modifier
                            .size(40.dp)
                            .clickable {
                                onStartLogin(LoginChannel.GOOGLE)
                            })
                }
            }
        }

        val annotatedText = buildAnnotatedString {
            append(stringResource(id = R.string.登录即同意))

            pushStringAnnotation(tag = "用户服务协议", annotation = "")
            withStyle(style = SpanStyle(color = Color.White)) {
                append(stringResource(id = R.string._用户服务协议_))
            }
            pop()

            append(stringResource(id = R.string.和))

            pushStringAnnotation(tag = "隐私协议", annotation = "")
            withStyle(style = SpanStyle(color = Color.White)) {
                append(stringResource(id = R.string._隐私协议_))
            }
            pop()
        }

        Spacer(modifier = Modifier.weight(1f))

        val questionFeedback = configs.questionFeedback
        if (!questionFeedback.isNullOrEmpty()) {
            Text(text = "遇到问题 >", modifier = Modifier.clickable {
                onFeedback()
            }, fontSize = 12.sp, color = Color.White)
        }

        Spacer(modifier = Modifier.weight(1f))

        ClickableText(
            text = annotatedText,
            modifier = Modifier
                .padding(bottom = 16.dp, start = 20.dp, end = 20.dp)
                .navigationBarsPadding(),
            style = TextStyle.Default.copy(
                color = colorResource(id = R.color.white_alpha_50), fontSize = 12.sp
            )
        ) { offset ->
            // We check if there is an *URL* annotation attached to the text
            // at the clicked position
            annotatedText.getStringAnnotations(
                tag = "用户服务协议", start = offset, end = offset
            ).firstOrNull()?.also { _ ->
                context.startActivity(
                    JsBridgeWebActivity.newIntentFromLogin(
                        context, Const.Url.AGREEMENT_URL.addTitleByUrl(context.getString(R.string.用户服务协议)).cacheEnableByUrl(false)
                    )
                )
            }

            annotatedText.getStringAnnotations(
                tag = "隐私协议", start = offset, end = offset
            ).firstOrNull()?.also { _ ->
                context.startActivity(
                    JsBridgeWebActivity.newIntentFromLogin(
                        context, Const.Url.PRIVACY_URL.addTitleByUrl(context.getString(R.string.隐私协议)).cacheEnableByUrl(false)
                    )
                )
            }
        }
    }
}

@Composable
private fun ColumnScope.FastLoginWay(
    configs: LoginConfigs,
    onStartLogin: (@LoginChannelType Int) -> Unit,
) {
    AnimatedVisibility(
        visible = configs.hasFastLogin, modifier = Modifier.padding(horizontal = 48.dp)
    ) {
        Button(
            onClick = {
                onStartLogin(LoginChannel.ONETAP)
            },
            modifier = Modifier
                .fillMaxWidth()
                .height(56.dp),
            shape = CircleShape,
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFF4EFFF),
                contentColor = Color(0xFF1D2129),
            ),
        ) {
            Text(
                text = stringResource(id = R.string.quick_login), fontSize = 16.sp, fontWeight = FontWeight.Medium
            )
        }
    }
}

@Preview(widthDp = 375, heightDp = 812, locale = "en-rUS")
@Composable
private fun StartLoginScreenPreview() {
    MaterialTheme {
        StartLoginScreen(
            LoginConfigs(
                hasFacebookLogin = true,
                hasFastLogin = false,
                hasGoogleLogin = false,
                hasPhoneLogin = false,
                userFromChina = false,
                slogan = "全球华人娱乐交友平台",
            )
        )
    }
}

@Composable
fun FeedbackPage(content: String, onBack: (Context) -> Unit = {}) {
    UCOOScreen(title = "问题反馈", onBack = onBack) {
        Text(text = content, modifier = Modifier.padding(top = 25.dp, start = 16.dp, end = 16.dp), fontSize = 14.sp, color = Color.White)
    }
}


@Preview(widthDp = 375, heightDp = 812, locale = "en-rUS")
@Composable
private fun PreviewFeedbackPage() {
    MaterialTheme {
        FeedbackPage(content = "若遇到账号无法注册登录或其他任何产品使用问题，可发邮件至下方客服邮箱解决：<EMAIL> 说明：请在邮件中描述清楚您的问题，并留下您的联系方式，客服将在X个工作日内进行处理。")
    }
}