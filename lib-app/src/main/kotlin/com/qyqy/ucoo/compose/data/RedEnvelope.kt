package com.qyqy.ucoo.compose.data

import com.qyqy.ucoo.account.User

/**
 *  # 状态
 *  - 未开
 *       - 可开
 *       - 倒计时
 *  - 领取结果
 *       - 抢成功
 *       - 抢失败
 *  - 已领取
 *  - 红包已过期
 */
sealed interface RedEnvelope {

    object Loading : RedEnvelope

    /**
     * 红包还没打开
     */
    data class Info(
        val user: User,
        val desc: String,
        val ps: String,
        val balance: Int,
        val activeTimestamp: Long
    ) : RedEnvelope


    /**
     * 抢红包成功
     */
    data class Success(
        val goldCount: Int,
        val senderName: String,
    ) : RedEnvelope

    /**
     * 红包已领取
     */
    data class AlreadyCollected(val coinCount: Int) : RedEnvelope

    /**
     * 红包已过期
     */
    object Expired : RedEnvelope

    /**
     * 没抢到红包
     */
    object Failed : RedEnvelope

    /**
     * 红包加载失败
     */
    object None : RedEnvelope
}

val RedEnvelope.isExpired: Boolean
    get() = this is RedEnvelope.Expired

val RedEnvelope.isCollected: Boolean
    get() = this is RedEnvelope.AlreadyCollected || this is RedEnvelope.Failed || this is RedEnvelope.Success

val RedEnvelope.isOver: Boolean
    get() = isExpired || isCollected || (this is RedEnvelope.Info && this.balance <= 0)

/**
 *  是否为领取结果状态
 */
val RedEnvelope.isGrabResult: Boolean
    get() = this is RedEnvelope.Success || this == RedEnvelope.Failed

val RedEnvelope.isUncovered: Boolean
    get() = this is RedEnvelope.Info