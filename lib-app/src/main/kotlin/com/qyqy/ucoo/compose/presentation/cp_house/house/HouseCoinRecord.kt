package com.qyqy.ucoo.compose.presentation.cp_house.house

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.annotation.StringRes
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.cp_house.bean.CoinRecord
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.compose.theme.colorDarkScreenBackground
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.utils.LogUtils
import kotlinx.coroutines.launch
import retrofit2.http.GET
import retrofit2.http.Query
import java.text.SimpleDateFormat
import java.util.Locale

@Composable
fun HouseCoinRecordScreen() {
    val text = LocalHouseText.current
    val backOwner = LocalOnBackPressedDispatcherOwner.current
    Scaffold(
        modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding(),
        topBar = {
            AppTitleBar(title = stringResource(id = text.coinRecord), onBack = {
                backOwner?.onBackPressedDispatcher?.onBackPressed()
            })
        },
        containerColor = colorDarkScreenBackground
    ) { pd ->
        val pagerState = rememberPagerState {
            2
        }
        val titles = remember {
            listOf(
                HCTab(text.hcIncome, change_type_income),
                HCTab(text.hcOutcome, change_type_outcome)
            )
        }
        val scope = rememberCoroutineScope()
        val selectedTabIndex = pagerState.currentPage
        Column(
            modifier = Modifier.padding(pd),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            TabRow(
                selectedTabIndex = selectedTabIndex,
                containerColor = Color.Transparent,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .fillMaxWidth(0.6f),
                divider = {},
                indicator = { tabPositions ->
                    Box(
                        Modifier
                            .tabIndicatorOffset(tabPositions[selectedTabIndex])
                            .background(Color.Transparent),
                        contentAlignment = Alignment.Center
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp, 3.dp)
                                .background(
                                    Color.White,
                                    RoundedCornerShape(4.dp)
                                )
                        )
                    }
                }
            ) {
                titles.forEachIndexed { index, tab ->
                    val selected = index == pagerState.currentPage
                    Tab(selected = selected, onClick = {
                        scope.launch {
                            pagerState.scrollToPage(index)
                        }
                    }) {
                        Text(
                            modifier = Modifier.heightIn(min = 30.dp),
                            text = stringResource(id = tab.titleRes),
                            fontSize = 16.sp,
                            color = if (selected) Color.White else colorWhite50Alpha
                        )
                    }
                }
            }

            HorizontalPager(state = pagerState) { index ->
                val changeType = titles[index].changeType
                RecordPage(changeType = changeType)
            }
        }
    }
}

@Composable
private fun RecordPage(
    changeType: Int,
    vm: HouseCoinRecordViewModel = viewModel(key = "vm-$changeType", factory = viewModelFactory {
        initializer {
            HouseCoinRecordViewModel(changeType)
        }
    })
) {
    LaunchedEffect(key1 = changeType) {
        LogUtils.d("record", "changeType:$changeType,vm$vm")
    }
    PullRefreshBox(modifier = Modifier
        .fillMaxSize()
        .padding(horizontal = 16.dp),
        isRefreshing = vm.isRefreshing,
        onRefresh = { vm.refresh() }) {
        val list by vm.dataState
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            if (vm.isLoaded) {
                if (list.isEmpty()) {
                    item {
                        EmptyView(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(0.75f),
                            textRes = R.string.no_records
                        )
                    }
                } else {
                    items(list) {
                        HCRecordItem(data = it)
                        HorizontalDivider(thickness = 0.5.dp, color = Color(0x14FFFFFF))
                    }
                }
            }
            itemLoadMore(list.isNotEmpty() && vm.allowLoad, vm.isLoadingNow, vm.hasMore) {
                vm.loadMore()
            }
        }
    }
}

private data class HCTab(@StringRes val titleRes: Int, val changeType: Int)
private data class HCData(
    val title: String,
    val time: String,
    val desc: String,
    val source: String = "",
    val id: Int = 0,
)


@Composable
private fun HCRecordItem(data: HCData) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 20.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            AppText(text = data.title, fontSize = 14.sp, color = Color.White)
            if (data.source.isNotEmpty()) {
                Spacer(modifier = Modifier.height(8.dp))
                AppText(text = data.source, fontSize = 12.sp, color = colorWhite50Alpha)
            } else {
                Spacer(modifier = Modifier.height(4.dp))
            }
            Spacer(modifier = Modifier.height(8.dp))
            AppText(text = data.time, fontSize = 12.sp, color = colorWhite50Alpha)
        }
        AppText(text = data.desc, fontSize = 14.sp, color = Color.White)
    }
}

//1: 获取记录。2: 消耗记录
private const val change_type_income = 1
private const val change_type_outcome = 2

interface HouseCoinRecordApi {
    @GET("api/friendship/v1/cp_room/balance/change_record")
    suspend fun getRecordList(
        @Query("change_type") changeType: Int,
        @Query("last_id") lastId: Int = 0
    ): ApiResponse<List<CoinRecord>>
}

private class HouseCoinRecordViewModel(val changeType: Int) : LiStateViewModel<HCData>() {
    private val api = createApi<HouseCoinRecordApi>()

    private var lastId = 0

    init {
        refresh()
    }

    override fun onStartRequest(isRefresh: Boolean) {
        lastId = if (isRefresh) {
            0
        } else {
            dataState.value.lastOrNull()?.id ?: -1
        }
    }

    override suspend fun fetch(): List<HCData> {
        return runApiCatching { api.getRecordList(changeType, lastId) }
            .toastError()
            .map {
                val sdf = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
                it.map { record ->
                    HCData(
                        id = record.id,
                        title = record.sceneName,
                        source = record.user?.nickname.orEmpty(),
                        time = sdf.format(record.createTimestamp * 1000),
                        desc = "${if (changeType == change_type_income) "+" else "-"}${record.changeCount}${
                            app.getString(
                                R.string.小屋币
                            )
                        }"
                    )
                }
            }.getOrNull().orEmpty()
    }

}