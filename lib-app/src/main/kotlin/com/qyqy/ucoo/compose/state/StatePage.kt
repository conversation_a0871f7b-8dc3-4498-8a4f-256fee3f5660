package com.qyqy.ucoo.compose.state

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.utils.OnClick

@Composable
fun <T> StatePage(
    dataFetcher: suspend () -> Result<T>,
    modifier: Modifier = Modifier,
    errorModifier: Modifier = Modifier
        .fillMaxWidth()
        .aspectRatio(1f),
    initValue: UIState<T> = UIState.Loading(""),
    textRetry: String = stringResource(id = R.string.点击重试),
    key1: Any? = null,
    content: @Composable (data: T) -> Unit
) {
    var key by rememberSaveable {
        mutableIntStateOf(0)
    }
    val state by produceState(initialValue = initValue, key1 = key1, key2 = key) {
        if (this.value is UIState.Error) {
            this.value = initValue
        }
        this.value = dataFetcher.invoke().toUIState()
    }
    Box(modifier = modifier) {
        when (state) {
            is UIState.Data -> content((state as UIState.Data<T>).value)
            is UIState.Error -> ErrorView(
                modifier = errorModifier,
                throwable = (state as UIState.Error).throwable
            ) {
                AppText(
                    text = textRetry,
                    fontSize = 16.sp,
                    color = Color.White,
                    lineHeight = 32.sp,
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .background(MaterialTheme.colorScheme.primary, Shapes.chip)
                        .padding(horizontal = 20.dp)
                        .clickWithShape {
                            key += 1
                        }
                )
            }

            is UIState.Loading -> IconLoading(modifier = Modifier.align(Alignment.Center))
        }
    }
}

@Composable
fun <T> StatePage(
    state: UIState<T>, modifier: Modifier = Modifier,
    loadingModifier: Modifier = Modifier.fillMaxSize(),
    loadingUI: @Composable () -> Unit = {
        Box(modifier = loadingModifier) {
            IconLoading(modifier = Modifier.align(Alignment.Center))
        }
    },
    errorModifier: Modifier = Modifier
        .fillMaxWidth()
        .aspectRatio(1f),
    textRetry: String = stringResource(id = R.string.点击重试),
    onRetry: OnClick = {},
    content: @Composable (data: T) -> Unit
) {
    Box(modifier = modifier) {
        when (state) {
            is UIState.Data -> content((state as UIState.Data<T>).value)
            is UIState.Error -> ErrorView(
                modifier = errorModifier,
                throwable = (state as UIState.Error).throwable
            ) {
                AppText(
                    text = textRetry,
                    fontSize = 16.sp,
                    color = Color.White,
                    lineHeight = 32.sp,
                    modifier = Modifier
                        .padding(top = 20.dp)
                        .background(MaterialTheme.colorScheme.primary, Shapes.chip)
                        .clickWithShape(onClick = onRetry)
                        .padding(horizontal = 20.dp)
                )
            }

            is UIState.Loading -> loadingUI()
        }
    }
}