package com.qyqy.ucoo.compose.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LocalTextStyle
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.colorFemaleBackground
import com.qyqy.ucoo.compose.theme.colorMaleBackground


@Composable
fun AgeGender(age: Int, isBoy: <PERSON><PERSON><PERSON>, modifier: Modifier = Modifier) {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = Modifier
            .background(if (isBoy) colorMaleBackground else colorFemaleBackground, shape = RoundedCornerShape(8.dp))
            .height(16.dp)
            .padding(horizontal = 4.dp)
            .then(modifier)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_gender_male.takeIf { isBoy } ?: R.drawable.ic_gender_female),
            contentDescription = "gender",
            modifier = Modifier.size(12.dp, 12.dp)
        )
        Spacer(modifier = Modifier.width(2.dp))
        AppText(
            text = age.toString(), color = Color.White, fontSize = 10.sp, style = LocalTextStyle.current.merge(
                TextStyle(platformStyle = PlatformTextStyle(includeFontPadding = false))
            )
        )
    }
}


@Preview(backgroundColor = 0xFF2196F3)
@Composable
fun AgeGenderPreview() {
    AgeGender(age = 20, isBoy = false)
}