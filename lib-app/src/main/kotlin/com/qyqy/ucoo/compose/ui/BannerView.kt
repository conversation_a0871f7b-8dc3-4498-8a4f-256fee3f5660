package com.qyqy.ucoo.compose.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.map.BannerInfo
import kotlinx.coroutines.launch

enum class UCOOScreenLocation(val position: Int = 0) {
    GLOBAL(1),//全局
    CHATROOM(2),//语音房页
    TRIBE_HALL(3),
    PRIVATE_CHAT(4),
    MESSAGE_PAGE(5),
    HOME_PAGE(6),
    ENTERTAINMENT(7),
    MOMENT(8),//动态
    MINE(9),//我的
    CP_LIST(10),//组CP
    ATTRACTIVE(11)//首页-推荐
}

//<editor-fold desc="Banner">
@Composable
fun UCOOBanner(modifier: Modifier = Modifier, list: List<BannerInfo>) {
    val scope = rememberCoroutineScope()
    val visible = list.isNotEmpty()
    val context = LocalContext.current
    if (visible) {
        Box(
            modifier = modifier
        ) {
            val realPageCount = list.size
            val state = rememberViewPagerState(initialPage = 0, pageCount = realPageCount)
            val cur by remember {
                derivedStateOf { state.fixCurrentPage }
            }
            LoopViewPagerEffect(pagerState = state, realCount = realPageCount)
            LoopAdjustEffect(realPageCount, state.pageCount, cur) {
                scope.launch {
                    state.scrollToPage(it)
                }
            }
            HorizontalPager(modifier = Modifier.fillMaxSize(1f), pageSpacing = 4.dp, state = state) {
                val model = list.getOrNull(mapLoopViewPagerPage(it, realPageCount)) ?: return@HorizontalPager
                ComposeImage(
                    model = model.picUrl,
                    modifier = Modifier
                        .fillMaxSize()
                        .clip(RoundedCornerShape(12.dp))
                        .click {
                            Analytics.reportClickEvent(model.slug)
                            AppLinkManager.open(context, model.jumpLink, model.container)
                        }, contentScale = ContentScale.FillWidth
                )
            }
            if (realPageCount > 1) {
                CircleIndicator(
                    count = realPageCount,
                    currentIndex = mapLoopViewPagerPage(cur, realPageCount),
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = 8.dp)
                )
            }
        }
    }
}

@Composable
fun UCOOBannerView(location: UCOOScreenLocation, modifier: Modifier = Modifier) {
    val banners = if (isPreviewOnCompose) {
        listOf<BannerInfo>()
    } else {
        val list by UIConfig.flowBannerList.collectAsStateWithLifecycle(initialValue = UIConfig.banners)
        val bannerList = remember {
            derivedStateOf { list.filter { it.support(location.position) } }
        }
        bannerList.value
    }
    UCOOBanner(list = banners, modifier = modifier)
}
//</editor-fold>