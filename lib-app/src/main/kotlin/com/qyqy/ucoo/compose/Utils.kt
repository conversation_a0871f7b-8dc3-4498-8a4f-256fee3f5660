package com.qyqy.ucoo.compose

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Canvas
import android.graphics.PixelFormat
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.view.View
import androidx.annotation.ColorInt
import androidx.appcompat.content.res.AppCompatResources
import androidx.compose.foundation.clickable
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.isImeVisible
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisallowComposableCalls
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.movableContentOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.focus.onFocusEvent
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.compositeOver
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.LocalUserPartition
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.app
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.utils.ComposePath
import com.qyqy.ucoo.utils.LogUtil
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import java.io.File
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import kotlin.math.roundToInt

/**
 * 取整法：将每个颜色通道的值舍入到最接近的整数值
 */
fun Color.getApproximateColor(): Color {
    val originalColor = this
    // 取整法：将每个颜色通道的值舍入到最接近的整数值
    return Color(
        originalColor.red.roundToInt().coerceIn(0, 255),
        originalColor.green.roundToInt().coerceIn(0, 255),
        originalColor.blue.roundToInt().coerceIn(0, 255)
    )
}

fun Color.getShadowColor(alpha: Float = 0.4f, darkenFactor: Float = 0.8f): Color {
    return this
        .copy(alpha = alpha) // 增加透明度
        .compositeOver(Color.Black.copy(alpha = 1f - darkenFactor)) // 叠加黑色形成更真实的阴影
}

fun <T, M> StateFlow<T>.map(
    coroutineScope: CoroutineScope,
    mapper: (value: T) -> M,
): StateFlow<M> = map { mapper(it) }.stateIn(
    coroutineScope,
    SharingStarted.Eagerly,
    mapper(value)
)

fun String.colorFromHex() = Color(
    try {
        android.graphics.Color.parseColor(
            if (this.startsWith("#")) this else "#$this"
        )
    } catch (e: Exception) {
        LogUtil.e("-----$this")
        android.graphics.Color.TRANSPARENT
    }
)

fun <T> T?.orDefault(default: T): T = this.takeIf { this != null } ?: default

fun String?.ifEmpty(value: String): String = this.takeUnless {
    it.isNullOrEmpty()
} ?: value


private class PlaceholderDrawable constructor(
    @ColorInt color: Int,
    private val placeholderDrawable: Drawable,
) : Drawable() {

    private val colorDrawable = ColorDrawable(color)

    override fun onBoundsChange(bounds: Rect) {
        super.onBoundsChange(bounds)
        if (!bounds.isEmpty) {
            val size = bounds.width().coerceAtMost(bounds.height())
            val left = bounds.width().minus(size).div(2).plus(bounds.left)
            val top = bounds.height().minus(size).div(2).plus(bounds.top)
            placeholderDrawable.bounds = Rect(left, top, left.plus(size), top.plus(size))
        }
        colorDrawable.bounds = bounds
    }

    override fun draw(canvas: Canvas) {
        colorDrawable.draw(canvas)
        placeholderDrawable.draw(canvas)
    }

    override fun setAlpha(alpha: Int) = Unit

    override fun setColorFilter(colorFilter: android.graphics.ColorFilter?) = Unit

    override fun getOpacity(): Int = PixelFormat.TRANSLUCENT

}

private val ucooDefaultDrawable by lazy {
    AppCompatResources.getDrawable(app, R.drawable.ic_ucoo_foreground)!!
}
private val cupidDefaultDrawable by lazy {
    AppCompatResources.getDrawable(app, R.drawable.ic_cupid_foreground)!!
}

@Composable
fun composePlaceholderDrawable(
    @ColorInt color: Int = 0xFF333333.toInt(),
    placeholderDrawable: Drawable = run {
        if (isEditOnCompose) {
            val context = LocalContext.current
            val resId = if (LocalUserPartition.current.isUCOO) {
                R.drawable.ic_ucoo_foreground
            } else {
                R.drawable.ic_cupid_foreground
            }
            AppCompatResources.getDrawable(context, resId)!!
        } else {
            if (LocalUserPartition.current.isUCOO) {
                ucooDefaultDrawable
            } else {
                cupidDefaultDrawable
            }
        }
    },
): Drawable {
    return PlaceholderDrawable(color, placeholderDrawable)
}

fun <T> MutableList<T>.updateIf(predicate: (T) -> T?) {
    forEachIndexed { index, item ->
        val new = predicate(item)
        if (new != null) {
            this[index] = new
            return
        }
    }
}

fun <T> MutableList<T>.updateIfExist(target: T, identity: Boolean = false, provider: (T) -> T) {
    forEachIndexed { index, item ->
        if (identity) {
            if (item === target) {
                this[index] = provider(item)
                return
            }
        } else {
            if (item == target) {
                this[index] = provider(item)
                return
            }
        }
    }
}

val User.isSelfOnCompose: Boolean
    @ReadOnlyComposable
    @Composable
    get() {
        return if (LocalView.current.isInEditMode) {
            id == "1"
        } else {
            id == sUser.id
        }
    }

val View.takeUnlessEditMode
    get() = takeUnless {
        it.isInEditMode
    }

val Boolean.trueOrNull
    get() = takeIf {
        it
    }

val isEditOnCompose: Boolean
    @Composable
    get() = LocalInspectionMode.current


@SuppressLint("ModifierFactoryUnreferencedReceiver")
@Composable
fun Modifier.noEffectClickable(
    enabled: Boolean = true,
    onClickLabel: String? = null,
    role: Role? = null,
    onClick: () -> Unit,
) = clickable(
    interactionSource = remember { MutableInteractionSource() },
    indication = null,
    enabled = enabled,
    onClickLabel = onClickLabel,
    role = role,
    onClick = onClick,
)

@Composable
fun Modifier.enableClick(
    onClickLabel: String? = null,
    role: Role? = null,
) = noEffectClickable(
    enabled = true,
    onClickLabel = onClickLabel,
    role = role,
    onClick = {},
)

@Composable
fun <T> List<T>.movable(
    transform: @Composable (item: T) -> Unit,
): @Composable (item: T) -> Unit {
    val composedItems = remember(this) { mutableMapOf<T, @Composable () -> Unit>() }
    return { item: T ->
        composedItems.getOrPut(item) {
            movableContentOf { transform(item) }
        }.invoke()
    }
}

fun Modifier.autoCloseKeyboard(): Modifier = composed {
    val keyboardController = LocalSoftwareKeyboardController.current
    pointerInput(this) {
        detectTapGestures(
            onPress = {
                keyboardController?.hide()
            },
        )
    }
}

@Composable
fun Modifier.clickWithShape(
    shape: Shape = Shapes.chip,
    enabled: Boolean = true,
    onClick: () -> Unit
) =
    this
        .clip(shape)
        .click(onClick = onClick, enabled = enabled)

@OptIn(ExperimentalLayoutApi::class)
fun Modifier.clearFocusOnKeyboardDismiss(): Modifier = composed {
    var isFocused by remember { mutableStateOf(false) }
    var keyboardAppearedSinceLastFocused by remember { mutableStateOf(false) }
    if (isFocused) {
        val imeIsVisible = WindowInsets.isImeVisible
        val focusManager = LocalFocusManager.current
        LaunchedEffect(imeIsVisible) {
            if (imeIsVisible) {
                keyboardAppearedSinceLastFocused = true
            } else if (keyboardAppearedSinceLastFocused) {
                delay(150)
                focusManager.clearFocus()
            }
        }
    }
    onFocusEvent {
        if (isFocused != it.isFocused) {
            isFocused = it.isFocused
            if (isFocused) {
                keyboardAppearedSinceLastFocused = false
            }
        }
    }
}

val Int.timeFormat: String
    get() = if (this < 10) "0$this" else this.toString()

val String.timeFormat: String
    get() = toIntOrNull()?.timeFormat ?: this

@Composable
inline fun <reified VM : ViewModel> viewModelOfActivity(): VM? {
    val context = LocalContext.current
    val owner = context.asComponentActivity
    return if (owner != null) {
        viewModel<VM>(viewModelStoreOwner = owner)
    } else {
        null
    }
}

@Composable
fun ComposeLifecycleObserve(
    key: String = "",
    lifecycleOwner: LifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current,
    onStateChanged: (event: Lifecycle.Event) -> Unit,
) {
    DisposableEffect(key1 = key, effect = {
        val observer = LifecycleEventObserver { source, event -> onStateChanged(event) }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    })
}

fun getSampleImageUrl(width: Int = 200, height: Int = 300, random: Int = 1): String =
    "https://picsum.photos/${width}/${height}?random=$random"


@Composable
inline fun <T> rememberWithPrevious(
    key1: Any?,
    crossinline calculation: @DisallowComposableCalls (T?) -> T,
): T {
    val cacheRef = rememberRef<T>()
    val value = remember(key1) {
        calculation(cacheRef.value)
    }
    SideEffect {
        cacheRef.value = value
    }
    return value
}

@Composable
inline fun <T> rememberRefWithPrevious(
    key1: Any?,
    crossinline calculation: @DisallowComposableCalls (T?) -> MutableState<T>,
): MutableState<T> {
    val cacheRef = rememberRef<T>()
    val state = remember(key1) {
        calculation(cacheRef.value)
    }
    SideEffect {
        cacheRef.value = state.value
    }
    return state
}

@Composable
fun <T> keepLastNonNullState(newState: T?): T? {
    val lastState = remember {
        mutableStateOf<T?>(null)
    }
    return if (newState == null) {
        lastState.value
    } else {
        SideEffect {
            lastState.value = newState
        }
        newState
    }
}

@Composable
inline fun <T> rememberSaveableRefWithPrevious(
    key1: Any?,
    crossinline calculation: @DisallowComposableCalls (T?) -> MutableState<T>,
): MutableState<T> {
    val cacheRef = rememberSaveableRef<T>()
    val state = rememberSaveable(key1) {
        calculation(cacheRef.value)
    }
    SideEffect {
        cacheRef.value = state.value
    }
    return state
}

@Composable
fun <T> rememberSaveableRef(): MutableState<T?> {
    // for some reason it always recreated the value with vararg keys,
    // leaving out the keys as a parameter for remember for now
    return rememberSaveable {
        mutableStateOf(null)
    }
}

@Composable
fun <T> rememberRef(): MutableState<T?> {
    // for some reason it always recreated the value with vararg keys,
    // leaving out the keys as a parameter for remember for now
    return remember {
        mutableStateOf(null)
    }
}

@Composable
fun <T> rememberPrevious(
    current: T,
    shouldUpdate: (prev: T?, curr: T) -> Boolean = { a: T?, b: T -> a != b },
): T? {
    val ref = rememberRef<T>()

    // launched after render, so the current render will have the old value anyway
    SideEffect {
        if (shouldUpdate(ref.value, current)) {
            ref.value = current
        }
    }

    return ref.value
}


fun getTodayDate() = run {
    SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date())
}

@Composable
fun rememberDateState(): State<String> = run {
    val context = LocalContext.current
    val dateState = remember {
        mutableStateOf(getTodayDate())
    }
    val br = remember {
        object : BroadcastReceiver() {
            override fun onReceive(context: Context?, intent: Intent?) {
                dateState.value = getTodayDate()
            }
        }
    }
    DisposableEffect(key1 = Unit) {
        context.registerReceiver(br, IntentFilter(Intent.ACTION_DATE_CHANGED))
        onDispose {
            context.unregisterReceiver(br)
        }
    }
    dateState
}


fun Context.getCacheFile(type: String): File {
    return getExternalFilesDir(type) ?: File(filesDir, type)
}

@Composable
fun LaunchOnceEffect(key: Any? = null, block: suspend CoroutineScope.() -> Unit) {
    var launched by rememberSaveable(key) {
        mutableStateOf(false)
    }
    if (!launched) {
        LaunchedEffect(key1 = key) {
            try {
                block()
            } finally {
                launched = true
            }
        }
    }
}

fun formatTimeWithHours(time: Long): String {
    if (time <= 0) {
        return "00:00:00"
    }
    val secondTime = time.plus(500).div(1000).toInt() // 四舍五入
    val hours = secondTime.div(3600)
    val minute = secondTime.rem(3600).div(60)
    val second = secondTime.rem(60)
    return String.format("%02d:%02d:%02d", hours, minute, second)
}


/**
 * @param borderWidth
 * @param brush
 * @param radius
 * @param mode 0 不是任何一遍 1左边 2上边 3右边 4下边 5左右 6上下
 */
fun Modifier.border(
    borderWidth: Dp = 1.dp,
    brush: Brush,
    radius: Dp = 0.dp,
    mode: Int = 0
) = this.drawBehind {

    if (borderWidth == 0.dp || mode <= 0) {
        return@drawBehind
    }

    val strokeWidth = borderWidth.toPx()
    val width = size.width
    val height = size.height
    ComposePath.doRun {
        it.apply {
            when (mode) {
                2 -> {
                    moveTo(0f, radius.toPx())
                    quadraticTo(
                        0f, 0f,
                        radius.toPx(), 0f
                    )
                    // 上边线 + topEndRadius
                    lineTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width, 0f,
                        width, radius.toPx()
                    )
                }

                4 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width, height,
                        width, height - radius.toPx()
                    )
                }

                1 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f, 0f,
                        0f, radius.toPx()
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )
                }

                3 -> {
                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width, 0f,
                        width, radius.toPx()
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width, height,
                        width - radius.toPx(), height
                    )
                }


                5 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f, 0f,
                        0f, radius.toPx()
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )

                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width, 0f,
                        width, radius.toPx()
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width, height,
                        width - radius.toPx(), height
                    )
                }


                6 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width, height,
                        width, height - radius.toPx()
                    )
                }

                else -> {

                }
            }
        }
        drawPath(
            path = it,
            brush = brush,
            style = Stroke(strokeWidth)
        )
    }
}

/**
 * @param borderWidth
 * @param brush
 * @param radius
 * @param mode 0 不是任何一遍 1左边 2上边 3右边 4下边 5左右 6上下
 */
fun Modifier.border(
    borderWidth: Dp = 1.dp,
    color: Color,
    radius: Dp = 0.dp,
    mode: Int = 0
) = this.drawBehind {

    if (borderWidth == 0.dp || mode <= 0) {
        return@drawBehind
    }

    val strokeWidth = borderWidth.toPx()
    val width = size.width
    val height = size.height

    ComposePath.doRun {
        it.apply {
            when (mode) {
                2 -> {
                    moveTo(0f, radius.toPx())
                    quadraticTo(
                        0f, 0f,
                        radius.toPx(), 0f
                    )
                    // 上边线 + topEndRadius
                    lineTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width, 0f,
                        width, radius.toPx()
                    )
                }

                4 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width, height,
                        width, height - radius.toPx()
                    )
                }

                1 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f, 0f,
                        0f, radius.toPx()
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )
                }

                3 -> {
                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width, 0f,
                        width, radius.toPx()
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width, height,
                        width - radius.toPx(), height
                    )
                }


                5 -> {
                    moveTo(radius.toPx(), 0f)
                    quadraticTo(
                        0f, 0f,
                        0f, radius.toPx()
                    )
                    lineTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )

                    moveTo(width - radius.toPx(), 0f)
                    quadraticTo(
                        width, 0f,
                        width, radius.toPx()
                    )
                    lineTo(width, height - radius.toPx())
                    quadraticTo(
                        width, height,
                        width - radius.toPx(), height
                    )
                }


                6 -> {
                    moveTo(0f, height - radius.toPx())
                    quadraticTo(
                        0f, height,
                        radius.toPx(), height
                    )
                    lineTo(width - radius.toPx(), height)
                    quadraticTo(
                        width, height,
                        width, height - radius.toPx()
                    )
                }

                else -> {

                }
            }
        }

        drawPath(
            path = it,
            color = color,
            style = Stroke(strokeWidth)
        )
    }


}