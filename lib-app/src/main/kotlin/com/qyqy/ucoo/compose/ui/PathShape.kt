package com.qyqy.ucoo.compose.ui

import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection

class PathShape(
    private val builder: (size: Size, layoutDirection: LayoutDirection) -> Path
) : Shape {

    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = builder(size, layoutDirection)
        return Outline.Generic(path)
    }

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        return (other as? PathShape)?.builder == builder
    }

    override fun hashCode(): Int {
        return builder.hashCode()
    }
}