package com.qyqy.ucoo.compose.domain.usecase.relationship

import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.domain.SuspendUseCase
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi.ViewResult
import com.qyqy.ucoo.user.RelationshipRepository
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.currentCoroutineContext
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flattenMerge
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.isActive

class GetFamilyRelationUseCase constructor(
    private val relationshipRepository: RelationshipRepository = RelationshipRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : SuspendUseCase<String, Flow<ViewResult>>(ioDispatcher) {
    override suspend fun execute(parameters: String): Flow<ViewResult> {
        return flow {
            emit(flow {
                val tagList = relationshipRepository.getAllLabelList()
                if (tagList == null) {
                    emit(ViewResult.ErrorResult(null))
                } else {
                    emit(ViewResult.FamilyTagListResult(tagList))
                }
            })
            emit(flow config@{
                val ret = relationshipRepository.getRelationshipSettings(parameters)
                val configs = ret?.first
                if (configs == null) {
                    emit(ViewResult.ErrorResult(null))
                    return@config
                }
                val emptyCount = configs[1] - configs[0]
                emit(
                    ViewResult.FamilyConfigInfoResult(
                        emptyCount = emptyCount,
                        totalCount = configs[1],
                        maxCount = configs[2],
                        pricePerLabel = configs[3],
                        ruleLink = ret.second,
                    )
                )
                if (configs[0] == 0) {
                    emit(ViewResult.FamilyLabelListResult(persistentListOf()))
                } else {
                    val labelList = relationshipRepository.getMyRelationshipLabelList(parameters, 0)
                    if (!labelList.isNullOrEmpty()) {
                        emit(ViewResult.FamilyLabelListResult(labelList))
                        var relativeId: Int = labelList.last().label.relativeId
                        val moreList = buildList {
                            while (currentCoroutineContext().isActive) {
                                val list = relationshipRepository.getMyRelationshipLabelList(parameters, relativeId)
                                if (list.isNullOrEmpty()) {
                                    break
                                }
                                relativeId = list.last().label.relativeId
                                addAll(list)
                            }
                        }
                        if (moreList.isNotEmpty()) {
                            emit(ViewResult.FamilyLabelListResult(persistentListOf<Relationship.Label.Normal>().also {
                                it.addAll(labelList)
                                it.addAll(moreList)
                            }))
                        }
                    }
                }
            })
        }.flattenMerge(2)
    }
}