package com.qyqy.ucoo.compose.ui.wish

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.ui.dialog.ContentAlertDialog
import com.qyqy.cupid.ui.dialog.DialogButton
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.profile.wish.RoomWishViewModel
import com.qyqy.cupid.ui.profile.wish.WishEntry
import com.qyqy.cupid.ui.profile.wish.WishGift
import com.qyqy.cupid.ui.profile.wish.WishPage
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

@Composable
fun WishRoomHostPage(
    wishViewModel: RoomWishViewModel,
    dialogQueue: DialogQueue<*>,
    onSendGift: (User, gift: WishGift) -> Unit = { _, _ -> },
    onNavProfile: (User) -> Unit = {},
) {
    LaunchedEffect(key1 = wishViewModel) {
        wishViewModel.queryRoomWishList()
    }

    val scope = rememberCoroutineScope()

    BackHandler(wishViewModel.wishPage != WishPage.Home) {
        wishViewModel.pop()
    }

    WishPageScaffold(minHeight = 520.dp) {
        AnimatedContent(targetState = wishViewModel.wishPage, label = "host") { page ->
            when (page) {
                WishPage.Home -> {
                    WishRoomHomePage(
                        wishViewModel = wishViewModel,
                        onAddWish = {
                            wishViewModel.navToPage(WishPage.Add)
                        },
                        onRemoveWish = { index, item ->
                            dialogQueue.pushCenterDialog(true) { dialog, _ ->
                                ContentAlertDialog(
                                    content = stringResource(id = R.string.确定要删除心愿一吗, index),
                                    startButton = DialogButton(stringResource(id = R.string.取消)) {
                                        dialog.dismiss()
                                    },
                                    endButton = DialogButton(stringResource(id = R.string.确定)) {
                                        dialog.dismiss()
                                        wishViewModel.removeWish(scope, item)
                                    }
                                )
                            }
                        },
                        onSendGift = onSendGift,
                        onNavToDetail = { index, user ->
                            wishViewModel.navToPage(WishPage.Detail(index, user?.id))
                        },
                        onNavToRecord = {
                            wishViewModel.navToPage(WishPage.Record)
                        },
                    )
                }

                is WishPage.Detail -> WishDetailPage(
                    index = page.index,
                    userId = page.userId,
                    wishViewModel = wishViewModel,
                    onBack = {
                        wishViewModel.pop()
                    },
                    onNavToRecord = {
                        wishViewModel.navToPage(WishPage.Record)
                    },
                    onAddWish = {
                        wishViewModel.navToPage(WishPage.Add)
                    },
                    onRemoveWish = { index, item ->
                        dialogQueue.pushCenterDialog(true) { dialog, _ ->
                            ContentAlertDialog(
                                content = stringResource(id = R.string.确定要删除心愿一吗, index),
                                startButton = DialogButton(stringResource(id = R.string.取消)) {
                                    dialog.dismiss()
                                },
                                endButton = DialogButton(stringResource(id = R.string.确认)) {
                                    dialog.dismiss()
                                    wishViewModel.removeWish(scope, item)
                                }
                            )
                        }
                    },
                    onSendGift = onSendGift,
                    onNavProfile = onNavProfile
                )

                WishPage.Add -> EditWishHostPage(
                    wishViewModel = wishViewModel,
                    onBack = {
                        wishViewModel.pop()
                    },
                    onSave = { item ->
                        wishViewModel.saveNewWish(scope, item)
                    }
                )

                WishPage.Record -> WishHelpRecordPage(
                    wishViewModel = wishViewModel,
                    maxHeight = 400.dp,
                    onBack = {
                        wishViewModel.pop()
                    },
                    onNavProfile = onNavProfile
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewWishRoomHostPage() {
    Box(modifier = Modifier.fillMaxSize(), contentAlignment = Alignment.BottomCenter) {
        WishRoomHostPage(RoomWishViewModel.Preview, DialogQueue<IDialogAction>())
    }
}

@Composable
fun WishRoomHomePage(
    wishViewModel: RoomWishViewModel,
    onAddWish: OnClick = {},
    onRemoveWish: (Int, WishEntry) -> Unit = { _, _ -> },
    onSendGift: (User, gift: WishGift) -> Unit = { _, _ -> },
    onNavToDetail: (Int, User?) -> Unit = { _, _ -> },
    onNavToRecord: OnClick = {},
    onNavProfile: (User) -> Unit = {},
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Row(
            modifier = Modifier
                .align(Alignment.End)
                .padding(end = 16.dp, bottom = 10.dp)
                .clickable(onClick = onNavToRecord),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(text = stringResource(id = R.string.心愿助力记录), color = Color.White, fontSize = 12.sp)
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_arrow_right),
                contentDescription = null,
                tint = Color.White
            )
        }

        var isRefreshing by remember {
            mutableStateOf(wishViewModel.autoRefresh1)
        }

        val scope = rememberCoroutineScope()

        val onRefresh: () -> Unit = {
            scope.launch {
                wishViewModel.queryRoomWishList()
                isRefreshing = false
            }
        }

        if (isRefreshing) {
            LaunchedEffect(key1 = Unit) {
                if (isRefreshing) {
                    onRefresh()
                }
            }
        }

        CupidPullRefreshBox(
            isRefreshing = isRefreshing,
            onRefresh = onRefresh,
            modifier = Modifier.fillMaxWidth(),
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(max = 450.dp)
                    .verticalScroll(rememberScrollState())
                    .padding(horizontal = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = stringResource(id = R.string.帮助麦上好友达成心愿),
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(top = 15.dp, start = 15.dp, end = 15.dp),
                    color = Color(0xFFEBDDFF),
                    fontSize = 14.sp,
                    textAlign = TextAlign.Center
                )

                VerticalGrid(
                    modifier = Modifier.padding(top = 12.dp),
                    columns = 4,
                    horizontalSpace = 6.dp,
                    verticalSpace = 8.dp
                ) {

                    wishViewModel.userWishEntries.forEachIndexed { index, wishEntry ->
                        Column(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(0.60145f)
                                .paint(painterResource(id = R.drawable.bg_item_room_user_wish), contentScale = ContentScale.FillBounds)
                                .noEffectClickable {
                                    if (wishEntry.user?.isSelf == true) {
                                        if (wishEntry.list.isNullOrEmpty()) {
                                            onAddWish()
                                        } else {
                                            onNavToDetail(index, wishEntry.user)
                                        }
                                    } else if (!wishEntry.list.isNullOrEmpty()) {
                                        onNavToDetail(index, wishEntry.user)
                                    }
                                }
                                .padding(4.dp)
                        ) {

                            val user = wishEntry.user
                            val list = wishEntry.list

                            Row(verticalAlignment = Alignment.CenterVertically) {
                                CircleComposeImage(
                                    model = user?.avatarUrl ?: R.drawable.ic_empty_wish_seats,
                                    modifier = Modifier
                                        .size(24.dp)
                                        .noEffectClickable {
                                            if (user != null) {
                                                onNavProfile(user)
                                            }
                                        },
                                    borderStroke = BorderStroke(0.5.dp, Color.White)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Column {
                                    Text(
                                        text = user?.nickname ?: stringResource(id = R.string.虚位以待),
                                        color = Color.White,
                                        fontSize = 9.sp,
                                        maxLines = 1,
                                        lineHeight = 9.sp,
                                        overflow = TextOverflow.Ellipsis
                                    )

                                    if (user?.isSelf == true || !list.isNullOrEmpty()) {
                                        AutoSizeText(
                                            text = if (user?.isSelf == true) {
                                                if (list.isNullOrEmpty()) {
                                                    stringResource(id = R.string.暂无__)
                                                } else {
                                                    stringResource(id = R.string.查看详情__)
                                                }
                                            } else {
                                                stringResource(id = R.string.感恩方式__)
                                            },
                                            color = Color(0xFFFFF50F),
                                            fontSize = 9.sp,
                                            maxLines = 1
                                        )
                                    }
                                }
                            }

                            Column(
                                modifier = Modifier
                                    .padding(top = 10.dp)
                                    .fillMaxSize(),
                                verticalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterVertically),
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                if (!list.isNullOrEmpty()) {
                                    Box(
                                        modifier = Modifier
                                            .size(48.dp)
                                            .background(Color(0xFFEFC0FF), RoundedCornerShape(8.dp))
                                    ) {
                                        ComposeImage(
                                            model = list.first().gift.icon,
                                            modifier = Modifier
                                                .size(40.dp)
                                                .align(Alignment.Center),
                                            loading = null
                                        )
                                    }
                                }

                                if (!list.isNullOrEmpty()) {
                                    val item = list.first()
                                    AutoSizeText(
                                        text = "${item.process}/${item.count}",
                                        color = if (item.process < item.count) Color(0xFFD354FF) else Color(0xFF642EFF),
                                        fontSize = 12.sp,
                                        maxLines = 2
                                    )
                                } else {
                                    AutoSizeText(
                                        text = if (user?.isSelf == true) {
                                            stringResource(id = R.string.点击添加我的心愿)
                                        } else {
                                            stringResource(id = R.string.暂无心愿)
                                        },
                                        color = Color(0xFFD354FF),
                                        fontSize = 12.sp,
                                        maxLines = 2
                                    )
                                }

                                if (!list.isNullOrEmpty()) {
                                    Box(
                                        modifier = Modifier
                                            .fillMaxWidth()
                                            .height(20.dp)
                                            .clip(CircleShape)
                                            .run {
                                                if (user?.isSelf == true) {
                                                    background(Brush.verticalGradient(listOf(Color(0xFFFD8EFF), Color(0xFFFC43FF))))
                                                } else {
                                                    background(Brush.verticalGradient(listOf(Color(0xFFFD8EFF), Color(0xFF8247FF))))
                                                }
                                            }
                                            .clickable {
                                                if (user?.isSelf == true) {
                                                    onRemoveWish(1, list.first())
                                                } else {
                                                    onSendGift(user!!, list.first().gift)
                                                }
                                            },
                                        contentAlignment = Alignment.Center
                                    ) {
                                        AutoSizeText(
                                            text = if (user?.isSelf == true) {
                                                stringResource(id = R.string.删除心愿)
                                            } else {
                                                stringResource(id = R.string.帮TA达成)
                                            },
                                            color = Color.White,
                                            fontSize = 12.sp
                                        )
                                    }
                                }

                            }
                        }
                    }
                }

                if (wishViewModel.wishListConfig.taRule.isNotEmpty()) {
                    Text(
                        text = wishViewModel.wishListConfig.taRule,
                        modifier = Modifier.padding(top = 16.dp),
                        color = colorResource(id = R.color.white_alpha_50),
                        fontSize = 12.sp,
                        lineHeight = 16.sp,
                    )
                }
            }
        }
    }
}


@Composable
fun WishPageScaffold(minHeight: Dp = 420.dp, content: @Composable ColumnScope.() -> Unit) {
    Box(
        modifier = Modifier
            .imePadding()
            .fillMaxWidth()
            .heightIn(min = minHeight),
    ) {
        Column(
            modifier = Modifier.matchParentSize()
        ) {
            Image(
                painter = painterResource(id = R.drawable.bg_wish_page_top),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth
            )
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .background(Color(0xFFA26FE9))
            )
            Image(
                painter = painterResource(id = R.drawable.bg_cpd_wish_page_bottom),
                contentDescription = null,
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth
            )
        }
        Column(
            modifier = Modifier
                .animateContentSize()
                .fillMaxWidth()
                .navigationPadding(20.dp)
        ) {
            Spacer(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(9.4f)
            )
            content()
        }
    }
}