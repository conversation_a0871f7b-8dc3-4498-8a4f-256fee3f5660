package com.qyqy.ucoo.compose.presentation.room

import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Color
import android.graphics.PointF
import android.graphics.drawable.Drawable
import android.os.SystemClock
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.ViewGroup.MarginLayoutParams
import android.view.animation.BounceInterpolator
import android.widget.FrameLayout
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.animation.doOnStart
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.lifecycle.viewModelScope
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import com.bumptech.glide.Glide
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.github.penfeizhou.animation.FrameAnimationDrawable
import com.overseas.common.utils.dp
import com.overseas.common.utils.dpF
import com.overseas.common.utils.removeIfCompat
import com.overseas.common.utils.resumeIfActive
import com.overseas.common.utils.suspendOnEnd
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.im.compat.EmojiMessageContent
import com.qyqy.ucoo.im.compat.IMUtils
import com.qyqy.ucoo.im.compat.UCEmojiMessage
import com.qyqy.ucoo.im.compat.UCMicEmojiMessage
import com.qyqy.ucoo.im.room.ChatRoomGiftViewModel
import com.qyqy.ucoo.im.room.ChatRoomViewModel
import com.qyqy.ucoo.widget.LuckyNumberView
import com.qyqy.ucoo.widget.launchWhenAttach
import com.qyqy.ucoo.widget.shape.ShapeFrameLayout
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.util.ScaleType
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.suspendCancellableCoroutine
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.coroutines.yield
import java.io.File
import kotlin.random.Random

interface AnimatedEmojiView {

    companion object Factory {
        fun create(context: Context, content: EmojiMessageContent): AnimatedEmojiView? {
            return when (content.type) {
                UCMicEmojiMessage.TYPE_WEBP, UCMicEmojiMessage.TYPE_WEBP_AND_RESULT -> AnimatedResultEmojiView(context, content)
                else -> null
            }
        }
    }

    context(CoroutineScope)
    suspend fun playAnimateEmoji(once: Boolean)

    fun onRelease()
}

@SuppressLint("ViewConstructor")
class DiceEmojiView(context: Context, private val resultValue: Int) : AppCompatImageView(context), AnimatedEmojiView {

    init {
        if (BuildConfig.DEBUG) {
            require(resultValue in 1..6)
        }
    }

    context(CoroutineScope)
    override suspend fun playAnimateEmoji(once: Boolean) {
        val resultResId = when (resultValue) {
            2 -> R.drawable.dice_2
            3 -> R.drawable.dice_3
            4 -> R.drawable.dice_4
            5 -> R.drawable.dice_5
            6 -> R.drawable.dice_6
            else -> R.drawable.dice_1
        }

        val resList = arrayOf(
            R.drawable.dice_action_0,
            R.drawable.dice_action_1,
            R.drawable.dice_action_2,
            R.drawable.dice_action_3,
        )

        delay(150)

        var count = -1
        while (isActive && count++ <= 14) {
            setImageResource(resList[count.rem(4)])
            delay(120)
        }

        setImageResource(resultResId)
        delay(1000)
    }

    override fun onRelease() {
        setImageDrawable(null)
    }
}

@SuppressLint("ViewConstructor")
class GuessingFistEmojiView(context: Context, private val resultValue: Int) : AppCompatImageView(context), AnimatedEmojiView {

    init {
        if (BuildConfig.DEBUG) {
            require(resultValue in 1..3)
        }
    }

    context(CoroutineScope)
    override suspend fun playAnimateEmoji(once: Boolean) {
        val resultResId = when (resultValue) {
            2 -> R.drawable.effect_stone
            3 -> R.drawable.effect_cloth
            else -> R.drawable.effect_scissor
        }

        val resList = arrayOf(
            R.drawable.effect_scissor,
            R.drawable.effect_stone,
            R.drawable.effect_cloth,
        )

        delay(150)

        var count = 0
        var exclude = 3
        while (isActive && count++ < 9) {
            var index = Random.nextInt(3)
            if (exclude == index) {
                index = index.plus(1).rem(3)
            }
            setImageResource(resList[index])
            exclude = index
            delay(450)
        }

        setImageResource(resultResId)
        delay(1000)
    }

    override fun onRelease() {
        setImageDrawable(null)
    }

}

@SuppressLint("ViewConstructor")
class LuckyNumberEmojiView(context: Context, private val type: Int, private val resultValue: String) : FrameLayout(context), AnimatedEmojiView {

    private val evaAnimView: EvaAnimViewV3 = EvaAnimViewV3(context)

    private val resultView: LuckyNumberView = LuckyNumberView(context)

    init {
        addView(evaAnimView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        addView(resultView.also {
            it.setAttr(2.dpF, 15.dpF)
            it.setNumber(resultValue)
        }, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
    }

    context(CoroutineScope)
    override suspend fun playAnimateEmoji(once: Boolean) {
        resultView.isVisible = false
        evaAnimView.isVisible = true
        evaAnimView.scaleX = 1f
        evaAnimView.scaleY = 1f
        evaAnimView.alpha = 1f
        evaAnimView.setLastFrame(true)
        evaAnimView.setScaleType(ScaleType.CENTER_CROP)
        evaAnimView.setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)

        suspendCancellableCoroutine {
            evaAnimView.setAnimListener(object : IEvaAnimListener {
                override fun onFailed(errorType: Int, errorMsg: String?) {
                    it.resumeIfActive(Unit)
                }

                override fun onVideoComplete(lastFrame: Boolean) {
                    it.resumeIfActive(Unit)
                }

                override fun onVideoDestroy() = Unit

                override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) {
                    if (frameIndex >= 45) {
                        it.resumeIfActive(Unit)
                    }
                }

                override fun onVideoRestart() = Unit

                override fun onVideoStart(isRestart: Boolean) = Unit

            })

            it.invokeOnCancellation { _ ->
                evaAnimView.setAnimListener(null)
                evaAnimView.stopPlay()
            }

            evaAnimView.startPlay(
                evaAnimView.context.assets, when (type) {
                    UCEmojiMessage.TYPE_NUMBER_3 -> "effect_lucky_number_3.mp4"
                    UCEmojiMessage.TYPE_NUMBER_5 -> "effect_lucky_number_5.mp4"
                    else -> "effect_lucky_number_1.mp4"
                }
            )
        }

        ObjectAnimator.ofPropertyValuesHolder(
            evaAnimView,
            PropertyValuesHolder.ofFloat(View.SCALE_X, 1f, 0.2f),
            PropertyValuesHolder.ofFloat(View.SCALE_Y, 1f, 0.2f),
            PropertyValuesHolder.ofFloat(View.ALPHA, 1f, 0f),
        ).setDuration(150).suspendOnEnd(true)

        ObjectAnimator.ofPropertyValuesHolder(
            resultView,
            PropertyValuesHolder.ofFloat(View.ALPHA, 0.5f, 1f),
            PropertyValuesHolder.ofFloat(View.SCALE_X, 0.2f, 1f),
            PropertyValuesHolder.ofFloat(View.SCALE_Y, 0.2f, 1f),
        ).apply {
            doOnStart {
                evaAnimView.isVisible = false
                resultView.isVisible = true
            }
            duration = 150
            interpolator = BounceInterpolator()
        }.suspendOnEnd(true)

        delay(1000)
    }

    override fun onRelease() {
        evaAnimView.setAnimListener(null)
        evaAnimView.stopPlay()
    }
}


@SuppressLint("ViewConstructor")
class AnimatedResultEmojiView(context: Context, private var content: EmojiMessageContent) : FrameLayout(context), AnimatedEmojiView {

    private val animView = AppCompatImageView(context)

    private val resultView = AppCompatImageView(context)

    init {
        addView(animView, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
        addView(resultView.also {
            it.isInvisible = true
        }, LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT))
    }

    fun bindContent(content: EmojiMessageContent) {
        this.content = content
    }

    context(CoroutineScope)
    override suspend fun playAnimateEmoji(once: Boolean) {
        resultView.isInvisible = true
        animView.isVisible = true

        animView.scaleX = 1f
        animView.scaleY = 1f
        animView.alpha = 1f

        // 预先加载
        if (content.emojiResult.isNotEmpty()) {
            Glide.with(resultView).load(content.emojiResult).into(resultView)
        }

        suspendCancellableCoroutine { const ->
            val callback = object : Animatable2Compat.AnimationCallback() {

                override fun onAnimationEnd(drawable: Drawable) {
                    (drawable as? FrameAnimationDrawable<*>)?.unregisterAnimationCallback(this)
                    const.resumeIfActive(Unit)
                }
            }

            // 不能使用内存缓存，因为会复用drawable, 导致相同的麦位表情无法独立播放
            Glide.with(animView).load(content.emojiEffect.effectFile).skipMemoryCache(true).addListener(object : RequestListener<Drawable> {
                override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable>, isFirstResource: Boolean): Boolean {
                    const.resumeIfActive(Unit)
                    return false
                }

                override fun onResourceReady(resource: Drawable, model: Any, target: Target<Drawable>?, dataSource: DataSource, isFirstResource: Boolean): Boolean {
                    if (const.isActive && resource is FrameAnimationDrawable<*>) {
                        resource.setLoopLimit(1)
                        resource.registerAnimationCallback(callback)
                    } else {
                        const.resumeIfActive(Unit)
                    }
                    return false
                }

            }).into(animView)

            const.invokeOnCancellation { e ->
                Glide.with(animView).clear(animView)
                (animView.drawable as? FrameAnimationDrawable<*>)?.also {
                    it.unregisterAnimationCallback(callback)
                    it.stop()
                }
            }
        }

        if (content.emojiResult.isNotEmpty()) {
            //        val transitionEnable = once && content.type == UCMicEmojiMessage.TYPE_WEBP_AND_RESULT
            val transitionEnable = false
            if (transitionEnable) {
                ObjectAnimator.ofPropertyValuesHolder(
                    animView,
                    PropertyValuesHolder.ofFloat(View.SCALE_X, 1f, 0.2f),
                    PropertyValuesHolder.ofFloat(View.SCALE_Y, 1f, 0.2f),
                    PropertyValuesHolder.ofFloat(View.ALPHA, 1f, 0f),
                ).setDuration(150).suspendOnEnd(true)

                ObjectAnimator.ofPropertyValuesHolder(
                    resultView,
                    PropertyValuesHolder.ofFloat(View.ALPHA, 0.5f, 1f),
                    PropertyValuesHolder.ofFloat(View.SCALE_X, 0.2f, 1f),
                    PropertyValuesHolder.ofFloat(View.SCALE_Y, 0.2f, 1f),
                ).apply {
                    doOnStart {
                        animView.isVisible = false
                        resultView.isVisible = true
                    }
                    duration = 150
                    interpolator = BounceInterpolator()
                }.suspendOnEnd(true)
            } else {
                animView.isVisible = false
                resultView.isVisible = true
            }

            if (once) {
                delay(1000)
            }
        }
    }

    override fun onRelease() {
        if (animView.context.asActivity?.isDestroyed == true) {
            return
        }
        Glide.with(animView).clear(animView)
        animView.setImageDrawable(null)

        Glide.with(resultView).clear(resultView)
        resultView.setImageDrawable(null)
    }

    fun showResult() {
        animView.isVisible = false
        resultView.isVisible = true
        resultView.scaleX = 1f
        resultView.scaleY = 1f
        resultView.alpha = 1f
        Glide.with(resultView).load(content.emojiResult).into(resultView)
    }
}

@SuppressLint("ViewConstructor")
class EvaEmojiView(context: Context, private val file: File) : EvaAnimViewV3(context), AnimatedEmojiView {

    init {
        setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
    }

    context(CoroutineScope)
    override suspend fun playAnimateEmoji(once: Boolean) {
        suspendCancellableCoroutine {
            setAnimListener(object : IEvaAnimListener {
                override fun onFailed(errorType: Int, errorMsg: String?) {
                    it.resumeIfActive(Unit)
                }

                override fun onVideoComplete(lastFrame: Boolean) {
                    it.resumeIfActive(Unit)
                }

                override fun onVideoDestroy() = Unit

                override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) = Unit

                override fun onVideoRestart() = Unit

                override fun onVideoStart(isRestart: Boolean) = Unit

            })

            it.invokeOnCancellation { _ ->
                setAnimListener(null)
                stopPlay()
            }

            startPlay(file)
        }

    }

    override fun onRelease() {
        setAnimListener(null)
        stopPlay()
    }
}

class MicEmojiController(
    private val containerView: ViewGroup,
    private val roomPlayerLayout: View,
    private val chatRoomViewModel: ChatRoomViewModel,
) {

    private val emojiStateList = mutableListOf<MicEmojiState>()

    private val eventFlow = MutableStateFlow(0)

    init {
        chatRoomViewModel.viewModelScope.launch {
            var first = true
            chatRoomViewModel.uiState.map {
                it.roomState.roomMode
            }.distinctUntilChanged().onEach {
                if (first) {
                    first = false
                    return@onEach
                }
                emojiStateList.forEach {
                    it.cancel()
                }
                emojiStateList.clear()
            }.launchIn(this)

            chatRoomViewModel.uiState.map {
                it.roomState to run {
                    buildMap {
                        it.micListState.getOrNull()?.forEachIndexed { index, it ->
                            if (it.hasUser) {
                                put(it.user.id, index)
                            }
                        }
                    }
                }
            }.distinctUntilChangedBy { // 做去重是为了防止刷新频繁
                it.second
            }.combine(eventFlow) { (room, indexMap), _ ->
                emojiStateList.removeIfCompat {
                    val index = indexMap[it.userId]
                    if (index != null) {
                        if (!it.showed) {
                            show(room, index, it)
                            false
                        } else {
                            if (index != it.showIndex) {
                                it.cancel()
                                !it.showed
                            } else {
                                false
                            }
                        }
                    } else {
                        it.cancel()
                        !it.showed
                    }
                }
            }.launchIn(this)
        }
    }

    fun addMicEmojiState(emojiState: MicEmojiState) {
        IMUtils.launchOnMain {
            emojiStateList.removeIfCompat {
                if (it.userId == emojiState.userId) {
                    it.cancel()
                    !it.showed
                } else {
                    false
                }
            }

            emojiStateList.add(emojiState)
            eventFlow.update {
                it + 1
            }
        }
    }

    private fun show(room: Room, index: Int, emojiState: MicEmojiState) {
        val anchorView = ChatRoomGiftViewModel.findMicViewById(roomPlayerLayout, room, emojiState.userId, index) ?: return
        val toPointF = anchorView.getViewCoordsByView()
        val context = containerView.context
        val width = anchorView.width.times(0.7).toInt()
        val height = anchorView.height.times(0.7).toInt()
        val parentView = ShapeFrameLayout(context).apply {
            setBackgroundColor(0xCC000000.toInt())
            updateRadius(1000f)
            updateStroke(1.dp, Color.WHITE)
        }
        containerView.addView(parentView, MarginLayoutParams(width, height).also {
            it.leftMargin = toPointF.x.toInt() - width / 2
            it.topMargin = toPointF.y.toInt() - height / 2
        })

        emojiState.show(parentView, index) {
            emojiStateList.removeIfCompat {
                it.userId == emojiState.userId && it.identifier == emojiState.identifier
            }
            containerView.removeView(parentView)
        }
    }

    /**
     * 获取当前视图在窗口中的坐标
     * 此函数用于计算视图相对于窗口的中心点坐标
     *
     * @return PointF 返回视图在窗口中的中心点坐标
     */
    private fun View.getViewCoordsByView(): PointF {
        return this.let {
            // 创建一个IntArray来存储视图左上角在窗口中的坐标
            val loc = IntArray(2)
            // 获取视图左上角在窗口中的坐标
            it.getLocationInWindow(loc)
            // 计算并返回视图在窗口中的中心点坐标
            PointF(loc[0].plus(it.width.div(2f)), loc[1].plus(it.height.div(2f)))
        }
    }

}

data class MicEmojiState(val content: EmojiMessageContent) {

    val identifier = SystemClock.elapsedRealtime()

    val userId: String
        get() = content.user.id

    var showed = false
        private set

    var showIndex = -1
        private set

    private var job: Job? = null

    private var canceled = false

    fun show(parentView: ViewGroup, index: Int, onFinish: () -> Unit) {
        if (canceled) {
            return
        }
        val emojiView = AnimatedEmojiView.Factory.create(parentView.context, content)
        if (emojiView !is View) {
            onFinish()
            return
        }
        showIndex = index
        showed = true
        val width = parentView.layoutParams.width
        val height = parentView.layoutParams.height
        parentView.addView(
            emojiView,
            FrameLayout.LayoutParams(width.times(0.6).toInt(), height.times(0.6).toInt()).also {
                it.gravity = Gravity.CENTER
            }
        )

        job = emojiView.launchWhenAttach {
            try {
                withTimeoutOrNull(6000) {
                    emojiView.playAnimateEmoji(true)
                }
            } finally {
                withContext(NonCancellable) {
                    yield()
                    emojiView.onRelease()
                    onFinish()
                }
            }
        }
    }

    fun cancel() {
        canceled = true
        job?.cancel()
        job = null
    }
}

