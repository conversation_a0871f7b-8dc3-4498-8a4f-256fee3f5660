package com.qyqy.ucoo.compose.state

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import java.io.Serializable

sealed class StateValue {
    data object Idle : StateValue()
    data object Refreshing : StateValue()
    data object LoadMore : StateValue()
    data object Success : StateValue()
    data class Error(val message: String?) : StateValue()
    data class Empty(val message: String?) : StateValue()
}

val StateValue.isLoading: Boolean
    get() = this is StateValue.LoadMore

val StateValue.isRefreshing: Boolean
    get() = this is StateValue.Refreshing

val StateValue.isError: Boolean
    get() = this is StateValue.Error


val StateValue.isSuccess: Boolean
    get() = this is StateValue.Success

val StateValue.isEmpty: Boolean
    get() = this is StateValue.Empty


@Stable
class ContentState(
    initialValue: StateValue,
    private val onStateChanged: (StateValue) -> Unit = {}
) {

    var current: StateValue by mutableStateOf(initialValue)
        private set

    fun setValue(stateValue: StateValue) {
        if (current != stateValue) {
            current = stateValue
            onStateChanged(current)
        }

    }

}

interface IEmpty {

    @get:Composable
    val text: String?

    @get:Composable
    val icon: Painter?
}


val IEmpty.isError: Boolean
    get() = this is IError


interface IError : IEmpty {
    val throwable: Throwable?
}

class Empty : IEmpty {

    private var _text: String? = null

    private var _textResId: Int = -1

    private var _iconPainter: Painter? = null

    private var _iconResId: Int = -1

    constructor(text: String, @DrawableRes iconResId: Int = R.drawable.ic_empty_for_conversation) {
        this._text = text
        this._iconResId = iconResId
    }

    constructor(text: String, painter: Painter) {
        this._text = text
        this._iconPainter = painter
    }

    constructor(
        @StringRes textResId: Int = R.string.什么都没有,
        @DrawableRes iconResId: Int = R.drawable.ic_empty_for_conversation
    ) {
        this._textResId = textResId
        this._iconResId = iconResId
    }

    constructor(@StringRes textResId: Int = R.string.什么都没有, painter: Painter) {
        this._textResId = textResId
        this._iconPainter = painter
    }

    override val text: String
        @Composable
        get() = _text ?: stringResource(id = _textResId)

    override val icon: Painter
        @Composable
        get() = _iconPainter ?: painterResource(id = _iconResId)

}


interface IEmptyOwner {

    @get:Composable
    val empty: Empty?
        get() = null
}

sealed interface UIState<out T> {

    @Stable
    class Loading : UIState<Nothing> {

        private var _text: String? = null

        private var _textResId: Int = -1

        constructor(text: String) {
            _text = text
        }

        constructor(textResId: Int) {
            _textResId = textResId
        }

        val text: String
            @Composable
            get() = _text ?: stringResource(id = _textResId)
    }

    data class Error constructor(
        private val empty: Empty,
        override val throwable: Throwable? = null,
    ) : UIState<Nothing>, IError, IEmpty by empty {
        constructor(throwable: Throwable? = null) : this(Empty(), throwable)
    }

    class Data<T>(val value: T) : UIState<T>

    val data: T?
        get() = if (this is Data) {
            value
        } else null

    val isLoading: Boolean
        get() = this is Loading

    val isError: Boolean
        get() = this is Error
}

fun <T> Result<T>.toUIState(): UIState<T> =
    if (this.isSuccess) UIState.Data(this.getOrThrow())
    else UIState.Error(this.exceptionOrNull())

fun <T : Any?> T.toUIData() = UIState.Data(this)

@Stable
@Composable
fun <T : IEmptyOwner> UIStateContent(
    uiState: UIState<T>,
    loading: @Composable UIState.Loading.() -> Unit = {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            CircularProgressIndicator(
                modifier = Modifier.requiredSize(48.dp),
                color = colorResource(id = R.color.white)
            )
            if (text.isNotEmpty()) {
                Text(
                    text = text,
                    modifier = Modifier.padding(top = 10.dp),
                    color = colorResource(id = R.color.white),
                    fontSize = 16.sp
                )
            }
        }
    },
    onEmptyOrError: (IEmpty) -> Unit = {},
    emptyOrError: @Composable IEmpty.() -> Unit = {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .noEffectClickable {
                    onEmptyOrError(this)
                },
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
        ) {
            icon?.also {
                Image(painter = it, contentDescription = null)
            }
            text?.also {
                Text(
                    text = it,
                    color = colorResource(id = R.color.white),
                    fontSize = 16.sp
                )
            }
        }
    },
    content: @Composable UIState.Data<T>.() -> Unit,
) {
    AnimatedContent(targetState = uiState, label = "UIStateContent", contentKey = {
        when (it) {
            is UIState.Loading -> 0
            is UIState.Error -> 1
            else -> 2
        }
    }) { data ->
        when (data) {
            is UIState.Loading -> {
                loading(data)
            }

            is UIState.Error -> {
                emptyOrError(data)
            }

            is UIState.Data -> {
                data.value.empty?.also {
                    emptyOrError(it)
                } ?: content(data)
            }
        }
    }
}

fun loadingUiState(text: String = "") = UIState.Loading(text)