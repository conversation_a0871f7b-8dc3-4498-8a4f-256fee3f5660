@file:OptIn(ExperimentalComposeApi::class)

package com.qyqy.ucoo.compose.presentation.cp_house

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ExperimentalComposeApi
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastForEachReversed
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asViewModelStoreOwner
import com.qyqy.ucoo.compose.data.CPMemoriesBean
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.router.UCOOPreviewTheme
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.timeline.EventPosition
import com.qyqy.ucoo.compose.ui.timeline.ItemsList
import com.qyqy.ucoo.compose.ui.timeline.JetLimeColumn
import com.qyqy.ucoo.compose.ui.timeline.JetLimeDefaults
import com.qyqy.ucoo.compose.ui.timeline.JetLimeEventDefaults
import com.qyqy.ucoo.compose.ui.timeline.JetLimeExtendedEvent
import com.qyqy.ucoo.compose.vm.cp_house.CPHouseViewModel
import com.qyqy.ucoo.home.main.MySwitch
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale

private sealed interface MemoriesUIAction {
    object ClickShowSetting : MemoriesUIAction

    object ClickPopBackAction : MemoriesUIAction//点击回退
}

@Composable
fun CPHouseMemoriesScreen(room_id: Int, modifier: Modifier = Modifier) {
    var isShowHomeSetting by remember {
        mutableStateOf(false)
    }

    val context = LocalContext.current
    val viewModel = viewModel(modelClass = CPHouseViewModel::class, factory = viewModelFactory {
        initializer {
            CPHouseViewModel(room_id = room_id)
        }
    }, viewModelStoreOwner = context.asViewModelStoreOwner(LocalViewModelStoreOwner.current!!))

    LaunchedEffect(key1 = Unit) {
        viewModel.refreshCpHouseMemories()
    }

    val houseInfo by viewModel.houseInfo.collectAsStateWithLifecycle()
    val memoriesInfo by viewModel.memoriesInfo.collectAsStateWithLifecycle()


    val controller = LocalUCOONavController.current
    LoadingLayout {
        AppTheme {
            CPHouseMemoriesContent(bean = memoriesInfo) {
                when (it) {
                    MemoriesUIAction.ClickShowSetting -> {
                        isShowHomeSetting = true
                    }

                    MemoriesUIAction.ClickPopBackAction -> {
                        controller.popBackStack()
                    }
                }
            }
        }


        val loadingFlag = LocalContentLoading.current
        if (isShowHomeSetting) {
            AnimatedDialog(
                isActiveClose = false,
                properties = AnyPopDialogProperties(direction = DirectionState.CENTER),
                onDismiss = { isShowHomeSetting = false }
            ) {
                MemoriesSettingContent(houseInfo.isHidden, {
                    loadingFlag.value = true
                    viewModel.updateHouseVisible(it) {
                        loadingFlag.value = false
                    }
                }, {
                    isShowHomeSetting = false
                })
            }
        }
    }
}

@Composable
private fun CPHouseMemoriesContent(bean: CPMemoriesBean, onAction: (MemoriesUIAction) -> Unit = {}) {
    Column(
        modifier = Modifier
            .background(color = Color(0xFF1C1D1E))
            .fillMaxSize(1f)
            .systemBarsPadding()
    ) {
        AppTitleBar(
            title = stringResource(id = R.string.与Ta的独家记忆),
            onBack = {
                onAction(MemoriesUIAction.ClickPopBackAction)
            }
        ) {
            Text(
                stringResource(id = R.string.展示设置), color = Color.White, modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 8.dp)
                    .click {
                        onAction(MemoriesUIAction.ClickShowSetting)
                    }
            )
        }

        Column(
            modifier = Modifier
                .padding(top = 16.dp)
                .padding(horizontal = 16.dp)
                .fillMaxWidth()
                .background(color = Color(0xFF272829), shape = RoundedCornerShape(8.dp)),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            //region 顶部信息
            Text(
                buildAnnotatedString {
                    append(stringResource(id = R.string.陪伴))
                    withStyle(
                        SpanStyle(
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold,
                        )
                    ) {
                        append(bean.togetherDays.toString())
                    }
                    append(stringResource(id = R.string.day))
                }, modifier = Modifier
                    .padding(top = 16.dp)
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                color = Color.White,
                fontSize = 14.sp
            )

            Box(
                modifier = Modifier
                    .padding(top = 16.dp)
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .height(8.dp)
            ) {
                Spacer(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(color = Color(0xFF3D3E3E), shape = RoundedCornerShape(8.dp))
                )
                Spacer(
                    modifier = Modifier
                        .fillMaxHeight()
                        .fillMaxWidth(Math.min(1f, bean.intimacyScore / bean.nextLevelIntimacyScore.toFloat()))
                        .background(color = Color(0xFF34EDF9), shape = RoundedCornerShape(8.dp))
                )
            }
            Text(
                buildAnnotatedString {
                    withStyle(
                        SpanStyle(
                            color = Color(0xFF34EDF9)
                        )
                    ) {
                        append(bean.intimacyScore.toString())
                    }
                    append("/")
                    append(bean.nextLevelIntimacyScore.toString())
                }, modifier = Modifier
                    .padding(top = 8.dp)
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth(),
                color = Color.White,
                fontSize = 14.sp,
                lineHeight = 14.sp
            )
            //endregion

            Row(horizontalArrangement = Arrangement.Center, modifier = Modifier.padding(bottom = 8.dp)) {
                Column(
                    modifier = Modifier
                        .padding(top = 16.dp)
                        .width(72.dp), horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ComposeImage(
                        model = bean.maleUser.avatarUrl, modifier = Modifier
                            .size(64.dp)
                            .clip(CircleShape)
                            .border(1.dp, color = Color(0xffff5e8b), shape = CircleShape)
                    )
                    Text(
                        text = bean.maleUser.nickname, maxLines = 1, overflow = TextOverflow.Ellipsis,
                        fontSize = 14.sp, lineHeight = 14.sp, color = Color.White
                    )
                }

                ComposeImage(
                    model = R.drawable.ic_house_memories_arrow, modifier = Modifier
                        .padding(top = 34.dp)
                        .size(28.dp)
                )
                ComposeImage(model = bean.house.icon, modifier = Modifier.size(80.dp))
                ComposeImage(
                    model = R.drawable.ic_house_memories_arrow, modifier = Modifier
                        .padding(top = 34.dp)
                        .size(28.dp)
                        .rotate(180f)
                )

                Column(
                    modifier = Modifier
                        .padding(top = 16.dp)
                        .width(72.dp), horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ComposeImage(
                        model = bean.femaleUser.avatarUrl, modifier = Modifier
                            .size(64.dp)
                            .clip(CircleShape)
                            .border(1.dp, color = Color(0xffff5e8b), shape = CircleShape)
                    )
                    Text(
                        text = bean.femaleUser.nickname, maxLines = 1, overflow = TextOverflow.Ellipsis,
                        fontSize = 14.sp, lineHeight = 14.sp, color = Color.White
                    )
                }
            }

            //region timeline
            if (bean.history.isNotEmpty()) {
                Text(
                    text = stringResource(id = R.string.独家回忆),
                    modifier = Modifier.paint(painterResource(id = R.drawable.ic_house_memory_title)),
                    color = Color.White, fontSize = 12.sp, lineHeight = 20.sp,
                    fontWeight = FontWeight.SemiBold, textAlign = TextAlign.Center
                )
                Spacer(
                    modifier = Modifier
                        .size(2.dp, 8.dp)
                        .background(color = Color(0xffff5e8b))
                )
                JetLimeColumn(
                    modifier = Modifier.width(((150 * 2) + 8 * 2 + 6 + 1.5).dp),
                    itemsList = ItemsList(bean.history),
                    style = JetLimeDefaults.columnStyle(
                        contentDistance = 8.dp,
                        lineBrush = Brush.verticalGradient(listOf(Color(0xffff5e8b), Color(0xffff5e8b))),
                        lineThickness = 2.dp
                    ),
                    contentPadding = PaddingValues(horizontal = 4.dp),
                ) { index, item, position ->
                    JetLimeExtendedEvent(
                        style = JetLimeEventDefaults.eventStyle(
                            position = EventPosition.MIDDLE,
                            pointFillColor = Color.White,
                            pointRadius = 6.dp,
                            pointStrokeColor = Color(0xFFFF5E8B),
                            pointStrokeWidth = 2.dp,
                        ),
                        additionalContentMaxWidth = 150.dp,
                        additionalContent = {
                            Box(
                                modifier = Modifier
                                    .width(150.dp)
                            ) {
                                if (item.isLeft) {
                                    MemoriesItem(item)
                                }
                            }
                        },
                    ) {
                        if (!item.isLeft) {
                            MemoriesItem(item)
                        }
                    }
                }
            }
            //endregion
        }
    }
}

@Composable
private fun MemoriesItem(item: CPMemoriesBean.History) {
    Column(
        modifier = Modifier
            .padding(bottom = if (item.isLeft) 8.dp else 0.dp)
            .width(150.dp)
            .background(
                color = Color(0xff3d3e3e),
                shape = RoundedCornerShape(
                    if (item.isLeft) 8.dp else 0.dp,
                    if (item.isLeft) 0.dp else 8.dp,
                    8.dp,
                    8.dp
                )
            )
            .padding(vertical = 12.dp, horizontal = 8.dp)
    ) {
        Text(buildAnnotatedString {
            append(SimpleDateFormat("yyyy-MM-dd ", Locale.getDefault()).format(Date(item.create_timestamp * 1000)))
        }, fontSize = 14.sp, lineHeight = 14.sp, color = Color.White, fontWeight = FontWeight.Bold)
        val annotatedString = buildAnnotatedString {
            appendInlineContent("prefixIcon")
            append(item.content)
        }
        val inlineContentMap = mapOf(
            "prefixIcon" to InlineTextContent(
                Placeholder(
                    width = 8.sp,
                    height = 4.sp,
                    placeholderVerticalAlign = PlaceholderVerticalAlign.TextCenter
                )
            ) {
                Spacer(
                    modifier = Modifier
                        .size(4.dp)
                        .background(color = Color(0xFF34EDF9), shape = CircleShape)
                )
            }
        )
        Text(
            text = annotatedString, inlineContent = inlineContentMap,
            color = Color.White, fontSize = 12.sp, lineHeight = 12.sp, fontWeight = FontWeight.Medium
        )
    }
}

@Composable
private fun MemoriesSettingContent(isSwitcherOpened: Boolean, onValueChanged: (Boolean) -> Unit, onDismiss: () -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxWidth(0.8f)
            .background(color = Color(0xFF222222))
            .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            stringResource(id = R.string.独家回忆), color = Color.White, fontSize = 17.sp,
            lineHeight = 17.sp, fontWeight = FontWeight.Medium
        )
        Row(
            modifier = Modifier
                .padding(vertical = 20.dp, horizontal = 16.dp)
                .background(color = Color(0x0dffffff), shape = RoundedCornerShape(8.dp))
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    stringResource(id = R.string.隐藏CP小屋), color = Color.White,
                    fontSize = 16.sp, lineHeight = 16.sp
                )
                Spacer(modifier = Modifier.height(8.dp))
                Text(
                    stringResource(id = R.string.隐藏后无法看到你的小屋), color = Color(0x80FFFFFF),
                    fontSize = 12.sp, lineHeight = 18.sp
                )
            }
            Spacer(modifier = Modifier.width(12.dp))
            MySwitch(active = isSwitcherOpened, inactivedColor = Color(0xFF575757)) {
                onValueChanged(!isSwitcherOpened)
            }
        }
        AppButton(text = stringResource(id = R.string.我知道了), background = Color(0xFF945EFF), color = Color.White, contentPadding = PaddingValues(vertical = 8.dp, horizontal = 24.dp)) {
            onDismiss()
        }
    }
}

@Composable
@Preview
private fun CPHouseMemoriesScreenPreview() {
    UCOOPreviewTheme {
        CPHouseMemoriesContent(
            CPMemoriesBean(
                maleUser = userForPreview,
                femaleUser = userForPreview,
                history = listOf(
                    CPMemoriesBean.History(
                        create_timestamp = 1722666218,
                        content = "今天，我与她一起，在CP小屋中，完成了一个重要的任务。",
                    ),
                    CPMemoriesBean.History(
                        create_timestamp = 1722666218,
                        content = "今天，我与她一起，在CP小屋中，完成了一个重要的任务。",
                    ),
                    CPMemoriesBean.History(
                        create_timestamp = 1722666218,
                        content = "今天，我与她一起，在CP小屋中，完成了一个重要的任务。",
                    ),
                    CPMemoriesBean.History(
                        create_timestamp = 1722666218,
                        content = "今天，我与她一起，在CP小屋中，完成了一个重要的任务。",
                    ),
                )
            )
        )
    }
}

@Composable
@Preview
private fun MemoriesItemPreview() {
    UCOOPreviewTheme {
        MemoriesSettingContent(false, {}, {})
    }
}