package com.qyqy.ucoo.compose.presentation.member

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.formatTimeWithHours
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.sUserKV
import com.qyqy.ucoo.user.ActivateOption
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlin.time.Duration.Companion.hours
import kotlin.time.DurationUnit

@Composable
fun MemberPriceOptionList(
    selectedMemberOption: ActivateOption?,
    list: List<ActivateOption>,
    onSelectChange: (ActivateOption) -> Unit,
    modifier: Modifier = Modifier,
) {
    LazyRow(
        modifier = modifier.selectableGroup(),
        state = rememberLazyListState(),
        contentPadding = PaddingValues(horizontal = 16.5.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(items = list, key = {
            "priceOption-${it.productId}"
        }) { item ->
            MemberPriceOptionItem(memberOption = item, selected = selectedMemberOption?.productId == item.productId, onClick = {
                onSelectChange(item)
            })
        }
    }
}

@Composable
fun MemberPriceOptionItem(
    memberOption: ActivateOption,
    selected: Boolean,
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
) {
    Box(
        modifier = modifier
            .size(96.dp, 126.dp)
            .clip(Shapes.corner12)
            .selectable(
                selected = selected,
                onClick = onClick,
                enabled = true,
                role = Role.RadioButton,
                interactionSource = interactionSource,
                indication = null,
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(
                    Brush.linearGradient(
                        if (selected) {
                            listOf(Color(0xFFFFEFD4), Color(0xFFFFC46C))
                        } else {
                            listOf(Color(0xFF4C4C4C), Color(0xFF333333))
                        }, Offset(0f, 0.0f), Offset(Float.POSITIVE_INFINITY, Float.POSITIVE_INFINITY)
                    )
                )
                .border(
                    if (selected) 1.5.dp else 0.5.dp,
                    if (selected) Color(0xFFA1732B) else Color(0xFF625F5A),
                    Shapes.corner12
                ),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(20.dp)
            ) {
                if (memberOption.label.isNotEmpty()) {
                    Box(
                        modifier = Modifier
                            .fillMaxHeight()
                            .clip(RoundedCornerShape(topStart = 8.dp, bottomEnd = 8.dp))
                            .background(Brush.horizontalGradient(listOf(Color(0xFFFF7070), Color(0xFFF53F3F))))
                            .padding(horizontal = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        AppText(
                            text = memberOption.label,
                            color = Color.White,
                            fontSize = 10.sp,
                        )
                    }
                }
            }
            val textColor = if (selected) Color(0xFF6F4F23) else Color(0xFFFFDBA9)
            AppText(text = memberOption.equity, color = textColor, fontSize = 14.sp, modifier = Modifier.padding(top = 2.dp))
            AutoSizeText(text = buildAnnotatedString {
                append(memberOption.transformPrice)
                "\\d+(\\.\\d+)?".toRegex().find(memberOption.transformPrice)?.also {
                    addStyle(
                        SpanStyle(fontSize = 16.sp, fontWeight = FontWeight.Medium, fontFamily = null),
                        0,
                        it.range.first
                    )
                }
            }, modifier = Modifier.padding(horizontal = 1.5.dp), color = textColor, fontSize = 24.sp, fontWeight = FontWeight.Bold, fontFamily = D_DIN)
            AppText(
                text = memberOption.extraNote,
                fontSize = 12.sp,
                color = Color(if (selected) 0xFF6F4F23 else 0xFF74695A),
                textDecoration = if (memberOption.extraNoteDel) TextDecoration.LineThrough else null
            )

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(24.dp)
            ) {
                if (memberOption.needCountDown) {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(24.dp)
                            .background(Brush.horizontalGradient(listOf(Color(0xFFFF7070), Color(0xFFF53F3F)))),
                        contentAlignment = Alignment.Center
                    ) {
                        val start by remember {
                            val k = "ucoo_price_tick_start_mills"
                            var s = 0L
                            if (!isPreviewOnCompose) {
                                s = sUserKV.getLong(k, 0)
                                if (s == 0L) {
                                    s = System.currentTimeMillis()
                                    sUserKV.putLong(k, s)
                                }
                            }
                            mutableLongStateOf(s)
                        }
                        var text by remember {
                            mutableStateOf("")
                        }
                        val context = LocalContext.current
                        val scope = rememberCoroutineScope()
                        LaunchedEffect(key1 = Unit) {
                            scope.launch {
                                while (true) {
                                    val mills24Hour = 24.hours.toLong(DurationUnit.MILLISECONDS)
                                    val time = mills24Hour - (System.currentTimeMillis() - start) % (mills24Hour)
                                    text = context.getString(R.string.time_limit, formatTimeWithHours(time))
                                    delay(1000L)
                                }
                            }
                        }
                        AppText(text = text, color = Color.White, fontSize = 12.sp)
                    }
                }
            }
        }
    }
}

@Preview
@Composable
fun PriceOptionItemPreview() {
    val selectItem =
        ActivateOption("40", "周会员", "原$4.99", "周会员", "11", 2, "", "mark", "", "", 2, needCountDown = true, true)
    MemberPriceOptionList(selectItem, listOf(
        selectItem,
        ActivateOption("40", "", "备注", "周会员", "12", 2, "", "mark", "", "", 2),
        ActivateOption("40", "", "备注", "周会员", "13", 2, "", "mark", "", "", 2)
    ), {})
}