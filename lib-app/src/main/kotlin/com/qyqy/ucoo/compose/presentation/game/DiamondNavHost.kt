package com.qyqy.ucoo.compose.presentation.game

import android.content.Context
import android.os.Bundle
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.safeDrawingPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.router.IDestination
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.state.Empty
import com.qyqy.ucoo.compose.state.IEmptyOwner
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toastRes
import kotlinx.coroutines.launch
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import java.text.SimpleDateFormat
import java.util.Locale


sealed interface UIAction {

    data object OnMall : UIAction
    data object OnHistory : UIAction

    data class OnStartGame(val id: Int, val router: String) : UIAction

    data class OnRouterByLink(val link: String) : UIAction

    data class OnExchangeOfDiamond(val id: Int) : UIAction

    data object Retry : UIAction

}

sealed class DiamondDestination : IDestination {

    /**
     * 我的钻石
     */
    data object My : DiamondDestination() {

        override val route = "my-diamond"
    }

    /**
     * 钻石商城
     */
    data object Mall : DiamondDestination() {

        override val route = "diamond-mall"

    }

    /**
     * 钻石明细
     */
    data object History : DiamondDestination() {

        override val route = "diamond-history"

    }
}

object DiamondScreenNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        DiamondScreen(
            when (bundle.getString(Const.KEY_ID)) {
                DiamondDestination.Mall.route -> DiamondDestination.Mall
                else -> DiamondDestination.My
            }
        )
    }

    fun navigate(context: Context, start: DiamondDestination) {
        navigate(context) {
            putExtra(Const.KEY_ID, start.route)
        }
    }
}

@Composable
fun DiamondScreen(startDestination: DiamondDestination) {
    val navController = rememberNavController()

    val context = LocalContext.current

    val gameDiamondApi = remember {
        createApi<GameDiamondApi>()
    }

    var diamond by remember {
        mutableIntStateOf(app.accountManager.accountFlow.value.accountInfo.diamond)
    }

    var uiState1 by remember {
        mutableStateOf<UIState<GameList>>(UIState.Loading(R.string.加载中))
    }

    var uiState2 by remember {
        mutableStateOf<UIState<ExchangeList>>(UIState.Loading(R.string.加载中))
    }

    NavHost(
        navController = navController,
        startDestination = startDestination.route,
        modifier = Modifier
            .fillMaxSize()
            .safeDrawingPadding(),
        enterTransition = {
            slideInHorizontally(initialOffsetX = { it })
        },
        exitTransition = {
            slideOutHorizontally(targetOffsetX = { -it })
        },
        popEnterTransition = {
            slideInHorizontally(initialOffsetX = { -it })
        },
        popExitTransition = {
            slideOutHorizontally(targetOffsetX = { it })
        },
    ) {
        composable(route = DiamondDestination.My.route) {
            if (uiState1 is UIState.Loading) {
                LaunchedEffect(key1 = Unit) {
                    runApiCatching {
                        gameDiamondApi.fetchGameList()
                    }.onSuccess {
                        diamond = it.diamondAccount.diamond
                        uiState1 = UIState.Data(it)
                    }.onFailure {
                        uiState1 = UIState.Error(Empty(R.string.加载失败点击重试, R.drawable.ic_empty_for_fans), throwable = it)
                    }
                }
            }
            MyDiamondPage(diamond = diamond, uiState = uiState1) {
                when (it) {
                    is UIAction.Retry -> uiState1 = UIState.Loading(R.string.加载中)
                    is UIAction.OnMall -> navController.navigateTo(DiamondDestination.Mall)
                    is UIAction.OnStartGame -> {
//                        AppLinkManager.open(context, it.router)
                    }

                    else -> Unit
                }
            }
        }
        composable(route = DiamondDestination.Mall.route) {
            if (uiState2 is UIState.Loading) {
                LaunchedEffect(key1 = Unit) {
                    runApiCatching {
                        gameDiamondApi.fetchExchangeList()
                    }.onSuccess {
                        diamond = it.diamondAccount.diamond
                        uiState2 = UIState.Data(it)
                    }.onFailure {
                        uiState2 = UIState.Error(Empty(R.string.加载失败点击重试, R.drawable.ic_empty_for_fans), throwable = it)
                    }
                }
            }

            var showLoading by remember {
                mutableStateOf(false)
            }
            val coroutineScope = rememberCoroutineScope()

            Box {
                DiamondMallPage(diamond = diamond, uiState = uiState2) {
                    when (it) {
                        is UIAction.Retry -> uiState2 = UIState.Loading(R.string.加载中)
                        is UIAction.OnRouterByLink -> AppLinkManager.open(context, it.link)
                        is UIAction.OnExchangeOfDiamond -> {
                            coroutineScope.launch {
                                showLoading = true
                                runApiCatching {
                                    gameDiamondApi.postDiamondExchange(mapOf("item_id" to it.id.toString()))
                                }.onSuccess { json ->
                                    toastRes(R.string.兑换成功)
                                    json.getIntOrNull("diamond_balance")?.apply {
                                        diamond = this
                                    }
                                }.toastError()
                                showLoading = false
                            }
                        }

                        is UIAction.OnHistory -> navController.navigateTo(DiamondDestination.History)

                        else -> Unit
                    }
                }

                if (showLoading) {
                    Loading {
                        showLoading = false
                    }
                }
            }
        }

        composable(route = DiamondDestination.History.route) {
            DiamondHistoryPage(request = {
                runApiCatching {
                    gameDiamondApi.fetchDiamondHistoryList(it)
                }
            })
        }
    }
}

@Stable
@Serializable
data class GameList(
    @SerialName("entries")
    val items: List<GameItem>,
    @SerialName("user_account")
    val diamondAccount: DiamondAccount = DiamondAccount(),
) : IEmptyOwner {
    override val empty: Empty?
        @Composable
        @ReadOnlyComposable
        get() = if (items.isEmpty()) {
            Empty(stringResource(id = R.string.什么都没有))
        } else {
            null
        }
}

@Stable
@Serializable
data class ExchangeList(
    val items: List<ExchangeItem>,
    @SerialName("user_account")
    val diamondAccount: DiamondAccount = DiamondAccount(),
) : IEmptyOwner {
    override val empty: Empty?
        @Composable
        @ReadOnlyComposable
        get() = if (items.isEmpty()) {
            Empty(stringResource(id = R.string.什么都没有))
        } else {
            null
        }
}

@Serializable
data class DiamondAccount(
    val diamond: Int = 0, // 0
    @SerialName("rule_link")
    val ruleLink: String = "", // https://api.test.ucoofun.com/h5/diamondrule
    @SerialName("rule_text")
    val ruleText: String = "", // 查看如何获得钻石
    val tooltips: String = "", // 温馨提示：钻石有效期为7天，过期将无法使用，请即时前往钻石商城进行兑换
)

@Serializable
data class ExchangeItem(
    val id: Int, // 6
    val name: String, // 气泡
    val desc: String, // 龙年暴富·7天
    val price: Int, // 99999
    val icon: String = "", // https://media.ucoofun.com/wallet/mall/chat_bubble_icon.png
    @SerialName("price_type")
    val priceType: Int = 1, // 1
    @SerialName("item_type")
    val itemType: Int = 5, // 5
    val number: Int = 1, // 7
    @SerialName("exchange_condition")
    val exchangeCondition: Int = 1, // 1
)

@Serializable
data class GameItem(
    val id: Int, // 1
    val name: String, // 极速赛车
    val img: String = "", // https://media.ucoofun.com/wallet/rewardconf/20240422-190902.jpeg
    val desc: String = "", // 极速赛车
    val link: String = "", // www.baidu.com
    val order: Int = 0, // 1
    val channel: Int = 0, // 1
    @SerialName("channel_id")
    val channelId: Int = 0, // 1
)

@Serializable
data class DiamondRecord(
    val id: Int,
    val title: String,
    val timestamp: Int,
    @SerialName("change_amount") val changeAmount: String,
) {
    private var _formatTime: String? = null

    val formatTime: String
        get() = _formatTime ?: SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(timestamp.times(1000L)).also {
            _formatTime = it
        }
}

@Stable
@Serializable
data class DiamondHistory(
    val items: List<DiamondRecord>,
    @SerialName("has_more")
    val hasMore: Boolean = false,
) : IEmptyOwner {
    override val empty: Empty?
        @Composable
        @ReadOnlyComposable
        get() = if (items.isEmpty()) {
            Empty(stringResource(id = R.string.什么都没有))
        } else {
            null
        }
}
