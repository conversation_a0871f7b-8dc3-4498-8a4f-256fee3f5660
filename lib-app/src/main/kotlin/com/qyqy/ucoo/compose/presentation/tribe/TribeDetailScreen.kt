package com.qyqy.ucoo.compose.presentation.tribe

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.presentation.chatgroup.page.ExpandableText
import com.qyqy.ucoo.compose.router.UCOOPreviewTheme
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppScrollableTabRow
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.compose.ui.verticalScrollWithScrollbar
import com.qyqy.ucoo.compose.vm.OnlineMemberListViewModel
import com.qyqy.ucoo.runWithTopActivity
import com.qyqy.ucoo.tribe.TribeDetailActivity
import com.qyqy.ucoo.tribe.bean.TribeConst
import com.qyqy.ucoo.tribe.bean.TribeInfo
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import com.qyqy.ucoo.tribe.bean.TribeUser
import com.qyqy.ucoo.tribe.task.v2.TribeTaskContainer

sealed interface TribeDetailUIAction {
    data object ClickBottomButton : TribeDetailUIAction
    data object JoinTribeAction : TribeDetailUIAction
    data class GoUserProfileAction(val user: User) : TribeDetailUIAction
    data class GoSettingsAction(val tribeInfo: TribeInfo) : TribeDetailUIAction
}

val LocalTribeInfoProvider = staticCompositionLocalOf { TribeInfo() }

@Composable
fun TribeDetailScreen(
    info: TribeInfo?,
    onAction: (TribeDetailUIAction) -> Unit
) {
    if (info == null) {
        return
    }
    val descDialogShow = remember {
        mutableStateOf(false)
    }

    val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher

    val backgroundColor = Color(0xff1C1D1E)

    BoxWithConstraints(
        modifier = Modifier
            .fillMaxSize()
    ) {
        val scrollState = rememberScrollState()
        var titleBarHeight by remember { mutableFloatStateOf(Float.NaN) }
        var topBoxHeight by remember { mutableFloatStateOf(Float.NaN) }
        var tabRowHeight by remember { mutableFloatStateOf(0f) }

        val scrollFraction by remember {
            derivedStateOf {
                if (topBoxHeight.isNaN() || titleBarHeight.isNaN()) {
                    return@derivedStateOf 0f
                }
                if (topBoxHeight <= 0) {
                    0f
                } else {
                    scrollState.value.div(topBoxHeight.minus(titleBarHeight)).coerceIn(0f, 1f)
                }
            }
        }

        val nestedScrollConnection = remember {
            object : NestedScrollConnection {
                override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                    if (available.y < 0 && scrollState.canScrollForward) {
                        val deltaY = -available.y
                        val consumedY = -scrollState.dispatchRawDelta(deltaY)
                        return available.copy(y = consumedY)
                    }
                    return Offset.Zero
                }
            }
        }

        val offsetY = 10.dp

        val minHeight by with(LocalDensity.current) {
            remember {
                derivedStateOf {
                    if (titleBarHeight.isNaN() || tabRowHeight.isNaN()) {
                        return@derivedStateOf <EMAIL>(offsetY)
                    }
                    <EMAIL>(titleBarHeight.toDp())
                        .minus(tabRowHeight.toDp())
                }
            }
        }

        Column(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(nestedScrollConnection)
                .verticalScroll(scrollState)
                .background(color = backgroundColor)
        ) {
            Box(modifier = Modifier.onSizeChanged {
                topBoxHeight = it.height.toFloat()
            }) {
                //顶部
                ComposeImage(
                    model = info.avatarUrl, contentDescription = "group info header img",
                    contentScale = ContentScale.FillWidth,
                    modifier = Modifier
                        .height(200.dp)
                        .fillMaxWidth()
                )
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier
                        .fillMaxWidth()
                ) {
                    Spacer(modifier = Modifier.height(176.dp))

                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier
                            .background(
                                color = backgroundColor,
                                shape = RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp)
                            )
                            .padding(top = 64.dp)
                    ) {
                        Text(
                            info.name ?: "", color = Color.White,
                            fontSize = 12.sp,
                        )
                        Text(
                            "ID:${info.publicId}",
                            color = Color(0x80FFFFFF),
                            fontSize = 12.sp,
                            modifier = Modifier.padding(top = 8.dp),
                            lineHeight = 12.sp,
                            fontWeight = FontWeight.Normal
                        )
                        ExpandableText(
                            text = info.bulletin.ifEmpty { stringResource(R.string.group_no_description) },
                            style = SpanStyle(color = Color(0x80FFFFFF))
                            // Composable 大小的动画效果
                            , modifier = Modifier
                                .padding(horizontal = 32.dp, vertical = 16.dp)
                                .animateContentSize(),
                            collapsedMaxLine = 4
                        ) {
                            descDialogShow.value = !descDialogShow.value
                        }

                        Spacer(modifier = Modifier.height(24.dp))

                        Spacer(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(8.dp)
                                .background(color = Color(0x0DFFFFFF))
                        )
                    }
                }
                //群组介绍
                Column(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Spacer(modifier = Modifier.height(128.dp))
                    ComposeImage(
                        model = info.avatarUrl, modifier = Modifier
                            .size(96.dp)
                            .clip(RoundedCornerShape(16.dp))
                    )
                }
            }

            if (info.isMyTribe) {
                val tabs by remember {
                    mutableStateOf(listOf(AppTab(app.getString(R.string.任务)), AppTab(app.getString(R.string.成员))))
                }
                val pagerState = rememberPagerState {
                    tabs.size
                }
                AppScrollableTabRow(
                    tabs = tabs,
                    pagerState = pagerState,
                    modifier = Modifier.onSizeChanged {
                        tabRowHeight = it.height.toFloat()
                    })
                HorizontalPager(state = pagerState, modifier = Modifier.height(minHeight), beyondViewportPageCount = 2) {
                    when (it) {
                        0 -> {
                            //任务
                            CompositionLocalProvider(LocalTribeInfoProvider provides info) {
                                TribeTaskContainer(modifier = Modifier.fillMaxSize())
                            }
                        }

                        1 -> {
                            //成员
                            TribeMemberListWidget(info = info, onAction)
                        }

                        else -> {}
                    }
                }
            } else {
                //未加入部落
                Column(
                    modifier = Modifier
                        .heightIn(max = minHeight)
                ) {
                    //群组成员信息title
                    Row(
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp, 24.dp)
                    ) {
                        Text(
                            stringResource(id = R.string.部落成员数量),
                            color = Color.White,
                            modifier = Modifier.weight(1f),
                            fontSize = 16.sp,
                            lineHeight = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            "${info.onlineMemberCnt}/${info.memberCnt}",
                            color = Color(0xff8c8c8d)
                        )


                    }
                    VerticalGrid(
                        columns = 5
                    ) {
                        info.firstPageMembers.take(5).forEachIndexed { index, memberItem ->
                            FirstPageMemberItemWidget(
                                member = memberItem,
                                index == 4,
                                modifier = Modifier.click {
                                    if (index == 4) {
//                                        onAction(TribeDetailUIAction.JoinTribeAction)
                                        runWithTopActivity {
                                            TribeDetailActivity.start(this, info)
                                        }
                                    } else {
                                        onAction(TribeDetailUIAction.GoUserProfileAction(memberItem.user.toAppUser()))
                                    }
                                })
                        }
                    }
                    Box(modifier = Modifier.click(noEffect = true) {
                        onAction(TribeDetailUIAction.JoinTribeAction)
                    }) {
                        Box {
                            ComposeImage(
                                model = R.drawable.ic_tribe_task_placeholder, modifier = Modifier
                                    .padding(vertical = 16.dp)
                                    .fillMaxWidth(),
                                contentScale = ContentScale.FillWidth
                            )
                        }
                        Row(
                            modifier = Modifier.align(Alignment.Center),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                stringResource(id = R.string.加入部落解锁任务),
                                fontSize = 16.sp,
                                lineHeight = 16.sp,
                                color = Color.White,
                                fontWeight = FontWeight.Medium
                            )
                            ComposeImage(model = R.drawable.ic_arrow_right)
                        }
                    }
                }
            }
        }

        Box(
            Modifier
                .onSizeChanged {
                    titleBarHeight = it.height.toFloat()
                }
                .drawBehind {
                    drawRect(backgroundColor.copy(alpha = scrollFraction))
                }
                .fillMaxWidth()
                .windowInsetsPadding(WindowInsets.statusBars)
        ) {
            ComposeImage(model = R.drawable.ic_arrow_left, modifier = Modifier
                .size(48.dp)
                .padding(12.dp)
                .click {
                    onBackPressedDispatcher?.onBackPressed()
                })
            Text(
                stringResource(id = R.string.部落资料), modifier = Modifier
                    .align(Alignment.Center)
                    .alpha(scrollFraction),
                fontSize = 18.sp, lineHeight = 26.sp, color = Color.White
            )
            if (info.isMyTribe) {
                ComposeImage(model = R.drawable.ic_settings, modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .size(48.dp)
                    .click {
                        onAction(TribeDetailUIAction.GoSettingsAction(info))
                    }
                    .padding(12.dp))
            }
        }

        if (descDialogShow.value) {
            var descScrollState = rememberScrollState()
            Dialog(onDismissRequest = {
                descDialogShow.value = false
            }) {
                Column(
                    modifier = Modifier
                        .size(270.dp, 320.dp)
                        .background(color = Color(0xff222222), shape = RoundedCornerShape(8.dp))
                        .padding(horizontal = 16.dp, vertical = 20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        stringResource(R.string.group_detail_description),
                        color = Color.White,
                        fontSize = 17.sp
                    )
                    Spacer(modifier = Modifier.height(4.dp))
                    Box(
                        modifier = Modifier
                            .verticalScrollWithScrollbar(descScrollState)
                            .weight(1f)
                    ) {
                        Text(
                            info.bulletin ?: "",
                            color = Color(0x80FFFFFF)
                        )
                    }
                    Button(
                        {
                            descDialogShow.value = false
                        }, modifier = Modifier
                            .widthIn(160.dp)
                            .heightIn(36.dp)
                            .padding(top = 12.dp)
                    ) {
                        Text(
                            text = stringResource(R.string.confirm), fontSize = 16.sp,
                        )
                    }
                }
            }
        }

        if (info.relationWithMe != TribeConst.RELATION_MEMBER) {
            AppButton(
                text = when (info.relationWithMe) {
                    TribeConst.RELATION_APPLYING -> stringResource(id = R.string.申请中)
                    TribeConst.RELATION_MEMBER -> stringResource(id = R.string.进入聊天)
                    else -> stringResource(id = R.string.申请加入部落)
                },
                brush = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xff8B56FC))),
                fontSize = 14.sp,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .absoluteOffset(y = -20.dp)
                    .graphicsLayer {
                        alpha = if (info.relationWithMe == TribeConst.RELATION_APPLYING) 0.5f else 1f
                    }
                    .padding(horizontal = 16.dp)
                    .fillMaxWidth()
                    .height(44.dp),
            ) {
                onAction(TribeDetailUIAction.ClickBottomButton)
            }
        }
    }
}


/**
 * 部落成员列表管理
 *
 */
@Composable
private fun TribeMemberListWidget(info: TribeInfo, onAction: (TribeDetailUIAction) -> Unit) {
    val vm = viewModel(modelClass = OnlineMemberListViewModel::class, factory = viewModelFactory {
        initializer {
            OnlineMemberListViewModel(info.idStr)
        }
    })
    val list by vm.dataState
    var orderModel by remember(info.id) {
        mutableStateOf(0)
    }
    LaunchedEffect(key1 = orderModel) {
        vm.orderType = orderModel
    }

    Column {
        Row(modifier = Modifier.padding(16.dp)) {
            Text(
                stringResource(id = R.string.部落成员数量) + "${info.onlineMemberCnt}/${info.memberCnt}",
                modifier = Modifier.weight(1f),
                fontSize = 12.sp,
                lineHeight = 12.sp,
                color = Color(0x80FFFFFF)
            )
            Row(verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier
                    .widthIn(64.dp)
                    .click {
                        if (orderModel != 1) {
                            orderModel = 1
                        } else {
                            orderModel = 2
                        }
                    }) {
                Text(
                    stringResource(id = R.string.周活跃度),
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    color = if (orderModel in 1..2) Color(0xff945EFF) else Color(0x80FFFFFF)
                )
                ComposeImage(model = if (orderModel == 2) R.drawable.ic_tribe_order_asc else if (orderModel == 1) R.drawable.ic_tribe_order_desc else R.drawable.ic_tribe_order)
            }
            Row(verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center,
                modifier = Modifier
                    .widthIn(64.dp)
                    .click {
                        if (orderModel != 3) {
                            orderModel = 3
                        } else {
                            orderModel = 4
                        }
                    }) {
                Text(
                    stringResource(id = R.string.总活跃度),
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    color = if (orderModel in 3..4) Color(0xff945EFF) else Color(0x80FFFFFF)
                )
                ComposeImage(model = if (orderModel == 4) R.drawable.ic_tribe_order_asc else if (orderModel == 3) R.drawable.ic_tribe_order_desc else R.drawable.ic_tribe_order)
            }
        }
        LazyColumn(modifier = Modifier.fillMaxSize()) {
            if (vm.isLoaded) {
//                items(info.firstPageMembers) {
//                    TribeMemberItemWidget(member = it, modifier = Modifier.click {
//                        onAction(TribeDetailUIAction.GoUserProfileAction(it.user.toAppUser()))
//                    })
//                }
                items(list) {
                    TribeMemberItemWidget(member = it, modifier = Modifier.click {
                        onAction(TribeDetailUIAction.GoUserProfileAction(it.user.toAppUser()))
                    })
                }
            }
            itemLoadMore(list.isNotEmpty() && vm.allowLoad, vm.isLoadingNow, vm.hasMore) {
                vm.loadMore()
            }
        }
    }
}


/**
 * 未加入部落时, 只展示第一页的成员
 *
 * @param member 成员信息
 * @param wasLookMore 如果true会显示加入查看更多
 */
@Composable
private fun FirstPageMemberItemWidget(
    member: TribeMemberItem,
    wasLookMore: Boolean = false,
    modifier: Modifier
) {
    //头像
    Column(
        modifier = modifier.fillMaxWidth(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(modifier = Modifier.size(56.dp)) {

            if (wasLookMore) {
                ComposeImage(
                    model = R.drawable.ic_tribe_lookmore,
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                CircleComposeImage(model = member.user.avatarUrl, modifier = Modifier.fillMaxSize())
                if (member.is_online) {
                    Spacer(
                        modifier = Modifier
                            .size(10.dp)
                            .background(color = Color(0xff00b42a), shape = CircleShape)
                            .align(Alignment.BottomEnd)
                    )
                }
            }
        }
        //名称
        if (wasLookMore) {
            Text(
                stringResource(id = R.string.加入部落查看全部),
                lineHeight = 16.sp,
                fontSize = 12.sp,
                maxLines = 2,
                color = Color(0xaaFFFFFF),
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(top = 8.dp)
            )
        } else {
            Text(
                member.user.nickname,
                lineHeight = 12.sp,
                fontSize = 12.sp,
                maxLines = 1,
                color = Color.White,
                textAlign = TextAlign.Center,
                overflow = TextOverflow.Ellipsis,
                modifier = Modifier
                    .padding(top = 8.dp)
            )
        }
        if (!wasLookMore && (member.isOwner || member.isAdmin)) {
            ComposeImage(model = if (member.isOwner) R.drawable.ic_first_leader else R.drawable.ic_second_leader)
        }
    }
}

@Composable
@Preview
private fun TribeDetailScreenPreview() {
    UCOOPreviewTheme {
        val info = TribeInfo(
            id = 1,
            name = "测试部落",
            avatarUrl = "https://img.alicdn.com/imgextra/i1/O1CN01Q3YJYq1ZQ0J6XJYJ1_!!6000000006411-2-tps-200-200.png",
            publicId = "test",
            bulletin = "测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告测试部落公告",
            memberCnt = 2,
            firstPageMembers = mutableListOf<TribeMemberItem>().apply {
                for (i in 0..6) {
                    add(
                        TribeMemberItem(
                            user = TribeUser(
                                nickname = "测试成asdfsdf员1",
                                avatarUrl = "https://img.alicdn.com/imgextra/i1/O1CN01Q3YJYq1ZQ0J6XJYJ1_!!6000000006411-2-tps-200-200.png",
                                publicId = "test1"
                            ),
                            role = Math.min(i * 5, 10)
                        )
                    )
                }
            },
            relationWithMe = 0

        )
        TribeDetailScreen(info = info) {

        }
    }
}