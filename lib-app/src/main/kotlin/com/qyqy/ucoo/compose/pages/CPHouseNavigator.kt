package com.qyqy.ucoo.compose.pages

import android.content.Context
import android.os.Bundle
import androidx.activity.viewModels
import androidx.collection.mutableScatterMapOf
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.cupid.ui.LocalAppNavController
import com.qyqy.cupid.ui.NavArgument
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.cp_house.CPHouseIntimacyHTScreen
import com.qyqy.ucoo.compose.presentation.cp_house.CPHouseMainScreen
import com.qyqy.ucoo.compose.presentation.cp_house.CPHouseMemoriesScreen
import com.qyqy.ucoo.compose.presentation.cp_house.house.HouseCoinRecordScreen
import com.qyqy.ucoo.compose.presentation.cp_house.house.HouseDressUpScreen
import com.qyqy.ucoo.compose.presentation.cp_house.house.HouseIntimateRankScreen
import com.qyqy.ucoo.compose.presentation.cp_house.house.HouseLotteryViewModel
import com.qyqy.ucoo.compose.presentation.cp_house.house.HouseTreasureBoxScreen
import com.qyqy.ucoo.compose.presentation.cp_house.house.LotteryRecordScreen
import com.qyqy.ucoo.compose.router.IDestination
import com.qyqy.ucoo.compose.router.LocalNavigationViewModelOwner
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.vm.cp_house.CPHouseViewModel
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.utils.LogUtils

sealed class CPHouseDestination : IDestination {
    data object HomeDestination : CPHouseDestination() {
        override val route: String = "cp_house_home"
    }

    data object MemoriesDestination : CPHouseDestination() {
        override val route: String = "cp_house_memories"
    }

    data object OtherDestination : CPHouseDestination() {
        override val route: String = "cp_house_other"
    }

    /**
     * 小屋币记录
     */
    data object HCRecordDestination : CPHouseDestination() {
        override val route: String = "cp_house_coin_record"
    }

    /**
     * 小屋币记录
     */
    data object HouseIntimacyHistoryDestination : CPHouseDestination() {
        override val route: String = "cp_house_intimacy_record"
    }

    /**
     * 小屋装扮
     */
    data object HouseDressUpDest : CPHouseDestination() {
        override val route: String = "cp_house_dressup"
    }

    data object HouseTreasureBox : CPHouseDestination() {
        override val route: String = "cp_house_treasure_box"
    }

    /**
     * 亲密度排行榜
     */
    data object HouseIntimateRank : CPHouseDestination() {
        override val route: String = "cp_house_intimate_rank"
    }

    data object LotteryRecord : CPHouseDestination() {
        override val route: String = "cp_house_lottery_record"
    }
}

object CPHouseNavigator : ScreenNavigator {
    fun navigate(
        context: Context,
        room_id: Int,
        destination: CPHouseDestination = CPHouseDestination.HomeDestination,
        extra: Bundle? = null
    ) {
        this.navigate(context) {
            putExtra(Const.KEY_ID, destination.route)
            putExtra("room_id", room_id)
            extra?.let {
                putExtras(it)
            }
        }
    }

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val navController = rememberNavController()
        val key = bundle.getString(Const.KEY_ID)
        val destination = when (key) {
            CPHouseDestination.OtherDestination.route -> CPHouseDestination.HomeDestination
            CPHouseDestination.HCRecordDestination.route -> CPHouseDestination.HCRecordDestination
            CPHouseDestination.HouseDressUpDest.route -> CPHouseDestination.HouseDressUpDest
            CPHouseDestination.HouseTreasureBox.route -> CPHouseDestination.HouseTreasureBox
            CPHouseDestination.HouseIntimateRank.route -> CPHouseDestination.HouseIntimateRank
            CPHouseDestination.LotteryRecord.route -> CPHouseDestination.LotteryRecord
            else -> CPHouseDestination.HomeDestination
        }
        val rootArguments = mutableScatterMapOf<String, NavArgument>()
        val scope = rememberCoroutineScope()
        val appNavController = remember {
            AppNavController(navController, rootArguments, scope)
        }

        val roomId = bundle.getInt("room_id", -1)
        if (roomId == -1) {
            activity.finish()
            LogUtils.w("CPHouseNavigator", "<==========roomId cannot be null or empty")
            return
        }

        val viewModel = viewModel(modelClass = CPHouseViewModel::class, factory = viewModelFactory {
            initializer {
                CPHouseViewModel(room_id = roomId)
            }
        }, viewModelStoreOwner = activity)

        viewModel.mMasterUserId = bundle.getInt("user_id", 0)
        viewModel.mFrom = bundle.getInt("from", 0)

        LifecycleResumeEffect(key1 = Unit) {
            viewModel.refreshHouseInfo()
            onPauseOrDispose { }
        }

        AppTheme {
            CompositionLocalProvider(
                LocalUCOONavController provides navController,
                LocalAppNavController provides appNavController,
                LocalNavigationViewModelOwner provides activity
            ) {
                NavHost(
                    navController = navController,
                    startDestination = destination.route,
                    modifier = Modifier.fillMaxSize(),
                    enterTransition = {
                        slideInHorizontally(initialOffsetX = { it })
                    },
                    exitTransition = {
                        slideOutHorizontally(targetOffsetX = { -it })
                    },
                    popEnterTransition = {
                        slideInHorizontally(initialOffsetX = { -it })
                    },
                    popExitTransition = {
                        slideOutHorizontally(targetOffsetX = { it })
                    },
                ) {
                    composable(CPHouseDestination.HomeDestination.route) {
                        CPHouseMainScreen(roomId)
                    }
                    composable(CPHouseDestination.MemoriesDestination.route) {
                        CPHouseMemoriesScreen(roomId)
                    }

                    composable(CPHouseDestination.HCRecordDestination.route) {
                        HouseCoinRecordScreen()
                    }

                    composable(CPHouseDestination.HouseDressUpDest.route) {
                        HouseDressUpScreen()
                    }

                    composable(CPHouseDestination.HouseTreasureBox.route) {
                        val vm by activity.viewModels<HouseLotteryViewModel>()
                        HouseTreasureBoxScreen(vm)
                    }
                    composable(CPHouseDestination.HouseIntimacyHistoryDestination.route) {
                        CPHouseIntimacyHTScreen()
                    }

                    composable(CPHouseDestination.HouseIntimateRank.route) {
                        HouseIntimateRankScreen()
                    }
                    composable(CPHouseDestination.LotteryRecord.route) {
                        LotteryRecordScreen()
                    }
                }
            }
        }
    }
}
