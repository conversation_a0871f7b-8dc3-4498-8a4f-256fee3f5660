package com.qyqy.ucoo.compose.vm.room

import com.qyqy.ucoo.compose.domain.usecase.room.FetchBackgroundListUseCase
import com.qyqy.ucoo.compose.domain.usecase.room.SetBackgroundUseCase
import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.compose.vm.room.BackgroundSettingMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.room.BackgroundSettingMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.room.BackgroundSettingMvi.ViewResult
import com.qyqy.ucoo.compose.vm.room.BackgroundSettingMvi.ViewState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.merge

class BackgroundSettingViewModel(
    private val roomId: Int,
    private val fetchUseCase: FetchBackgroundListUseCase,
    private val setUseCse: SetBackgroundUseCase,
) : MviViewModel<ViewEvent, ViewResult, ViewState, ViewEffect>(ViewState()) {

    init {
        processEvent(ViewEvent.Fetch)
    }

    override fun Flow<ViewEvent>.toResults(): Flow<ViewResult> {
        return merge(
            filterIsInstance<ViewEvent.Fetch>().mapLatest {
                fetchUseCase.invoke(roomId).fold({
                    ViewResult.BackgroundList(it)
                }) {
                    ViewResult.ErrorResult(it.message)
                }
            },
            filterIsInstance<ViewEvent.SelectBackground>().mapLatest {
                if (it.index == cur.selectIndex) {
                    ViewResult.NoOp
                } else {
                    ViewResult.SelectBackgroundResult(it.index)
                }
            }, filterIsInstance<ViewEvent.SetBackground>().mapLatest {
                val currentSelectId = cur.list[cur.selectIndex].id
                if (cur.currentUseId != currentSelectId) {
                    setUseCse.invoke(roomId to currentSelectId).fold({
                        ViewResult.SetBackgroundResult(true, currentSelectId)
                    }) {
                        ViewResult.ErrorResult(it.message)
                    }
                } else {
                    ViewResult.SetBackgroundResult(false, currentSelectId)
                }
            }
        )
    }

    override fun Flow<ViewResult>.filterStateResult(): Flow<ViewResult> {
        return filter {
            it is ViewResult.BackgroundList
                    || it is ViewResult.SelectBackgroundResult
        }
    }

    override suspend fun ViewResult.reduce(state: ViewState): ViewState {
        return when (this) {
            is ViewResult.BackgroundList -> {
                var id = -1
                var index = -1
                val list = list.mapIndexed { i, item ->
                    if (item.inUse) {
                        id = item.id
                        index = i
                    }
                    item.prop
                }
                state.copy(currentUseId = id, selectIndex = index, list = list)
            }

            is ViewResult.SelectBackgroundResult -> {
                state.copy(selectIndex = index)
            }

            else -> state
        }
    }

    override fun Flow<ViewResult>.toEffects(): Flow<ViewEffect> {
        return merge(
            filterIsInstance<ViewResult.ErrorResult>().filter {
                it.toastError.isNullOrEmpty().not()
            }.mapLatest { result ->
                ViewEffect.Toast(result.toastError!!)
            },
            filterIsInstance<ViewResult.SetBackgroundResult>().mapLatest {
                ViewEffect.Back(it.changed, it.id)
            }
        )
    }
}