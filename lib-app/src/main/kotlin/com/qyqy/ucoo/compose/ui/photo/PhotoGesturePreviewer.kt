@file:OptIn(ExperimentalFoundationApi::class)

package com.qyqy.ucoo.compose.ui.photo

import androidx.activity.compose.BackHandler
import androidx.compose.animation.Animatable
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.calculatePan
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.absoluteOffset
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.UiComposable
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.positionChanged
import androidx.compose.ui.input.pointer.util.addPointerInputChange
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.round
import androidx.compose.ui.util.fastForEach
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch
import kotlin.math.abs


object PreviewerDefault {

    @Composable
    fun behindContent(
        previewState: PhotoPreviewState,
        animationSpec: AnimationSpec<Color>? = spring(stiffness = Spring.StiffnessMedium),
    ): @UiComposable @Composable BoxScope.() -> Unit {
        return {
//            val color = remember { Animatable(if (previewState.previewIsReady) Color.Black else Color.Transparent) }
//            LaunchedEffect(previewState.state) {
//                if (previewState.isAnimPreviewing) {
//                    if (previewState.inPreviewing) {
//                        color.animateTo(Color.Black, animationSpec ?: spring())
//                    } else {
//                        color.animateTo(Color.Transparent, animationSpec ?: spring())
//                    }
//                }
//            }
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .graphicsLayer {
                        alpha = if (previewState.closeOffsetMode) {
                            200
                                .minus(abs(previewState.gestureState.currentOffset.y))
                                .coerceAtLeast(0f)
                                .div(200)
                        } else 1f
                    }
                    .background(Color.Black)
            )
        }
    }
}

@Composable
fun PhotoGesturePreviewer(
    previewState: PhotoPreviewState,
    modifier: Modifier = Modifier,
    pageSpacing: Dp = 10.dp,
    enableGesture: Boolean = true,
    animationSpec: AnimationSpec<PhotoTransform>? = null,
    behindContent: @Composable @UiComposable BoxScope.() -> Unit = PreviewerDefault.behindContent(previewState = previewState),
    inFrontContent: @Composable @UiComposable BoxScope.() -> Unit = {},
    photoContent: @Composable @UiComposable BoxScope.(isTransition: Boolean, IPhotoPreviewModel, Modifier, ContentScale) -> Unit,
) {
    if (!previewState.isPreviewing) {
        return
    }

    val scope = rememberCoroutineScope()

    val density = LocalDensity.current

    val gestureState = previewState.gestureState

    val pagerState = previewState.rememberPagerState()

    LaunchedEffect(key1 = Unit) {
        var lastPage: Int = -1
        snapshotFlow {
            pagerState.settledPage
        }.collectLatest { page ->
            if (lastPage != page) {
                gestureState.snapTo(
                    scaleValue = gestureState.minimumScale,
                    offsetValue = Offset.Zero,
                    rotationValue = 0f,
                )
            }
            lastPage = page
        }
    }

    BoxWithConstraints(
        modifier = modifier.fillMaxSize(),
        contentAlignment = Alignment.Center,
    ) {

        val maxWidth = maxWidth
        val maxHeight = maxHeight

        BackHandler {
            previewState.endPreview()
        }

        val gestureModifier = remember(pagerState) {

            val onDrag = { change: Offset, dragInitialScale: Float ->
                val newOffset = gestureState.currentOffset.plus(change.times(gestureState.minimumScale.div(dragInitialScale)))
                val newScale = dragInitialScale.times(
                    1f.minus(abs(newOffset.y).div(gestureState.layoutSize.height.times(1.2f)).coerceAtMost(0.25f))
                )
                scope.launch(start = CoroutineStart.UNDISPATCHED) {
                    gestureState.snapTo(scaleValue = newScale, offsetValue = newOffset)
                }
            }

            var allowGestureClose = false
            var inLeftBound = false
            var inRightBound = false
            var dragInitialScale = gestureState.minimumScale
            var mode = 0

            with(gestureState) {
                Modifier.photoGesture(
                    scope = scope,
                    key = Unit,
                    enable = {
                        (it || enableGesture) && previewState.previewIsReady
                    },
                    onTap = {
                        previewState.endPreview()
                    },
                    dispatchFirstDown = {
                        previewState.closeOffsetMode = false
                        val scrollableX = getScrollableBounds().x
                        allowGestureClose = photoSize.height.times(currentScale) <= layoutSize.height
                        inLeftBound = currentOffset.x >= scrollableX && pagerState.currentPage != 0
                        inRightBound = currentOffset.x <= -scrollableX && pagerState.currentPage < pagerState.pageCount.minus(1)
                        dragInitialScale = minimumScale
                        mode = 0
                    },
                    tryConsumeInput = { event, touchSlop, _, _, pan ->
                        kotlin.run {
                            var indeterminacy = false
                            if (pan.x > 0 && inLeftBound) {
                                if (abs(pan.x) >= touchSlop && abs(pan.x) > abs(pan.y)) {
                                    mode = 1
                                    return@run true
                                }
                                indeterminacy = true
                            } else if (pan.x < 0 && inRightBound) {
                                if (abs(pan.x) >= touchSlop && abs(pan.x) > abs(pan.y)) {
                                    mode = 1
                                    return@run true
                                }
                                indeterminacy = true
                            }

                            if (allowGestureClose && abs(pan.y) > touchSlop && abs(pan.y) >= abs(pan.x)) { // 垂直滑动，手势关闭预览
                                mode = 2
                                previewState.closeOffsetMode = true
                                dragInitialScale = currentScale
                                onDrag(event.calculatePan(), dragInitialScale)
                                return@run true
                            }
                            if (indeterminacy) null else false
                        }
                    },
                    onGestureStart = { event, velocityTracker, _, _, _ ->
                        if (mode == 2) {
                            val panChange = event.calculatePan()
                            if (panChange != Offset.Zero) {
                                onDrag(panChange, dragInitialScale)
                            }
                            event.changes.fastForEach { change ->
                                if (change.positionChanged()) {
                                    velocityTracker.addPointerInputChange(change)
                                    change.consume()
                                }
                            }
                        }
                    }
                ) { velocity ->
                    mode == 2 && previewState.invokeGestureClose(density, pagerState.currentPage, currentOffset.y, velocity.y)
                }
            }
        }

        // 底部视图
        behindContent()

        // 先隐藏
        HorizontalPager(
            state = pagerState,
            modifier = Modifier
                .fillMaxSize(),
            pageSpacing = pageSpacing,
            userScrollEnabled = previewState.previewIsReady,
            key = { page ->
                previewState.getPreviewKeyByPage(page) ?: page
            }
        ) { page ->

            val model = previewState.getPreviewModelByPage(page)!!
            val previewKey = model.key

            if (previewState.previewIsReady && page == pagerState.currentPage) {
                previewState.transitionAnimKey = previewKey
            }

            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .then(gestureModifier),
                contentAlignment = Alignment.Center
            ) {

                val enable by remember {
                    derivedStateOf {
                        pagerState.settledPage == page
                    }
                }
                val photoModifier = gestureState.run {
                    val aspectRatio = model.aspectRatio
                    if (aspectRatio <= 0) {
                        Modifier
                            .requiredSize(DpSize(maxWidth, maxHeight))
                            .applyPhotoGesture(enable)
                    } else {
                        Modifier
                            .requiredSize(
                                if (maxWidth.value.div(maxHeight.value) <= aspectRatio) {
                                    DpSize(maxWidth, maxWidth.div(aspectRatio))
                                } else {
                                    DpSize(maxHeight.times(aspectRatio), maxHeight)
                                }
                            )
                            .applyPhotoGesture(enable)
                    }
                }

                photoContent(false, model, photoModifier, ContentScale.Fit)
            }
        }

//        val defaultAnimateSpec = animationSpec ?: spring()
//
//        //fixed https://console.firebase.google.com/u/1/project/ucoo-386017/crashlytics/app/android:com.qyqy.ucoo/issues/6dfa5b10797b3aabe9e305034faf2423
//        //总之先让他返回吧
//        val model = previewState.getPreviewModelByPage(pagerState.currentPage)?:return@BoxWithConstraints
//
//        val previewKey = model.key
//
//        val previewKeyItem = previewState.getPreviewKeyItem(previewKey)
//
//        val itemDpSize by rememberUpdatedState(newValue = with(density) {
//            if (previewKeyItem != null) {
//                DpSize(previewKeyItem.size.width.toDp(), previewKeyItem.size.height.toDp())
//            } else {
//                DpSize.Zero
//            }
//        })
//
//        val transformPosition by rememberUpdatedState(newValue = with(density) {
//            previewKeyItem?.position?.minus(
//                Offset(
//                    maxWidth.minus(itemDpSize.width).div(2).toPx(),
//                    maxHeight.minus(itemDpSize.height).div(2).toPx()
//                )
//            ) ?: Offset.Zero
//        })

//        val transformAnimator = remember(previewState.outPreviewing) {
//            if (previewState.outPreviewing) {
//                androidx.compose.animation.core.Animatable(
//                    PhotoTransform(
//                        with(density) {
//                            val size = gestureState.photoSize.times(gestureState.currentScale)
//                            DpSize(size.width.toDp(), size.height.toDp())
//                        },
//                        gestureState.currentOffset
//                    ),
//                    PhotoTransform.PhotoTransformToVector
//                )
//            } else {
//                if (itemDpSize.width.value <= 0 || itemDpSize.height.value <= 0) {
//                    androidx.compose.animation.core.Animatable(
//                        PhotoTransform(DpSize.Zero, Offset.Zero),
//                        PhotoTransform.PhotoTransformToVector
//                    )
//                } else {
//                    androidx.compose.animation.core.Animatable(
//                        PhotoTransform(itemDpSize, transformPosition),
//                        PhotoTransform.PhotoTransformToVector
//                    )
//                }
//            }
//        }

//        val aspectRatio = model.aspectRatio
//        val layoutSizeRatio = itemDpSize.width.value.div(itemDpSize.height.value)
//        val contentScale = when {
//            aspectRatio <= 0 -> ContentScale.Fit
//            layoutSizeRatio == aspectRatio -> ContentScale.Fit
//            layoutSizeRatio < aspectRatio -> ContentScale.FillHeight
//            else -> ContentScale.FillWidth
//        }

        // 变换视图
//        photoContent(
//            true,
//            model,
//            Modifier
//                .requiredSize(transformAnimator.value.size)
//                .absoluteOffset {
//                    transformAnimator.value.position.round()
//                }
//                .alpha(if (previewState.isAnimPreviewing) 1f else 0f),
//            contentScale,
//        )

        if (previewState.isAnimPreviewing) {
            LaunchedEffect(previewState.inPreviewing) {
                if (previewState.inPreviewing) {
//                    val transformSize = if (aspectRatio <= 0) {
//                        DpSize(maxWidth, maxHeight)
//                    } else {
//                        if (maxWidth.value.div(maxHeight.value) <= aspectRatio) {
//                            DpSize(maxWidth, maxWidth.div(aspectRatio))
//                        } else {
//                            DpSize(maxHeight.times(aspectRatio), maxHeight)
//                        }
//                    }.times(gestureState.minimumScale)
//                    transformAnimator.animateTo(PhotoTransform(transformSize, Offset.Zero), defaultAnimateSpec)
                    previewState.previewReady()
                } else {
//                    if (itemDpSize.width.value <= 0 || itemDpSize.height.value <= 0) {
//                        transformAnimator.animateTo(PhotoTransform(DpSize.Zero, Offset.Zero), defaultAnimateSpec)
//                    } else {
//                        transformAnimator.animateTo(PhotoTransform(itemDpSize, transformPosition), defaultAnimateSpec)
//                    }
//                    previewState.transitionAnimKey = null
//                    delay(15)
                    previewState.previewClosed()
                }
            }
        }

        // 顶部视图
        inFrontContent()
    }
}