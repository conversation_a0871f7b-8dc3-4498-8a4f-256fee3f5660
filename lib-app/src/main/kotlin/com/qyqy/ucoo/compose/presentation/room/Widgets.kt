

package com.qyqy.ucoo.compose.presentation.room

import com.qyqy.ucoo.LocalUserPartition
import androidx.compose.foundation.Image
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicText
import androidx.compose.foundation.text.InlineTextContent
import androidx.compose.foundation.text.appendInlineContent
import androidx.compose.material3.LocalContentColor
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Immutable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.movableContentOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.takeOrElse
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.Placeable
import androidx.compose.ui.layout.SubcomposeLayout
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.Placeholder
import androidx.compose.ui.text.PlaceholderVerticalAlign
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextLayoutResult
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.Constraints
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.util.fastForEach
import androidx.core.text.HtmlCompat
import com.bumptech.glide.integration.compose.placeholder
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.toAnnotatedString
import com.qyqy.ucoo.config.LevelConfig
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.utils.EntityCallback


data class SpanClickItem(
    val tag: String,
    val router: String,
)

@Immutable
class TextSpan private constructor(
    val text: AnnotatedString,
    val inlineContentList: List<IInlineTextContent>? = null,
    val clickTags: List<SpanClickItem>? = null,
) {
    class Builder(private val builder: AnnotatedString.Builder) : Appendable by builder {

        private val inlineTextStyles: MutableList<IInlineTextContent> = mutableListOf()

        private val clickTags: MutableList<SpanClickItem> = mutableListOf()

        fun appendInlineContent(inlineTextContent: IInlineTextContent) {
            builder.appendInlineContent(inlineTextContent)
            inlineTextStyles.add(inlineTextContent)
        }

        fun appendInlineContentList(inlineTextContentList: List<IInlineTextContent>) {
            for (item in inlineTextContentList) {
                builder.appendInlineContent(item)
            }
            inlineTextStyles.addAll(inlineTextContentList)
        }

        fun clickable(item: SpanClickItem) {
            clickTags.add(item)
        }

        fun toTextSpan(): TextSpan {
            return TextSpan(
                builder.toAnnotatedString(),
                inlineTextStyles.toList(),
                clickTags.toList(),
            )
        }
    }

}

private fun AnnotatedString.Builder.appendInlineContent(inlineTextContent: IInlineTextContent) {
    val alternateText = inlineTextContent.alternateText
    if (alternateText != null) {
        appendInlineContent(inlineTextContent.key, alternateText)
    } else {
        appendInlineContent(inlineTextContent.key)
    }
}

inline fun AnnotatedString.Builder.color(color: Color, builder: (AnnotatedString.Builder).() -> Unit) {
    withStyle(SpanStyle(color = color)) {
        builder()
    }
}

inline fun buildTextSpan(builder: (AnnotatedString.Builder).(TextSpan.Builder) -> Unit): TextSpan {
    return AnnotatedString.Builder().run {
        TextSpan.Builder(this).also {
            builder(it)
        }.toTextSpan()
    }
}

/**
 * 提前不知道尺寸
 */
fun inlineTextContent(
    key: String,
    alternateText: String? = null,
    paddingValues: PaddingValues = PaddingValues(0.dp),
    content: @Composable (Modifier) -> Unit,
): IInlineTextContent {
    return InlineTextContentPreMeasureImpl(key, alternateText, paddingValues, content)
}

/**
 * 提前知道尺寸
 */
fun inlineTextContent(
    key: String,
    density: Density,
    width: Int,
    height: Int = width,
    alternateText: String? = null,
    paddingValues: PaddingValues = PaddingValues(0.dp),
    content: @Composable (Modifier) -> Unit,
): IInlineTextContent {
    return object : IInlineTextContent {

        override val key = key

        override val alternateText = alternateText

        override val inlineTextContent: InlineTextContent = InlineTextContent(
            with(density) {
                Placeholder(
                    width = paddingValues.let {
                        it.calculateLeftPadding(LayoutDirection.Ltr).plus(it.calculateRightPadding(LayoutDirection.Ltr))
                    }.plus(width.dp).toSp(),
                    height = paddingValues.let {
                        it.calculateTopPadding().plus(it.calculateBottomPadding())
                    }.plus(height.dp).toSp(),
                    placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                )
            }
        ) {
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues),
                contentAlignment = Alignment.Center
            ) {
                content(Modifier.fillMaxSize())
            }
        }
    }
}

interface IInlineTextContent {

    val key: String

    val alternateText: String?

    val inlineTextContent: InlineTextContent
}

interface IPreMeasure {


    val measured: Boolean

    @Composable
    fun preMeasureContent()

    @Composable
    fun applyDpSize(dpSize: DpSize): InlineTextContent
}


@Stable
private class InlineTextContentPreMeasureImpl(
    override val key: String,
    override val alternateText: String?,
    private val paddingValues: PaddingValues,
    content: @Composable (Modifier) -> Unit,
) : IInlineTextContent, IPreMeasure {

    private val children = movableContentOf(content)

    override var measured = false

    override lateinit var inlineTextContent: InlineTextContent
        private set

    @Composable
    override fun preMeasureContent() {
        if (measured) {
            return
        }
        children(Modifier)
    }

    @Composable
    override fun applyDpSize(dpSize: DpSize): InlineTextContent {
        if (measured) {
            return inlineTextContent
        }
        measured = true
        inlineTextContent = with(LocalDensity.current) {
            val width = paddingValues.let {
                it.calculateLeftPadding(LayoutDirection.Ltr).plus(it.calculateRightPadding(LayoutDirection.Ltr))
            }.plus(dpSize.width).toSp()

            val height = paddingValues.let {
                it.calculateTopPadding().plus(it.calculateBottomPadding())
            }.plus(dpSize.height).toSp()

            InlineTextContent(
                Placeholder(
                    width = width,
                    height = height,
                    placeholderVerticalAlign = PlaceholderVerticalAlign.Center
                )
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(paddingValues),
                    contentAlignment = Alignment.Center
                ) {
                    children(Modifier.fillMaxSize())
                }
            }
        }
        return inlineTextContent
    }
}


fun AnnotatedString.Builder.appendUserName(user: User, appendText: String? = null) {
    withStyle(
        style = SpanStyle(
            color = if (user.isBoy) Color(0xFF50FFF3) else Color(0xFFFFA5A5),
            fontSize = 14.sp
        )
    ) {
        append(user.nickname)
        if (!appendText.isNullOrEmpty()) {
            append(appendText)
        }
    }
}

@Composable
fun UserNameText(user: User, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.CenterStart
    ) {
        AppText(
            text = user.nickname,
            modifier = modifier,
            color = if (user.isBoy) Color(0xFF50FFF3) else Color(0xFFFFA5A5),
            fontSize = 14.sp
        )
    }
}

@Composable
fun LevelBadge(level: Int, modifier: Modifier = Modifier) {
    if (level <= 0) {
        Image(
            painter = painterResource(id = R.drawable.label_new),
            contentDescription = "level",
            modifier = modifier.sizeIn(minWidth = 54.dp, minHeight = 20.dp)
        )
    } else {
        Box(
            modifier = modifier.width(IntrinsicSize.Max),
            contentAlignment = Alignment.CenterStart
        ) {
            Box(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .fillMaxWidth(0.85f)
                    .height(18.dp)
                    .paint(painter = rememberDrawablePainter(drawable = LevelConfig.getLevelBackgroundDrawable(level)))
            )

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Image(
                    painter = painterResource(id = LevelConfig.getLevelIcon(level)),
                    contentDescription = "level",
                    modifier = Modifier.height(if (level > 80) 28.dp else 24.dp),
                    contentScale = ContentScale.FillHeight,
                )
                Text(
                    text = stringResource(id = R.string.format_lv, level),
                    color = Color.White,
                    fontSize = 12.sp,
                    fontWeight = FontWeight.Bold,
                    modifier = Modifier
                        .padding(start = 1.dp)
                        .widthIn(min = 30.dp),
                    maxLines = 1,
                    textAlign = TextAlign.Center
                )

                Spacer(modifier = Modifier.width(5.dp))
            }
        }
    }
}

@Composable
fun PublishCp(avatar: String, bgUrl: String?, modifier: Modifier = Modifier) {
    Box(
        modifier = modifier.clip(CircleShape),
        contentAlignment = Alignment.CenterStart
    ) {
        ComposeImage(
            model = bgUrl?.takeIf {
                it.isNotEmpty()
            } ?: R.drawable.ic_label_love,
            modifier = modifier,
            loading = placeholder(R.drawable.ic_label_love)
        )
        CircleComposeImage(
            model = avatar,
            modifier = Modifier
                .padding(start = 1.dp)
                .requiredSize(15.dp)
        )
    }
}

@Composable
fun PreMeasureSubcomposeLayout(
    modifier: Modifier = Modifier,
    dependentContents: @Composable () -> Unit,
    mainContent: @Composable (List<DpSize>) -> Unit,
) {
    val density = LocalDensity.current
    SubcomposeLayout(
        modifier = modifier
    ) { constraints: Constraints ->
        val dependentConstraints = constraints.copy(minWidth = 0, minHeight = 0)
        val dependentSizes: List<DpSize> = with(density) {
            subcompose(SlotsEnum.Dependent, dependentContents)
                .map {
                    val placeable = it.measure(dependentConstraints)
                    DpSize(placeable.width.toDp(), placeable.height.toDp())
                }
        }

        val mainPlaceable: List<Placeable> = subcompose(SlotsEnum.Main) {
            mainContent(dependentSizes)
        }.map {
            it.measure(constraints)
        }

        val maxWidth = mainPlaceable.maxOfOrNull { it.width }.orDefault(0)
        val maxHeight = mainPlaceable.maxOfOrNull { it.height }.orDefault(0)

        layout(maxWidth, maxHeight) {
            mainPlaceable.fastForEach {
                it.placeRelative(0, 0)
            }
        }
    }
}


/**
 * Enum class for SubcomposeLayouts with main and dependent Composables
 */
private enum class SlotsEnum { Main, Dependent }

@Composable
fun SpanBox(
    textSpan: TextSpan,
    modifier: Modifier = Modifier,
    textContext: @Composable BoxScope.(Map<String, InlineTextContent>) -> Unit,
) {
    Box(
        modifier = modifier,
        contentAlignment = Alignment.CenterStart,
    ) {
        val list = textSpan.inlineContentList
        PreMeasureSubcomposeLayout(dependentContents = {
            list?.forEach { item ->
                if (item is IPreMeasure && !item.measured) {
                    item.preMeasureContent()
                }
            }
        }) { dpSizes ->
            var index = 0
            val inlineContent = list?.associate { item ->
                if (item is IPreMeasure && !item.measured) {
                    item.key to item.applyDpSize(dpSizes.getOrNull(index++) ?: DpSize(24.dp, 24.dp).also {
                        Firebase.crashlytics.log(
                            "SpanBox calculate error: ${
                                list.count {
                                    it is IPreMeasure && !it.measured
                                }
                            } < $index => dpSizes:${dpSizes}"
                        )
                    })
                } else {
                    item.key to item.inlineTextContent
                }
            }
            textContext(inlineContent.orEmpty())
        }
    }
}

val LocalLinkHandler = staticCompositionLocalOf<EntityCallback<String>?> { {} }


@Composable
fun SpanText(
    textSpan: TextSpan,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    style: TextStyle = LocalTextStyle.current,
) {
    val isUCOO = LocalUserPartition.current.isUCOO
    val linkHandler = LocalLinkHandler.current
    SpanBox(textSpan = textSpan, modifier = modifier) { inlineContent ->
        val clickTags = textSpan.clickTags
        if (clickTags.isNullOrEmpty()) {
            Text(
                text = textSpan.text,
                color = color,
                fontSize = fontSize,
                fontStyle = fontStyle,
                fontWeight = fontWeight,
                fontFamily = fontFamily,
                letterSpacing = letterSpacing,
                textDecoration = textDecoration,
                textAlign = textAlign,
                lineHeight = lineHeight,
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                minLines = minLines,
                inlineContent = inlineContent,
                onTextLayout = onTextLayout,
                style = style,
            )
        } else {
            val context = LocalContext.current
            AppClickableText(
                text = textSpan.text,
                color = color,
                fontSize = fontSize,
                fontStyle = fontStyle,
                fontWeight = fontWeight,
                fontFamily = fontFamily,
                letterSpacing = letterSpacing,
                textDecoration = textDecoration,
                textAlign = textAlign,
                lineHeight = lineHeight,
                overflow = overflow,
                softWrap = softWrap,
                maxLines = maxLines,
                minLines = minLines,
                inlineContent = inlineContent,
                onTextLayout = onTextLayout,
                style = style,
            ) { offset ->
                clickTags.forEach {
                    textSpan.text.getStringAnnotations(
                        tag = it.tag,
                        start = offset,
                        end = offset
                    ).firstOrNull()?.also { _ ->
                        linkHandler?.invoke(it.router) ?: run {
                            if (isUCOO) {
                                AppLinkManager.open(context, it.router)
                            } else {
                                AppLinkManager.controller?.navigateByLink(it.router)
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun AppClickableText(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    style: TextStyle = LocalTextStyle.current,
    inlineContent: Map<String, InlineTextContent> = mapOf(),
    onTextLayout: (TextLayoutResult) -> Unit = {},
    onClick: (Int) -> Unit,
) {
    val textColor = color.takeOrElse {
        style.color.takeOrElse {
            LocalContentColor.current
        }
    }

    val layoutResult = remember { mutableStateOf<TextLayoutResult?>(null) }
    val pressIndicator = Modifier.pointerInput(onClick) {
        detectTapGestures { pos ->
            layoutResult.value?.let { layoutResult ->
                onClick(layoutResult.getOffsetForPosition(pos))
            }
        }
    }

    BasicText(
        text = text,
        modifier = modifier.then(pressIndicator),
        style = style.merge(
            color = textColor,
            fontSize = fontSize,
            fontWeight = fontWeight,
            textAlign = textAlign ?: TextAlign.Unspecified,
            lineHeight = lineHeight,
            fontFamily = fontFamily,
            textDecoration = textDecoration,
            fontStyle = fontStyle,
            letterSpacing = letterSpacing
        ),
        onTextLayout = {
            layoutResult.value = it
            onTextLayout(it)
        },
        overflow = overflow,
        softWrap = softWrap,
        maxLines = maxLines,
        minLines = minLines,
        inlineContent = inlineContent
    )
}



@Composable
fun RichText(
    rich: List<RichItem>,
    modifier: Modifier = Modifier,
    color: Color = Color.Unspecified,
    fontSize: TextUnit = TextUnit.Unspecified,
    fontStyle: FontStyle? = null,
    fontWeight: FontWeight? = null,
    fontFamily: FontFamily? = null,
    letterSpacing: TextUnit = TextUnit.Unspecified,
    textDecoration: TextDecoration? = null,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
    overflow: TextOverflow = TextOverflow.Clip,
    softWrap: Boolean = true,
    maxLines: Int = Int.MAX_VALUE,
    minLines: Int = 1,
    onTextLayout: (TextLayoutResult) -> Unit = {},
    style: TextStyle = LocalTextStyle.current,
) {
    val density = LocalDensity.current
    val textSpan = remember(rich) {
        buildTextSpan {
            rich.forEachIndexed { index, item ->
                if (item.type == 1) {
                    val key = "text_1_$index"
                    val html = HtmlCompat.fromHtml(item.text, HtmlCompat.FROM_HTML_OPTION_USE_CSS_COLORS).toAnnotatedString()
                    if (!item.link.isNullOrEmpty()) {
                        it.clickable(SpanClickItem(key, item.link))
                        pushStringAnnotation(tag = key, annotation = "")
                    }
                    append(html)
                    if (!item.link.isNullOrEmpty()) {
                        pop()
                    }
                } else if (item.type == 2) {
                    val key = "icon_2_$index"
                    if (!item.link.isNullOrEmpty()) {
                        it.clickable(SpanClickItem(key, item.link))
                        pushStringAnnotation(tag = key, annotation = "")
                    }
                    it.appendInlineContent(inlineTextContent(
                        key = key,
                        density = density,
                        width = item.width,
                        height = item.height,
                        paddingValues = PaddingValues(start = 2.dp)
                    ) { modifier ->
                        ComposeImage(
                            model = item.icon, contentDescription = null, modifier = if (item.radius > 0) {
                                modifier.clip(RoundedCornerShape(item.radius.dp))
                            } else {
                                modifier
                            }
                        )
                    })
                    if (!item.link.isNullOrEmpty()) {
                        pop()
                    }
                }
            }
        }
    }
    SpanText(
        textSpan = textSpan,
        modifier = modifier,
        color = color,
        fontSize = fontSize,
        fontStyle = fontStyle,
        fontWeight = fontWeight,
        fontFamily = fontFamily,
        letterSpacing = letterSpacing,
        textDecoration = textDecoration,
        textAlign = textAlign,
        lineHeight = lineHeight,
        overflow = overflow,
        softWrap = softWrap,
        maxLines = maxLines,
        minLines = minLines,
        onTextLayout = onTextLayout,
        style = style,
    )
}


/**
 * 列表item模板
 * ⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯
 * │ --------   ⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯   --------  │
 * │ │      │                          │      │  │
 * │ --------   ⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯   --------  │
 * ⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯⎯
 */
@Composable
inline fun SimpleItem(
    modifier: Modifier = Modifier,
    startContent: @Composable RowScope.() -> Unit,
    centerContent: @Composable ColumnScope.() -> Unit,
    endContent: @Composable RowScope.() -> Unit,
) {
    Row(modifier, verticalAlignment = Alignment.CenterVertically) {
        startContent()
        Column(modifier = Modifier.weight(1f), verticalArrangement = Arrangement.Center) {
            centerContent()
        }
        endContent()
    }
}
