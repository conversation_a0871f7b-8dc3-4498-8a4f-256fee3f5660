package com.qyqy.ucoo.compose.state

import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyListScope
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridItemSpanScope
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.StateLayoutDefaults.defaultEmpty
import com.qyqy.ucoo.compose.state.StateLayoutDefaults.defaultError
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.LoadingLayout
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicator
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorColors
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import eu.bambooapps.material3.pullrefresh.PullRefreshState
import eu.bambooapps.material3.pullrefresh.pullRefresh
import eu.bambooapps.material3.pullrefresh.rememberPullRefreshState
import kotlinx.coroutines.CoroutineScope


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun StateLayout(
    contentState: ContentState,
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    modifier: Modifier = Modifier,
    emptyClickRetry: Boolean = false,
    empty: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultEmpty(emptyText = it) {
            if (emptyClickRetry) {
                onRefresh()
            }
        }
    },
    error: @Composable BoxScope.(errorMsg: String?) -> Unit = {
        defaultError(errorText = it)
    },
    customEmpty: Boolean = false,
    content: @Composable BoxScope.() -> Unit,
) {
    val refreshState = rememberPullRefreshState(refreshing = isRefreshing, onRefresh)
    Box(modifier = modifier.pullRefresh(refreshState)) {
        LoadingLayout(modifier = Modifier.fillMaxSize()) {
            val state = contentState.current
            if (state is StateValue.Error) {
                error(state.message)
            } else if (state == StateValue.Success || state == StateValue.Refreshing || state == StateValue.LoadMore) {
                content()
            } else if (state is StateValue.Empty) {
                if (customEmpty) {
                    empty(state.message)
                } else {
                    content()
                }
            } else {

            }
        }
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = refreshState,
            modifier = Modifier.align(Alignment.TopCenter),
            colors = PullRefreshIndicatorDefaults.colors(
                containerColor = colorResource(id = R.color.black_500),
                contentColor = colorResource(id = R.color.color_primary_pink),
            ),
            scale = true
        )
    }

}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PullRefreshBox(
    modifier: Modifier = Modifier,
    isRefreshing: Boolean,
    onRefresh: () -> Unit,
    colors: PullRefreshIndicatorColors = PullRefreshIndicatorDefaults.colors(
        containerColor = colorResource(id = R.color.black_500),
        contentColor = colorResource(id = R.color.color_primary_pink),
    ),
    pullRefreshState: PullRefreshState = rememberPullRefreshState(refreshing = isRefreshing, onRefresh = onRefresh),
    content: @Composable BoxScope.() -> Unit,
) {
    Box(modifier = modifier.pullRefresh(pullRefreshState)) {
        content()
        PullRefreshIndicator(
            refreshing = isRefreshing,
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter),
            colors = colors,
            scale = true
        )
    }
}


@Composable
fun rememberContentState(stateValue: StateValue? = null): ContentState {
    return remember {
        ContentState(stateValue ?: StateValue.LoadMore)
    }
}


object StateLayoutDefaults {
    @Composable
    fun defaultError(
        @DrawableRes errorDrawableRes: Int = R.drawable.ic_empty_for_conversation,
        errorText: String? = null,
    ): Unit {
        Column(
            modifier = Modifier.fillMaxSize(1f), horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = errorDrawableRes), contentDescription = "error", modifier = Modifier.size(
                    160.dp, 160.dp
                )
            )
            Spacer(modifier = Modifier.height(12.dp))
            if (!errorText.isNullOrEmpty()) {
                Text(fontSize = 12.sp, text = errorText, color = Color(0x66FFFFFF))
            }
        }
    }

    @Composable
    fun defaultEmpty(
        @DrawableRes emptyDrawableRes: Int = R.drawable.ic_empty_for_conversation,
        emptyText: String? = null,
        onClick: () -> Unit = {}
    ): Unit {
        Column(
            modifier = Modifier
                .fillMaxSize(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            Image(
                painter = painterResource(id = emptyDrawableRes),
                contentDescription = "empty",
                modifier = Modifier
                    .size(
                        160.dp, 160.dp
                    )
                    .click(noEffect = true) {
                        onClick()
                    }
            )
            Spacer(modifier = Modifier.height(12.dp))
            if (!emptyText.isNullOrBlank()) {
                Text(fontSize = 12.sp, text = emptyText, color = MaterialTheme.colorScheme.onSurface)
            }
        }
    }

    @Composable
    fun LoadMoreIndicator(viewModel: StateViewModel<*, *>, @StringRes noMoreStringResId: Int = R.string.no_more_data) {
        val visible by viewModel.loadMoreVisibleFlow.collectAsState()
        val hasMore by viewModel.hasMoreFlow.collectAsState()
        if (visible) {
            BottomLoadBar(hasMore, noMoreStringResId = noMoreStringResId)
        }

        if (hasMore) {
            LaunchedEffect("loadMore") {
                viewModel.loadMore()
            }
        }
    }


    @Composable
    fun BottomLoadBar(
        hasMore: Boolean,
        modifier: Modifier = Modifier,
        @StringRes noMoreStringResId: Int = R.string.no_more_data
    ) {
        Box(
            modifier = modifier
                .fillMaxWidth()
                .height(48.dp),
            contentAlignment = Alignment.Center
        ) {
            if (hasMore) {
                CircularProgressIndicator(modifier = Modifier.requiredSize(20.dp), strokeWidth = 4.dp)
            } else {
                Text(text = stringResource(id = noMoreStringResId), color = Color.White, fontSize = 12.sp)
            }
        }
    }

    @Composable
    fun LoadMoreIndicatorWithoutVM(visible: Boolean, hasMore: Boolean, noMoreStr: String, onLoadMore: () -> Unit = {}) {

        if (visible) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(48.dp),
                contentAlignment = Alignment.Center
            ) {
                if (hasMore) {
                    CircularProgressIndicator(modifier = Modifier.requiredSize(24.dp))
                } else {
                    Text(text = noMoreStr, color = Color.White, fontSize = 12.sp)
                }
            }
        }

        if (hasMore) {
            LaunchedEffect("loadMore") {
                onLoadMore()
            }
        }
    }
}


@Preview
@Composable
fun StateLayoutPreviewer() {
    val contentState = rememberContentState()
    StateLayout(contentState, true, onRefresh = {}) {

    }
}

@Preview
@Composable
private fun BottomBarPreview() {
    AppTheme {
        StateLayoutDefaults.BottomLoadBar(hasMore = true)
    }
}

typealias OnLoadMore = suspend CoroutineScope.() -> Unit

@Composable
fun LoadMoreView(onLoadMore: OnLoadMore, modifier: Modifier = Modifier, loadKey: String? = null) {
    LaunchedEffect(key1 = loadKey, onLoadMore)
    ComposeLoading(modifier)
}

@Composable
fun ComposeLoading(modifier: Modifier = Modifier, color: Color = MaterialTheme.colorScheme.primary) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator(modifier = Modifier.requiredSize(20.dp), strokeWidth = 2.dp, color = color)
    }
}


fun LazyListScope.itemLoadMore(
    allowLoading: Boolean,
    isLoading: Boolean,
    hasMore: Boolean,
    onLoadMore: () -> Unit = {}
) {
    item {
        if (allowLoading) {
            LaunchedEffect(key1 = Unit) {
                onLoadMore.invoke()
            }
        }
        if (isLoading && hasMore) {
            ComposeLoading()
        }
    }
}


@Composable
fun LoadMoreEffect(
    allowLoading: Boolean = true,
    hasMore: Boolean = true,
    key1: Any = Unit,
    color: Color = Color.White,
    ui: @Composable () -> Unit = @Composable {
        if (hasMore) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 48.dp),
                contentAlignment = Alignment.Center
            ) {
                CircularProgressIndicator(modifier = Modifier.size(24.dp), color = color)
            }
        }
    },
    onLoadMore: () -> Unit = {}
) {
    if (allowLoading) {
        LaunchedEffect(key1 = key1) {
            onLoadMore.invoke()
        }
    }
    ui()
}


fun LazyGridScope.itemLoadMore(
    allowLoading: Boolean,
    isLoading: Boolean,
    hasMore: Boolean,
    key: Any? = null,
    span: (LazyGridItemSpanScope.() -> GridItemSpan)? = null,
    contentType: Any? = null,
    onLoadMore: () -> Unit = {}
) {
    item(key = key, span = span, contentType = contentType) {
        if (allowLoading) {
            LaunchedEffect(key1 = Unit) {
                onLoadMore.invoke()
            }
        }
        if (isLoading && hasMore) {
            ComposeLoading()
        }
    }
}
