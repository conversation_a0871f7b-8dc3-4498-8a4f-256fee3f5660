package com.qyqy.ucoo.compose.presentation.cp_house.house

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.presentation.cp_house.CPHouseApi
import com.qyqy.ucoo.compose.presentation.cp_house.bean.HouseTaskItem
import com.qyqy.ucoo.compose.presentation.cp_house.bean.TaskSeries
import com.qyqy.ucoo.compose.presentation.cp_house.bean.TaskType
import com.qyqy.ucoo.compose.presentation.room.RichText
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorHouseBlue
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch


@Composable
fun HouseTaskPanel(
    loading: Boolean = false,
    taskSeries: List<TaskSeries> = emptyList(),
    onItemClick: EntityCallback<HouseTaskItem> = {}
) {
    val text = LocalHouseText.current
    key(taskSeries) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(colorHouseBlue, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .navigationPadding(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(54.dp), contentAlignment = Alignment.Center
            ) {
                Text(
                    text = stringResource(id = text.titleTask),
                    color = Color.White,
                    fontSize = 16.sp
                )
            }
            val titles = remember(taskSeries) {
                taskSeries.map { it.name }
            }
            val pagerState = rememberPagerState {
                titles.size
            }
            val scope = rememberCoroutineScope()
            val selectedTabIndex = pagerState.currentPage
            if (loading || taskSeries.isEmpty()) {
                Spacer(modifier = Modifier.height(30.dp))
            } else {
                TabRow(
                    selectedTabIndex = selectedTabIndex,
                    containerColor = Color.Transparent,
                    modifier = Modifier
                        .fillMaxWidth(0.6f)
                        .heightIn(min = 30.dp),
                    divider = {},
                    indicator = { tabPositions ->
                        Box(
                            Modifier
                                .tabIndicatorOffset(tabPositions[selectedTabIndex])
                                .background(Color.Transparent),
                            contentAlignment = Alignment.Center
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(12.dp, 3.dp)
                                    .background(
                                        Color.White,
                                        RoundedCornerShape(4.dp)
                                    )
                            )
                        }
                    }
                ) {
                    titles.forEachIndexed { index, res ->
                        val selected = index == pagerState.currentPage
                        Tab(selected = selected, onClick = {
                            scope.launch {
                                pagerState.scrollToPage(index)
                            }
                        }) {
                            Text(
                                modifier = Modifier.heightIn(min = 30.dp),
                                text = res,
                                color = if (selected) Color.White else colorWhite50Alpha
                            )
                        }
                    }
                }
            }
            Spacer(modifier = Modifier.height(20.dp))
            //pager
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(400.dp)
            ) {
                if (loading) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .size(48.dp)
                            .align(Alignment.Center),
                        color = Color.White
                    )
                } else {
                    HorizontalPager(
                        state = pagerState,
                        modifier = Modifier.fillMaxSize()
                    ) { pageIndex ->
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = 16.dp),
                            verticalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            items(taskSeries[pageIndex].taskList) { item ->
                                TaskItem(taskItem = item) {
                                    onItemClick.invoke(item)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun TaskItem(taskItem: HouseTaskItem, onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.White, Shapes.small)
            .padding(8.dp, 14.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        ComposeImage(model = taskItem.icon, modifier = Modifier.size(44.dp))
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(start = 8.dp)
        ) {
            AppText(
                text = taskItem.name,
                fontSize = 14.sp,
                color = Color(0xFF1D2129),
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(10.dp))
            RichText(rich = taskItem.bonusRichDesc, fontSize = 14.sp)
        }
        Box(
            modifier = Modifier
                .height(32.dp)
                .widthIn(min = 68.dp)
                .clickWithShape(onClick = onClick)
                .background(
                    Brush.verticalGradient(listOf(Color(0xFF81ADFF), Color(0xFF5B58EF))),
                    Shapes.chip
                )
                .graphicsLayer {
                    alpha = if (taskItem.isFinished) 0.5f else 1f
                }
                .padding(horizontal = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(text = taskItem.btnLabel, color = Color.White, fontSize = 14.sp)
        }
    }
}

@Composable
fun HouseTaskPanelUI(vm: HouseTaskViewModel = viewModel(), onRefreshHouseInfo: OnClick = {}) {
    val loading by vm.loading
    val taskSeries by vm.taskSeriesState
    val scope = rememberCoroutineScope()
    LaunchedEffect(key1 = vm) {
        vm.refresh()
    }
    val context = LocalContext.current
    HouseTaskPanel(taskSeries = taskSeries, loading = loading) {
        scope.launch {
            if (it.type == TaskType.CHECK_IN.code) {
                vm.checkIn()
                    .onSuccess {
                        vm.refresh()
                        onRefreshHouseInfo.invoke()
                    }.toastError()
            } else {
                vm.finishTask(it.id)
                    .onSuccess {
                        val link = it.parseValue<String>("jump_link").orEmpty()
                        AppLinkManager.open(context = context, link)
                    }.toastError()
            }
        }
    }
}

class HouseTaskDialog(private val onRefreshHouseInfo: OnClick = {}) :
    AnimatedDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        HouseTaskPanelUI(onRefreshHouseInfo = onRefreshHouseInfo)
    }
}

@Preview
@Composable
private fun HouseTaskPreview() {
    PreviewCupidTheme {
        HouseTaskPanel()
    }
}

class HouseTaskViewModel : ViewModel() {
    private val _loading: MutableState<Boolean> = mutableStateOf(true)
    val loading: ComposeState<Boolean> = _loading
    private val cpHouseApi = createApi<CPHouseApi>()

    private val _taskSeriesState: MutableState<List<TaskSeries>> =
        mutableStateOf(emptyList())
    val taskSeriesState: ComposeState<List<TaskSeries>> = _taskSeriesState

    fun refresh() {
        viewModelScope.launch {
            runApiCatching { cpHouseApi.getHouseTaskInfo() }
                .onSuccess { obj ->
                    _taskSeriesState.value =
                        obj.parseValue<List<TaskSeries>>("infos").orEmpty()
                    _loading.value = false
                }.toastError()
        }
    }

    suspend fun checkIn() = runApiCatching { cpHouseApi.checkIn() }

    suspend fun finishTask(taskId: Int) = runApiCatching { cpHouseApi.finishTask(taskId) }

}