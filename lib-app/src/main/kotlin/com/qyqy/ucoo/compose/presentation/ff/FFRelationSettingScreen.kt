package com.qyqy.ucoo.compose.presentation.ff

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.utils.id2String
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.LoadingLayout
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.FamilyRelation
import com.qyqy.ucoo.toast
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

class FamilyListViewModel : StateViewModelWithIntPage<FamilyRelation>() {
    private val api = createApi<FamilyFriendApi>()

    init {
        refresh()
    }

    override suspend fun loadData(pageNum: Int): Result<List<FamilyRelation>> {
        val id = when (pageNum) {
            firstPage -> 0
            else -> listFlow.value.lastOrNull()?.id ?: 0
        }
        return runApiCatching { api.getFamilyList(id) }.map { obj ->
            obj["relatives"]?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<FamilyRelation>>(
                    it
                )
            }.orEmpty()
        }.toastError()
    }

    suspend fun giveupReleationship(id: Int): Result<Boolean> {
        return runApiCatching { api.giveupRelationship(mapOf("relationship_id" to id.toString())) }
            .map { true }.toastError().onSuccess {
                toast(id2String(R.string.cpd_broke_family))
            }
    }
}

context(BaseActivity)
@Composable
fun FFRelationSettingScreen(onRemoved: () -> Unit, familyListViewModel: FamilyListViewModel = viewModel()) {
    var dialogVisible by remember {
        mutableStateOf(false)
    }
    var brokeId by remember {
        mutableIntStateOf(0)
    }
    AppTheme {
        LoadingLayout(modifier = Modifier.fillMaxSize()) {

            Box(modifier = Modifier.windowInsetsPadding(WindowInsets.Companion.systemBars)) {

                val scope = rememberCoroutineScope()
                val loadingState = LocalContentLoading.current
                Column(modifier = Modifier.fillMaxSize(1f)) {
                    AppTitleBar(title = stringResource(id = R.string.title_family_setting), onBack = { onBackPressedDispatcher.onBackPressed() })
                    FamilyList(onRemoved = {
                        dialogVisible = true
                        brokeId = it
                    }, familyListViewModel)
                }
                if (dialogVisible) {
                    Dialog(onDismissRequest = {
                        dialogVisible = false
                    }) {
                        Column(
                            modifier = Modifier
                                .width(270.dp)
                                .background(Color(0xFF222222), Shapes.small)
                                .padding(vertical = 20.dp),
                            horizontalAlignment = Alignment.CenterHorizontally,
                        ) {
                            Text(
                                modifier = Modifier
                                    .padding(top = 12.dp)
                                    .padding(horizontal = 16.dp),
                                text = stringResource(id = R.string.confirm_broke_family),
                                color = colorResource(id = R.color.white_alpha_50)
                            )
                            Row(
                                modifier = Modifier
                                    .padding(top = 30.dp)
                                    .fillMaxWidth(),
                                horizontalArrangement = Arrangement.SpaceEvenly
                            ) {
                                val sizeModifier = Modifier.size(113.dp, 36.dp)
                                Button(
                                    modifier = sizeModifier,
                                    onClick = { dialogVisible = false },
                                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF464646)),
                                ) {
                                    Text(text = stringResource(id = R.string.取消), color = Color(0xFFA3A3A3))
                                }
                                Button(
                                    modifier = sizeModifier,
                                    onClick = {
                                        scope.launch {
                                            loadingState.value = true
                                            InviteManager.giveupReleationship(brokeId).onSuccess {
                                                familyListViewModel.filterItems { f -> f.id != brokeId }
                                                onRemoved()
                                            }
                                            loadingState.value = false
                                            dialogVisible = false
                                        }
                                    },
                                    colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF945EFF))
                                ) {
                                    Text(text = stringResource(id = R.string.confirm), color = Color.White)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun FamilyList(onRemoved: (id: Int) -> Unit, familyListViewModel: FamilyListViewModel) {
    StateListView(
        viewModel = familyListViewModel,
        keyProvider = { _, familyRelation -> familyRelation.id }) { item: FamilyRelation, _: Int, _: CoroutineScope, _: MutableState<Boolean> ->
        RemoveFriendItem(user = item) {
            onRemoved(item.id)
        }
    }
}
