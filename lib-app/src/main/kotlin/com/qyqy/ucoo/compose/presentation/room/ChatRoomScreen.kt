package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.util.fastForEach
import androidx.compose.ui.util.fastMapIndexed
import androidx.compose.ui.zIndex
import kotlin.math.max
import kotlin.math.min

@Composable
fun ChatRoomScaffold(
    panelState: KeyboardPanelState,
    modifier: Modifier = Modifier,
    topBar: @Composable ColumnScope.() -> Unit = {},
    bottomBar: @Composable ColumnScope.() -> Unit = {},
    panelContent: @Composable BoxScope.() -> Unit = {},
    overlayContent: @Composable BoxScope.() -> Unit = {},
    safeContent: @Composable BoxScope.() -> Unit = {},
    messageContent: @Composable BoxScope.() -> Unit = {},
) {
    Box(modifier = modifier) {
        KeyboardLayout(panelState = panelState) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .statusBarsPadding()
                    .keyboardSystemBarsPadding(panelState)
            ) {
                topBar()
                Box(
                    modifier = Modifier
                        .zIndex(2f)
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    messageContent()
                    safeContent()
                }
                bottomBar()
            }
            Box(
                modifier = Modifier.fillMaxWidth(),
                contentAlignment = Alignment.TopCenter
            ) {
                panelContent()
            }
        }

        overlayContent()
    }
}

@Composable
fun KeyboardLayout(panelState: KeyboardPanelState, modifier: Modifier = Modifier, content: @Composable () -> Unit) {
    Layout(
        content = content,
        modifier = modifier
    ) { measurables, constraints ->
        check(measurables.size == 2)
        var layoutWidth = constraints.minWidth
        var layoutHeight = 0
        val placeables = measurables.fastMapIndexed { index, measurable ->
            // 测量每个子元素，我们修改了最小的宽高为0
            // 这是因为，当父元素被设置为 fillMaxSize 时，constraints.maxWidth 与 constraints.maxHeight 会被设置为 Int.MAX_VALUE
            // 而我们的子元素并不需要也占据满父元素，所以我们将最小宽高设置为0
            val contentConstraints = if (index == 0) {
                constraints.copy(minWidth = 0, minHeight = 0, maxHeight = constraints.maxHeight.minus(panelState.paddingHeight).coerceAtLeast(0))
            } else {
                constraints.copy(minWidth = 0, minHeight = 0)
            }
            measurable.measure(contentConstraints).also {
                layoutWidth = max(it.width, layoutWidth)
                layoutHeight += it.height
            }
        }
        var yPosition = 0
        layout(min(layoutWidth, constraints.maxWidth), layoutHeight.coerceIn(constraints.minHeight, constraints.maxHeight)) {
            placeables.fastForEach { placeable ->
                // placeRelative 会根据当前 layoutDirection 自动调整子元素的位置（从左至右或从右至左）
                placeable.placeRelative(x = 0, y = yPosition)
                yPosition += placeable.height
            }
        }
    }
}