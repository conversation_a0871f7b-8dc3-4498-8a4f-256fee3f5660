package com.qyqy.ucoo.compose.presentation.wedding

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContextCompat
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.getShadowColor
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingPreInfo
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingRoomType
import com.qyqy.ucoo.compose.state.ComposeLoading
import com.qyqy.ucoo.compose.state.StatePage
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.toUIData
import com.qyqy.ucoo.compose.state.toUIState
import com.qyqy.ucoo.compose.theme.MI_SANS
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

@Composable
fun WeddingPreviewScreen(onlySelf: Boolean = false) {
    val owner = LocalOnBackPressedDispatcherOwner.current
    var mode by remember {
        mutableStateOf(if (onlySelf) CheckMode.SELF else CheckMode.ALL)
    }
    val vm = viewModel<WeddingPreviewViewModel>()
    LaunchedEffect(key1 = mode) {
        vm.fetchData(mode == CheckMode.SELF)
    }
    val state by vm.state
    val context = LocalContext.current
    val rule by vm.ruleState
    WallScreen(title = stringResource(R.string.婚礼预告), onBack = { owner?.onBackPressedDispatcher?.onBackPressed() }, onQuestion = {
        AppLinkManager.open(context, rule)
    }) {
        Column(modifier = Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 26.dp)
                    .padding(top = 16.dp, bottom = 6.dp)
            ) {
                CheckModeSwitchButton(mode = mode, modifier = Modifier.align(Alignment.BottomEnd)) {
                    mode = it
                }
            }
            StatePage(state = state, loadingUI = {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1f), contentAlignment = Alignment.Center
                ) {
                    ComposeLoading(color = Color.White)
                }
            }, onRetry = { vm.fetchData(mode == CheckMode.SELF) }) { list ->
                LazyColumn(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(horizontal = 10.dp), verticalArrangement = Arrangement.spacedBy(18.dp)
                ) {
                    if (list.isEmpty()) {
                        item {
                            WeddingEmpty(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(top = 70.dp),
                                emptyMessage = stringResource(id = R.string.empty_booking_list)
                            )
                        }
                    } else {
                        items(list) { item ->
                            val info = item.weddingInfo
                            WeddingPreviewItem(
                                userLeft = info.lightUser,
                                userRight = info.targetUser,
                                ringImageUrl = info.ring.icon,
                                theme = WeddingTheme.getByType(info.roomType),
                                roomTypeDesc = item.roomTypeDesc,
                                roomNum = info.audioroom.publicId,
                                time = item.previewTime,
                                buttonText = item.btnDesc,
                                started = item.weddingInfo.status == WeddingPreInfo.WeddingInfo.ONGOING
                            ) {
                                val roomId = info.audioroom.id.toString()
                                AppLinkManager.openChatRoom(context, roomId,2)
                            }
                        }
                        item {
                            Spacer(modifier = Modifier.height(20.dp))
                        }
                    }
                }
            }
        }
    }
}

class WeddingPreviewViewModel : ViewModel() {
    private val _state: MutableState<UIState<List<WeddingPreInfo>>> = mutableStateOf(UIState.Loading(""))
    val state: ComposeState<UIState<List<WeddingPreInfo>>> = _state
    private val api = createApi<WeddingApi>()

    private var _listAll: List<WeddingPreInfo>? = null
    private var _listSelf: List<WeddingPreInfo>? = null

    private val _ruleState: MutableState<String> = mutableStateOf("")
    val ruleState: ComposeState<String> = _ruleState

    fun fetchData(onlyMe: Boolean = false) {
        viewModelScope.launch {
            if (onlyMe) {
                _listSelf?.also {
                    _state.value = it.toUIData()
                } ?: run {
                    _state.value = UIState.Loading("")
                }
            } else {
                _listAll?.also {
                    _state.value = it.toUIData()
                } ?: run {
                    _state.value = UIState.Loading("")
                }
            }
            _state.value = runApiCatching { api.getWeddingPreviewData(onlyMe) }
                .toastError()
                .map {
                    _ruleState.value = it.parseValue<String>("rule_url").orEmpty()
                    it.parseValue<List<WeddingPreInfo>>("weddings").orEmpty()
                }
                .onSuccess {
                    if (onlyMe) {
                        _listSelf = it
                    } else {
                        _listAll = it
                    }
                }
                .toUIState()
        }
    }
}

private const val aspectRatioItem = 351 / 268f

@Composable
private fun WeddingPreviewItem(
    roomTypeDesc: String,
    theme: WeddingTheme,
    userLeft: AppUser,
    userRight: AppUser,
    ringImageUrl: String,
    roomNum: String,
    time: String,
    buttonText: String,
    started: Boolean = true,
    onClick: OnClick = {}
) {
    WeddingCard(theme = theme, roomTypeDesc = roomTypeDesc, modifier = Modifier.fillMaxWidth()) {
        Spacer(modifier = Modifier.height(36.dp))
        Row(horizontalArrangement = Arrangement.Center) {
            UserColumn(user = userLeft, color = theme.primaryColor, borderColor = theme.secondaryColor)
            ComposeImage(model = ringImageUrl, modifier = Modifier.size(56.dp), contentScale = ContentScale.FillBounds)
            UserColumn(user = userRight, color = theme.primaryColor, borderColor = theme.secondaryColor)
        }
        val textStyle = remember {
            TextStyle(color = theme.primaryColor, fontSize = 14.sp, fontWeight = FontWeight.Medium)
        }
        Spacer(modifier = Modifier.height(24.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 36.dp), horizontalArrangement = Arrangement.SpaceBetween
        ) {
            AppText(text = stringResource(R.string.婚礼房号), style = textStyle)
            AppText(text = roomNum, style = textStyle)
        }
        Spacer(modifier = Modifier.height(16.dp))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 36.dp), horizontalArrangement = Arrangement.SpaceBetween
        ) {
            AppText(text = stringResource(R.string.婚礼时间), style = textStyle)
            AppText(text = time, style = textStyle)
        }
        Spacer(modifier = Modifier.height(10.dp))
        val colorFiler = remember {
            ColorFilter.colorMatrix(ColorMatrix().apply {
                setToSaturation(0f)
            })
        }
        val showButton = buttonText.isNotEmpty()
        Box(
            modifier = Modifier
                .widthIn(min = 124.dp)
                .heightIn(min = 58.dp)
                .then(if (showButton) Modifier.paint(painterResource(if (started) theme.buttonRes else R.drawable.btn_wd_not_start)) else Modifier)
                .click(enabled = started && showButton, onClick = onClick),
            contentAlignment = Alignment.Center
        ) {
            if (showButton) {
                AppText(
                    text = buttonText,
                    fontWeight = FontWeight.Bold,
                    textAlign = TextAlign.Center,
                    color = Color.White,
                    modifier = Modifier
                        .widthIn(min = 124.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Composable
fun WeddingCard(theme: WeddingTheme, roomTypeDesc: String, modifier: Modifier = Modifier, content: @Composable () -> Unit) {
    val context = LocalContext.current
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = modifier.paint(
            painter =
            rememberDrawablePainter(drawable = ContextCompat.getDrawable(context, theme.bgRes)), contentScale = ContentScale.FillBounds
        )
    ) {
        Text(
            text = roomTypeDesc,
            modifier = Modifier.padding(top = 24.dp),
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 14.sp,
                fontFamily = FontFamily.MI_SANS,
                fontWeight = FontWeight.Normal,
                color = Color.White,
                shadow = Shadow(
                    color = theme.primaryColor.getShadowColor(),
                    offset = Offset(3f, 3f),
                    blurRadius = 4f
                ),
            )
        )
        content()
    }
}

@Preview
@Composable
private fun CardPre() {
    val theme = WeddingTheme.getByType(WeddingRoomType.LOVE_FOREVER_RED)
    WeddingCard(
        theme = theme,
        roomTypeDesc = "真爱永恒", modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(aspectRatioItem)
    ) {

    }
}

@Preview
@Composable
private fun PreviewItem() {
    val u = userForPreview
    WeddingPreviewItem(
        theme = WeddingTheme.getByType(WeddingRoomType.STAR_PROMISE_BLUE),
        roomTypeDesc = "星辰之诺",
        userLeft = u,
        userRight = u,
        ringImageUrl = "",
        roomNum = "520",
        time = "2025.03.08.20:00-23:00",
        buttonText = "去祝福"
    )
}