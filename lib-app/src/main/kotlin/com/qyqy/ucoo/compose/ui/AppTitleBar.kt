package com.qyqy.ucoo.compose.ui

import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R


@Composable
fun AppTitleBar(
    title: String,
    @DrawableRes backIcon: Int = R.drawable.ic_navigation_back,
    modifier: Modifier = Modifier,
    onBack: () -> Unit = {},
    content: @Composable BoxScope.() -> Unit = {}
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(48.dp),
    ) {
        Image(
            painter = painterResource(id = backIcon),
            contentDescription = "back",
            contentScale = ContentScale.Inside,
            modifier = Modifier
                .fillMaxHeight()
                .aspectRatio(1f)
                .clip(CircleShape)
                .clickable {
                    onBack()
                }
        )
        if (title.isNotEmpty()) {
            Text(
                text = title,
                color = Color.White,
                fontSize = 18.sp,
                modifier = Modifier.align(Alignment.Center)
            )
        }
        content()
    }
}

@Composable
fun TitleMenuAppBarScaffold(
    modifier: Modifier = Modifier,
    horizontalArrangement: Arrangement.Horizontal = Arrangement.Start,
    back: @Composable RowScope.() -> Unit = {},
    title: @Composable ColumnScope.() -> Unit = {},
    subTitle: @Composable ColumnScope.() -> Unit = {},
    menu: @Composable RowScope.() -> Unit = {},
    extension: @Composable BoxScope.() -> Unit = {},
) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = horizontalArrangement,
    ) {
        back()
        Column {
            title()
            subTitle()
        }
        Box(modifier = Modifier.weight(1f)) {
            extension()
        }
        menu()
    }
}


@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun AppTitleBarPreviewer() {
    AppTitleBar(title = "发送亲友团邀请")
}