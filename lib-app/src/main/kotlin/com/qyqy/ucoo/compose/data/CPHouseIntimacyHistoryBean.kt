package com.qyqy.ucoo.compose.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep

/**
{
"change_reason": "xxxx" ,
"score": "xxxx" ,
"create_timestamp": 1000000
}
 */
@Keep
@Serializable
data class CPHouseIntimacyHistoryBean(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("user_name")
    val userName: String = "",
    @SerialName("change_reason")
    val changeReason: String = "",
    @SerialName("create_timestamp")
    val createTimestamp: Int = 0,
    @SerialName("readable_timestamp")
    val readableTimeStamp: String = "",
    @SerialName("score")
    val score: String = ""
)