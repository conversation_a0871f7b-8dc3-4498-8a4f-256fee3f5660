package com.qyqy.ucoo.compose.presentation.room

import android.app.Activity
import android.content.Context
import android.os.Bundle
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.res.vectorResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.zIndex
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.qyqy.cupid.widgets.wheel.datetime.WheelTextPicker
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.CrossPkConfigResponse
import com.qyqy.ucoo.compose.data.PkRoomBasicInfo
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.vm.room.cross.CrossRoomPkRepository
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.toast
import kotlinx.coroutines.launch

object RoomPkCreateNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val config = remember {
            bundle.getParcelable<CrossPkConfigResponse>(Const.KEY_PARAMS)?.checkFix()
        }
        val selfRoomInfo = remember {
            bundle.getParcelable<PkRoomBasicInfo>(Const.KEY_DATA)
        }
        if (config != null && selfRoomInfo != null) {
            RoomPkCreateHost(config = config, selfRoomInfo = selfRoomInfo)
        } else {
            LaunchedEffect(Unit) {
                activity.finish()
            }
        }
    }

    fun navigate(context: Context, room: Room, config: CrossPkConfigResponse) {
        navigate(context) {
            putExtra(Const.KEY_PARAMS, config)
            putExtra(
                Const.KEY_DATA, PkRoomBasicInfo(
                    id = room.roomId,
                    publicId = room.publicId,
                    title = room.roomName,
                    avatarUrl = room.owner.avatarUrl,
                    audienceCount = room.audienceCount
                )
            )
        }
    }
}

@Composable
fun RoomPkCreateHost(
    config: CrossPkConfigResponse,
    selfRoomInfo: PkRoomBasicInfo,
) {
    val activity = LocalContext.current as? Activity
    val navController = rememberNavController()
    val targetRoomInfoState = rememberSaveable {
        mutableStateOf<PkRoomBasicInfo?>(null)
    }
    NavHost(
        navController = navController,
        startDestination = "create",
        enterTransition = {
            slideInHorizontally(initialOffsetX = { it })
        },
        exitTransition = {
            slideOutHorizontally(targetOffsetX = { -it })
        },
        popEnterTransition = {
            slideInHorizontally(initialOffsetX = { -it })
        },
        popExitTransition = {
            slideOutHorizontally(targetOffsetX = { it })
        },
    ) {
        composable("create") {

            val pkRepository = remember {
                CrossRoomPkRepository()
            }

            val scope = rememberCoroutineScope()

            RoomPkCreateScreen(
                config = config,
                selfRoomInfo = selfRoomInfo,
                targetRoomInfoState = targetRoomInfoState,
                onNavRoomSelected = {
                    navController.navigate("list")
                },
                onBackPressed = {
                    activity?.finish()
                }
            ) { id, duration ->
                Analytics.reportClickEvent("invite_cross_pk", useShushu = true)
                scope.launch {
                    pkRepository.invitePk(id, duration, 0).onSuccess {
                        toast(it.toast)
                        activity?.finish()
                    }.toastError()
                }
            }
        }

        composable("list") {
            RoomListScreen(
                onRoomSelect = {
                    if (selfRoomInfo.id != it.id) {
                        targetRoomInfoState.value = it
                        navController.popBackStack()
                    }
                }
            ) {
                navController.popBackStack()
            }
        }
    }
}

@Composable
private fun RoomPkCreateScreen(
    config: CrossPkConfigResponse,
    selfRoomInfo: PkRoomBasicInfo,
    targetRoomInfoState: State<PkRoomBasicInfo?>,
    onNavRoomSelected: () -> Unit = {},
    onBackPressed: () -> Unit = {},
    onSendInvitation: (Int, Int) -> Unit = { _, _ -> }
) {

    val targetRoomInfo by targetRoomInfoState

    var pkDuration by rememberSaveable {
        mutableIntStateOf(config.defaultDuration)
    }

    val enabled by remember {
        derivedStateOf {
            targetRoomInfo != null && pkDuration > 0
        }
    }

    // 主容器，设置背景颜色
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .statusBarsPadding()
    ) {
        // 头部区域，包含返回按钮和标题
        Header(onBackPressed = onBackPressed)

        // 主要内容区域
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .padding(top = 16.dp)
                .weight(1f),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // 房间选择区域
            RoomSelectionSection(
                selfRoomInfo = selfRoomInfo,
                targetRoomInfo = targetRoomInfo,
                onNavRoomSelected = onNavRoomSelected
            )

            // 设置区域
            SettingsSection(pkDuration, config) {
                if (it in config.durations) {
                    pkDuration = it
                }
            }

            // 空白区域，将按钮推到底部
            Spacer(modifier = Modifier.height(32.dp))

            // 操作按钮
            ActionButtons(enabled = enabled, onSendInvitation = {
                targetRoomInfo?.also {
                    onSendInvitation(it.id, pkDuration)
                }
            })

        }

        // 底部把手
        BottomHandle()
    }
}


@Composable
private fun Header(onBackPressed: () -> Unit) {
    AppTitleBar(title = stringResource(R.string.create_cross_room_pk), onBack = onBackPressed)
}

@Composable
private fun RoomSelectionSection(selfRoomInfo: PkRoomBasicInfo, targetRoomInfo: PkRoomBasicInfo?, onNavRoomSelected: () -> Unit) {
    // 房间选择区域，包含当前房间和对方房间
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(Color(0xFFFF30DA), Color(0xFF4D88FF)),
                    start = androidx.compose.ui.geometry.Offset.Zero, // 左上角 (0, 0)
                    end = androidx.compose.ui.geometry.Offset.Infinite // 右下角 (无限大, 无限大) - 覆盖整个区域)
                ),
                shape = RoundedCornerShape(16.dp)
            )
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(Color(0x00FFFFFF), Color(0xFFFFFFFF)),
                    start = androidx.compose.ui.geometry.Offset.Zero, // 左上角 (0, 0)
                    end = androidx.compose.ui.geometry.Offset.Infinite // 右下角 (无限大, 无限大) - 覆盖整个区域)
                ),
                shape = RoundedCornerShape(16.dp),
                alpha = 0.65f
            )
            .border(
                width = 1.dp,
                brush = Brush.Companion.sweepGradient(
                    listOf(
                        Color(0xFFB7BBFF),
                        Color(0xFF8BB2FF),
                        Color(0xFFFF72E5),
                        Color(0xFFE2C4FF),
                    )
                ),
                shape = RoundedCornerShape(16.dp)
            )
            .border(
                width = 1.dp,
                brush = Brush.Companion.sweepGradient(
                    listOf(
                        Color(0xFFFFFFFF), Color(0xA6FFFFFF)
                    )
                ),
                shape = RoundedCornerShape(16.dp),
            )
            .padding(16.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            // 当前房间信息
            RoomInfoBox(roomInfo = selfRoomInfo, label = stringResource(R.string.my_side))

            // VS图标
            Image(
                painter = painterResource(id = R.drawable.ic_cross_room_pk_v1),
                contentDescription = stringResource(R.string.vs),
                modifier = Modifier
                    .size(width = 68.dp, height = 56.dp)
                    .align(Alignment.CenterVertically)
            )

            Box(modifier = Modifier.noEffectClickable(onClick = onNavRoomSelected)) {
                if (targetRoomInfo != null) {
                    RoomInfoBox(roomInfo = targetRoomInfo, label = stringResource(R.string.opponent_side))
                } else {
                    // 对方房间信息
                    Column(
                        modifier = Modifier
                            .clip(RoundedCornerShape(12.dp))
                            .background(Color(0x26FFFFFF))
                            .border(
                                width = 0.5.dp,
                                brush = Brush.verticalGradient(
                                    colors = listOf(Color(0x4DFFFFFF), Color(0x00FFFFFF))
                                ),
                                shape = RoundedCornerShape(12.dp)
                            ),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        // 对方房间图片区域
                        Box(
                            modifier = Modifier.width(110.dp)
                        ) {
                            // "对方"标签
                            Box(
                                modifier = Modifier
                                    .align(Alignment.TopStart)
                                    .height(20.dp)
                                    .background(
                                        color = Color(0xFFFFD39C),
                                        shape = RoundedCornerShape(
                                            topStart = 12.dp,
                                            topEnd = 0.dp,
                                            bottomStart = 0.dp,
                                            bottomEnd = 12.dp
                                        )
                                    )
                                    .padding(horizontal = 8.dp)
                                    .zIndex(1f),
                                contentAlignment = Alignment.Center
                            ) {
                                Text(
                                    text = stringResource(R.string.opponent_side),
                                    color = Color(0xFF4C2D08),
                                    fontSize = 12.sp,
                                    lineHeight = 12.sp,
                                    fontWeight = FontWeight.SemiBold
                                )
                            }

                            // 对方头像占位
                            Box(
                                modifier = Modifier
                                    .align(Alignment.Center)
                                    .padding(top = 24.dp)
                                    .size(56.dp)
                                    .background(
                                        color = Color(0x59FFFFFF),
                                        shape = CircleShape
                                    ),
                                contentAlignment = Alignment.Center
                            ) {
                                Image(
                                    painter = painterResource(R.drawable.ic_add_photo),
                                    contentDescription = stringResource(R.string.placeholder),
                                    modifier = Modifier.size(24.dp),
                                    colorFilter = ColorFilter.tint(Color(0xFFA798E9))
                                )
                            }

                        }

                        // 邀请按钮
                        Box(
                            modifier = Modifier
                                .padding(top = 16.dp, bottom = 8.dp)
                                .align(Alignment.CenterHorizontally)
                                .size(64.dp, 28.dp)
                                .background(
                                    color = Color(0xFF945EFF),
                                    shape = CircleShape
                                ),
                            contentAlignment = Alignment.Center
                        ) {
                            Text(
                                text = stringResource(R.string.invite),
                                color = Color.White,
                                fontSize = 12.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun RoomInfoBox(roomInfo: PkRoomBasicInfo, label: String) {
    Column(
        verticalArrangement = Arrangement.spacedBy(8.dp),
    ) {
        // 房间图片
        Box(
            modifier = Modifier
                .size(110.dp)
                .clip(RoundedCornerShape(12.dp))
        ) {


            ComposeImage(
                model = roomInfo.avatarUrl,
                contentDescription = stringResource(R.string.current_room_image),
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )

            // "label"标签
            Box(
                modifier = Modifier
                    .align(Alignment.TopStart)
                    .height(20.dp)
                    .background(
                        color = Color(0xFFFFD39C),
                        shape = RoundedCornerShape(
                            topStart = 12.dp,
                            topEnd = 0.dp,
                            bottomStart = 0.dp,
                            bottomEnd = 12.dp
                        )
                    )
                    .padding(horizontal = 8.dp)
                    .zIndex(1f),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = label,
                    color = Color(0xFF4C2D08),
                    fontSize = 12.sp,
                    lineHeight = 12.sp,
                    fontWeight = FontWeight.SemiBold
                )
            }
        }

        // 房间名称
        Text(
            text = roomInfo.title,
            modifier = Modifier.width(110.dp),
            color = Color(0xFF7138BC),
            fontWeight = FontWeight.Medium,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            fontSize = 14.sp,
            textAlign = TextAlign.Center
        )
    }
}

@Composable
private fun SettingsSection(pkDuration: Int, config: CrossPkConfigResponse, onDurationChange: (Int) -> Unit) {
    // 设置区域，包含获胜方式和时间设置

    val showSelectDialog = rememberSaveable {
        mutableStateOf(false)
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFF272829))
            .padding(16.dp)
    ) {
        Column {
            // 获胜方式设置
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.winning_method),
                    color = Color.White,
                    fontSize = 14.sp
                )

                Text(
                    text = config.getDesc(),
                    color = Color.White.copy(alpha = 0.5f),
                    fontSize = 12.sp
                )
            }

            // 分隔线
            Divider(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 20.dp),
                color = Color.White.copy(alpha = 0.1f),
                thickness = 1.dp
            )

            // 时间设置
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .noEffectClickable {
                        showSelectDialog.value = true
                    },
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.time_setting),
                    color = Color.White,
                    fontSize = 14.sp
                )

                // 时间选择器
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    horizontalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    Text(
                        text = stringResource(R.string.format_minutes, pkDuration),
                        color = Color.White.copy(alpha = 0.5f),
                        fontSize = 12.sp
                    )

                    Icon(
                        imageVector = ImageVector.vectorResource(id = R.drawable.ic_arrow_right),
                        contentDescription = stringResource(R.string.select),
                        tint = Color.White.copy(alpha = 0.5f),
                        modifier = Modifier.size(12.dp)
                    )
                }
            }
        }

        if (showSelectDialog.value) {
            WheelPickerBottomSheet(
                initValue = pkDuration,
                durations = config.durations,
                title = stringResource(R.string.time_setting),
                modifier = Modifier.fillMaxWidth(),
                onSelectedChanged = onDurationChange
            ) {
                showSelectDialog.value = false
            }
        }
    }
}

@Composable
private fun ActionButtons(enabled: Boolean, onSendInvitation: () -> Unit) {
    // 操作按钮区域
    // 启用状态的按钮
    Button(
        onClick = onSendInvitation,
        modifier = Modifier
            .fillMaxWidth()
            .height(44.dp),
        enabled = enabled,
        shape = RoundedCornerShape(22.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = Color(0xFF945EFF),
            contentColor = Color.White,
            disabledContainerColor = Color(0xFF945EFF).copy(alpha = 0.3f),
            disabledContentColor = Color.White.copy(alpha = 0.3f)
        )
    ) {
        Text(
            text = stringResource(R.string.send_pk_invitation),
            fontSize = 16.sp
        )
    }
}

@Composable
private fun BottomHandle() {
    // 底部把手，用于手势操作
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(bottom = 8.dp),
        contentAlignment = Alignment.Center
    ) {
        Box(
            modifier = Modifier
                .width(134.dp)
                .height(5.dp)
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(100.dp)
                )
        )
    }
}

@Composable
fun WheelPickerBottomSheet(
    initValue: Int,
    durations: List<Int>,
    title:String,
    modifier: Modifier = Modifier,
    containerColor: Color = Color(0xFF222222),
    shape: Shape = Shapes.corner12,
    onSelectedChanged: (Int) -> Unit = {},
    onDismissRequest: () -> Unit = {},
) {
    val scope = rememberCoroutineScope()

    var isActiveClose by remember { mutableStateOf(false) }
    AnimatedDialog(isActiveClose, onDismiss = onDismissRequest) {
        Box(
            modifier = modifier.background(containerColor, shape)
        ) {
            Column(
                modifier = Modifier.padding(bottom = 16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(44.dp),
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = title, color = Color.White, fontSize = 16.sp)
                }

                val context = LocalContext.current
                val texts = remember {
                    durations.map {
                        context.getString(R.string.format_minutes, it)
                    }
                }
                val startIndex = remember {
                    durations.indexOfFirst { it == initValue }.coerceIn(0, durations.lastIndex)
                }

                var snappedIndex by rememberSaveable {
                    mutableIntStateOf(startIndex)
                }

                WheelTextPicker(
                    modifier = Modifier.width(114.dp),
                    startIndex = startIndex,
                    height = 240.dp,
                    texts = texts,
                    rowCount = 5,
                    style = TextStyle(fontSize = 16.sp, color = Color.White),
                    color = Color.White,
                    unSelectedColor = Color.White.copy(alpha = 0.8f),
                ) { index ->
                    snappedIndex = index
                    index
                }

                Button(
                    onClick = {
                        scope.launch {
                            durations.getOrNull(snappedIndex)?.also {
                                onSelectedChanged(it)
                            }
                            isActiveClose = true
                        }
                    },
                    modifier = Modifier
                        .padding(horizontal = 32.dp)
                        .fillMaxWidth()
                        .height(44.dp),
                    shape = RoundedCornerShape(22.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color(0xFF945EFF),
                        contentColor = Color.White,
                    )
                ) {
                    Text(
                        text = stringResource(R.string.confirm_changes),
                        fontSize = 16.sp
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewRoomPkCreateScreen() {
    RoomPkCreateScreen(CrossPkConfigResponse.createEmpty().checkFix(), PkRoomBasicInfo(1, "1", "房间名称", "", 1), remember { mutableStateOf(null) })
}