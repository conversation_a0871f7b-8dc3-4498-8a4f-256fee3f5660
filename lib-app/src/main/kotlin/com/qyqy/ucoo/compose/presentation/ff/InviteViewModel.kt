package com.qyqy.ucoo.compose.presentation.ff

import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonPrimitive

abstract class InviteViewModel : StateViewModelWithIntPage<UserInfo>() {

    protected abstract suspend fun protectedInviteUser(userId: Int): Result<Boolean>

    suspend fun inviteUser(userId: Int): Result<Boolean> {
        return protectedInviteUser(userId).onSuccess {
            updateItem({ it.userId == userId }) {
                it.copy(hasInvited = true)
            }
        }
    }
}

class LatestViewModel(private val tagId: Int) : InviteViewModel() {

    private val api = createApi<FamilyFriendApi>()

    init {
        refresh()
    }

    private var lastChatId = 0

    override suspend fun loadData(pageNum: Int): Result<List<UserInfo>> {
        val lastId = if (pageNum == firstPage) 0 else lastChatId
        val result = runApiCatching { api.getLatestChatUserList(lastId) }
        return if (result.isSuccess) {
            val jsonObject = result.getOrThrow()
            lastChatId = jsonObject["last_chat_id"]?.jsonPrimitive?.intOrNull ?: 0
            jsonObject["users"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<UserInfo>>(it) }?.let { Result.success(it) }
                ?: Result.success(emptyList())
        } else {
            Result.failure<List<UserInfo>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    override suspend fun protectedInviteUser(userId: Int): Result<Boolean> {
        return InviteManager.invite(userId, tagId)
    }
}

class MyFriendsViewModel(private val tagId: Int) : InviteViewModel() {

    private val api = createApi<FamilyFriendApi>()

    init {
        refresh()
    }

    override suspend fun loadData(pageNum: Int): Result<List<UserInfo>> {
        val lastId = if (pageNum == firstPage) 0 else listFlow.value.lastOrNull()?.relationId ?: 0
        return runApiCatching {
            api.getFriendsList(lastId)
        }.mapCatching { obj ->
            obj["friends"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<UserInfo>>(it) }.orEmpty()
        }
    }

    override suspend fun protectedInviteUser(userId: Int): Result<Boolean> {
        return InviteManager.invite(userId, tagId)
    }
}
