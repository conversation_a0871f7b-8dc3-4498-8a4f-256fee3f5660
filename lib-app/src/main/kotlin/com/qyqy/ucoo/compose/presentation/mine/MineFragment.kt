package com.qyqy.ucoo.compose.presentation.mine

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.base.BaseFragment
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.home.IHomeTab
import com.qyqy.ucoo.utils.statistic.ReportKey
import com.qyqy.ucoo.utils.statistic.ReportObj

class MineFragment : BaseFragment(), IHomeTab, ReportObj {

    override val reportKey: String
        get() = ReportKey.mine

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        return ComposeView(requireContext()).apply {
            // Dispose of the Composition when the view's LifecycleOwner
            // is destroyed
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                val k by restoreKey
                key(k) {
                    MinePageRouter()
                }
            }
        }
    }

    override fun onResume(first: Boolean) {
        super.onResume(first)
        Analytics.reportClickEvent(TracePoints.MY_PAGE)
    }
}