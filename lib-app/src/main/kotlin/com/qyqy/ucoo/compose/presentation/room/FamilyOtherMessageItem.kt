package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.chatgroup.page.LocalMsgEventHandler
import com.qyqy.ucoo.compose.presentation.chatgroup.page.MsgEvents
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry

class FamilyCreatedMessageItem(entry: UIMessageEntry<UCCustomMessage>) : MessageItem.EventMessage(entry, contentType = "cupid-family-created") {
    @Composable
    override fun LazyItemScope.Content() {
        Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
            FamilyCreatedContent(message.getJsonString("value").orEmpty(), message.getJsonString("name").orEmpty())
        }
    }
}


@Composable
fun FamilyCreatedContent(tribeId: String, name: String, modifier: Modifier = Modifier) {
    val shape = Shapes.corner12
    val eh = LocalMsgEventHandler.current
    Column(
        modifier = modifier
            .width(300.dp)
            .background(Color.White, shape)
            .border(0.5.dp, Color(0xFFF0F0F0), shape)
            .padding(12.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val mod = Modifier.fillMaxWidth()
        val style = TextStyle(Color(0xFF4E5969), fontSize = 14.sp, lineHeight = 18.sp)
        Text(text = stringResource(id = R.string.cpd_family_created_title), modifier = mod, style = style)
        Text(text = stringResource(id = R.string.cpd_format_family_name, name), modifier = mod, style = style)
        Text(text = stringResource(id = R.string.cpd_format_family_id, tribeId), modifier = mod, style = style)
        Text(text = stringResource(id = R.string.cpd_family_guide), modifier = mod, style = style)
        Row(verticalAlignment = Alignment.CenterVertically, modifier = Modifier
            .click {
                eh?.handle(MsgEvents.EditFamilyBuilt)
            }
            .padding(top = 8.dp)) {
            Text(
                text = stringResource(id = R.string.cpd_edit_family_built),
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.primary
            )
            Spacer(modifier = Modifier.width(2.dp))
            Icon(
                painter = painterResource(id = R.drawable.ic_arrow_right),
                contentDescription = "",
                modifier = Modifier.size(12.dp),
                tint = MaterialTheme.colorScheme.primary
            )
        }
    }
}

@Preview
@Composable
private fun FamilyCreatedPreview() {
    PreviewCupidTheme {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(2f), contentAlignment = Alignment.Center
        ) {
            FamilyCreatedContent("123", "Super Hero")
        }
    }
}