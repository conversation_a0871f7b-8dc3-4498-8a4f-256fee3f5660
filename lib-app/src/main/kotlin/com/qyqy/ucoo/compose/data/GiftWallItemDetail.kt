package com.qyqy.ucoo.compose.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import com.qyqy.ucoo.im.bean.BlessEntity
import com.qyqy.ucoo.im.bean.Gift
import com.qyqy.ucoo.im.bean.IGift

/**
{
"id": 34,
"name": "空中花园",
"icon": "https://xxxx",
"price_type": 1,
"price": 45,
"is_blindbox_gift": false, // 如果是盲盒礼物，交互上是拉起礼物面板，定位到盲盒。
"blindbox_id": 0,  // 非盲盒礼物时为0
"packet_cnt": 20,  // 背包中该礼物数量，当该数量大于等于赠送数量时，用背包礼物赠送，当前需求中，赠送数量都是1
"star_cnt": 35,
"action_btn_txt": "赠送/求打赏/打赏礼物",
"can_action": true,
"cant_action_hint": "该礼物已下线", # can_give为False时，按钮点击提示，可能为"", 为""则点击无反应
"hint": "背包礼物可赠送",

"beg_cnt": 5
}
 */
@Keep
@Serializable
data class GiftWallItemDetail(
    @SerialName("action_btn_txt")
    val actionBtnTxt: String = "",
    @SerialName("blindbox_id")
    val blindboxId: Int = 0,
    @SerialName("can_action")
    val canAction: Boolean = false,
    @SerialName("cant_action_hint")
    val cantActionHint: String = "",
    @SerialName("hint")
    val hint: String = "",
    @SerialName("icon")
    override val icon: String = "",
    @SerialName("id")
    override val id: Int = 0,
    @SerialName("is_blindbox_gift")
    val isBlindboxGift: Boolean = false,
    @SerialName("name")
    override val name: String = "",
    @SerialName("packet_cnt")
    val packetCnt: Int = 0,
    @SerialName("price")
    override val price: Int = 0,
    @SerialName("price_type")
    override val priceType: Int = 0,
    @SerialName("star_cnt")
    val starCnt: Int = 0,
    @SerialName("beg_cnt")
    val begCount: Int? = null,
    @SerialName("popup_type")
    val popupType: Int = 3,
    /**
     * 礼物类型,详情见[com.qyqy.ucoo.im.bean.CPGift]
     */
    @SerialName("t")
    val type: Int = 0,
    @SerialName("bless")
    val bless: BlessEntity? = null,

    ) : IGift, java.io.Serializable {
    constructor(gift: IGift) : this(id = gift.id, icon = gift.icon, name = gift.name, price = gift.price, priceType = gift.priceType)
}