package com.qyqy.ucoo.compose.ui.dialogs

import android.app.Activity
import android.view.ViewGroup
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.selection.selectable
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.semantics.Role
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.base.show
import com.qyqy.ucoo.compose.data.FirstRechargeGood
import com.qyqy.ucoo.compose.data.FirstRechargeInfo
import com.qyqy.ucoo.compose.data.ReChargeItem
import com.qyqy.ucoo.compose.data.SubItem
import com.qyqy.ucoo.compose.rememberSaveableRefWithPrevious
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CommonButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.sUserKV
import com.qyqy.ucoo.user.WalletContract
import com.qyqy.ucoo.user.WalletViewModel
import com.qyqy.ucoo.widget.dialog.AppBottomSheetDialog
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach


@Composable
private fun FirstRechargeDialog(
    info: FirstRechargeInfo,
    onBuy: (Int, String, String) -> Unit = { _, _, _ -> },
) {
    val isPlayChannel = BuildConfig.FLAVOR_PAY == "googlePay"
    // 当前选中的付款方式
    var selectedFkOption by rememberSaveableRefWithPrevious<FirstRechargeGood?>(info.goods) { prev ->
        if (isPlayChannel) {
            mutableStateOf(info.goods.find { it.fkChannel == 1 })
        } else {
            if (prev == null) {
                mutableStateOf(info.goods.firstOrNull())
            } else {
                mutableStateOf(info.goods.find {
                    it.fkChannel == prev.fkChannel
                } ?: info.goods.firstOrNull())
            }
        }
    }

    // 当前选中的付款方式对应的商品
    val selectedMemberOption by remember {
        derivedStateOf {
            selectedFkOption?.items?.firstOrNull()
        }
    }

    //region 2.40.0 版本更新dialog, 使用动态高度

    Box {

        Column(
            modifier = Modifier
                .padding(top = 104.dp)
                .fillMaxWidth()
                .background(
                    brush = Brush.verticalGradient(
                        colors = listOf(Color(0xFFFF4057), Color(0xFFFFA0C2))
                    ), shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
                .border(
                    width = 1.dp, brush = Brush.verticalGradient(
                        colors = listOf(Color(0xFFFF6F80), Color(0xFFFFA0C2))
                    ), shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
                )
        ) {
            Box() {
                Column(
                    modifier = Modifier
                        .padding(horizontal = 16.dp)
                        .padding(top = 48.dp)
                        .fillMaxWidth()
                        .background(
                            brush = Brush.verticalGradient(
                                listOf(Color(0xFFFFE4CC), Color(0xFFFFF4EA))
                            )
                        )
                        .padding(bottom = 138.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        buildAnnotatedString {
                            if (info.payPriceLabel.isNotBlank() && info.bonusPriceLabel.isNotBlank()) {
                                append(stringResource(id = R.string.充))
                                withStyle(
                                    SpanStyle(color = Color(0xFFFF3650), fontSize = 20.sp)
                                ) {
                                    append(info.payPriceLabel)
                                }
                                append(stringResource(id = R.string.获得))
                                withStyle(
                                    SpanStyle(color = Color(0xFFFF3650), fontSize = 20.sp)
                                ) {
                                    append(info.bonusPriceLabel)
                                }
                                append(stringResource(id = R.string.礼包))
                            }
                        }, fontSize = 14.sp,
                        fontWeight = FontWeight.Medium,
                        color = Color(0xff7a4016),
                        modifier = Modifier.padding(top = 12.dp)
                    )


                    selectedMemberOption?.apply {
                        LazyVerticalGrid(
                            columns = GridCells.Fixed(4),
                            modifier = Modifier.padding(vertical = 8.dp, horizontal = 12.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp, Alignment.CenterHorizontally)
                        ) {
                            if (topItem != null) {
                                item {
                                    GoodItem(topItem)
                                }
                            }
                            items(subItems) {
                                GoodItem(it)
                            }
                        }


                        if (isPlayChannel) {

                        } else {
                            Text(
                                stringResource(id = R.string.支付方式), fontSize = 16.sp, lineHeight = 16.sp,
                                color = Color(0xff7a4016), modifier = Modifier.padding(top = 12.dp)
                            )
                            if (info.goods.size > 1) {
                                LazyVerticalGrid(
                                    columns = GridCells.Fixed(if (info.goods.size % 3 == 0) 3 else 2),
                                    modifier = Modifier
                                        .padding(top = 16.dp)
                                        .padding(horizontal = 8.dp)
                                        .selectableGroup(),
                                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                                ) {
                                    items(info.goods) { good ->
                                        FkItem(good,
                                            selectedFkOption == good, {
                                                selectedFkOption = good
                                            })
                                    }
                                }
                            }
                        }
                    }


                }

                //region 小装饰以及点击按钮
                ComposeImage(
                    model = R.drawable.ic_first_recharge_coin1,
                    modifier = Modifier
                        .padding(top = 27.dp)
                        .size(45.dp, 51.dp)
                )
                ComposeImage(
                    model = R.drawable.ic_first_recharge_coin3,
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .padding(top = 30.dp)
                        .size(69.dp, 56.dp),
                    contentScale = ContentScale.FillWidth
                )

                //region 购买按钮
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .paint(
                            painter = painterResource(id = R.drawable.ic_first_recharge_bottom),
                            contentScale = ContentScale.FillWidth
                        )
                        .align(Alignment.BottomCenter)
                ) {

                    val buttonTransition = rememberInfiniteTransition(label = "button_anim")
                    val buttonTraction by buttonTransition.animateFloat(
                        initialValue = 1f, targetValue = 0.9f, animationSpec = infiniteRepeatable(
                            tween(800, easing = LinearEasing), repeatMode = RepeatMode.Reverse
                        ), label = "button_animation"
                    )

                    Box(modifier = Modifier
                        .scale(buttonTraction)
                        .paint(painter = painterResource(id = R.drawable.ic_first_recharge_buy_bg))
                        .align(Alignment.Center)
                        .click(noEffect = true) {
                            selectedMemberOption?.also {
                                selectedFkOption?.fkChannel?.also { fkChannel ->
                                    onBuy(fkChannel, it.productId, it.fkLink)
                                }
                            }
                        }) {
                        Text(
                            text = buildAnnotatedString {
                                append(stringResource(id = R.string.立即购买))
                                if (selectedMemberOption != null) {
                                    withStyle(
                                        SpanStyle(fontSize = 16.sp)
                                    ) {
                                        append("（${selectedMemberOption?.transformPrice?.replace(" ", "")}）")
                                    }
                                }
                            }, fontSize = 20.sp, color = Color.White,
                            fontWeight = FontWeight.SemiBold,
                            modifier = Modifier.align(Alignment.Center)
                        )
                    }
                }
                //endregion

                ComposeImage(
                    model = R.drawable.ic_first_recharge_coin2,
                    modifier = Modifier
                        .align(Alignment.BottomEnd)
                        .padding(bottom = 108.dp)
                        .size(51.dp, 42.dp),
                    contentScale = ContentScale.FillWidth
                )

                ComposeImage(
                    model = R.drawable.ic_first_recharge_hand,
                    modifier = Modifier
                        .padding(end = 24.dp, bottom = 4.dp)
                        .size(72.dp, 80.dp)
                        .align(Alignment.BottomEnd),
                    contentScale = ContentScale.FillWidth,
                    forceThird = true
                )
                //endregion
            }

        }

        ComposeImage(
            model = R.drawable.ic_first_recharge_header,
            contentScale = ContentScale.FillWidth,
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(horizontal = 19.dp)
                .fillMaxWidth()
        )
    }

    //endregion
}

@Composable
private fun FkItem(
    good: FirstRechargeGood,
    selected: Boolean,
    onClick: () -> Unit,
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
) {
    Row(
        modifier = Modifier
            .height(36.dp)
            .background(if (selected) Color(0xFFFF3541) else Color.White, Shapes.moreSmall)
            .run {
                if (selected) {
                    border(1.dp, Color(0xFFFFBBC6), Shapes.moreSmall)
                } else {
                    this
                }
            }
            .selectable(
                selected = selected,
                onClick = onClick,
                enabled = true,
                role = Role.RadioButton,
                interactionSource = interactionSource,
                indication = null,
            )
            .padding(horizontal = 6.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center
    ) {
//        Image(painter = good.icon, contentDescription = null)
        ComposeImage(model = good.icon, modifier = Modifier.size(24.dp))
        Spacer(modifier = Modifier.width(4.dp))
        AppText(
            text = good.name,
            fontSize = 12.sp,
            color = if (selected) Color(0xFFFFFFFF) else Color(0xFF7A4016)
        )
    }
}

@Composable
private fun GoodItem(item: SubItem) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Column(
            modifier = Modifier
                .clip(Shapes.corner12)
                .background(Brush.verticalGradient(listOf(Color(0xFFFFF9E3), Color(0xFFF3C292))))
                .border(
                    width = 1.dp,
                    brush = Brush.verticalGradient(listOf(Color(0xFFFFE6A6), Color(0xFFF4BB82))),
                    shape = Shapes.corner12
                ),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                contentAlignment = Alignment.Center,
                modifier = Modifier
                    .padding(top = 6.dp)
                    .padding(horizontal = 6.dp)
            ) {
                ComposeImage(
                    model = R.drawable.ic_first_recharge_light_bg, modifier = Modifier.size(64.dp),
                    contentScale = ContentScale.FillWidth
                )
                ComposeImage(
                    model = item.icon,
                    modifier = Modifier.size(64.dp),
                    contentScale = ContentScale.FillWidth
                )
            }
            if (item.bonus_label.isNotBlank()) {
                Text(
                    item.bonus_label,
                    modifier = Modifier
                        .align(Alignment.End)
                        .widthIn(min = 56.dp)
                        .background(
                            brush = Brush.horizontalGradient(
                                listOf(
                                    Color(0xffff3952), Color(0xffff40a7)
                                )
                            ), shape = RoundedCornerShape(topStart = 8.dp, bottomEnd = 12.dp)
                        )
                        .padding(horizontal = 8.dp, vertical = 2.dp),
                    fontSize = 10.sp, color = Color.White, lineHeight = 10.sp,
                    textAlign = TextAlign.Center
                )
            } else {
                Spacer(modifier = Modifier.height(6.dp))
            }
        }
        Spacer(modifier = Modifier.height(8.dp))

        Text(
            buildAnnotatedString {
                append(item.text)
                withStyle(
                    SpanStyle(
                        color = Color(0xFFFF3650)
                    )
                ) {
                    append("\n")
                    append(item.duration_label)
                }
            }, textAlign = TextAlign.Center, fontSize = 12.sp, lineHeight = 14.sp, fontWeight = FontWeight.Medium,
            color = Color(0xff7a4016)
        )

//        if (item.value != -1) {
//            AppText(text = item.text, color = Color(0xFF7A4016), fontSize = 12.sp)
//            Spacer(modifier = Modifier.height(2.dp))
//        }


//        Row(
//            verticalAlignment = Alignment.CenterVertically
//        ) {
//            Image(
//                painter = painterResource(id = R.drawable.ic_sign_gold), contentDescription = null,
//                modifier = Modifier.size(16.dp),
//                contentScale = ContentScale.FillBounds
//            )
//            Spacer(modifier = Modifier.width(4.dp))
//            if (item.value == -1) {
//                AppText(text = item.text, color = Color(0xFF7A4016), fontSize = 12.sp)
//            } else {
//                AppText(text = stringResource(id = R.string.多少金币, item.value), color = Color(0xFF7A4016), fontSize = 12.sp)
//            }
//        }
    }
}

@Preview(widthDp = 375)
@Composable
private fun FirstRechargeDialogPreview() {
    FirstRechargeDialog(
        FirstRechargeInfo(
            payPriceLabel = "\$0.99",
            bonusPriceLabel = "\$80",
            goods = listOf(
                FirstRechargeGood(
                    1, items = listOf(
                        ReChargeItem(
                            topItem = SubItem(text = "100个金币", bonus_label = "金币奖励", duration_label = "七天"), subItems = listOf(
                                SubItem(text = "「玫瑰花」礼物", bonus_label = "礼物", value = 100),
                                SubItem(text = "「龙年好运」气泡", value = 500),
                                SubItem(text = "「UCOO新贵」勋章", value = 1000),
                            ), currencyNumber = "100", currencyMark = "$"
                        )
                    ), name = "google支付"
                ),
                FirstRechargeGood(
                    3, items = listOf(
                        ReChargeItem(
                            topItem = SubItem(text = "100个金币"), subItems = listOf(
                                SubItem(text = "「玫瑰花」礼物", value = 100),
                                SubItem(text = "「龙年好运」气泡", value = 500),
                                SubItem(text = "「UCOO新贵」勋章", value = 1000),
                            )
                        )
                    ), name = "微信支付"
                )
            )
        )
    )
}

object FirstRechargeDialog {

    fun show(
        activity: Activity,
        flow: StateFlow<FirstRechargeInfo>,
    ) {
        AppBottomSheetDialog.Builder(activity).also {
            it.setOnDismissListener {
                UIConfig.refreshPendantConfig(2000)
            }
            val composeView = ComposeView(activity).apply {
                layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            }
            it.setView(composeView)
            it.show()
            val viewModel = WalletViewModel()
//            Analytics.appReportEvent(DataPoint.exposureBody("zh_first_charge_expose"))
            Analytics.appReportEvent(DataPoint.exposureBody(TracePoints.ZH_FIRST_RECHARGE_PENDANT_CLICK, useShushu = true))
            composeView.setContent {
                LaunchedEffect(key1 = Unit) {
                    viewModel.effect.onEach { toast ->
                        toast.show()
                    }.launchIn(this)
                }
                FirstRechargeDialog(
                    flow.collectAsState().value
                ) { fkChannel, productId, fkLink ->
//                    Analytics.appReportEvent(DataPoint.clickBody("zh_first_charge_click"))
                    Analytics.appReportEvent(DataPoint.clickBody(TracePoints.ZH_FIRST_RECHARGE_POPUP_BUY_BTN_CLICK, useShushu = true))
                    viewModel.sendEvent(WalletContract.Event.Buy(activity, fkChannel, productId, fkLink, 2))
                }
            }
        }
    }
}