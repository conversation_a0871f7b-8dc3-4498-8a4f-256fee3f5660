package com.qyqy.ucoo.compose.vm.cp_house

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.CPHouseFullInfo
import com.qyqy.ucoo.compose.data.CPMemoriesBean
import com.qyqy.ucoo.compose.presentation.cp_house.CPHouseApi
import com.qyqy.ucoo.compose.presentation.cp_house.bean.HRank
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.im.bean.RoomTokenRequest
import com.qyqy.ucoo.im.bean.RtcToken
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.ToastUtils
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

class CPHouseViewModel(val room_id: Int) : ViewModel() {

    private val api by lazy {
        createApi<CPHouseApi>()
    }

    var mFrom = 0
    var mMasterUserId = 0

    sealed class Action {
        data object ShowTask : Action()
    }

    val _actionFlow = MutableSharedFlow<Action>()
    val actionFlow = _actionFlow.asSharedFlow()

    //小屋 - 详细信息
    private val _houseInfo = MutableStateFlow(CPHouseFullInfo.EMPTY)
    val houseInfo = _houseInfo.asStateFlow()

    //小屋 - 独家记忆
    private val _memoriesInfo = MutableStateFlow(CPMemoriesBean())
    val memoriesInfo = _memoriesInfo.asStateFlow()


    private val _rankState: MutableState<HRank> = mutableStateOf(
        HRank(
            loading = true,
            myRoom = HRank.Room(INVALID_USER, INVALID_USER, 0, 0, ""),
            rankingData = emptyList()
        )
    )

    val rankState: ComposeState<HRank> = _rankState

    fun cacheRank(hRank: HRank) {
        _rankState.value = hRank
    }

    fun sendAction(action: Action, delay: Long = 0L) {
        viewModelScope.launch {
            if (delay > 0) {
                delay(delay)
            }
            _actionFlow.emit(action)
        }
    }

    /**
     * 抽奖完毕后升级小屋信息
     *
     * @param balance 剩余小屋币
     * @param refreshPropList 是否刷新道具列表
     */
    fun updateHouseInfo(balance: Int, refreshPropList: Boolean = false) {
        viewModelScope.launch {
            _houseInfo.value = _houseInfo.value.copy(balance = balance)
        }
        if (refreshPropList) {
            //获取到道具后
            refreshCpHouseHomeProp(CPHouseFullInfo.CPHouseFullPropItem.TYPE_HOUSE)
            refreshCpHouseHomeProp(CPHouseFullInfo.CPHouseFullPropItem.TYPE_CAR)
        }
    }

    /**
     * 刷新亲密小屋的详细消息
     */
    fun refreshHouseInfo() {
        viewModelScope.launch {
            runApiCatching {
                api.getCpHouseInfo(room_id)
            }.onSuccess { info ->
                _houseInfo.emit(info)
                //刷新佩戴道具
                _housePropMap[CPHouseFullInfo.CPHouseFullPropItem.TYPE_HOUSE]?.let {
                    val newList = it.value.toList().apply {
                        forEach {
                            it.isUsing = it.id == info.house?.id
                        }
                    }
                    it.emit(newList)
                }

                _housePropMap[CPHouseFullInfo.CPHouseFullPropItem.TYPE_CAR]?.let {
                    val newList = it.value.toList().apply {
                        forEach {
                            it.isUsing = it.id == info.car?.id
                        }
                    }
                    it.emit(newList)
                }

            }.toastError()
        }
    }

    /**
     * 设置亲密小屋是否可见
     *
     * @param isHidden 是否隐藏
     */
    fun updateHouseVisible(isHidden: Boolean, callback: () -> Unit = {}) {
        viewModelScope.launch {
            runApiCatching {
                api.setCpHouseVisible(mapOf("hide" to isHidden.toString()))
            }.onSuccess {
                callback()
                ToastUtils.showShort(R.string.modify_success)

                //升级本地的信息
                val newValue = _houseInfo.value.copy(isHidden = isHidden)
                _houseInfo.emit(newValue)

                //升级全局的

                //升级accountManager里的数据
//                accountManager.updateSelfUser {
//                    this.cpRoomInfo = this.cpRoomInfo?.copy(isHidden = isHidden)
//                }
//                newValue.MyCpUser(sUser.userId)?.let {
//                    UserManager.fetchUserById(it.id)
//                }
            }.onFailure {
                callback()
            }.toastError()
        }
    }

    /**
     * 刷新亲密小屋的美好回忆列表
     */
    fun refreshCpHouseMemories() {
        viewModelScope.launch {
            runApiCatching {
                api.getCpHouseMemories()
            }.onSuccess {
                var directionLeft = false
                for (idx in (it.history.size - 1) downTo 0) {
                    it.history[idx].isLeft = directionLeft
                    directionLeft = !directionLeft
                }
                _memoriesInfo.emit(it)
            }
        }
    }

    //region Cp 房间的道具列表
    //各类道具
    private val _housePropMap =
        ConcurrentHashMap<Int, MutableStateFlow<List<CPHouseFullInfo.CPHouseFullPropItem>>>()
    private val housePropMap =
        ConcurrentHashMap<Int, StateFlow<List<CPHouseFullInfo.CPHouseFullPropItem>>>()

    /**
     * 刷新cp room的道具列表
     *
     * @param type 道具类型
     */
    fun refreshCpHouseHomeProp(type: Int) {
        //不存在此类型时直接进行创建
        if (!_housePropMap.containsKey(type)) {
            getCpHouseHomeProp(type)
        }
        viewModelScope.launch {
            runApiCatching {
                api.getCpHouseHomeProp(type)
            }.onSuccess {
                _housePropMap[type]?.emit(it)
            }
        }
    }

    /**
     * 获取cp room指定类型的道具列表
     * @param type // 装扮类型 17:小屋模型 18:cp小屋车辆
     */
    fun getCpHouseHomeProp(type: Int): StateFlow<List<CPHouseFullInfo.CPHouseFullPropItem>> {
        val stateFlow = _housePropMap.getOrPut(type) {
            MutableStateFlow(listOf())
        }

        return housePropMap.getOrPut(type) {
            stateFlow.asStateFlow()
        }
    }
    //endregion

    private val roomRepository by lazy {
        RoomRepository()
    }

    fun joinPrivateRoom(onSuccess: (Pair<Room, RtcToken>) -> Unit, onComplete: () -> Unit) {
        val roomId = houseInfo.value.privateRoomId
        val request = RoomTokenRequest(true)
        viewModelScope.launch {
            roomRepository.joinChatRoom(roomId, request).onSuccess {
                onComplete()
                onSuccess(it)
            }.onFailure {
                onComplete()
            }.toastError()
        }
    }
}