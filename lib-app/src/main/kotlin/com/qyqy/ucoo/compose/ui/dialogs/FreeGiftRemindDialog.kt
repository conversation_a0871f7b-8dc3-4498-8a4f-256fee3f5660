package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.bean.PrivilegedGiftRemindBean
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage

@Composable
fun FreeGiftRemindContent(bean: PrivilegedGiftRemindBean, clickOK: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(
                brush = Brush.verticalGradient(
                    listOf(
                        Color(0xFF8435E9),
                        Color(0xFFD7AEF8)
                    )
                ),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            bean.desc,
            lineHeight = 21.sp,
            fontSize = 14.sp,
            color = Color.White,
            modifier = Modifier.padding(horizontal = 16.dp),
            textAlign = TextAlign.Center,
        )
        ComposeImage(
            model = bean.gift.icon, modifier = Modifier
                .padding(top = 16.dp)
                .size(100.dp)
                .background(
                    color = Color(0x33ffffff),
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(10.dp),
            contentScale = ContentScale.FillWidth
        )
        Text(
            bean.gift.name, fontSize = 12.sp,
            color = Color.White,
            textAlign = TextAlign.Center,
            lineHeight = 12.sp,
            modifier = Modifier.padding(top = 8.dp, bottom = 20.dp)
        )
        Box(
            modifier = Modifier
                .sizeIn(174.dp, 36.dp, 210.dp, 36.dp)
                .background(
                    brush = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xff8b56fc))),
                    shape = CircleShape
                )
                .click(noEffect = true) { clickOK() }
        ) {
            Text(bean.btnTxt, modifier = Modifier.align(Alignment.Center), color = Color.White, fontSize = 12.sp, lineHeight = 12.sp)
        }
    }
}

@Composable
@Preview
private fun FreeGiftRemindDialogPreview() {
    Box(modifier = Modifier.padding(10.dp)) {
        FreeGiftRemindContent(PrivilegedGiftRemindBean(AppUser(),"点击赠送", "这是描述"))
    }
}