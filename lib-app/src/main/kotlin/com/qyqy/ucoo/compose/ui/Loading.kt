package com.qyqy.ucoo.compose.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.widget.LoadingDrawable

@Composable
fun Loading(
    properties: DialogProperties = DialogProperties(),
    onTipText: (() -> String)? = null,
    onDismissRequest: () -> Unit = {},
) {
    Dialog(onDismissRequest = onDismissRequest, properties = properties) {
        val context = LocalContext.current
        val radius = LocalDensity.current.run {
            3.dp.toPx()
        }
        val loadingDrawable = remember {
            LoadingDrawable(radius).also {
                it.sizeMode = false
                it.setColor(context.getColor(R.color.white_alpha_50))
            }
        }
        Column(
            modifier = Modifier
                .size(60.dp)
                .background(colorResource(id = R.color.gray_800_alpha_50), shape = Shapes.medium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(5.dp, Alignment.CenterVertically)
        ) {
            Image(painter = rememberDrawablePainter(drawable = loadingDrawable), contentDescription = "loading")
            val text = onTipText?.invoke()
            if (!text.isNullOrBlank()) {
                Text(text = text, color = colorResource(id = R.color.white_alpha_50))
            }
        }
    }
}

@Composable
fun LoadingWidget(modifier: Modifier = Modifier, color: Color = Color(0x80FFFFFF)) {
    val radius = LocalDensity.current.run {
        3.dp.toPx()
    }
    val loadingDrawable = remember {
        LoadingDrawable(radius).also {
            it.sizeMode = false
            it.setColor(color.toArgb())
        }
    }
    Image(
        painter = rememberDrawablePainter(drawable = loadingDrawable),
        contentScale = ContentScale.Inside, contentDescription = "loading", modifier = modifier.size(60.dp)
    )
}