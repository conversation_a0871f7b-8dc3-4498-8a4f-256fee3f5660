package com.qyqy.ucoo.compose.domain.usecase.moment

import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.moment.MomentApi
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.JsonObject

class LikeMomentUseCase constructor(
    private val api: MomentApi = createApi(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Map<String, Any>, JsonObject>(ioDispatcher) {

    override suspend fun execute(parameters: Map<String, Any>): Result<JsonObject> {
        return runApiCatching {
            api.likeMoment(parameters)
        }
    }

}