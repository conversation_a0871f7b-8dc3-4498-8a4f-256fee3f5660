package com.qyqy.ucoo.compose.presentation.wedding.bean

import com.qyqy.ucoo.account.AppUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class BookingResult(
    @SerialName("audioroom")
    val audioroom: Audioroom = Audioroom(),
    @SerialName("booking_date")
    val bookingDate: String = "",
    @SerialName("start_time")
    val startTime: String = "",
    @SerialName("end_time")
    val endTime: String = "",
    @SerialName("light_user")
    val lightUser: AppUser = AppUser(),
    @SerialName("target_user")
    val targetUser: AppUser = AppUser(),
    @SerialName("ring")
    val ring: Ring = Ring(),
    @SerialName("room_type")
    val roomType: Int = 0,
    val status: Int = 0,
){
    val displayDate:String
        get() = "$bookingDate.$startTime-$endTime"
}

