package com.qyqy.ucoo.compose.ui.photo

import androidx.annotation.FloatRange
import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.exponentialDecay
import androidx.compose.foundation.gestures.awaitEachGesture
import androidx.compose.foundation.gestures.awaitFirstDown
import androidx.compose.foundation.gestures.calculateCentroid
import androidx.compose.foundation.gestures.calculateCentroidSize
import androidx.compose.foundation.gestures.calculatePan
import androidx.compose.foundation.gestures.calculateRotation
import androidx.compose.foundation.gestures.calculateZoom
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.saveable.Saver
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.AwaitPointerEventScope
import androidx.compose.ui.input.pointer.PointerEvent
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.input.pointer.positionChanged
import androidx.compose.ui.input.pointer.util.VelocityTracker
import androidx.compose.ui.input.pointer.util.addPointerInputChange
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.Velocity
import androidx.compose.ui.unit.toSize
import androidx.compose.ui.util.fastAny
import androidx.compose.ui.util.fastForEach
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.CoroutineStart
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.pow

interface PhotoGestureScope {

    fun Modifier.photoGesture(
        scope: CoroutineScope,
        key: Any = Unit,
        enable: (isTap: Boolean) -> Boolean = { true },
        panZoomLock: Boolean = false,
        onTap: ((Offset) -> Unit)? = null,
        dispatchFirstDown: AwaitPointerEventScope.() -> Unit = {},
        tryConsumeInput: AwaitPointerEventScope.(PointerEvent, Float, Float, Float, Offset) -> Boolean? = { _, _, _, _, _ -> false },
        onGestureStart: (event: PointerEvent, velocityTracker: VelocityTracker, pan: Offset, zoom: Float, rotation: Float) -> Unit = { _, _, _, _, _ -> },
        onGestureEnd: (Velocity) -> Boolean = { false },
    ): Modifier

    fun Modifier.applyPhotoGesture(enable: Boolean): Modifier
}

@Composable
fun rememberPhotoGestureState(
    extraHorizontalOffset: Float = 120f,
    extraVerticalOffset: Float = 150f,
    minimumScale: Float = 1f,
    maximumScale: Float = 3f,
    doubleTapScale: Float = 2f,
    currentScale: Float = minimumScale,
    currentOffset: Offset = Offset.Zero,
): PhotoGestureState = rememberSaveable(saver = PhotoGestureState.Saver) {
    PhotoGestureState(
        extraHorizontalOffset = extraHorizontalOffset,
        extraVerticalOffset = extraVerticalOffset,
        minimumScale = minimumScale,
        maximumScale = maximumScale,
        doubleTapScale = doubleTapScale,
        currentScale = currentScale,
        currentOffset = currentOffset,
    )
}


@Stable
class PhotoGestureState internal constructor(
    private val extraHorizontalOffset: Float = 0f,
    private val extraVerticalOffset: Float = 0f,
    private val alwaysOffsetX: Boolean = true,
    private val alwaysOffsetY: Boolean = false,
    val minimumScale: Float = 1f,
    val maximumScale: Float = 3f,
    val doubleTapScale: Float = 2f,
    currentScale: Float = minimumScale,
    currentOffset: Offset = Offset.Zero,
    currentRotation: Float = 0f,
) : PhotoGestureScope {

    internal companion object {
        /**
         * The default [Saver] implementation for [PhotoState].
         */
        val Saver: Saver<PhotoGestureState, *> = listSaver(
            save = {
                listOf<Any>(
                    it.extraHorizontalOffset,
                    it.extraVerticalOffset,
                    it.alwaysOffsetX,
                    it.alwaysOffsetY,
                    it.minimumScale,
                    it.maximumScale,
                    it.doubleTapScale,
                    it.currentScale,
                    it.currentOffset.x,
                    it.currentOffset.y,
                    it.currentRotation,
                )
            },
            restore = {
                PhotoGestureState(
                    extraHorizontalOffset = it[0] as Float,
                    extraVerticalOffset = it[1] as Float,
                    alwaysOffsetX = it[2] as Boolean,
                    alwaysOffsetY = it[3] as Boolean,
                    minimumScale = it[4] as Float,
                    maximumScale = it[5] as Float,
                    doubleTapScale = it[6] as Float,
                    currentScale = (it[7] as Float).coerceIn(it[4] as Float, it[5] as Float),
                    currentOffset = Offset(it[8] as Float, it[9] as Float),
                    currentRotation = it[10] as Float,
                )
            }
        )
    }

    init {
        require(extraHorizontalOffset >= 0f)
        require(extraHorizontalOffset >= 0f)
        require(minimumScale > 0f)
        require(currentScale >= minimumScale)
        require(currentScale <= maximumScale)
        require(doubleTapScale in minimumScale..maximumScale)
    }

    private val scale = Animatable(currentScale)

    private val offsetX = Animatable(currentOffset.x)

    private val offsetY = Animatable(currentOffset.y)

    private val rotation = Animatable(currentRotation)

    var layoutSize by mutableStateOf(Size.Zero)
        private set

    var photoSize by mutableStateOf(Size.Zero)
        private set

    val currentScale: Float
        get() = scale.value

    val currentOffset: Offset
        get() = Offset(offsetX.value, offsetY.value)

    val currentRotation: Float
        get() = rotation.value


    internal fun getScrollableBounds() = calculateScrollableBounds()

    suspend fun animateTo(
        scaleValue: Float? = null,
        offsetValue: Offset? = null,
        rotationValue: Float? = null,
    ) {
        coroutineScope {
            if (scaleValue != null) {
                launch {
                    scale.animateTo(scaleValue)
                }
            }
            if (offsetValue != null) {
                launch {
                    offsetX.animateTo(offsetValue.x)
                }
                launch {
                    offsetY.animateTo(offsetValue.y)
                }
            }
            if (rotationValue != null) {
                launch {
                    rotation.animateTo(rotationValue)
                }
            }
        }
    }

    suspend fun snapTo(
        scaleValue: Float? = null,
        offsetValue: Offset? = null,
        rotationValue: Float? = null,
    ) {
        coroutineScope {
            if (scaleValue != null) {
                scale.snapTo(scaleValue)
            }
            if (offsetValue != null) {
                offsetX.snapTo(offsetValue.x)
                offsetY.snapTo(offsetValue.y)
            }
            if (rotationValue != null) {
                rotation.snapTo(rotationValue)
            }
        }
    }

    override fun Modifier.photoGesture(
        scope: CoroutineScope,
        key: Any,
        enable: (isTap: Boolean) -> Boolean,
        panZoomLock: Boolean,
        onTap: ((Offset) -> Unit)?,
        dispatchFirstDown: AwaitPointerEventScope.() -> Unit,
        tryConsumeInput: AwaitPointerEventScope.(PointerEvent, Float, Float, Float, Offset) -> Boolean?,
        onGestureStart: (event: PointerEvent, velocityTracker: VelocityTracker, pan: Offset, zoom: Float, rotation: Float) -> Unit,
        onGestureEnd: (Velocity) -> Boolean,
    ): Modifier {

        val onGesture: (centroid: Offset, pan: Offset, zoom: Float, rotation: Float) -> Unit =
            { _, offsetChange, zoomChange, rotationChange ->
                scope.launch(start = CoroutineStart.UNDISPATCHED) {
                    val newScale = if (zoomChange > 1f && currentScale >= maximumScale) { // 放大越界
                        currentScale.times(zoomChange.minus(1f).times(inDampingFactor(currentScale.minus(maximumScale))).plus(1f))
                    } else if (zoomChange < 1f && currentScale <= minimumScale) { // 缩小越界
                        currentScale.times(zoomChange.minus(1f).times(outDampingFactor(minimumScale.minus(currentScale))).plus(1f))
                    } else {
                        currentScale.times(zoomChange)
                    }
                    scale.snapTo(newScale)

                    val (scrollableX, scrollableY) = calculateScrollableBounds()

                    val newX = if (!alwaysOffsetX && photoSize.width.times(currentScale) <= layoutSize.width) {
                        currentOffset.x.plus(offsetChange.x).coerceIn(-scrollableX, scrollableX)
                    } else if (extraHorizontalOffset > 0f && offsetChange.x > 0f && currentOffset.x >= scrollableX) { // 水平向右越界
                        currentOffset.x.plus(offsetChange.x.times(offsetXDampingFactor(currentOffset.x.minus(scrollableX))))
                    } else if (extraHorizontalOffset > 0f && offsetChange.x < 0f && currentOffset.x <= -scrollableX) { // 水平向左越界
                        currentOffset.x.plus(offsetChange.x.times(offsetXDampingFactor((-scrollableX).minus(currentOffset.x))))
                    } else {
                        currentOffset.x.plus(offsetChange.x)
                            .coerceIn(-scrollableX.plus(extraHorizontalOffset), scrollableX.plus(extraHorizontalOffset))
                    }

                    val newY = if (!alwaysOffsetY && photoSize.height.times(currentScale) <= layoutSize.height) {
                        currentOffset.y.plus(offsetChange.y).coerceIn(-scrollableY, scrollableY)
                    } else if (extraVerticalOffset > 0f && offsetChange.y > 0f && currentOffset.y >= scrollableY) { // 垂直向上越界
                        currentOffset.y.plus(offsetChange.y.times(offsetYDampingFactor(currentOffset.y.minus(scrollableY))))
                    } else if (extraVerticalOffset > 0f && offsetChange.y < 0f && currentOffset.y <= -scrollableY) { // 垂直向下越界
                        currentOffset.y.plus(offsetChange.y.times(offsetYDampingFactor((-scrollableY).minus(currentOffset.y))))
                    } else {
                        currentOffset.y.plus(offsetChange.y)
                            .coerceIn(-scrollableY.plus(extraVerticalOffset), scrollableY.plus(extraVerticalOffset))
                    }
                    offsetX.snapTo(newX)
                    offsetY.snapTo(newY)

                    rotation.snapTo(currentRotation + rotationChange)
                }
            }

        return onSizeChanged { layoutSize = it.toSize() }
            .pointerInput(key) {
                detectTapGestures(
                    onDoubleTap = { tapOffset ->
                        if (!enable(false)) {
                            return@detectTapGestures
                        }
                        scope.launch {
                            if (currentScale > minimumScale) {
                                animateTo(scaleValue = minimumScale, offsetValue = Offset.Zero)
                            } else {
                                val targetScale = if (layoutSize.width == photoSize.width) {
                                    layoutSize.height.div(photoSize.height)
                                } else if (layoutSize.height == photoSize.height) {
                                    layoutSize.width.div(photoSize.width)
                                } else {
                                    doubleTapScale
                                }
                                animateTo(
                                    scaleValue = targetScale.coerceIn(doubleTapScale, maximumScale),
                                    offsetValue = calculateOffset(tapOffset, size)
                                )
                            }
                        }
                    },
                    onTap = {
                        if (!enable(true)) {
                            return@detectTapGestures
                        }
                        onTap?.invoke(it)
                    }
                )
            }
            .pointerInput(key) {
                awaitEachGesture {
                    var rotation = 0f
                    var zoom = 1f
                    var pan = Offset.Zero
                    val touchSlop = viewConfiguration.touchSlop
                    var lockedToPanZoom = false

                    awaitFirstDown(requireUnconsumed = false)
                    if (!enable(false)) {
                        return@awaitEachGesture
                    }
                    scope.launch {
                        offsetX.updateBounds(null, null)
                        offsetY.updateBounds(null, null)
                        offsetX.stop()
                        offsetY.stop()
                    }
                    dispatchFirstDown()

                    var hasConsumed = false
                    var pastTouchSlop = false
                    val velocityTracker = VelocityTracker()

                    do {
                        val event = awaitPointerEvent()
                        val canceled = event.changes.fastAny { it.isConsumed }
                        if (!canceled) {
                            val zoomChange = event.calculateZoom()
                            val rotationChange = event.calculateRotation()
                            val panChange = event.calculatePan()

                            if (!hasConsumed) {
                                zoom *= zoomChange
                                rotation += rotationChange
                                pan += panChange

                                val consumed = tryConsumeInput(event, touchSlop, zoom, rotation, pan)
                                if (consumed != null) {
                                    hasConsumed = consumed
                                }

                                if (!hasConsumed) {
                                    val centroidSize = event.calculateCentroidSize(useCurrent = false)
                                    val zoomMotion = abs(1 - zoom) * centroidSize
                                    val rotationMotion = abs(rotation * PI.toFloat() * centroidSize / 180f)
                                    val panMotion = pan.getDistance()

                                    if (zoomMotion > touchSlop ||
                                        rotationMotion > touchSlop ||
                                        (consumed == false && panMotion > touchSlop) // 上游确定不消费，才有机会消费
                                    ) {
                                        hasConsumed = true
                                        pastTouchSlop = true
                                        lockedToPanZoom = panZoomLock && rotationMotion < touchSlop
                                    }
                                }
                            }

                            if (pastTouchSlop) {
                                val centroid = event.calculateCentroid(useCurrent = false)
                                val effectiveRotation = if (lockedToPanZoom) 0f else rotationChange
                                if (effectiveRotation != 0f ||
                                    zoomChange != 1f ||
                                    panChange != Offset.Zero
                                ) {
                                    onGesture(centroid, panChange, zoomChange, effectiveRotation)
                                }
                                event.changes.fastForEach {
                                    if (it.positionChanged()) {
                                        velocityTracker.addPointerInputChange(it)
                                        it.consume()
                                    }
                                }
                            } else if (hasConsumed) {
                                onGestureStart(event, velocityTracker, pan, zoom, rotation)
                            }
                        }
                    } while (!canceled && event.changes.fastAny { it.pressed })

                    val velocity = velocityTracker.calculateVelocity()

                    // 结束一次手势周期
                    if (onGestureEnd(velocity)) {
                        return@awaitEachGesture
                    }

                    val finalScale = currentScale.coerceIn(minimumScale, maximumScale)
                    if (currentScale != finalScale) {
                        scope.launch {
                            scale.animateTo(finalScale)
                        }
                    }

                    val (scrollableX, scrollableY) = calculateScrollableBounds()

                    val decay = exponentialDecay<Float>()
                    if (currentOffset.x in (-scrollableX..scrollableX)) {
                        scope.launch {
                            offsetX.updateBounds(-scrollableX, scrollableX)
                            offsetX.animateDecay(velocity.x, decay)
                            offsetX.updateBounds(null, null)
                        }
                    } else {
                        scope.launch {
                            offsetX.animateTo(currentOffset.x.coerceIn(-scrollableX, scrollableX))
                        }
                    }

                    if (currentOffset.y in (-scrollableY..scrollableY)) {
                        scope.launch {
                            offsetY.updateBounds(-scrollableY, scrollableY)
                            offsetY.animateDecay(velocity.y, decay)
                            offsetY.updateBounds(null, null)
                        }
                    } else {
                        scope.launch {
                            offsetY.animateTo(currentOffset.y.coerceIn(-scrollableY, scrollableY))
                        }
                    }
                }
            }
    }

    override fun Modifier.applyPhotoGesture(enable: Boolean): Modifier {
        return onSizeChanged {
            if (enable) {
                photoSize = it.toSize()
            }
        }
            .graphicsLayer {
                if (enable) {
                    scaleX = currentScale
                    scaleY = currentScale
                    translationX = currentOffset.x
                    translationY = currentOffset.y
//                    rotationZ = currentRotation
                }
            }
    }


    private fun calculateScrollableBounds(): Offset {
        if (layoutSize.isEmpty() || photoSize.isEmpty()) {
            return Offset.Zero
        }
        return Offset(
            x = ((photoSize.width * currentScale - layoutSize.width) / 2).coerceAtLeast(0f),
            y = ((photoSize.height * currentScale - layoutSize.height) / 2).coerceAtLeast(0f),
        )
    }


    private fun calculateOffset(tapOffset: Offset, size: IntSize): Offset {
        val offsetX = (-(tapOffset.x - (size.width / 2f)) * 2f)
            .coerceIn(-size.width / 2f, size.width / 2f)
        return Offset(offsetX, 0f)
    }


    /**
     * f: y=2 (x-0.7)^(2)
     */
    private fun outDampingFactor(@FloatRange(0.0, 0.7) scaleX: Float) =
        scaleX.minus(0.7f).pow(2).times(2f)

    /**
     * f: y=0.25 (x-2)^(2)
     */
    private fun inDampingFactor(@FloatRange(0.0, 2.0) scaleX: Float) =
        scaleX.minus(2).pow(2).times(0.25f)


    private fun offsetXDampingFactor(offsetX: Float) =
        0.8f.minus(offsetX.div(extraHorizontalOffset)).coerceAtLeast(0f)

    private fun offsetYDampingFactor(offsetX: Float) =
        0.8f.minus(offsetX.div(extraVerticalOffset)).coerceAtLeast(0f)

}