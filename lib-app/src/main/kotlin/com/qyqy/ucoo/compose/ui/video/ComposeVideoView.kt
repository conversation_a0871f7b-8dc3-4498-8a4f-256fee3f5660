

package com.qyqy.ucoo.compose.ui.video

import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.widget.video.XVideoView

/**
 *  @time 2024/7/24
 *  <AUTHOR>
 *  @package com.qyqy.keya.widget.video
 *  简单的视频播放器(封装过)
 *  后面考虑看是不是要接入exo或ijk
 */
@Composable
fun XVideoViewWidget(url: String, autoPlay: Boolean = false) {
    val lifecycleOwner = androidx.lifecycle.compose.LocalLifecycleOwner.current
    val ctx = LocalContext.current

    AndroidView(
        factory = {
            XVideoView(ctx).apply {
                prepare(url)
                getPlayer()?.setLooping(false)
                getPlayer()?.attachLifecycle(lifecycleOwner)
                if (autoPlay) {
                    getPlayer()?.start()
                }
            }
        },
        modifier = Modifier.fillMaxSize(),
        onRelease = {
            it.release()
        }
    )
}