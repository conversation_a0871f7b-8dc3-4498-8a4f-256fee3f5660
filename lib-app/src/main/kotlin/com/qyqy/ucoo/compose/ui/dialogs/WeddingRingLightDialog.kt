package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.WeddingRing
import com.qyqy.ucoo.compose.data.WeddingRingLightBean
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.router.UCOOPreviewTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage

@Composable
fun WeddingRingLightContent(bean: WeddingRingLightBean,onClick:(isConfirm:Boolean)->Unit) {
    Column(
        modifier = Modifier
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(Color(0xff202247), Color(0xff202247))
                ), shape = RoundedCornerShape(16.dp)
            )
            .paint(painter = painterResource(id = R.drawable.ic_wedding_ring_light_bg), contentScale = ContentScale.FillWidth, alignment = Alignment.TopCenter)
            .padding(vertical = 32.dp, horizontal = 28.dp)
        ,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.Center,
            modifier = Modifier
                .fillMaxWidth()
                .padding(bottom = 16.dp)
        ) {
            Spacer(
                modifier = Modifier
                    .padding(end = 6.dp)
                    .width(52.dp)
                    .height(0.5.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            listOf(Color(0x00FFE589), Color(0xffFFE589))
                        )
                    )
            )
            Text(
                stringResource(id = R.string.戒指点亮提醒),
                color = Color(0xffffe589),
                fontSize = 18.sp,
                fontWeight = FontWeight.SemiBold,
                lineHeight = 18.sp
            )
            Spacer(
                modifier = Modifier
                    .padding(start = 6.dp)
                    .width(52.dp)
                    .height(0.5.dp)
                    .background(
                        brush = Brush.horizontalGradient(
                            listOf(Color(0xffFFE589), Color(0x00FFE589))
                        )
                    )
            )
        }
        Row(verticalAlignment = Alignment.CenterVertically) {
            ComposeImage(
                model = bean.sender.avatarUrl, modifier = Modifier
                    .size(58.dp)
                    .clip(CircleShape)
            )
            ComposeImage(model = bean.ring.icon, modifier = Modifier
                .padding(horizontal = 10.dp)
                .size(72.dp))
            ComposeImage(
                model = bean.receiver.avatarUrl, modifier = Modifier
                    .size(58.dp)
                    .clip(CircleShape)
            )
        }
        Text(
            bean.content, color = Color(0xffffe37e), fontSize = 12.sp, lineHeight = 16.sp, textAlign = TextAlign.Center,
            modifier = Modifier.padding(vertical = 32.dp)
        )
        Row {
            AppButton(
                text = stringResource(id = R.string.我知道了),
                fontSize = 16.sp,
                color = Color.White,
                modifier = Modifier
                    .widthIn(min = 113.dp)
                    .background(Color(0xff464646), Shapes.chip),
                onClick = {
                    onClick(false)
                }
            )
            Spacer(modifier = Modifier.width(12.dp))
            AppButton(
                text = stringResource(id = R.string.立即预约),
                fontSize = 16.sp,
                color = Color.White,
                modifier = Modifier
                    .widthIn(min = 113.dp)
                    .background(Color(0xff945EFF), Shapes.chip),
                onClick = {
                    onClick(true)
                }
            )
        }
    }
}

@Composable
@Preview
fun WeddingRingLightContentPreview() {
    UCOOPreviewTheme {
        WeddingRingLightContent(
            WeddingRingLightBean(
                userForPreview,
                userForPreview,
                WeddingRing(),
                content = "您送给\"xx用户昵称\"的【xx戒指】已成功点亮~婚礼预约通道已为您开启。"
            )
        ){

        }
    }
}