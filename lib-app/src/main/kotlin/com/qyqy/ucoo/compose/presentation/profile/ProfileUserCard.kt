package com.qyqy.ucoo.compose.presentation.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.layoutId
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import androidx.constraintlayout.compose.ConstraintSet
import androidx.constraintlayout.compose.Dimension
import androidx.constraintlayout.compose.Visibility
import androidx.core.graphics.toColorInt
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.colorList
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isRegionVisible
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.regionFormatted
import com.qyqy.ucoo.asBase
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.mine.CpRelationShipCard
import com.qyqy.ucoo.compose.presentation.room.LevelBadge
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeAvatarView
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun ProfileUserCard(state: ProfileMvi.ViewState) {
    val user = state.user
    val activity = LocalContext.current.asBase
    val constraints = decoupledConstraints(state, ltr = LocalLayoutDirection.current == LayoutDirection.Ltr)
    ConstraintLayout(
        constraintSet = constraints,
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFF12151D), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .padding(start = 16.dp, end = 16.dp, top = 16.dp, bottom = 16.dp)
    ) {

        if (user.avatarFrame.isNullOrEmpty()) {
            CircleComposeImage(
                model = user.avatarUrl,
                modifier = Modifier
                    .layoutId("avatar")
                    .size(56.dp)
                    .border(1.dp, Color.White, CircleShape),
            )
        } else {
            ComposeAvatarView(
                user = user,
                modifier = Modifier
                    .layoutId("avatar")
                    .size(70.dp),
            )
        }

        Column(modifier = Modifier.layoutId("info")) {
            Text(
                text = user.nickname,
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = TextStyle(
                    brush = user.colorList?.run {
                        Brush.horizontalGradient(map { Color(it.toColorInt()) })
                    }
                )
            )

            FlowRow(
                modifier = Modifier.padding(vertical = 3.dp),
                horizontalArrangement = Arrangement.spacedBy(4.dp),
                verticalArrangement = Arrangement.spacedBy(4.dp),
            ) {
                AgeGender(
                    age = user.age,
                    isBoy = user.isBoy,
                    modifier = Modifier.align(Alignment.CenterVertically)
                )

                if (user.isVip) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .height(20.dp),
                        painter = painterResource(id = R.drawable.ic_vip_tag),
                        contentDescription = null,
                        contentScale = ContentScale.FillHeight,
                    )
                }

                if (user.profileMedal.t == 1) {
                    LevelBadge(level = user.level, modifier = Modifier.align(Alignment.CenterVertically))
                } else if (user.profileMedal.t == 2 && user.profileMedal.medal.icon.isNotEmpty()) {
                    ComposeImage(
                        model = user.profileMedal.medal.icon,
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .size(user.profileMedal.medal.width.dp, user.profileMedal.medal.height.dp)
                    )
                }

                val density = LocalDensity.current

                user.characteristicMedalList.forEach {
                    Box(
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .height(24.dp),
                        contentAlignment = Alignment.BottomStart
                    ) {
                        ComposeImage(
                            model = it.icon,
                            modifier = Modifier.size(
                                (it.width / density.density).dp, (Math.min(it.height / density.density, 24f)).dp
                            ),
                            contentScale = ContentScale.FillHeight
                        )
                    }
                }

                if (state.extra.certified) {
                    Row(
                        modifier = Modifier
                            .align(Alignment.CenterVertically)
                            .height(16.dp)
                            .background(Color(0xFF6D3E23), CircleShape)
                            .padding(horizontal = 5.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_user_real_name),
                            contentDescription = null,
                            modifier = Modifier.size(10.dp)
                        )

                        Text(
                            text = stringResource(id = R.string.已认证),
                            fontSize = 8.sp,
                            lineHeight = 8.sp,
                            color = Color(0xFFFFD7C0)
                        )
                    }
                }
            }

            Row(
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = stringResource(R.string.id_num, user.publicId),
                    color = colorResource(id = R.color.white_alpha_50),
                    fontSize = 12.sp
                )
                if (user.isRegionVisible) {
                    Spacer(modifier = Modifier.width(8.dp))
                    Image(
                        painter = painterResource(id = R.drawable.ic_moment_location),
                        contentDescription = null,
                        modifier = Modifier.height(14.dp),
                        contentScale = ContentScale.FillHeight
                    )
                    Text(
                        text = user.regionFormatted,
                        color = colorResource(id = R.color.white_alpha_50),
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }


        Row(modifier = Modifier.layoutId("tree")) {
            val activityIcon = user.activities?.firstOrNull {
                it.t == 2
            }
            if (activityIcon != null) {
                ComposeImage(
                    model = activityIcon.icon,
                    modifier = Modifier
                        .height(60.dp)
                        .clickable {
                            activity?.startActivity(JsBridgeWebActivity.newIntent(activity, activityIcon.h5Link))
                        },
                    contentScale = ContentScale.Inside
                )
            }

            if (user.isFirstClassUser) {
                Box {
                    ComposeImage(
                        model = R.drawable.ic_firstclass_actived,
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .height(60.dp)
                            .clickable {

                            },
                        contentScale = ContentScale.Inside
                    )
                    Text(
                        user.firstClassUserNo, modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .padding(bottom = 4.dp)
                            .background(color = Color(0xff2E0503), shape = RoundedCornerShape(8.dp))
                            .border(
                                0.5.dp,
                                Brush.verticalGradient(listOf(Color(0xffe5c994), Color(0xffffedf), Color(0xFFC3994F))),
                                shape = RoundedCornerShape(8.dp)
                            )
                            .padding(horizontal = 6.dp, vertical = 2.dp),
                        color = Color(0xFFE9D9A8),
                        fontSize = 10.sp,
                        lineHeight = 10.sp,
                        fontWeight = FontWeight.Bold
                    )
                }
            }
        }

        val cpRelationship = state.cpRelationship

        if (cpRelationship.hasPublicCp().orDefault(false) && state.user.isSelf.not()) {
            val ctx = LocalContext.current
            Box(
                modifier = Modifier
                    .layoutId("cp_card")
                    .padding(top = 16.dp)
            ) {
                CpRelationShipCard(cpRelationship = cpRelationship, onWhichCp = { }, onGoUserProfile = {
                    if (it.id == user.id) {
                        return@CpRelationShipCard
                    }
                    UserProfileNavigator.navigate(ctx, it)
                })
            }
        }
    }

}

private fun decoupledConstraints(state: ProfileMvi.ViewState, user: User = state.user, ltr: Boolean): ConstraintSet {
    return ConstraintSet {
        val avatar = createRefFor("avatar")
        val info = createRefFor("info")
        val tree = createRefFor("tree")
        val cpCard = createRefFor("cp_card")

        constrain(avatar) {
            top.linkTo(anchor = parent.top, margin = 4.dp)
            start.linkTo(anchor = parent.start)
        }

        constrain(info) {
            top.linkTo(avatar.top)
            bottom.linkTo(avatar.bottom)
            linkTo(start = avatar.end, end = tree.start, startMargin = 8.dp, endMargin = 6.dp, bias = 0f)
            width = Dimension.fillToConstraints
        }

        constrain(tree) {
//            visibility = if (user.activities?.firstOrNull { it.t == 2 } != null) Visibility.Visible else Visibility.Gone
            top.linkTo(anchor = parent.top, margin = 2.dp)
            end.linkTo(anchor = parent.end, margin = 8.dp)
        }

        //他人已官宣则展示CP卡片
        val cpCardVisible = state.cpRelationship.hasPublicCp().orDefault(false) && state.user.isSelf.not()
        constrain(cpCard) {
            visibility = if (cpCardVisible) Visibility.Visible else Visibility.Gone
            top.linkTo(anchor = avatar.bottom)
        }
    }
}


@Preview(showBackground = true, backgroundColor = 0xFF020202)
@Preview(locale = "ar", showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun PreviewProfileUserCard() {
    Column(modifier = Modifier.fillMaxWidth()) {
        ProfileUserCard(ProfileMvi.ViewState(user = userForPreview, tabs = emptyList()))
    }
}

