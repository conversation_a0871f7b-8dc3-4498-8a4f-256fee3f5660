

package com.qyqy.ucoo.compose.presentation.room

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.compose.theme.colorWhite30Alpha
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.bean.GiftWrapper
import com.qyqy.ucoo.im.bean.giftExtraV2
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.im.message.MsgEventCmd

class GiftMessageItem(
    val icon: String,
    val title: String,
    val desc: String,
    val extra: String? = null,
    entry: UIMessageEntry<UCGiftMessage>,
) : MessageItem.Base<UCGiftMessage>(entry, MsgEventCmd.GIVE_GIFT) {

    companion object {
        fun from(context: Context, entry: UIMessageEntry<UCGiftMessage>): GiftMessageItem {
            val giftModel = entry.message.gift
            val gift = giftModel.gift
            val icon = gift.icon
            val title = UIMessageUtils.getSingleGiftText(context, entry.requireMsgIndex, giftModel).toString()
            val desc = "${gift.name} x${giftModel.count}"
            //先试用giftExtra进行处理, 如果获取的extra为空, 再尝试获取sender_extra_hint和receiver_extra_hint
            val extra = giftModel.giftExtraV2(GiftWrapper.tribe)
            return GiftMessageItem(icon, title, desc, extra, entry)
        }
    }

    @Composable
    override fun LazyItemScope.Content() {
        UserMessageContent(entry, null) {
            GiftCard(icon = icon, title = title, desc = desc, extra = extra)
        }
    }
}

@Composable
fun GiftCard(icon: String, title: String, desc: String, extra: String? = null) {
    Column(
        modifier = Modifier
            .width(240.dp)
            .clip(RoundedCornerShape(12.dp))
            .background(Brush.horizontalGradient(listOf(Color(0xFF604082), Color(0xFF43254D))))
    ) {

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp, 8.dp), verticalAlignment = Alignment.CenterVertically
        ) {
            Column(modifier = Modifier.weight(1f)) {
                AppText(text = title, color = colorWhite50Alpha, fontSize = 14.sp)
                Spacer(modifier = Modifier.height(2.dp))
                AppText(text = desc, color = Color.White, fontSize = 14.sp)
            }
            ComposeImage(model = icon, modifier = Modifier.size(48.dp), contentScale = ContentScale.FillBounds)
        }
        if (!extra.isNullOrEmpty()) {
            AppText(
                text = extra,
                modifier = Modifier
                    .fillMaxWidth()
                    .background(colorWhite30Alpha)
                    .padding(16.dp, 4.dp),
                fontSize = 12.sp,
                color = Color(0xFFFFD37E)
            )
        }
    }
}

@Preview
@Composable
fun GiftCardPreview() {
    GiftCard("", "送给 皮皮皮皮限", "为友谊干杯x 1", "从龙年盲盒开出xxx")
}