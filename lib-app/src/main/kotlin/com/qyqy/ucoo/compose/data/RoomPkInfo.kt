package com.qyqy.ucoo.compose.data

import android.os.Parcelable
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.vm.room.cross.CrossRoomPkRepository
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.bean.RoomSettings
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.JsonObject
import retrofit2.http.*

// --- 1. 数据结构定义 ---

/**
 * 通用用户信息
 */
@Serializable
@Parcelize
data class PkUserInfo(
    @SerialName("userid") val userId: Int,
    @SerialName("public_id") val publicId: String,
    @SerialName("nickname") val nickname: String,
    @SerialName("avatar_url") val avatarUrl: String,
) : Parcelable

/**
 * 通用房间基本信息
 */
@Serializable
@Parcelize
data class PkRoomBasicInfo(
    @SerialName("id") val id: Int,
    @SerialName("public_id") val publicId: String,
    @SerialName("title") val title: String,
    @SerialName("avatar_url") val avatarUrl: String,
    @SerialName("audience_cnt") val audienceCount: Int = 0, // 观众数在某些场景下可能没有
    @SerialName("room_mode") val roomMode: Int = 0,
    @SerialName("room_background") val roomBackground: String = "",
) : Parcelable

/**
 * PK 房间具体信息 (包含 PK 相关数值)
 */
@Serializable
@Parcelize
data class PkRoomDetailInfo(
    @SerialName("is_mic_muted") val isMicMuted: Boolean = false, // 是否被对方静音
    @SerialName("mic_user_id") val thirdPushStreamId: String = "", // 是否被对方静音
    @SerialName("room_value") val roomValue: Int = 0,      // 房间 PK 值 (如礼物积分)
    @SerialName("cheers") val cheers: List<PkUserInfo>? = null // 贡献者榜单 (可能为空)
) : Parcelable

/**
 * PK 核心信息
 */
@Serializable
@Parcelize
data class PkInfo(
    @SerialName("id") val pkId: Int,                 // 本次 PK 的唯一 ID
    @SerialName("duration") val duration: Int,              // PK 总时长 (秒) - 可能因加时而变化
    @SerialName("pk_type") val pkType: Int,                // PK 类型 ID
    @SerialName("pk_type_desc") val pkTypeDesc: String?,    // PK 类型描述
    @SerialName("end_time") val endTime: Int,              // PK 结束时间戳 (秒) - 可能因加时而变化
    @SerialName("status") val status: Int,                // PK 状态 (后端定义的状态，可能与客户端状态不完全一致，但可参考)
    @SerialName("from_room_pk_info") val fromRoomPkInfo: PkRoomDetailInfo?, // 发起方房间 PK 详情
    @SerialName("target_room_pk_info") val targetRoomPkInfo: PkRoomDetailInfo?,// 目标方房间 PK 详情
    @SerialName("winner_room") val winnerRoom: Int? = null // 获胜房间信息，结束后才有
) : Parcelable

/**
 * 完整的跨房 PK 数据 (用于 Start/Sync/Result 事件和房间详情)
 */
@Serializable
@Parcelize
data class CrossRoomPkData(
    @SerialName("from_room") private val _fromRoom: PkRoomBasicInfo?,    // 发起方房间基本信息
    @SerialName("target_room") private val _targetRoom: PkRoomBasicInfo?,  // 目标方房间基本信息
    @SerialName("pk_info") private val _pkInfo: PkInfo?         // PK 核心信息 (可能在某些场景为空，如刚开始或异常时)
) : Parcelable {

    val fromRoom: PkRoomBasicInfo
        get() = _fromRoom!!

    val targetRoom: PkRoomBasicInfo
        get() = _targetRoom!!

    val pkInfo: PkInfo
        get() = _pkInfo!!

    fun checkIsValid(): Boolean {
        return _fromRoom != null && _targetRoom != null && _pkInfo != null
    }

    /**
     * 判断当前房间是发起方还是目标方
     * @param currentRoomId 当前用户所在的房间ID
     * @return true 如果是发起方, false 如果是目标方, null 如果当前房间不参与此次PK (理论上不应发生)
     */
    fun isFromRoom(currentRoomId: Int): Boolean? {
        return when (currentRoomId) {
            fromRoom.id -> true
            targetRoom.id -> false
            else -> null
        }
    }

    /**
     * 获取当前房间的 PK 详情信息
     * @param currentRoomId 当前用户所在的房间ID
     */
    fun getCurrentRoomPkDetail(currentRoomId: Int): PkRoomDetailInfo? {
        return if (isFromRoom(currentRoomId) == true) {
            pkInfo.fromRoomPkInfo
        } else {
            pkInfo.targetRoomPkInfo
        }
    }

    /**
     * 获取当前房间的基本信息
     * @param currentRoomId 当前用户所在的房间ID
     */
    fun getCurrentRoomBasicInfo(currentRoomId: Int): PkRoomBasicInfo {
        return if (isFromRoom(currentRoomId) == true) {
            fromRoom
        } else {
            targetRoom
        }
    }

    /**
     * 获取对方房间的 PK 详情信息
     * @param currentRoomId 当前用户所在的房间ID
     */
    fun getOpponentRoomPkDetail(currentRoomId: Int): PkRoomDetailInfo? {
        return if (isFromRoom(currentRoomId) == true) {
            pkInfo.targetRoomPkInfo
        } else {
            pkInfo.fromRoomPkInfo
        }
    }

    /**
     * 获取对方房间的基本信息
     * @param currentRoomId 当前用户所在的房间ID
     */
    fun getOpponentRoomBasicInfo(currentRoomId: Int): PkRoomBasicInfo {
        return if (isFromRoom(currentRoomId) == true) {
            targetRoom
        } else {
            fromRoom
        }
    }
}


// --- 信令事件对应的数据结构 ---

/**
 * 邀请跨房 PK 事件 (invite_cross_room_pk_event)
 */
@Serializable
data class InviteCrossRoomPkEvent(
    @SerialName("invite_room") val inviteRoom: PkRoomBasicInfo, // 邀请方房间信息
    @SerialName("target_room_id") val targetRoomId: Int, // 邀请方房间信息
    @SerialName("invite_code") val inviteCode: String,          // 接受/拒绝邀请时需要用到
    @SerialName("duration") val duration: Int,                 // PK 时长 (秒)
    @SerialName("pk_type") val pkType: Int,                   // PK 类型
    @SerialName("pk_type_desc") val pkTypeDesc: String?,       // PK 类型描述
    @SerialName("countdown") val countdown: Int                // 弹窗展示倒计时 (秒)
)

/**
 * 拒绝跨房 PK 邀请事件 (refuse_cross_room_pk_invite_event)
 */
@Serializable
data class RefuseCrossRoomPkInviteEvent(
    @SerialName("target_room") val targetRoom: PkRoomBasicInfo, // （被）拒绝邀请的房间信息 (即己方房间)
    @SerialName("toast") val toast: String?                    // 提示信息
)

/**
 * 跨房 PK 静音事件 (cross_room_pk_mic_muted_event)
 */
@Serializable
data class CrossRoomPkMicMutedEvent(
    @SerialName("target_room") val targetRoom: PkRoomBasicInfo, // 对方房间信息
    @SerialName("is_mic_muted") val isMicMuted: Boolean,        // 对方是否被己方静音
    @SerialName("mic_user_id") val thirdPushStreamId: String    // 对方是否被己方静音
)

/**
 * 跨房 PK 加时事件 (cross_room_pk_add_time_event)
 */
@Serializable
data class CrossRoomPkAddTimeEvent(
    @SerialName("user") val user: PkUserInfo?,            // 操作人信息
    @SerialName("add_time_duration") val addTimeDuration: Int, // 加时时长 (秒)
    @SerialName("end_time") val endTime: Int               // 新的结束时间戳 (秒)
)

/**
 * 跨房 PK 结束事件 (cross_room_pk_result_event)
 * 包含完整的 PK 数据以及结果展示倒计时
 */
data class CrossRoomPkResultEvent(
    val countdown: Int,// 结果展示时间 (秒)
    val pkData: CrossRoomPkData // 假设解析后会填充这个字段
)


// --- API 接口返回的数据结构 ---

/**
 * PK 配置信息项
 */
@Serializable
@Parcelize
data class PkTypeInfo(
    @SerialName("pk_type") val pkType: Int,
    @SerialName("pk_type_desc") val pkTypeDesc: String
) : Parcelable

/**
 * 跨房 PK 配置响应 (api/audioroom/v1/cross/conf)
 */
@Serializable
@Parcelize
data class CrossPkConfigResponse(
    @SerialName("pk_types") private var _pkTypes: List<PkTypeInfo>,
    @SerialName("default_pk_type") private var _defaultPkType: Int,
    @SerialName("durations") private var _durations: List<Int>,
    @SerialName("default_duration") private var _defaultDuration: Int,
    @SerialName("extra_durations") private var _extraDurations: List<Int>
) : Parcelable {
    companion object {
        fun createEmpty() = CrossPkConfigResponse(listOf(), 0, listOf(), 0, listOf())
    }

    fun checkFix(): CrossPkConfigResponse {
        if (_pkTypes.isEmpty()) {
            _defaultPkType = 0
            _pkTypes = listOf(PkTypeInfo(0, "收到礼物价值多少"))
        }

        if (_durations.isEmpty()) {
            _defaultDuration = 10
            _durations = List(60) { index ->
                index + 1
            }
        }

        if (_extraDurations.isEmpty()) {
            _extraDurations = listOf(1, 5, 10, 15)
        }
        return this
    }

    val pkTypes: List<PkTypeInfo>
        get() = _pkTypes

    val defaultPkType: Int
        get() = _defaultPkType

    val durations: List<Int>
        get() = _durations

    val defaultDuration: Int
        get() = _defaultDuration

    val extraDurations: List<Int>
        get() = _extraDurations

    fun getDesc() = (pkTypes.firstOrNull { it.pkType == defaultPkType } ?: pkTypes.first()).pkTypeDesc
}

/**
 * 可 PK 房间信息 (api/audioroom/v1/cross/room/list, api/audioroom/v1/cross/room/search)
 */
@Serializable
data class PkAvailableRoomInfo(
    @SerialName("has_ongoing_coin_pk_game")
    val hasOngoingCoinPkGame: Boolean = false, // false
    @SerialName("has_red_packet")
    val hasRedPacket: Boolean = false, // false
    @SerialName("id")
    val id: Int = 0, // 163
    @SerialName("inuse_mic_cnt")
    val inuseMicCnt: Int = 0, // 0
    @SerialName("member_cnt")
    val memberCnt: Int = 0, // 0
    @SerialName("room_mode")
    val roomMode: Int = 0, // 1
    @SerialName("title")
    val title: String = "", // 婚礼房 3
    @SerialName("owner")
    val owner: AppUser, // 房主
    @SerialName("recommend_user_list")
    val userList: List<AppUser>? = null, // 房间推荐用户
) {

    val roomModeName
        get() = RoomSettings.getRoomModeName(roomMode)
}

/**
 * 房间列表响应
 */
@Serializable
data class RoomListResponse(
    @SerialName("rooms") val rooms: List<PkAvailableRoomInfo>?
)

/**
 * 邀请 PK 响应 (api/audioroom/v1/cross/room/invite)
 */
@Serializable
data class InvitePkResponse(
    @SerialName("toast") val toast: String?
)

/**
 * 设置对方静音响应 (api/audioroom/v1/cross/room/mic/muted)
 */
@Serializable
data class SetMutedResponse(
    @SerialName("is_mic_muted") val isMicMuted: Boolean
)

/**
 * 房间详情中的跨房 PK 信息 (api/audioroom/v1/audioroom/detail)
 * 注意：这个结构和 Start/Sync 事件的 `CrossRoomPkData` 几乎一样
 */
@Serializable
data class RoomDetailCrossPkInfo(
    @SerialName("cross_pk_info") val crossPkInfo: CrossRoomPkData? // 可能为 null 如果当前不在 PK
)




