package com.qyqy.ucoo.compose.vm

import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.UserApi
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

class CPListViewModel: StateViewModelWithIntPage<AppUser>() {
    private val api by lazy{
        createApi<UserApi>()
    }

    override suspend fun loadData(pageNum: Int): Result<List<AppUser>> {
        if (pageNum != 1) {
            return Result.success(emptyList())
        }
        val result = runApiCatching { api.getCPRecommendUsers() }
        return if (result.isSuccess) {
            val jsonObject = result.getOrThrow()
            val list = jsonObject.getOrNull("users")?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<AppUser>>(it)
            } ?: emptyList()
            Result.success(list)
        } else {
            Result.failure<List<AppUser>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }
}