package com.qyqy.ucoo.compose.presentation.chatgroup.list

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupListResp
import com.qyqy.ucoo.core.Analytics

@Composable
fun ChatGroupList(
    list: List<ChatGroupListResp.Chatgroup>,
    toDetail:(ChatGroupListResp.Chatgroup) -> Unit,
    onOpenGroup: (ChatGroupListResp.Chatgroup) -> Unit,
    onJoin: (chatGroupId: Int) -> Unit
) {
    LazyColumn(
        modifier = Modifier
            .fillMaxSize()
    ) {
        items(list) { item ->
            ChatGroupItem(
                Modifier
                    .padding(horizontal = 16.dp, vertical = 8.dp)
                    .clickable {
                        toDetail(item)
                        Analytics.reportClickEvent(TracePoints.SQUARE_GROUP_CELL)
                    },
                item.avatarUrl,
                item.name,
                item.intro,
                item.sampleAvatars,
                item.memberCnt,
                item.relationWithMe,
                onJoinGroup = {
                    onJoin(item.chatGroupId)
                },
                onOpenGroup = {
                    onOpenGroup(item)
                }
            )
        }
    }
}