package com.qyqy.ucoo.compose.presentation.tribe

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.tribe.bean.TribeMemberItem
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.widget.custom.UserLevelView

@Composable
fun TribeMemberItemWidget(member: TribeMemberItem, modifier: Modifier = Modifier) {

    Row(
        modifier = modifier
            .padding(bottom = 24.dp)
            .fillMaxWidth()
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(modifier = Modifier.size(48.dp)) {
            CircleComposeImage(model = member.user.avatarUrl, modifier = Modifier.fillMaxSize())
            if (member.is_online) {
                Spacer(
                    modifier = Modifier
                        .padding(bottom = 2.dp)
                        .size(10.dp)
                        .background(color = Color(0xff00b42a), shape = CircleShape)
                        .border(1.5.dp, Color(0xff1C1D1E), CircleShape)
                        .align(Alignment.BottomEnd)
                )
            }
        }
        Spacer(modifier = Modifier.width(8.dp))
        Column(modifier = Modifier.weight(1f)) {
//            var user: AppUser? by remember(member.user.userId) {
//                mutableStateOf(null)
//            }
//            LaunchedEffect(key1 = member.user.userId) {
//                user = UserManager.getUserCacheById(member.user.id)
//            }
            Row(verticalAlignment = Alignment.CenterVertically) {
                Text(
                    member.user.nickname, fontSize = 14.sp,
                    lineHeight = 14.sp,
                    color = Color.White, modifier = Modifier
                        .padding(end = 4.dp)
                        .weight(1f, false), maxLines = 1, overflow = TextOverflow.Ellipsis
                )
                AgeGender(age = member.user.age, isBoy = member.user.isBoy)
            }
            Spacer(modifier = Modifier.height(8.dp))


            Row(
                modifier = Modifier.heightIn(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {

                val labels = remember(member) {
                    member.getLabels()
                }

                labels.forEach {
                    when (it.icon) {
                        "owner" -> ComposeImage(model = R.drawable.ic_first_leader, modifier = Modifier.padding(end = 2.dp).size(52.dp, 16.dp))

                        "admin" -> {
                            ComposeImage(model = R.drawable.ic_second_leader, modifier = Modifier.padding(end = 2.dp).size(52.dp, 16.dp))
                        }

                        "level" -> {
                            AndroidView(
                                factory = {
                                    UserLevelView(it)
                                },
                                modifier = Modifier.padding(end = 2.dp)
                            ) {
                                it.setLevel(member.user.level)
                            }
                        }

                        "vip" -> {
                            Image(
                                painter = painterResource(id = R.drawable.ic_vip_tag),
                                modifier = Modifier.padding(end = 2.dp),
                                contentDescription = null,
                                contentScale = ContentScale.FillWidth,
                            )
                        }

                        "certified" -> {
                            Row(
                                modifier = Modifier
                                    .padding(end = 2.dp)
                                    .background(
                                        color = Color(0xff6d3e23),
                                        shape = RoundedCornerShape(24.dp)
                                    )
                                    .padding(horizontal = 6.dp, vertical = 3.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                ComposeImage(model = R.drawable.ic_user_certification, modifier = Modifier.size(10.dp))
                                Text(stringResource(id = R.string.已认证), fontSize = 8.sp, lineHeight = 8.sp, color = Color(0xFFFFD7C0))
                            }
                        }

                        else -> {
                            ComposeImage(
                                model = it.icon, modifier = Modifier
                                    .padding(end = 2.dp)
                                    .size(Math.max(it.width, 1).dp, (Math.max(it.height, 1)).dp)
                            )
                        }
                    }
                }
            }
        }
        Text(
            member.weekActivePointStr(), textAlign = TextAlign.End, modifier = Modifier.padding(horizontal = 4.dp),
                    color = Color . White, fontSize = 12.sp, lineHeight = 12.sp
        )//周活跃度
        Text(
            member.totalActivePointStr(), textAlign = TextAlign.End, modifier = Modifier
                .widthIn(64.dp)
                .padding(end = 4.dp),
            color = Color.White, fontSize = 12.sp, lineHeight = 12.sp
        )//总活跃度
    }
}
