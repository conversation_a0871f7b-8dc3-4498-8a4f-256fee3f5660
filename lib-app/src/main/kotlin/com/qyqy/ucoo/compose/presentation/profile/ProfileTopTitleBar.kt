package com.qyqy.ucoo.compose.presentation.profile

import android.content.Intent
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.min
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.compose.enableClick
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.MenuItem
import com.qyqy.ucoo.compose.ui.PopupActionMenu
import com.qyqy.ucoo.mine.EditUserInfoActivity


@Composable
fun TopTitleBar(
    user: User,
    backgroundColor: Color,
    scrollFaction: Float,
    modifier: Modifier = Modifier,
    onToggleBlackUser: () -> Unit = {},
    onReportUser: () -> Unit = {},
    onToggleFollowUser: () -> Unit = {},
) {
    val context = LocalContext.current
    Row(
        modifier = modifier
            .drawBehind {
                drawRect(backgroundColor.copy(alpha = scrollFaction))
            }
            .fillMaxWidth()
            .enableClick()
            .padding(vertical = 12.dp, horizontal = 16.dp)
            .windowInsetsPadding(WindowInsets.statusBars),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(
            modifier = Modifier
                .size(24.dp, 24.dp)
                .clip(CircleShape)
                .clickable {
                    context.asActivity?.finish()
                },
            contentAlignment = Alignment.Center,
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_navigation_back),
                contentDescription = null,
                tint = Color.Unspecified
            )
        }
        AutoSizeText(
            text = user.nickname,
            modifier = Modifier
                .padding(start = 5.dp)
                .weight(1f)
                .graphicsLayer {
                    alpha = if (scrollFaction < 0.9f) {
                        0f
                    } else {
                        scrollFaction
                            .minus(0.9f)
                            .div(0.1f)
                    }
                },
            color = Color.White,
            fontSize = 17.sp,
            maxLines = 1,
            fontWeight = FontWeight.Bold,
            alignment = Alignment.CenterStart
        )
        if (user.isSelf) {
            Button(
                onClick = {
                    context.startActivity(Intent(context, EditUserInfoActivity::class.java))
                },
                modifier = Modifier
                    .padding(start = 3.dp)
                    .size(68.dp, 28.dp),
                shape = Shapes.medium,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0x806A6A6A)),
                contentPadding = PaddingValues()
            ) {
                Text(text = stringResource(id = R.string.edit_info), color = Color.White, fontSize = 12.sp)
            }
        } else {

            Box(
                modifier = Modifier
                    .height(28.dp)
                    .padding(start = 3.dp, end = 3.dp)
                    .widthIn(min = 72.dp)
                    .background(
                        color = if (user.followed) {
                            Color(0x80000000)
                        } else {
                            Color(0xFF945EFF)
                        },
                        shape = CircleShape
                    )
                    .noEffectClickable(onClick = onToggleFollowUser)
                    .padding(horizontal = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = if (user.followed) {
                        stringResource(id = R.string.cancel_follow)
                    } else {
                        stringResource(id = R.string.follow)
                    },
                    color = if (user.followed) {
                        colorResource(id = R.color.white_alpha_50)
                    } else {
                        Color.White
                    },
                    fontSize = 13.sp
                )
            }

            val expanded = remember { mutableStateOf(false) }

            Box(
                modifier = Modifier
                    .padding(start = 3.dp)
                    .size(32.dp, 32.dp)
                    .background(Color(0x80000000), CircleShape)
                    .clip(CircleShape)
                    .clickable {
                        expanded.value = true
                    }
                    .padding(6.dp),
                contentAlignment = Alignment.Center,
            ) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_more_menu),
                    contentDescription = null,
                    tint = Color.Unspecified
                )
            }

            PopupActionMenu(
                expanded = expanded,
                modifier = Modifier.paint(painterResource(R.drawable.popup_window_bubble)),
                verticalArrangement = Arrangement.spacedBy(10.dp, Alignment.CenterVertically),
                alignment = Alignment.TopEnd,
                offset = LocalDensity.current.run {
                    IntOffset(16.dp.roundToPx(), 40.dp.roundToPx())
                },
            ) {
                val itemModifier = Modifier.requiredHeight(36.dp)
                MenuItem(if (user.blacked) R.string.cancel_add_backlist else R.string.add_backlist, null, itemModifier, fontSize = 14.sp) {
                    onToggleBlackUser()
                }
                MenuItem(R.string.report, null, itemModifier, fontSize = 14.sp) {
                    onReportUser()
                    expanded.value = false
                }
            }
        }
    }
}
