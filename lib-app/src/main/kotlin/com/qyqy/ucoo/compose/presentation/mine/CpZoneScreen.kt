

package com.qyqy.ucoo.compose.presentation.mine

import androidx.compose.animation.core.animateDpAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.AlertDialog
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.Fragment
import androidx.fragment.app.findFragment
import com.bumptech.glide.integration.compose.placeholder
import com.overseas.common.sntp.SNTPManager
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.asFragmentActivity
import com.qyqy.ucoo.base.show
import com.qyqy.ucoo.compose.data.CpZoneWrapper
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.formatTimeWithHours
import com.qyqy.ucoo.compose.presentation.profile.LocalRelationShipEntranceFrom
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorWhite30Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi
import com.qyqy.ucoo.compose.vm.profile.UserProfileViewModel
import com.qyqy.ucoo.im.bean.CpTask
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.im.bean.Ongoing
import com.qyqy.ucoo.im.bean.isChallengeMode
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.im.room.CpContract
import com.qyqy.ucoo.im.room.CpViewModel
import com.qyqy.ucoo.mine.UserProfileActivity
import com.qyqy.ucoo.utils.taskflow.taskShowWithStack
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity.Companion.startJsWebActivity
import com.qyqy.ucoo.widget.dialog.CpZoneRuleDialogFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive

/**
 * [profileType] 0 我的页面，1我的个人主页，2他人的个人主页
 */
@Composable
fun CpZonePageRouter(
    profileType: Int,
    userId: String,
    cpZoneWrapper: CpZoneWrapper,
    viewModel: UserProfileViewModel,
    cpViewModel: CpViewModel
) {
    val context = LocalContext.current
    val activity = context.asActivity
    val fragment = if (profileType == 0) {
        LocalView.current.findFragment<Fragment>()
    } else {
        null
    }
    var showDissolveCp by remember { mutableStateOf(false) }

    LaunchedEffect(Unit) {
        cpViewModel.effect.onEach {
            if (it is CpContract.Effect.ShowToast) {
                it.show()
            } else if (it is CpContract.Effect.OnJoinNewRoom) {
                ChatRoomActivity.openRoomActivity(context, it.room, it.rtcToken)
            }
        }.launchIn(this)
    }

    CpZonePage(profileType, cpZoneWrapper) { uiAction, cpZone ->
        when (uiAction) {
            is UIAction.OnRefresh -> {
                viewModel.processEvent(ProfileMvi.ViewEvent.GetCpZone(userId))
            }

            is UIAction.OnCpHeatRule -> {
                fragment?.apply {
                    CpZoneRuleDialogFragment.newInstance(cpZone.cpRuleDesc).taskShowWithStack("CpZoneRuleDialogFragment")
                } ?: context.asFragmentActivity?.apply {
                    CpZoneRuleDialogFragment.newInstance(cpZone.cpRuleDesc).taskShowWithStack("CpZoneRuleDialogFragment")
                }
            }

            is UIAction.OnCpUpgrade -> {
                if (!cpZone.cpRuleUrl.isNullOrEmpty()) {
                    activity?.startJsWebActivity(cpZone.cpRuleUrl)
                }
            }

            is UIAction.OnGoUserProfile -> {
                if (profileType == 1 && uiAction.user.isSelf) {
                    return@CpZonePage
                }
                context.startActivity(UserProfileActivity.createIntent(context, uiAction.user))
            }

            is UIAction.OnCheckIn -> {
                if (!cpZone.cpTask!!.myCheckedStatus) {
                    viewModel.processEvent(ProfileMvi.ViewEvent.OnCpCheckInClicked(cpZone.cpTask))
                } else {
                    viewModel.processEvent(ProfileMvi.ViewEvent.OnCpCheckInRemindClicked(cpZone.targetId))
                }
            }

            is UIAction.OnPrivateRoom -> {
                cpViewModel.sendEvent(CpContract.Event.OnJoinCpVoiceRoom)
            }

            is UIAction.OnDissolveCp -> {
                showDissolveCp = true
            }

            is UIAction.OnDoTask -> {
                viewModel.processEvent(ProfileMvi.ViewEvent.OnDoTaskEvent(uiAction.taskId))
            }
        }
    }

    if (showDissolveCp) {
        AlertDialog(
            onDismissRequest = {
                showDissolveCp = false
            },
            text = {
                Text(
                    text = stringResource(id = R.string.tip_delete_cp),
                    fontSize = 14.sp
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDissolveCp = false
                    }
                ) {
                    Text(
                        text = stringResource(id = R.string.wait_think),
                        fontSize = 12.sp
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        viewModel.processEvent(ProfileMvi.ViewEvent.OnDissolveCpClicked)
                        showDissolveCp = false
                    },
                ) {
                    Text(
                        text = stringResource(id = R.string.delete_confirm),
                        fontSize = 12.sp,
                    )
                }
            },
            containerColor = Color(0xFF312136),
            textContentColor = Color.White,
        )
    }
}

private sealed interface UIAction {

    object OnCpHeatRule : UIAction

    object OnCpUpgrade : UIAction

    data class OnGoUserProfile(val user: User) : UIAction

    object OnCheckIn : UIAction

    object OnPrivateRoom : UIAction

    object OnDissolveCp : UIAction

    object OnRefresh : UIAction

    data class OnDoTask(val taskId: String) : UIAction
}


@Composable
private fun CpZonePage(profileType: Int, cpZoneWrapper: CpZoneWrapper, onAction: (UIAction, CpZone) -> Unit = { _, _ -> }) {
    val context = LocalContext.current

    val cpZone = cpZoneWrapper.cpZone
    val cpRelationship = cpZoneWrapper.cpRelationship

    Column(
        modifier = Modifier
            .verticalScroll(state = rememberScrollState())
            .fillMaxSize()
            .padding(horizontal = 16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp),
    ) {
        CompositionLocalProvider(LocalRelationShipEntranceFrom provides profileType) {
            CpRelationShipCard(cpRelationship = cpRelationship, onWhichCp = { }, onGoUserProfile = {
                if (profileType == 1 && it.isSelf) {
                    return@CpRelationShipCard
                }
                context.startActivity(UserProfileActivity.createIntent(context, it))
            })
        }
        cpZone ?: return

        CpZoneTaskPage(
            profileType = profileType,
            cpZone = cpZone,
        ) {
            onAction(it, cpZone)
        }
    }
}

@Composable
private fun ColumnScope.CpZoneTaskPage(
    profileType: Int,
    cpZone: CpZone,
    onAction: (UIAction) -> Unit = {},
) {
    if (cpZone.cpTask?.version != 2) {
        CpZoneContent(cpZone = cpZone, onAction = onAction)
    } else {
        if (cpZone.cpTask.isChallengeMode) {
            PublicCPBar(
                cpZone = cpZone,
                modifier = Modifier
                    .background(
                        Brush.verticalGradient(listOf(Color(0xFFFFDBE3), Color(0xFFFFACC9))),
                        Shapes.corner12
                    )
                    .padding(16.dp)
            ) {
                onAction(UIAction.OnCpUpgrade)
            }
        }
        CpZoneContent2(cpZone = cpZone, onAction = onAction)
    }
    Spacer(modifier = Modifier.height(24.dp))
    Row(
        modifier = Modifier
            .size(151.dp, 36.dp)
            .align(Alignment.CenterHorizontally)
            .background(
                Brush.verticalGradient(
                    listOf(
                        Color(0xFF4E4066),
                        Color(0xFF281F36)
                    )
                ), Shapes.medium
            )
            .clickable {
                onAction(UIAction.OnDissolveCp)
            }
            .clip(Shapes.medium),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_cp_love_4),
            contentDescription = null,
            colorFilter = ColorFilter.tint(Color(0xFF9B90AF))
        )
        Text(
            text = stringResource(id = R.string.delete_cp),
            color = Color(0xFF9B90AF),
            fontSize = 14.sp,
            fontWeight = FontWeight.Medium
        )
    }
    when (profileType) {
        0 -> Spacer(modifier = Modifier.height(25.dp))
        1 -> Spacer(modifier = Modifier.height(25.dp))
        else -> Spacer(modifier = Modifier.height(111.dp))
    }
}

@Composable
fun PublicCPBar(cpZone: CpZone, modifier: Modifier = Modifier, onClick: () -> Unit) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.padding(end = 5.dp),
            text = stringResource(id = R.string.CP等级),
            color = Color(0xFF590D27),
            fontSize = 16.sp,
            fontWeight = FontWeight.Bold,
        )

        if (cpZone.isPublishCp) {
            ComposeImage(
                model = cpZone.cpPublishLevelUrl,
                modifier = Modifier.size(93.dp, 28.dp),
                loading = placeholder(R.drawable.ic_show_love_bar),
                contentScale = ContentScale.Crop,
            )
            Spacer(modifier = Modifier.weight(1f))
        } else {
            AutoSizeText(
                text = stringResource(id = R.string.not_public_yet),
                modifier = Modifier
                    .weight(1f)
                    .padding(end = 5.dp),
                color = Color(0xFF804458),
                fontSize = 14.sp
            )
        }

        if (cpZone.isPublishCp.not() || (cpZone.isPublishCp && cpZone.hasNextLevel)) {
            Button(
                modifier = Modifier.size(68.dp, 28.dp),
                contentPadding = PaddingValues(horizontal = 5.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFFFF3A80)),
                onClick = onClick
            ) {
                AutoSizeText(
                    text = stringResource(id = if (cpZone.isPublishCp) R.string.升级 else R.string.goto_pub_cp),
                    color = Color.White,
                    fontSize = 13.sp
                )
            }
        }
    }
}

@Composable
private fun ColumnScope.CpZoneContent(
    cpZone: CpZone,
    onAction: (UIAction) -> Unit = {},
) {
    CpLevelItem(
        cpZone = cpZone, modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp), onAction = onAction
    )
    CpHeatItem(
        cpZone = cpZone, modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp), onAction = onAction
    )
    if (cpZone.cpTask != null) {
        DailyTaskOfCpHeat(
            cpUser = cpZone.targetUser,
            cpTask = cpZone.cpTask,
            modifier = Modifier.fillMaxWidth(),
            onAction = onAction,
        )
    }
}

@Composable
private fun CpLevelItem(cpZone: CpZone, modifier: Modifier, onAction: (UIAction) -> Unit) {
    ItemBox(modifier = modifier) {
        Row(
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                modifier = Modifier.padding(end = 5.dp),
                text = stringResource(id = R.string.CP等级),
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
            )

            if (cpZone.isPublishCp) {
                ComposeImage(
                    model = cpZone.cpPublishLevelUrl,
                    modifier = Modifier.size(93.dp, 28.dp),
                    loading = placeholder(R.drawable.ic_show_love_bar),
                    contentScale = ContentScale.Crop,
                )
            } else {
                AutoSizeText(
                    text = stringResource(id = R.string.暂无等级),
                    modifier = Modifier
                        .weight(1f)
                        .padding(end = 5.dp),
                    color = Color(0xFF939394),
                    fontSize = 14.sp,
                )
            }

            Button(
                modifier = Modifier.size(68.dp, 28.dp),
                contentPadding = PaddingValues(horizontal = 5.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF945EFF)),
                onClick = {
                    onAction(UIAction.OnCpUpgrade)
                }
            ) {
                AutoSizeText(text = stringResource(id = R.string.升级), color = Color.White, fontSize = 13.sp)
            }
        }
    }
}


@Composable
private fun CpHeatItem(cpZone: CpZone, modifier: Modifier, onAction: (UIAction) -> Unit) {
    ItemBox(modifier = modifier) {
        Column {
            Box(modifier = Modifier.fillMaxWidth()) {
                AppText(
                    text = buildAnnotatedString {
                        append(stringResource(id = R.string.qi_ri_lian_ai_re_du))
                        withStyle(SpanStyle(fontFamily = D_DIN, fontSize = 18.sp, color = Color(0xFFFE0466))) {
                            append("${cpZone.hotDegree} ℃")
                        }
                    },
                    modifier = Modifier.align(Alignment.CenterStart),
                    color = Color.White, fontSize = 16.sp, fontWeight = FontWeight.Bold,
                )
                Image(
                    painter = painterResource(id = R.drawable.ic_cp_zone_qa),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .clickable {
                            onAction(UIAction.OnCpHeatRule)
                        }
                )
            }
            BoxWithConstraints(
                modifier = Modifier
                    .padding(vertical = 16.dp)
                    .fillMaxWidth()
                    .height(16.dp)
                    .background(Color(0xFF474849), Shapes.small)
                    .clip(Shapes.small)
            ) {
                val progress = cpZone.hotDegree.toFloat().div(cpZone.hotDegreeGoal).coerceAtMost(1f)
                val width by animateDpAsState(
                    targetValue = maxWidth.times(progress),
                    label = "cp_heat_progress"
                )
                Box(
                    modifier = Modifier
                        .width(width)
                        .fillMaxHeight()
                        .background(Brush.horizontalGradient(listOf(Color(0xFFD3BCFF), Color(0xFF945EFF))), Shapes.small)
                )
            }
            Box(modifier = Modifier.fillMaxWidth()) {
                Text(
                    text = "0 ℃",
                    modifier = Modifier.align(Alignment.CenterStart),
                    color = Color.White,
                    fontSize = 14.sp,
                    fontFamily = D_DIN
                )
                Text(
                    text = "${cpZone.hotDegreeGoal} ℃",
                    modifier = Modifier.align(Alignment.CenterEnd),
                    color = Color.White,
                    fontSize = 14.sp,
                    fontFamily = D_DIN
                )
            }
        }
    }
}

@Composable
private fun DailyTaskOfCpHeat(
    cpUser: User,
    cpTask: CpTask,
    modifier: Modifier,
    onAction: (UIAction) -> Unit = {},
) {
    val context = LocalContext.current
    ItemBox(modifier) {
        Column {
            TaskTitle(cpTask)
            HorizontalDivider(modifier = Modifier.fillMaxWidth(), thickness = 1.dp, color = Color(0xFF33354A))
            TaskItem(
                stringResource(id = R.string.cp_sign_toge),
                stringResource(id = R.string.format_increase_degree),
                stringResource(id = R.string.format_increase_degree2, cpTask.checkedHotDegree),
                if (!cpTask.myCheckedStatus)
                    stringResource(R.string.sign_now)
                else if (!cpTask.cpCheckedStatus) {
                    stringResource(R.string.remind_cp)
                } else {
                    stringResource(R.string.complete_already)
                },
                cpTask.myCheckedStatus && cpTask.cpCheckedStatus,
            ) {
                onAction(UIAction.OnCheckIn)
            }
            HorizontalDivider(modifier = Modifier.fillMaxWidth(), thickness = 1.dp, color = Color(0xFF33354A))
            TaskItem(
                stringResource(id = R.string.format_audio_time, cpTask.audioroomTargetDuration.div(60)),
                stringResource(id = R.string.format_total_degree, cpTask.audioroomTogetherDuration.div(60)),
                stringResource(id = R.string.format_total_degree2, cpTask.audioroomHotDegree),
                if (cpTask.audioroomTogetherDuration < cpTask.audioroomTargetDuration)
                    stringResource(R.string.go_finish)
                else
                    stringResource(R.string.complete_already),
                cpTask.audioroomTogetherDuration >= cpTask.audioroomTargetDuration,
            ) {
                onAction(UIAction.OnPrivateRoom)
            }
            HorizontalDivider(modifier = Modifier.fillMaxWidth(), thickness = 1.dp, color = Color(0xFF33354A))
            TaskItem(
                stringResource(id = R.string.format_mic_time, cpTask.privateRoomTargetDuration.div(60)),
                stringResource(id = R.string.format_total_degree, cpTask.privateRoomTogetherDuration.div(60)),
                stringResource(id = R.string.format_total_degree2, cpTask.privateRoomHotDegree),
                if (cpTask.privateRoomTogetherDuration < cpTask.privateRoomTargetDuration)
                    stringResource(R.string.go_finish)
                else
                    stringResource(R.string.complete_already),
                cpTask.privateRoomTogetherDuration >= cpTask.privateRoomTargetDuration,
            ) {
                context.startActivity(ChatActivity.createIntent(context, cpUser))
            }
            HorizontalDivider(modifier = Modifier.fillMaxWidth(), thickness = 1.dp, color = Color(0xFF33354A))
            TaskItem(
                stringResource(id = R.string.format_gift_total, cpTask.cpCoinTarget),
                stringResource(id = R.string.format_gift_degress, cpTask.giftCoinCnt),
                stringResource(id = R.string.format_gift_degress2, cpTask.cpCoinHotDegree),
                if (cpTask.giftCoinCnt < cpTask.cpCoinTarget)
                    stringResource(R.string.go_finish)
                else
                    stringResource(R.string.complete_already),
                cpTask.giftCoinCnt >= cpTask.cpCoinTarget,
            ) {
                context.startActivity(ChatActivity.createIntent(context, cpUser))
            }
        }
    }
}

@Composable
private fun TaskTitle(cpTask: CpTask) {
    val deadline = cpTask.endTimestamp.times(1000)

    var countDownTime by remember {
        mutableStateOf(emptyList<String>())
    }

    LaunchedEffect(key1 = cpTask.version, key2 = cpTask.dailyTaskFinished) {
        if (cpTask.version != 1 || cpTask.dailyTaskFinished) {
            countDownTime = emptyList()
            return@LaunchedEffect
        }
        while (isActive) {
            delay(1000)
            val now = SNTPManager.now()
            if (now >= deadline) {
                countDownTime = emptyList()
                break
            } else {
                countDownTime = formatTimeWithHours(deadline.minus(now)).split(":")
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 18.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            AppText(
                text = cpTask.dailyTaskTitle.ifEmpty {
                    stringResource(id = R.string.cp_mei_ri_ren_wu)
                },
                color = Color.White,
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
            )

            AppText(
                text = if (cpTask.dailyTaskFinished) stringResource(R.string.today_finished) else stringResource(R.string.today_not_finished),
                modifier = Modifier
                    .padding(start = 8.dp)
                    .background(Color(0x4DFE0466), Shapes.extraSmall)
                    .padding(horizontal = 6.dp, vertical = 4.dp),
                color = Color(0xFFFE0466),
                fontSize = 12.sp,
            )

            Spacer(modifier = Modifier.weight(1f))

            if (countDownTime.size == 3) {
                Text(
                    text = countDownTime[0],
                    modifier = Modifier
                        .size(20.dp, 20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFF681D3B))
                        .wrapContentHeight(),
                    style = TextStyle(fontSize = 14.sp, color = Color(0xFFFE0466), fontFamily = D_DIN),
                    textAlign = TextAlign.Center,
                )

                Text(text = ":", color = Color(0xFFFE0466), fontSize = 14.sp, fontWeight = FontWeight.Bold)

                Text(
                    text = countDownTime[1],
                    modifier = Modifier
                        .size(20.dp, 20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFF681D3B))
                        .wrapContentHeight(),
                    style = TextStyle(fontSize = 14.sp, color = Color(0xFFFE0466), fontFamily = D_DIN),
                    textAlign = TextAlign.Center,
                )

                Text(text = ":", color = Color(0xFFFE0466), fontSize = 14.sp, fontWeight = FontWeight.Bold)

                Text(
                    text = countDownTime[2],
                    modifier = Modifier
                        .size(20.dp, 20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFF681D3B))
                        .wrapContentHeight(),
                    style = TextStyle(fontSize = 14.sp, color = Color(0xFFFE0466), fontFamily = D_DIN),
                    textAlign = TextAlign.Center,
                )
            }
        }

        AutoSizeText(
            text = cpTask.dailyTaskNote,
            color = Color(0xFF939394),
            fontSize = 12.sp,
            maxLines = 3,
        )
    }
}

@Composable
private fun CountDownTimerBar(hotDegreeDeadTime: Long, textColor: Color = Color(0xFFFE0466), onTickComplete: () -> Unit) {
    var countDownTime by remember {
        mutableStateOf(emptyList<String>())
    }

    LaunchedEffect(key1 = hotDegreeDeadTime) {
        while (isActive) {
            delay(1000)
            val now = SNTPManager.now()
            if (now >= hotDegreeDeadTime) {
                countDownTime = emptyList()
                onTickComplete.invoke()
                break
            } else {
                countDownTime = formatTimeWithHours(hotDegreeDeadTime.minus(now)).split(":")
            }
        }
    }
    if (countDownTime.size == 3) {
        val cellModifier = remember(key1 = "cellM") {
            Modifier
                .height(20.dp)
                .requiredWidthIn(22.dp)
                .clip(RoundedCornerShape(3.dp))
                .background(Color(0xFFFF249A))
                .padding(horizontal = 2.dp)
                .wrapContentHeight()
        }
        val style = remember(key1 = "style") {
            TextStyle(fontSize = 14.sp, color = textColor, fontFamily = D_DIN)
        }
        Text(
            text = countDownTime[0],
            modifier = cellModifier,
            style = style,
            textAlign = TextAlign.Center,
        )

        Text(text = ":", color = Color(0xFFFE0466), fontSize = 14.sp, fontWeight = FontWeight.Bold)

        Text(
            text = countDownTime[1],
            modifier = cellModifier,
            style = style,
            textAlign = TextAlign.Center,
        )

        Text(text = ":", color = Color(0xFFFE0466), fontSize = 14.sp, fontWeight = FontWeight.Bold)

        Text(
            text = countDownTime[2],
            modifier = cellModifier,
            style = style,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
private fun TaskItem(
    taskDesc: String,
    taskProgress1: String,
    taskProgress2: String,
    button: String,
    isDone: Boolean,
    action: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 18.dp),
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            AppText(
                text = taskDesc,
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                lineSpacingRatio = 0.2f,
            )

            AppText(
                text = buildAnnotatedString {
                    append(taskProgress1)
                    withStyle(SpanStyle(color = Color(0xFF945EFF))) {
                        append(taskProgress2)
                    }

                },
                color = Color(0xFF939394),
                fontSize = 12.sp,
                lineSpacingRatio = 0.2f,
            )
        }
        Spacer(modifier = Modifier.width(5.dp))
        Button(
            onClick = action,
            modifier = Modifier
                .padding(8.dp)
                .size(68.dp, 28.dp),
            enabled = !isDone,
            contentPadding = PaddingValues(horizontal = 5.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFF945EFF),
                disabledContainerColor = Color(0x80945EFF),
            )
        ) {
            AutoSizeText(text = button, color = Color.White, fontSize = 13.sp)
        }
    }
}

@Composable
private fun ItemBox(modifier: Modifier, content: @Composable BoxScope.() -> Unit) {
    Box(
        modifier = Modifier
            .border(1.dp, Color(0x1AFFFFFF), Shapes.corner12)
            .background(Color(0x0DFFFFFF), Shapes.corner12)
            .padding(horizontal = 16.dp)
            .then(modifier),
        content = content
    )
}

@Preview(backgroundColor = 0xFF111111, showBackground = true)
@Composable
fun PreviewCpZonePage() {
    CpZonePage(
        1,
        CpZoneWrapper(
            CpZone(
                60,
                100,
                233,
                userForPreview,
                userForPreview,
                CpTask()
            ),
            cpRelationship = Relationship.Cp.previewSimple1(),
        )
    )
}

@Composable
private fun ColumnScope.CpZoneContent2(
    cpZone: CpZone,
    onAction: (UIAction) -> Unit = {},
) {
    val cpTask = cpZone.cpTask

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(Shapes.corner12)
            .background(Brush.verticalGradient(listOf(Color(0xFFFFDBE3), Color(0xFFFF9EC0))))
            .padding(horizontal = 16.dp)
    ) {
        val challengeMode = cpTask.isChallengeMode

        Image(
            painter = painterResource(
                id = if (challengeMode) R.drawable.bg_cpzone_challenge
                else R.drawable.bg_cpzone_level
            ),
            contentDescription = null,
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(top = 10.dp)
                .height(54.dp),
            contentScale = ContentScale.FillHeight,
            alignment = Alignment.Center
        )

        if (challengeMode) {
            ChallengeTaskDesc(
                ongoing = cpTask!!.ongoing!!,
                modifier = Modifier
                    .align(Alignment.CenterHorizontally)
                    .padding(top = 8.dp)
                    .fillMaxWidth(),
            )
        } else {
            Spacer(modifier = Modifier.height(16.dp))
            PublicCPBar(
                cpZone = cpZone,
                modifier = Modifier
                    .background(colorWhite30Alpha, Shapes.small)
                    .padding(12.dp, 16.dp)
            ) {
                onAction(UIAction.OnCpUpgrade)
            }
            CpHeatItem2(
                cpZone = cpZone,
                modifier = Modifier
                    .padding(top = 12.dp)
                    .fillMaxWidth(),
                onAction = onAction
            )
        }

        if (cpTask != null) {

            TaskTitle2(cpTask, modifier = Modifier.padding(top = 10.dp))

            cpTask.taskList?.forEach { item ->
                HorizontalDivider(modifier = Modifier.fillMaxWidth(), thickness = 1.dp, color = Color(0x1AFFFFFF))
                TaskItem2(
                    taskDesc = item.taskName,
                    taskProgress = "${item.taskProgress}%%${item.taskBonus}",
                    button = item.taskBtnLabel,
                    isDone = item.taskIsFinished,
                ) {
                    onAction(UIAction.OnDoTask(item.taskId))
                }
            } ?: run {
                Spacer(modifier = Modifier.height(5.dp))
            }
        }
    }
}

@Composable
private fun CpHeatItem2(cpZone: CpZone, modifier: Modifier, onAction: (UIAction) -> Unit) {
    Column(
        modifier = modifier
            .clip(Shapes.small)
            .background(Color(0x4DFFFFFF))
            .padding(16.dp)
    ) {
        Row(modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.CenterVertically) {
            AppText(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.qi_ri_lian_ai_re_du))
                    withStyle(SpanStyle(fontFamily = D_DIN, fontSize = 18.sp, color = Color(0xFFFE0466))) {
                        append("${cpZone.hotDegree}℃")
                    }
                },
                color = Color(0xFF590D27), fontSize = 16.sp, fontWeight = FontWeight.Bold,
            )
            Spacer(modifier = Modifier.width(8.dp))
            Image(
                painter = painterResource(id = R.drawable.ic_cp_zone_qa),
                contentDescription = null,
                modifier = Modifier
                    .size(18.dp)
                    .clickable {
                        onAction(UIAction.OnCpHeatRule)
                    },
                colorFilter = ColorFilter.tint(Color(0xFF590D27))
            )
            Spacer(modifier = Modifier.weight(1f))
            if (cpZone.hotDegree < cpZone.hotDegreeGoal) {
                CountDownTimerBar(cpZone.hotDegreeDeadTime * 1000, Color.White) {
                    onAction(UIAction.OnRefresh)
                }
            }
        }
        Spacer(modifier = Modifier.height(9.dp))
        AppText(text = cpZone.heatTip, fontSize = 11.sp, color = Color(0xFFFE0466))
        BoxWithConstraints(
            modifier = Modifier
                .padding(vertical = 16.dp)
                .fillMaxWidth()
                .height(16.dp)
                .background(Color(0xFFFFB1CC), Shapes.small)
                .clip(Shapes.small)
        ) {
            val progress = cpZone.hotDegree.toFloat().div(cpZone.hotDegreeGoal).coerceAtMost(1f)
            val width by animateDpAsState(
                targetValue = maxWidth.times(progress),
                label = "cp_heat_progress"
            )
            Box(
                modifier = Modifier
                    .width(width)
                    .fillMaxHeight()
                    .background(Brush.horizontalGradient(listOf(Color(0xFFFFA9BE), Color(0xFFFF5B94))), Shapes.small)
            )
        }
        Box(modifier = Modifier.fillMaxWidth()) {
            Text(
                text = "0 ℃",
                modifier = Modifier.align(Alignment.CenterStart),
                color = Color(0xFF590D27),
                fontSize = 14.sp,
                fontFamily = D_DIN
            )
            Text(
                text = "${cpZone.hotDegreeGoal} ℃",
                modifier = Modifier.align(Alignment.CenterEnd),
                color = Color(0xFF590D27),
                fontSize = 14.sp,
                fontFamily = D_DIN
            )
        }
    }
}


@Composable
private fun TaskTitle2(cpTask: CpTask, modifier: Modifier) {
    val deadline = cpTask.endTimestamp.times(1000)

    var countDownTime by remember {
        mutableStateOf(emptyList<String>())
    }

    LaunchedEffect(key1 = cpTask.version, key2 = cpTask.dailyTaskFinished, key3 = cpTask.ongoing) {
        if (cpTask.version != 2 || cpTask.dailyTaskFinished || cpTask.ongoing == null) {
            countDownTime = emptyList()
            return@LaunchedEffect
        }
        while (isActive) {
            delay(1000)
            val now = SNTPManager.now()
            if (now >= deadline) {
                countDownTime = emptyList()
                break
            } else {
                countDownTime = formatTimeWithHours(deadline.minus(now)).split(":")
            }
        }
    }

    Column(
        modifier = modifier
            .fillMaxWidth()
            .padding(vertical = 18.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        Row(verticalAlignment = Alignment.CenterVertically) {
            AppText(
                text = buildAnnotatedString {
                    if (cpTask.version == 2) {
                        if (cpTask.ongoing != null) {
                            val text = stringResource(id = R.string.挑战第几日, cpTask.ongoing.activityDayIndex)
                            val index = text.lastIndexOf(cpTask.ongoing.activityDayIndex.toString())
                            append(text)
                            if (index > -1) {
                                addStyle(
                                    SpanStyle(
                                        color = Color(0xFFFF249A),
                                        fontSize = 24.sp,
                                        fontFamily = D_DIN,
                                        letterSpacing = 5.sp
                                    ), index, index.plus(cpTask.ongoing.activityDayIndex.toString().length)
                                )
                            }
                        } else {
                            append(cpTask.dailyTaskTitle.ifEmpty {
                                stringResource(id = R.string.热度值奖励任务)
                            })
                        }
                    } else {
                        append(cpTask.dailyTaskTitle.ifEmpty {
                            stringResource(id = R.string.cp_mei_ri_ren_wu)
                        })
                    }
                },
                color = Color(0xFF590D27),
                fontSize = 16.sp,
                fontWeight = FontWeight.Bold,
            )

            if (cpTask.showFinishStatus) {
                AutoSizeText(
                    text = if (cpTask.dailyTaskFinished) stringResource(R.string.today_finished) else stringResource(R.string.today_not_finished),
                    modifier = Modifier
                        .padding(horizontal = 8.dp)
                        .background(Color(0xFFFFD5E3), Shapes.extraSmall)
                        .padding(horizontal = 6.dp, vertical = 4.dp),
                    color = Color(0xFFFF249A),
                    fontSize = 12.sp,
                )
            }

            Spacer(modifier = Modifier.weight(1f))

            if (countDownTime.size == 3) {
                Text(
                    text = countDownTime[0],
                    modifier = Modifier
                        .size(20.dp, 20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFFFFD5E3))
                        .wrapContentHeight(),
                    style = TextStyle(fontSize = 14.sp, color = Color(0xFFFF249A), fontFamily = D_DIN),
                    textAlign = TextAlign.Center,
                )

                Text(text = ":", color = Color(0xFFFF249A), fontSize = 14.sp, fontWeight = FontWeight.Bold)

                Text(
                    text = countDownTime[1],
                    modifier = Modifier
                        .size(20.dp, 20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFFFFD5E3))
                        .wrapContentHeight(),
                    style = TextStyle(fontSize = 14.sp, color = Color(0xFFFF249A), fontFamily = D_DIN),
                    textAlign = TextAlign.Center,
                )

                Text(text = ":", color = Color(0xFFFF249A), fontSize = 14.sp, fontWeight = FontWeight.Bold)

                Text(
                    text = countDownTime[2],
                    modifier = Modifier
                        .size(20.dp, 20.dp)
                        .clip(RoundedCornerShape(3.dp))
                        .background(Color(0xFFFFD5E3))
                        .wrapContentHeight(),
                    style = TextStyle(fontSize = 14.sp, color = Color(0xFFFF249A), fontFamily = D_DIN),
                    textAlign = TextAlign.Center,
                )
            }
        }

        AutoSizeText(
            text = cpTask.dailyTaskNote,
            color = Color(0xFF804458),
            fontSize = 12.sp,
            maxLines = 3,
        )
    }
}


@Composable
private fun TaskItem2(
    taskDesc: String,
    taskProgress: String,
    button: String,
    isDone: Boolean,
    action: () -> Unit = {},
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 18.dp),
    ) {
        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            AppText(
                text = taskDesc,
                color = Color(0xFF590D27),
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                lineSpacingRatio = 0.2f,
            )

            AppText(
                text = buildAnnotatedString {
                    val list = taskProgress.split("%%")
                    list.forEachIndexed { index, text ->
                        if (index > 0 && index == list.lastIndex) {
                            withStyle(SpanStyle(color = Color(0xFFFE0466))) {
                                append(text)
                            }
                        } else {
                            append(text)
                        }
                    }
                },
                color = Color(0xFF804458),
                fontSize = 12.sp,
                lineSpacingRatio = 0.2f,
            )
        }
        Spacer(modifier = Modifier.width(5.dp))
        Button(
            onClick = action,
            modifier = Modifier
                .padding(8.dp)
                .size(68.dp, 28.dp),
            enabled = !isDone,
            contentPadding = PaddingValues(horizontal = 5.dp),
            colors = ButtonDefaults.buttonColors(
                containerColor = Color(0xFFFF3A80),
                disabledContainerColor = Color(0xFFFF8EB5),
            )
        ) {
            AutoSizeText(text = button, color = Color.White, fontSize = 13.sp)
        }
    }
}

@Composable
private fun ChallengeTaskDesc(ongoing: Ongoing, modifier: Modifier) {
    Column(modifier = modifier) {
        Text(
            text = ongoing.activityDesc,
            modifier = Modifier.padding(horizontal = 43.dp),
            color = Color(0xFF804458),
            fontSize = 11.sp,
            textAlign = TextAlign.Center,
        )
        LazyRow(
            modifier = Modifier
                .padding(top = 16.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(20.dp, Alignment.CenterHorizontally)
        ) {
            items(ongoing.activityBonusList) {
                Column {
                    Box(
                        modifier = Modifier
                            .size(80.dp)
                            .background(Color(0xFFFF87B1), Shapes.corner12),
                        contentAlignment = Alignment.Center
                    ) {
                        ComposeImage(model = it.icon, modifier = Modifier.size(68.dp), contentScale = ContentScale.Inside)
                    }
                    Text(
                        text = it.text,
                        modifier = Modifier
                            .padding(top = 8.dp)
                            .width(80.dp)
                            .padding(horizontal = 4.dp),
                        color = Color(0xFF804458),
                        fontSize = 12.sp,
                        textAlign = TextAlign.Center,
                    )
                }
            }
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF111111)
@Composable
private fun PreviewCpZonePage2() {
    CpZonePage(
        0,
        CpZoneWrapper(
            cpZone = CpZone(
                60,
                100,
                233,
                userForPreview,
                userForPreview,
                CpTask(version = 2)
            ).also {
                it.hasNextLevel = true
                it.isPublishCp = false
            },
            cpRelationship = Relationship.Cp.previewSimple2(),
        )
    )
}
