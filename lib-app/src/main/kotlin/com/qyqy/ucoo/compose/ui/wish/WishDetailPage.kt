package com.qyqy.ucoo.compose.ui.wish

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Icon
import androidx.compose.material3.LinearProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.profile.wish.RoomWishViewModel
import com.qyqy.cupid.ui.profile.wish.WishEntry
import com.qyqy.cupid.ui.profile.wish.WishGift
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick

@Composable
fun WishDetailPage(
    index: Int,
    userId: String?,
    wishViewModel: RoomWishViewModel,
    onBack: OnClick = {},
    onNavToRecord: OnClick = {},
    onAddWish: OnClick = {},
    onRemoveWish: (Int, WishEntry) -> Unit = { _, _ -> },
    onSendGift: (User, gift: WishGift) -> Unit = { _, _ -> },
    onNavProfile: (User) -> Unit = {},
) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(32.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                contentDescription = "back",
                modifier = Modifier
                    .padding(start = 22.dp)
                    .noEffectClickable(onClick = onBack),
                tint = Color.White,
            )

            Row(
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp)
                    .clickable(onClick = onNavToRecord),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(text = stringResource(id = R.string.心愿助力记录), color = Color.White, fontSize = 12.sp)
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_arrow_right),
                    contentDescription = null,
                    tint = Color.White
                )
            }
        }

        val model by remember {
            derivedStateOf {
                wishViewModel.userWishEntries.find {
                    it.user?.id == userId
                } ?: wishViewModel.userWishEntries.getOrNull(index)
            }
        }

        val targetUser = model?.user?.takeIf { !it.isSelf }
        val wishEntries = model?.list.orEmpty()
        val isSelf = model?.user?.isSelf == true

        Column(
            modifier = Modifier
                .padding(top = 15.dp)
                .fillMaxWidth()
                .heightIn(max = 450.dp)
                .verticalScroll(rememberScrollState())
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {

            wishEntries.forEachIndexed { index, wishEntry ->
                WishItem(targetUser, index, wishEntry, {
                    if (isSelf) {
                        onRemoveWish(index + 1, wishEntry)
                    } else {
                        onSendGift(targetUser!!, wishEntry.gift)
                    }
                }, onNavProfile)
            }

            if (isSelf && wishEntries.size < wishViewModel.wishListConfig.countLimit) {
                AddWishItem(onAddWish)
            } else if (!isSelf && wishEntries.isEmpty()) {
                Image(
                    painter = painterResource(id = R.drawable.ic_cpd_empty_wish),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(top = 45.dp)
                        .size(120.dp)
                )

                Text(
                    text = stringResource(id = R.string.暂无心愿),
                    color = colorResource(id = R.color.white_alpha_50),
                    fontSize = 12.sp
                )
            }

            if (isSelf && wishViewModel.wishListConfig.rule.isNotEmpty()) {
                Text(
                    text = wishViewModel.wishListConfig.rule,
                    modifier = Modifier.padding(top = 6.dp),
                    color = colorResource(id = R.color.white_alpha_50),
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                )
            } else if (!isSelf && wishViewModel.wishListConfig.taRule.isNotEmpty()) {
                Text(
                    text = wishViewModel.wishListConfig.taRule,
                    modifier = Modifier.padding(top = 6.dp),
                    color = colorResource(id = R.color.white_alpha_50),
                    fontSize = 12.sp,
                    lineHeight = 16.sp,
                )
            }
        }
    }
}



@Composable
private fun WishItem(
    targetUser: User?,
    index: Int,
    wishEntry: WishEntry,
    onClick: OnClick = {},
    onNavProfile: (User) -> Unit = {},
) {
    val self = targetUser == null
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
    ) {
        if (targetUser != null) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(40.dp)
                    .background(Color(0xFFE681FF))
                    .padding(horizontal = 8.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                CircleComposeImage(
                    model = targetUser.avatarUrl,
                    modifier = Modifier
                        .size(32.dp)
                        .noEffectClickable(onClick = {
                            onNavProfile(targetUser)
                        }),
                    borderStroke = BorderStroke(0.5.dp, Color(0xFFFFFFFF))
                )
                Text(
                    text = targetUser.nickname,
                    modifier = Modifier.basicMarquee(),
                    color = Color.White,
                    fontSize = 14.sp,
                    maxLines = 1,
                    fontWeight = FontWeight.Medium
                )
            }
        }

        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(88.dp)
                .background(Brush.verticalGradient(listOf(Color(0xFFF7EBFF), Color(0xFFECC4FF))))
                .border(
                    width = 0.5.dp,
                    brush = Brush.verticalGradient(listOf(Color(0x4DFFFFFF), Color(0x1AFFFFFF))),
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {

            Box(
                modifier = Modifier
                    .size(64.dp)
                    .background(Color(0xFFEFC0FF), RoundedCornerShape(4.dp))
            ) {
                ComposeImage(
                    model = wishEntry.gift.icon,
                    modifier = Modifier
                        .align(Alignment.Center)
                        .size(56.dp),
                    loading = null
                )
            }

            Column(
                modifier = Modifier
                    .weight(1f)
                    .height(64.dp),
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                AppText(
                    text = stringResource(id = R.string.心愿, index + 1, wishEntry.gift.name),
                    color = Color(0xFFD354FF),
                    fontSize = 14.sp,
                    maxLines = 1
                )
                Row(verticalAlignment = Alignment.CenterVertically) {
                    LinearProgressIndicator(
                        progress = { wishEntry.process.toFloat().div(wishEntry.count).coerceIn(0f, 1f) },
                        modifier = Modifier
                            .fillMaxWidth(0.75f)
                            .height(8.dp)
                            .clip(CircleShape),
                        color = Color(0xFFD354FF),
                        trackColor = Color(0xFFFFF4FE),
                    )
                    AppText(
                        text = "${wishEntry.process}/${wishEntry.count}",
                        modifier = Modifier.padding(start = 8.dp),
                        color = Color(0xFFD354FF),
                        fontSize = 12.sp,
                        maxLines = 1
                    )
                }
                AppText(
                    text = stringResource(id = R.string.感恩方式_, wishEntry.thanks.orEmpty()),
                    modifier = Modifier.basicMarquee(),
                    color = Color(0xFFD354FF),
                    fontSize = 12.sp,
                    maxLines = 1,
                    fontWeight = FontWeight.Medium
                )
            }

            if (self) {
                Icon(
                    painter = painterResource(id = R.drawable.ic_cpd_close_grey),
                    contentDescription = null,
                    modifier = Modifier
                        .align(Alignment.Top)
                        .offset(x = 4.dp, y = 3.dp)
                        .size(16.dp)
                        .noEffectClickable(onClick = onClick),
                    tint = Color(0xFFD354FF)
                )
            } else {
                AppButton(
                    text = stringResource(id = R.string.txt_give),
                    brush = Brush.verticalGradient(listOf(Color(0xFFFD8EFF), Color(0xFF8247FF))),
                    modifier = Modifier.size(72.dp, 28.dp),
                    color = Color.White,
                    fontSize = 14.sp,
                    onClick = onClick
                )
            }
        }
    }
}

@Composable
private fun AddWishItem(onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(80.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFFBD8EFF))
            .clickable(onClick = onClick),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
    ) {
        Image(
            painter = painterResource(id = R.drawable.ic_cpd_add_wish),
            contentDescription = null,
            modifier = Modifier.size(24.dp)
        )
        Text(text = stringResource(id = R.string.添加我的心愿), color = Color.White, fontSize = 14.sp)
    }
}

@Preview
@Composable
private fun PreviewWishListPage() {
    WishPageScaffold {
        WishDetailPage(0, userForPreview.id, RoomWishViewModel.Preview)
    }
}