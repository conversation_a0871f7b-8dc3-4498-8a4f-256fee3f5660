package com.qyqy.ucoo.compose.presentation.wedding

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingRoomType
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingWallData
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.compose.state.LoadMoreEffect
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults


@Composable
fun WeddingWallScreen(onlySelf: Boolean = false) {
    val owner = LocalOnBackPressedDispatcherOwner.current
    var mode by remember {
        mutableStateOf(if (onlySelf) CheckMode.SELF else CheckMode.ALL)
    }
    val pagerState = rememberPagerState(initialPage = if (onlySelf) 1 else 0) {
        2
    }
    LaunchedEffect(key1 = mode) {
        pagerState.scrollToPage(if (mode == CheckMode.SELF) 1 else 0)
    }
    val context = LocalContext.current
    WallScreen(title = stringResource(R.string.婚礼纪念墙), onBack = { owner?.onBackPressedDispatcher?.onBackPressed() },
        onQuestion = { AppLinkManager.open(context, WeddingWallViewModel.ruleUrl) }) {
        Column(modifier = Modifier.fillMaxSize()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 26.dp)
                    .padding(top = 16.dp, bottom = 6.dp)
            ) {
                CheckModeSwitchButton(mode = mode, modifier = Modifier.align(Alignment.BottomEnd)) {
                    mode = it
                }
            }

            HorizontalPager(state = pagerState, modifier = Modifier.fillMaxSize()) { pageIndex ->
                WallPage(checkMode = if (pageIndex == 0) CheckMode.ALL else CheckMode.SELF)
            }
        }
    }
}

@Composable
private fun WallPage(
    checkMode: CheckMode, vm: WeddingWallViewModel = viewModel(key = "mode-$checkMode", factory = viewModelFactory {
        initializer {
            WeddingWallViewModel(onlySelf = checkMode == CheckMode.SELF)
        }
    })
) {
    val list by vm.dataState
    PullRefreshBox(
        isRefreshing = vm.isRefreshing, onRefresh = { vm.refresh() }, colors = PullRefreshIndicatorDefaults.colors(
            containerColor = Color.White,
            contentColor = Color(0xFFFF89BC),
        )
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 10.dp), verticalArrangement = Arrangement.spacedBy(18.dp)
        ) {
            if (vm.isLoaded) {
                if (list.isEmpty()) {
                    item {
                        WeddingEmpty(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(top = 70.dp)
                        )
                    }
                } else {
                    items(list) { data ->
                        val record = data.lastRecord
                        WeddingWallItem(
                            userLeft = record.lightUser,
                            userRight = record.targetUser,
                            theme = WeddingTheme.getByType(data.roomType),
                            roomTypeDesc = data.roomTypeDesc,
                            ringImageUrl = record.ring.icon,
                            desc = stringResource(
                                R.string.format_wedding_wall_desc,
                                record.bookingDate,
                                record.startTime,
                                record.endTime,
                            )
                        )
                    }
                }
            }
            item {
                LoadMoreEffect(allowLoading = vm.allowLoad, hasMore = vm.hasMore) {
                    vm.loadMore()
                }
            }
        }
    }
}

class WeddingWallViewModel(private val onlySelf: Boolean) : LiStateViewModel<WeddingWallData>() {

    companion object {
        var ruleUrl = ""
            private set
    }

    private val api = createApi<WeddingApi>()
    private var lastId = 0

    override fun onStartRequest(isRefresh: Boolean) {
        super.onStartRequest(isRefresh)
        lastId = if (isRefresh) 0 else _dataState.value.lastOrNull()?.lastRecord?.id ?: 0
    }

    override suspend fun fetch(): List<WeddingWallData> {
        return runApiCatching {
            api.getWeddingWallData(onlySelf, lastId)
        }.map {
            ruleUrl = it.parseValue<String>("rule_url").orEmpty()
            it.parseValue<List<WeddingWallData>>("history")
        }.getOrNull().orEmpty()
    }
}


private const val aspectRatioItem = 357 / 262f

@Composable
private fun WeddingWallItem(
    theme: WeddingTheme,
    roomTypeDesc: String,
    userLeft: AppUser,
    userRight: AppUser,
    ringImageUrl: String,
    desc: String
) {
    WeddingCard(
        theme = theme,
        roomTypeDesc = roomTypeDesc,
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(aspectRatioItem)
    ) {
        Spacer(modifier = Modifier.height(32.dp))
        Row(horizontalArrangement = Arrangement.Center) {
            UserColumn(user = userLeft, color = theme.primaryColor)
            ComposeImage(model = ringImageUrl, modifier = Modifier.size(56.dp), contentScale = ContentScale.FillBounds)
            UserColumn(user = userRight, color = theme.primaryColor)
        }
        Spacer(modifier = Modifier.height(16.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(67.dp), contentAlignment = Alignment.Center
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_wedding_desc_shadow),
                contentDescription = "s",
                modifier = Modifier.size(251.dp, 67.dp),
                contentScale = ContentScale.FillBounds
            )
            AppText(
                text = desc,
                color = theme.primaryColor,
                lineHeight = 22.sp,
                fontSize = 14.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(top = 6.dp)
            )
        }
        Spacer(modifier = Modifier.height(24.dp))
    }
}

@Preview
@Composable
private fun CardPreview() {
    val user = userForPreview
    WeddingWallItem(
        userLeft = user, userRight = user,
        theme = WeddingTheme.getByType(WeddingRoomType.STAR_PROMISE_BLUE),
        roomTypeDesc = "星辰之诺",
        ringImageUrl = "", desc = "最近婚礼：2025.03.08.20:00-23:00\n累计举办婚礼：2次"
    )
}