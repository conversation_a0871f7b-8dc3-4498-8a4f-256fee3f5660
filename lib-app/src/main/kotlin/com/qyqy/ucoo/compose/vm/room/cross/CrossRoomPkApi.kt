package com.qyqy.ucoo.compose.vm.room.cross

import com.qyqy.ucoo.compose.data.CrossPkConfigResponse
import com.qyqy.ucoo.compose.data.InvitePkResponse
import com.qyqy.ucoo.compose.data.RoomDetailCrossPkInfo
import com.qyqy.ucoo.compose.data.RoomListResponse
import com.qyqy.ucoo.compose.data.SetMutedResponse
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Field
import retrofit2.http.FormUrlEncoded
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query


// 假设的 Result 包装类 (推荐使用 Kotlin 标准库的 Result)
// typealias Result<T> = kotlin.Result<T> // 如果使用标准库

// --- 2. Retrofit API 接口定义 ---

interface CrossRoomPkApi {

    /**
     * 获取跨房 PK 配置
     */
    @GET("api/audioroom/v1/cross/conf")
    suspend fun getCrossPkConfig(@Query("pre_check") check: Boolean): ApiResponse<CrossPkConfigResponse>

    /**
     * 获取跨房 PK 房间列表
     * @param lastId 上一页最后一个房间的 ID，用于分页
     */
    @GET("api/audioroom/v1/cross/room/list")
    suspend fun getCrossPkRoomList(@Query("last_id") lastId: Int = 0): ApiResponse<RoomListResponse>

    /**
     * 搜索跨房 PK 房间
     * @param publicId 房间的公开 ID
     */
    @GET("api/audioroom/v1/cross/room/search")
    suspend fun searchCrossPkRoom(@Query("public_id") publicId: String): ApiResponse<RoomListResponse>

    /**
     * 邀请跨房 PK
     */
    @FormUrlEncoded
    @POST("api/audioroom/v1/cross/room/invite")
    suspend fun inviteCrossPk(
        @Field("target_room_id") targetRoomId: Int,
        @Field("duration") duration: Int,
        @Field("pk_type") pkType: Int
    ): ApiResponse<InvitePkResponse>

    /**
     * 接受/拒绝跨房 PK 邀请
     */
    @FormUrlEncoded
    @POST("api/audioroom/v1/cross/room/accept")
    suspend fun acceptCrossPkInvite(
        @Field("invite_code") inviteCode: String,
        @Field("accept") accept: Boolean,
        @Field("is_auto") auto: Boolean,
    ): ApiResponse<JsonObject> // 或者 ApiResponse<Unit> 如果data为空

    /**
     * 设置对方房间静音
     */
    @FormUrlEncoded
    @POST("api/audioroom/v1/cross/room/mic/muted")
    suspend fun setOpponentMicMuted(
        @Field("pk_id") pkId: Int,
        @Field("target_room_id") targetRoomId: Int,
        @Field("muted") muted: Boolean
    ): ApiResponse<SetMutedResponse>

    /**
     * 跨房 PK 设置 (加时/终止)
     * @param setType 操作类型 (需要后端定义枚举值, 比如 1=加时, 2=终止)
     * @param pkId 当前 PK 的 ID
     * @param duration 当 setType 为加时时，需要传递加时时长 (秒)
     */
    @FormUrlEncoded
    @POST("api/audioroom/v1/cross/pk/set")
    suspend fun setCrossPk(
        @Field("set_type") setType: Int,
        @Field("pk_id") pkId: Int,
        @Field("duration") duration: Int? = null // 加时需要，终止不需要
    ): ApiResponse<JsonObject> // 或者 ApiResponse<Unit>

    /**
     * 获取语音房详情 (用于获取进行中的 PK 信息)
     * 注意：这里只定义了获取详情的接口，你需要确保能拿到详情里的 cross_pk_info 字段
     */
    @GET("api/audioroom/v1/audioroom/detail")
    suspend fun getAudioRoomDetail(): ApiResponse<RoomDetailCrossPkInfo> // 假设响应体只关心 PK 信息
}
