package com.qyqy.ucoo.compose.presentation.language

import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface LanguageApi {

    @GET("api/ucuser/v1/language/settings")
    suspend fun getSettings():ApiResponse<LangEntity>

    @POST("api/ucuser/v1/language/update")
    suspend fun update(@Body map:Map<String,String>):ApiResponse<JsonObject>
}