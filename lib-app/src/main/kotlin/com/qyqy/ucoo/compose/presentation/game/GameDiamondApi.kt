package com.qyqy.ucoo.compose.presentation.game

import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface GameDiamondApi {

    @GET("api/wallet/v1/diamond/earn/entry")
    suspend fun fetchGameList(): ApiResponse<GameList>

    @GET("api/wallet/v1/mall/exchange/list")
    suspend fun fetchExchangeList(@Query("exchange_type") type: Int = 1): ApiResponse<ExchangeList>

    @POST("api/wallet/v1/mall/exchange/item")
    suspend fun postDiamondExchange(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/audioroom/v1/game/match/apply")
    suspend fun startMatch(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/audioroom/v1/game/match/accept")
    suspend fun agreeInvite(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/audioroom/v1/game/match/refuse")
    suspend fun refuseInvite(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/audioroom/v1/game/match/cancel")
    suspend fun cancelMatch(@Body body: Map<String, String>): ApiResponse<JsonObject>

    @GET("api/wallet/v1/diamond/change/list")
    suspend fun fetchDiamondHistoryList(@Query("last_id") lastId: Int?): ApiResponse<DiamondHistory>


    //日区特别添加
    @GET("api/wallet/v1/diamond/exchange/list")
    suspend fun fetchDiamondExchangeList(@Query("last_id") lastId: Int?): ApiResponse<com.qyqy.cupid.data.ExchangeList>
}