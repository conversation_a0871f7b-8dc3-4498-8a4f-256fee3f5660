package com.qyqy.ucoo.compose.ui.video.controller

import android.content.Context
import android.content.pm.ActivityInfo
import android.os.Build
import android.util.AttributeSet
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.ProgressBar
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.TextView
import com.qyqy.ucoo.R
import site.xzwzz.video.player.bridge.ControlWrapper
import site.xzwzz.video.player.config.ConstantKeys
import site.xzwzz.video.player.player.VideoViewManager.getConfig
import site.xzwzz.video.player.player.VideoViewManager.setPlayOnMobileNetwork
import site.xzwzz.video.player.tool.PlayerUtils
import site.xzwzz.video.player.ui.view.InterControlView

class CustomBottomView : FrameLayout, InterControlView, OnSeekBarChangeListener {
    private lateinit var mContext: Context
    private lateinit var mControlWrapper: ControlWrapper

    private lateinit var mLlBottomContainer: LinearLayout
    private lateinit var mTvCurrTime: TextView
    private lateinit var mSeekBar: SeekBar
    private lateinit var mPbBottomProgress: ProgressBar

    lateinit var thumb: ImageView
        private set
    private lateinit var mIvStartPlay: ImageView
    private lateinit var mIvReplay: ImageView

    private var mIsDragging = false
    private var mIsShowBottomProgress = true

    constructor(context: Context) : this(context, null)

    constructor(context: Context, attrs: AttributeSet?) : this(context, attrs, 0)

    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(context, attrs, defStyleAttr) {
        init(context)
    }

    private fun init(context: Context) {
        mContext = context
        val view = LayoutInflater.from(getContext()).inflate(layoutId, this, true)
        initFindViewById(view)
        initListener()
        //5.1以下系统SeekBar高度需要设置成WRAP_CONTENT
        if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.LOLLIPOP_MR1) {
            mPbBottomProgress.layoutParams.height = ViewGroup.LayoutParams.WRAP_CONTENT
        }
    }

    private fun initFindViewById(view: View) {
        mLlBottomContainer = view.findViewById(R.id.ll_bottom_container)
        mTvCurrTime = view.findViewById(R.id.tv_curr_time)
        mSeekBar = view.findViewById(R.id.seekBar)
        mPbBottomProgress = view.findViewById(R.id.pb_bottom_progress)

        thumb = view.findViewById(R.id.iv_thumb)
        mIvStartPlay = view.findViewById(R.id.iv_start_play)
        mIvReplay = view.findViewById(R.id.iv_start_replay)

    }

    private fun initListener() {
        mSeekBar.setOnSeekBarChangeListener(this)

        mIvStartPlay.setOnClickListener {
            setPlayOnMobileNetwork(true)
            mControlWrapper.start()
        }
        mIvReplay.setOnClickListener {
            mControlWrapper.replay(true)
        }
    }


    protected val layoutId: Int
        get() = R.layout.custom_video_player_bottom

    /**
     * 是否显示底部进度条，默认显示
     */
    fun showBottomProgress(isShow: Boolean) {
        mIsShowBottomProgress = isShow
    }

    override fun attach(controlWrapper: ControlWrapper) {
        mControlWrapper = controlWrapper
    }

    override val view: View
        get() = this

    override fun onVisibilityChanged(isVisible: Boolean, anim: Animation?) {
        if (isVisible) {
            mLlBottomContainer.visibility = VISIBLE
        } else {
            mLlBottomContainer.visibility = GONE
        }
    }

    override fun onPlayStateChanged(playState: Int) {
        when (playState) {
            ConstantKeys.CurrentState.STATE_PREPARING -> {
                mIvStartPlay.visibility = VISIBLE
                mIvReplay.visibility = GONE
            }

            ConstantKeys.CurrentState.STATE_BUFFERING_PLAYING -> {
                mPbBottomProgress.progress = 0
                mPbBottomProgress.secondaryProgress = 0
                mSeekBar.progress = 0
                mSeekBar.secondaryProgress = 0

                mIvStartPlay.visibility = GONE
                mIvReplay.visibility = VISIBLE
                thumb.visibility = VISIBLE
            }

            ConstantKeys.CurrentState.STATE_IDLE -> {
                mPbBottomProgress.progress = 0
                mPbBottomProgress.secondaryProgress = 0
                mSeekBar.progress = 0
                mSeekBar.secondaryProgress = 0

                mIvStartPlay.visibility = VISIBLE
                mIvReplay.visibility = GONE
                thumb.visibility = VISIBLE
            }

            ConstantKeys.CurrentState.STATE_START_ABORT,
            ConstantKeys.CurrentState.STATE_PREPARED,
            ConstantKeys.CurrentState.STATE_ERROR,
            ConstantKeys.CurrentState.STATE_ONCE_LIVE -> {
                mIvStartPlay.visibility = VISIBLE
                mIvReplay.visibility = GONE
                thumb.visibility = VISIBLE
            }

            ConstantKeys.CurrentState.STATE_PLAYING -> {
                if (mIsShowBottomProgress) {
                    if (mControlWrapper.isShowing()) {
                        mPbBottomProgress.visibility = GONE
                        mLlBottomContainer.visibility = VISIBLE
                    } else {
                        mLlBottomContainer.visibility = GONE
                        mPbBottomProgress.visibility = VISIBLE
                    }
                } else {
//                    mLlBottomContainer.visibility = GONE
                }
                visibility = VISIBLE
                //开始刷新进度
                mControlWrapper.startProgress()

                mIvStartPlay.visibility = GONE
                mIvReplay.visibility = GONE
                thumb.visibility = GONE
            }

            ConstantKeys.CurrentState.STATE_PAUSED -> {
                mIvStartPlay.visibility = VISIBLE
                mIvReplay.visibility = GONE
                thumb.visibility = GONE
            }

            ConstantKeys.CurrentState.STATE_BUFFERING_PAUSED -> {
            }
        }
    }

    override fun onPlayerStateChanged(playerState: Int) {
        val activity = PlayerUtils.scanForActivity(mContext)
        if (activity != null && mControlWrapper.hasCutout()) {
            val orientation = activity.requestedOrientation
            val cutoutHeight = mControlWrapper.getCutoutHeight()
            if (orientation == ActivityInfo.SCREEN_ORIENTATION_PORTRAIT) {
                mLlBottomContainer.setPadding(0, 0, 0, 0)
                mPbBottomProgress.setPadding(0, 0, 0, 0)
            } else if (orientation == ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE) {
                mLlBottomContainer.setPadding(cutoutHeight, 0, 0, 0)
                mPbBottomProgress.setPadding(cutoutHeight, 0, 0, 0)
            } else if (orientation == ActivityInfo.SCREEN_ORIENTATION_REVERSE_LANDSCAPE) {
                mLlBottomContainer.setPadding(0, 0, cutoutHeight, 0)
                mPbBottomProgress.setPadding(0, 0, cutoutHeight, 0)
            }
        }
    }


    override fun onLockStateChanged(isLocked: Boolean) {
        onVisibilityChanged(!isLocked, null)
    }

    //region 进度相关
    /**
     * 刷新进度回调，子类可在此方法监听进度刷新，然后更新ui
     *
     * @param duration 视频总时长
     * @param position 视频当前时长
     */
    override fun setProgress(duration: Int, position: Int) {
        if (mIsDragging) {
            return
        }
        if (::mSeekBar.isInitialized) {
            if (duration > 0) {
                mSeekBar.isEnabled = true
                val pos = (position * 1.0 / duration * mSeekBar.max).toInt()
                mSeekBar.progress = pos
                mPbBottomProgress.progress = pos
            } else {
                mSeekBar.isEnabled = false
            }
            val percent = mControlWrapper.getBufferedPercentage()
            if (percent >= 95) {
                //解决缓冲进度不能100%问题
                mSeekBar.secondaryProgress = mSeekBar.max
                mPbBottomProgress.secondaryProgress = mPbBottomProgress.max
            } else {
                mSeekBar.secondaryProgress = percent * 10
                mPbBottomProgress.secondaryProgress = percent * 10
            }
        }

        mTvCurrTime.text = "${PlayerUtils.formatTime(position.toLong())}/${PlayerUtils.formatTime(duration.toLong())}"

        val config = getConfig()
        if (config.mIsShowToast) {
            var time = config.mShowToastTime
            if (time <= 0) {
                time = 5
            }
            val currentPosition = mControlWrapper.getCurrentPosition()
            if (duration - currentPosition < 2 * time * 1000) {
                //当前视频播放到最后3s时，弹出toast提示：即将自动为您播放下一个视频。
                if ((duration - currentPosition) / 1000 % 60 == time) {
                    Log.d("progress---", "即将自动为您播放下一个视频")
                    listener?.showToastOrDialog()
                }
            }
        }
    }

    override fun onStartTrackingTouch(seekBar: SeekBar) {
        mIsDragging = true
        mControlWrapper.stopProgress()
        mControlWrapper.stopFadeOut()
    }

    override fun onStopTrackingTouch(seekBar: SeekBar) {
        val duration = mControlWrapper.getDuration()
        val newPosition = duration * seekBar.progress / mPbBottomProgress.max
        mControlWrapper.seekTo(newPosition.toInt().toLong())
        mIsDragging = false
        mControlWrapper.startProgress()
        mControlWrapper.startFadeOut()
    }

    override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
        if (!fromUser) {
            return
        }
        val duration = mControlWrapper.getDuration()
        val newPosition = duration * progress / mPbBottomProgress.max
        mTvCurrTime.text = "${PlayerUtils.formatTime(newPosition)}/${PlayerUtils.formatTime(duration)}"
    }

    private var listener: OnToastListener? = null
    fun setListener(listener: OnToastListener?) {
        this.listener = listener
    }

    interface OnToastListener {
        fun showToastOrDialog()
    }
    //endregion
}