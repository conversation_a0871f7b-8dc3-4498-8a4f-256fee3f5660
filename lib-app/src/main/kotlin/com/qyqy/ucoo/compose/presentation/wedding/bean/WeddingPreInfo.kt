package com.qyqy.ucoo.compose.presentation.wedding.bean


import com.qyqy.ucoo.account.AppUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class WeddingPreInfo(
    @SerialName("room_type")
    val roomType: Int = 0,
    @SerialName("room_type_desc")
    val roomTypeDesc: String = "",
    @SerialName("btn_desc")
    val btnDesc: String = "",
    @SerialName("wedding_info")
    val weddingInfo: WeddingInfo = WeddingInfo()
) {
    val previewTime = "${weddingInfo.bookingDate} ${weddingInfo.startTime}-${weddingInfo.endTime}"

    @Serializable
    data class WeddingInfo(
        @SerialName("audioroom")
        val audioroom: Audioroom = Audioroom(),
        @SerialName("booking_date")
        val bookingDate: String = "",
        @SerialName("end_time")
        val endTime: String = "",
        @SerialName("light_user")
        val lightUser: AppUser = AppUser(),
        @SerialName("ring")
        val ring: Ring = Ring(),
        @SerialName("room_type")
        val roomType: Int = 0,
        @SerialName("start_time")
        val startTime: String = "",
        @SerialName("target_user")
        val targetUser: AppUser = AppUser(),
        val status: Int = 1
    ) {
        companion object {
            const val ONGOING = 3
        }
//        (1, "BOOKED", "已预约"),
//        (2, "CANCEL", "取消"),
//        (3, "ONGOING", "进行中"),
//        (4, "FINISHED", "结束"),
    }
}