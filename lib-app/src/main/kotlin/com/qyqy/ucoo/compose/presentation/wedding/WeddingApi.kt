package com.qyqy.ucoo.compose.presentation.wedding

import com.qyqy.ucoo.compose.presentation.wedding.bean.BookingResult
import com.qyqy.ucoo.compose.presentation.wedding.bean.BookingTimeEntity
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingConf
import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface WeddingApi {


    @GET("api/audioroom/v1/wedding/booking/conf")
    suspend fun getConf(): ApiResponse<WeddingConf>

    @GET("api/audioroom/v1/wedding/wedding/history")
    suspend fun getWeddingWallData(@Query("only_me") onlyMe: Boolean, @Query("last_id") lastId: Int = 0): ApiResponse<JsonObject>

    /**
     * 婚礼预告
     */
    @GET("api/audioroom/v1/wedding/wedding/list")
    suspend fun getWeddingPreviewData(@Query("only_me") onlyMe: Boolean): ApiResponse<JsonObject>


    /**
     * booking_timeranges:[BookingTimeEntity]
     */
    @GET("api/audioroom/v1/wedding/booking/timerange")
    suspend fun getTimeRange(@Query("room_id") roomId: Int): ApiResponse<JsonObject>

    /**
     * #获取预约婚礼的人
     * ```
     * {
     *     "users": [
     *         {
     *             "user": {    // 基础用户信息
     *             }
     *             "ring_light_id": 1     // 戒指点亮记录 id
     *         }
     *     ]
     * }
     * ```
     */
    @GET("api/audioroom/v1/wedding/booking/users")
    suspend fun getBookingUsers(@Query("room_id") roomId: Int): ApiResponse<JsonObject>


    /**
     * # 提交预约
     *
     * | 字段名         | 类型  | 描述                                      | 数据来源                                      |
     * |-----------------|-------|-------------------------------------------|-----------------------------------------------|
     * | `room_id`      | int   | 房间 ID                                   | 婚礼预约配置中的 `rooms[i].audioroom.id`      |
     * | `date`         | date  | 预约日期，格式为 `YYYY-MM-DD`             | 婚礼预约时间中的 `booking_timeranges[i].booking_date` |
     * | `range_type`   | int   | 时间范围类型                              | 婚礼预约时间中的 `booking_timeranges[i].range_type` |
     * | `ring_light_id`| int   | 预约婚礼的人的环形灯 ID                   | 预约婚礼的人中的 `users[i].ring_light_id`     |
     *
     */
    @POST("api/audioroom/v1/wedding/booking/create")
    suspend fun bookingWedding(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<BookingResult>

    /**
     * 获取戒指权益
     */
    @GET("api/audioroom/v1/wedding/detail")
    suspend fun getWeddingRingBenefits(@Query("room_type") weddingType: Int): ApiResponse<RingBenefits>

    /**
     * 购买戒指
     */
    @POST("api/audioroom/v1/wedding/ring/buy")
    suspend fun buyWeddingRing(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     * 获取预约信息
     */
    @GET("api/audioroom/v1/wedding/booking/info")
    suspend fun getBookingInfo(@Query("ring_light_id") ringLightId: Int): ApiResponse<JsonObject>
}
