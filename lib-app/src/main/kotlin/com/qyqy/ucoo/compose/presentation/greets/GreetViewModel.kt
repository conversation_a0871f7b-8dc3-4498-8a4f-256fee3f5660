package com.qyqy.ucoo.compose.presentation.greets

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.core.oss.Uploader
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toast
import io.github.album.MediaData
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

class GreetViewModel : ViewModel() {
    private val api = createApi<GreetingsApi>()

    private val flowGreetings = MutableStateFlow(Greetings())
    val greetingsFlow = flowGreetings.asStateFlow()

    private val _loading = MutableStateFlow(false)
    val loading = _loading.asStateFlow()

    private val _refresh = MutableStateFlow(false)
    val refresh = _refresh.asStateFlow()

    init {
        viewModelScope.launch {
            flowGreetItemAdd.collectLatest {
                onItemAdded(it)
            }
        }
        viewModelScope.launch {
            runWithLoading(refresh = true) {
                runApiCatching { api.getGreetings() }
                    .onSuccess {
                        flowGreetings.emit(it)
                    }.toastError()
            }
        }
    }


    private suspend fun runWithLoading(refresh: Boolean = false, block: suspend () -> Unit) {
        if (refresh) {
            _refresh.emit(true)
            block.invoke()
            _refresh.emit(false)
        } else {
            _loading.emit(true)
            block.invoke()
            _loading.emit(false)
        }
    }

    fun deleteItem(item: GreetItem) {
        viewModelScope.launch {
            runWithLoading {
                runApiCatching { api.deleteGreeting(item.deleteParams) }
                    .onSuccess {
                        retainGreetType {
                            buildList {
                                for (tab in it) {
                                    if (tab.contentType != item.contentType) {
                                        add(tab)
                                    } else {
                                        add(tab.copy(contentList = tab.contentList.filter { it.id != item.id }))
                                    }
                                }
                            }
                        }
                    }.toastError()
            }
        }
    }

    private fun retainGreetType(transform: (List<Greetings.Tab>) -> List<Greetings.Tab>) {
        val value = greetingsFlow.value
        val greetings = value.copy(tabs = transform(value.tabs))
        flowGreetings.value = greetings
    }

    fun addTextItem(text: String) {
        viewModelScope.launch {
            runWithLoading {
                addItem(GreetItem.TYPE_TEXT, text)
            }
        }
    }

    private suspend fun addItem(contentType: Int, content: String) {
        runApiCatching {
            api.addGreeting(mapOf("content_type" to contentType.toString(), "content" to content))
        }.onSuccess { item ->
            onItemAdded(item)
        }.toastError()
    }

    private fun onItemAdded(item: Greetings.Tab.Content) {
        retainGreetType { tabs ->
            buildList {
                for (tab in tabs) {
                    if (tab.contentType != item.contentType) {
                        add(tab)
                    } else {
                        val contentList = mutableListOf<Greetings.Tab.Content>()
                        contentList.add(item)
                        contentList.addAll(tab.contentList)
                        add(tab.copy(contentList = contentList))
                    }
                }
            }
        }
    }


    fun addImageItem(imageSelector: suspend () -> List<MediaData>) {
        viewModelScope.launch {
            val list = imageSelector.invoke()
            val m = list.firstOrNull() ?: run {
                toast("image select failed")
                return@launch
            }
            runWithLoading {
                val ret = Uploader.uploadMediaData(m, "greetings")
                ret?.takeIf { it.mediaUrl.isNullOrBlank().not() }?.let {
                    addItem(GreetItem.TYPE_IMAGE, it.mediaUrl!!)
                }
            }
        }
    }

    companion object {
        private val flowGreetItemAdd = MutableSharedFlow<Greetings.Tab.Content>(extraBufferCapacity = 2)
        fun onItemAdded(item: Greetings.Tab.Content) {
            flowGreetItemAdd.tryEmit(item)
        }
    }
}