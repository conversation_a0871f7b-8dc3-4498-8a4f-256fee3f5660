package com.qyqy.ucoo.compose.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import com.qyqy.ucoo.account.AppUser

/**
{
"id": 3,
"user": {
"userid": 1343,
"public_id": "101795",
"nickname": "胡佳奇",
"avatar_url": "https://media.ucoofun.com/default_male_avatar.png?x-oss-process=image/format,webp",
"gender": 2,
"age": 28,
"height": 0,
"avatar_frame": "",
"medal": null,
"medal_list": [],
"level": 0,
"country_flag": "https://media.ucoofun.com/opsite%2Fcountryflag%2FL_slices%2FCN.png",
"exp_level_info": {
"charm_level": 0,
"wealth_level": 0
}
},
"online_time": "在线",
"recommend_action": "查看了你的个人主页",
"recommend_time": "16小时前",
"is_unread": false
}
 */
@Keep
@Serializable
data class NewbieRecommendUserItem(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("is_unread")
    val isUnread: Boolean = false,
    @SerialName("online_time")
    val onlineTime: String = "",
    @SerialName("recommend_action")
    val recommendAction: String = "",
    @SerialName("recommend_time")
    val recommendTime: String = "",
    @SerialName("user")
    val user: AppUser = AppUser()
)