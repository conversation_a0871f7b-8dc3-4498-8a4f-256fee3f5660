package com.qyqy.ucoo.compose.presentation.ff

import com.qyqy.ucoo.http.ApiResponse
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface FamilyFriendApi {

    @GET("api/privatechat/v1/privatechat/user/list")
    suspend fun getLatestChatUserList(@Query("last_chat_id") lastChatId: Int): ApiResponse<JsonObject>

    @GET("api/friendship/v1/friends/list")
    suspend fun getFriendsList(@Query("last_relation_id") lastId: Int): ApiResponse<JsonObject>

    @POST("api/relationship/v1/relative/invite")
    suspend fun inviteUser(@Body map: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/relationship/v1/relative/accept")
    suspend fun acceptInvite(@Body map: Map<String, String>): ApiResponse<JsonObject>

    @POST("api/relationship/v1/relative/giveup")
    suspend fun giveupRelationship(@Body map: Map<String, String>): ApiResponse<JsonObject>

    @GET("api/relationship/v1/relative/list")
    suspend fun getFamilyList(@Query("last_relation_id") id: Int): ApiResponse<JsonObject>
}