package com.qyqy.ucoo.compose.vm.login

import android.os.SystemClock
import android.view.ViewGroup.LayoutParams
import com.qyqy.ucoo.AppUserPartition
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.res.stringResource
import androidx.credentials.CustomCredential
import androidx.credentials.GetCredentialResponse
import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.saveable
import com.adjust.sdk.Adjust
import com.adjust.sdk.AdjustEvent
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.login.LoginManager
import com.facebook.login.LoginResult
import com.google.android.libraries.identity.googleid.GoogleIdTokenCredential
import com.google.android.libraries.identity.googleid.GoogleIdTokenParsingException
import com.linecorp.linesdk.auth.LineLoginResult
import com.qyqy.cupid.ui.dialog.AccountBindContent
import com.qyqy.cupid.ui.dialog.DialogUIStyle
import com.qyqy.cupid.ui.dialog.EnsureContent
import com.qyqy.ucoo.ABTest
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.AppInfo
import com.qyqy.ucoo.AppType
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AccountBindInfo
import com.qyqy.ucoo.account.AccountToken
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appActivityTaskScheduler
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.compose.data.PhoneInfo
import com.qyqy.ucoo.compose.data.RegisterInfo
import com.qyqy.ucoo.compose.data.SelectItem
import com.qyqy.ucoo.compose.map
import com.qyqy.ucoo.compose.presentation.MainNavigator
import com.qyqy.ucoo.compose.presentation.login.CupidLoginDestination
import com.qyqy.ucoo.compose.presentation.login.LoginActivity
import com.qyqy.ucoo.compose.presentation.login.LoginDestination
import com.qyqy.ucoo.compose.ui.SimpleComposeDialog
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.isPlayChannel
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.login.LoginChannel
import com.qyqy.ucoo.login.LoginChannelType
import com.qyqy.ucoo.login.LoginRepository
import com.qyqy.ucoo.login.LoginRequest
import com.qyqy.ucoo.sAccountTokenFlow
import com.qyqy.ucoo.sAppKV
import com.qyqy.ucoo.sIsSignIn
import com.qyqy.ucoo.sUserKV
import com.qyqy.ucoo.setting.LoginConfigs
import com.qyqy.ucoo.setting.SettingsRepository
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.utils.NetworkManager
import com.qyqy.ucoo.utils.ShushuUtils
import com.qyqy.ucoo.utils.taskflow.task.JoinVoiceRoomTask
import com.qyqy.ucoo.utils.taskflow.task.JoinVoiceRoomTaskForSayHi
import com.qyqy.ucoo.utils.taskflow.task.VirtualFeatureGuideTask
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import kotlin.math.roundToInt

class LoginViewModel(savedStateHandle: SavedStateHandle) : ViewModel() {

    companion object {
        private const val RESEND_INTERVAL_MS = 60000
    }

    private val loginRepository = LoginRepository()

    private val _effectFlow = MutableSharedFlow<LoginViewEffect>()

    ///////////////////////////////////////////////////////////////////////////
    // facebook
    ///////////////////////////////////////////////////////////////////////////

    private var registeredForFB = false

    private val facebookCallback by lazy(LazyThreadSafetyMode.NONE) {
        object : FacebookCallback<LoginResult> {
            override fun onSuccess(result: LoginResult) {
                login(LoginRequest.Auth(LoginChannel.FACEBOOK2, result.accessToken.token))
            }

            override fun onCancel() {
                Analytics.appReportEvent(
                    DataPoint.eventBody(
                        DataTrace.Event.Login.Error, ABTest.Device.LoginUI,
                        extra = "facebook signIn error: canceled"
                    )
                )
                viewModelScope.launch {
                    _effectFlow.emit(
                        LoginViewEffect.Toast(
                            if (AppUserPartition.isUCOO) {
                                app.getString(R.string.登录已取消)
                            } else {
                                app.getString(R.string.cpd登录已取消)
                            }
                        )
                    )
                }
            }

            override fun onError(error: FacebookException) {
                Analytics.appReportEvent(
                    DataPoint.eventBody(
                        DataTrace.Event.Login.Error, ABTest.Device.LoginUI,
                        extra = "facebook signIn error: ${error.message}"
                    )
                )
                viewModelScope.launch {
                    _effectFlow.emit(LoginViewEffect.Toast(error.message.orEmpty()))
                }
            }
        }
    }

    private val faceBookCallbackManager by lazy(LazyThreadSafetyMode.NONE) {
        CallbackManager.Factory.create().also {
            LoginManager.getInstance().registerCallback(it, facebookCallback)
            registeredForFB = true
        }
    }

    ///////////////////////////////////////////////////////////////////////////
    // facebook
    ///////////////////////////////////////////////////////////////////////////

    ///////////////////////////////////////////////////////////////////////////
    // google
    ///////////////////////////////////////////////////////////////////////////

    fun handleGoogleSignInResult(result: GetCredentialResponse) {
        // Handle the successfully returned credential.
        when (val credential = result.credential) {
            is CustomCredential -> {
                if (credential.type == GoogleIdTokenCredential.TYPE_GOOGLE_ID_TOKEN_CREDENTIAL) {
                    try {
                        val googleIdTokenCredential = GoogleIdTokenCredential.createFrom(credential.data)
                        val idToken = googleIdTokenCredential.idToken
                        if (idToken.isEmpty()) {
                            Analytics.appReportEvent(
                                DataPoint.eventBody(
                                    DataTrace.Event.Login.Error, ABTest.Device.LoginUI,
                                    extra = "google signIn error: idToken is null"
                                )
                            )
                            viewModelScope.launch {
                                _effectFlow.emit(
                                    LoginViewEffect.Toast(
                                        if (AppUserPartition.isUCOO) {
                                            app.getString(R.string.出错啦_请重新登录)
                                        } else {
                                            app.getString(R.string.cpd出错啦_请重新登录)
                                        }
                                    )
                                )
                            }
                        } else {
                            login(LoginRequest.Auth(LoginChannel.GOOGLE, idToken))
                        }
                    } catch (e: GoogleIdTokenParsingException) {
                        val msg = "google signIn error: ${e.message}"
                        Analytics.appReportEvent(
                            DataPoint.eventBody(
                                DataTrace.Event.Login.Error,
                                ABTest.Device.LoginUI,
                                extra = msg
                            )
                        )
                        viewModelScope.launch {
                            _effectFlow.emit(LoginViewEffect.Toast(msg))
                        }
                    }
                } else {
                    val msg = "google signIn error, unrecognized custom credential type: ${credential.type}"
                    Analytics.appReportEvent(DataPoint.eventBody(DataTrace.Event.Login.Error, ABTest.Device.LoginUI, extra = msg))
                    viewModelScope.launch {
                        _effectFlow.emit(LoginViewEffect.Toast(msg))
                    }
                }
            }

            else -> {
                val msg =
                    "google signIn error, unrecognized credential type(${credential.javaClass.simpleName}): ${credential.type}"
                Analytics.appReportEvent(DataPoint.eventBody(DataTrace.Event.Login.Error, ABTest.Device.LoginUI, extra = msg))
                viewModelScope.launch {
                    _effectFlow.emit(LoginViewEffect.Toast(msg))
                }
            }
        }
    }

    ///////////////////////////////////////////////////////////////////////////
    // google
    ///////////////////////////////////////////////////////////////////////////

    //region LINE

    fun handleLineSignInResult(result: LineLoginResult) {
        when (result.responseCode) {
            com.linecorp.linesdk.LineApiResponseCode.SUCCESS -> {
                val profile = result.lineProfile
                val credential = result.lineCredential
                val token = credential!!.accessToken.tokenString
//                transitionIntent.putExtra("display_name", result.getLineProfile().getDisplayName());
//                transitionIntent.putExtra("status_message", result.getLineProfile().getStatusMessage());
//                transitionIntent.putExtra("user_id", result.getLineProfile().getUserId());
//                transitionIntent.putExtra("picture_url", result.getLineProfile().getPictureUrl().toString());
                login(LoginRequest.Auth(LoginChannel.LINE, token))
            }

            com.linecorp.linesdk.LineApiResponseCode.CANCEL -> {
                if (AppUserPartition.isUCOO) {
                    toastRes(R.string.登录已取消)
                } else {
                    toastRes(R.string.cpd登录已取消)
                }
                Analytics.appReportEvent(
                    DataPoint.eventBody(
                        DataTrace.Event.Login.Error, ABTest.Device.LoginUI,
                        extra = "line signIn error: canceled"
                    )
                )
            }

            else -> {
                Analytics.appReportEvent(
                    DataPoint.eventBody(
                        DataTrace.Event.Login.Error, ABTest.Device.LoginUI,
                        extra = "line signIn error: ${result.responseCode}"
                    )
                )
            }
        }
    }

    //endregion

    var showLoading: Boolean by savedStateHandle.saveable {
        mutableStateOf(false)
    }

    var phoneInfoState: PhoneInfo by savedStateHandle.saveable {
        mutableStateOf(PhoneInfo())
    }
        private set

    var registerInfoState: RegisterInfo by savedStateHandle.saveable {
        mutableStateOf(RegisterInfo())
    }
        private set

    val configFlow = SettingsRepository.loginSettingsFlow.map(viewModelScope) {
        val configs = it.getOrNull()
        configs.apply {
            if (this != null) {
                sAppKV.putInt("guess_native_region", guess_native_region)
            }
        } ?: LoginConfigs(
            hasFacebookLogin = BuildConfig.APPLICATION_ID != AppType.CUPID.applicationID,
            hasFastLogin = BuildConfig.APPLICATION_ID != AppType.CUPID.applicationID,
            hasGoogleLogin = true,
            hasLineLogin = BuildConfig.APPLICATION_ID == AppType.CUPID.applicationID,
            hasPhoneLogin = false,
            userFromChina = false,
            slogan = "",
            guess_native_region = sAppKV.getInt("guess_native_region", -1)
        )
    }

    val effectFlow = _effectFlow.asSharedFlow()

    init {
        viewModelScope.launch {
            SettingsRepository.fetchLoginSettings()
        }
    }

    override fun onCleared() {
        super.onCleared()
        if (registeredForFB) {
            LoginManager.getInstance().unregisterCallback(faceBookCallbackManager)
        }
    }

    suspend fun randomNickname(gender: Int) = loginRepository.randomNickname(gender).getOrNull().orEmpty()

    fun startLogin(loginActivity: LoginActivity, @LoginChannelType channel: Int) {
        viewModelScope.launch {
            if (!isProd && channel == LoginChannel.ONETAP) {
                val mobile = sAppKV.getString("onekey")
                if (mobile.length == 11) {
                    login(LoginRequest.Phone("86", mobile, "123456", false))
                    return@launch
                }
            }

            if (channel == LoginChannel.PHONE || configFlow.value.userFromChina) {
                phoneInfoState = PhoneInfo()
                _effectFlow.emit(
                    LoginViewEffect.Navigation(
//                    if(AppUserPartition.isCupid) CupidLoginDestination.PhoneInput else
                        LoginDestination.PhoneInput
                    )
                )
                return@launch
            }

            when (channel) {
                LoginChannel.ONETAP -> {
                    Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.QUICK, ABTest.Device.LoginUI))
                    login(LoginRequest.Auth(LoginChannel.ONETAP, AppInfo.deviceId))
                }

                LoginChannel.GOOGLE -> {
                    Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.GOOGLE, ABTest.Device.LoginUI))
                    loginActivity.googleSignIn()
                }

                LoginChannel.LINE -> {
                    loginActivity.lineSignIn()
                }

                else -> {
                    Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.FACEBOOK, ABTest.Device.LoginUI))
                    LoginManager.getInstance().logOut()
                    LoginManager.getInstance().logIn(loginActivity, faceBookCallbackManager, listOf("public_profile"))
                }
            }
        }
    }

    fun sendPhoneVerifyCode(phoneNumber: String) {
        Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.PHONE_INPUT_NEXT, ABTest.Device.LoginUI))
        viewModelScope.launch {
            showLoading = true
            loginRepository.sendPhoneVerifyCode(phoneInfoState.areaCode, phoneNumber)
                .onSuccess {
                    phoneInfoState = phoneInfoState.copy(
                        number = phoneNumber,
                        lastSendCodeTimestamp = SystemClock.elapsedRealtime(),
                        displayLeftSeconds = 60,
                    )
                    _effectFlow.emit(
                        LoginViewEffect.Navigation(
//                        if(AppUserPartition.isCupid) CupidLoginDestination.PhoneVerify else
                            LoginDestination.PhoneVerify
                        )
                    )
                }.onFailure {
                    _effectFlow.emit(LoginViewEffect.Toast(it.message.orEmpty()))
                }
            showLoading = false
        }
    }

    fun resendPhoneVerifyCode() {
        viewModelScope.launch {
            phoneInfoState.takeIf {
                it.number.length >= 11
            }?.also {
                showLoading = true
                loginRepository.sendPhoneVerifyCode(it.areaCode, it.number)
                    .onSuccess {
                        phoneInfoState = phoneInfoState.copy(
                            lastSendCodeTimestamp = SystemClock.elapsedRealtime(),
                            displayLeftSeconds = 60,
                        )
                    }.onFailure { e ->
                        _effectFlow.emit(LoginViewEffect.Toast(e.message.orEmpty()))
                    }
                showLoading = false
            } ?: run { // 数据异常，重新输入手机号
                phoneInfoState = phoneInfoState.copy(number = "", lastSendCodeTimestamp = 0, displayLeftSeconds = 0)
                _effectFlow.emit(
                    LoginViewEffect.Navigation(
//                    if(AppUserPartition.isCupid) CupidLoginDestination.PhoneInput else
                        LoginDestination.PhoneInput
                    )
                )
            }
        }
    }

    suspend fun tryStartResendTimer() {
        while (true) {
            val leftMs = RESEND_INTERVAL_MS.minus(SystemClock.elapsedRealtime().minus(phoneInfoState.lastSendCodeTimestamp))
            if (leftMs < 60) {
                break
            }
            phoneInfoState = phoneInfoState.copy(displayLeftSeconds = leftMs.div(1000f).roundToInt())
            if (leftMs < 1000) {
                delay(leftMs)
                break
            }
            delay(1000)
        }
        phoneInfoState = phoneInfoState.copy(displayLeftSeconds = 0)
    }

    fun checkPhoneVerifyCode(code: String) {
        Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.PHONE_VERIFY_NEXT, ABTest.Device.LoginUI))
        phoneInfoState.apply {
            val request = LoginRequest.Phone(areaCode, number, code, isBindPhone)
            if (isBindPhone) {
                bindPhone(request)
            } else {
                login(request)
            }
        }
    }


    fun saveAccountInfo(location: SelectItem, life: SelectItem, inviteCode: String) {
        registerInfoState = registerInfoState.copy(location = location, life = life, inviteCode = inviteCode)
//        if (registerInfoState.isBoy) {
//            registerUser("")
//        } else {
//            Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.EDIT_STATUS_NEXT, ABTest.Device.LoginUI))
//            viewModelScope.launch {
//                _effectFlow.emit(LoginViewEffect.Navigation(LoginDestination.UserInfoAvatar))
//            }
//        }
    }

    //region 华语区  步骤为 填充基础信息 -> 填充头像 -> 填充标签
    //华语区填充消息
    fun stagingAccountInfo(name: String, isBoy: Boolean, birthday: String) {
        registerInfoState = registerInfoState.copy(name = name, isBoy = isBoy, birthday = birthday)
        if (registerInfoState.isBoy) {
            registerUser("")
        } else {
            Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.EDIT_NAME_NEXT, ABTest.Device.LoginUI))
            viewModelScope.launch {
                _effectFlow.emit(LoginViewEffect.Navigation(LoginDestination.UserInfoAvatar))
            }
        }
    }

    //华语区填充头像
    fun stagingAvatarInfo(avatarUrl: String, inviteCode: String) {
        Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.EDIT_AVATAR, ABTest.Device.LoginUI))
        registerInfoState = registerInfoState.copy(avatar = avatarUrl, inviteCode = inviteCode)

        viewModelScope.launch {
            if (inviteCode.isNotBlank()) {
                _effectFlow.emit(LoginViewEffect.Navigation(LoginDestination.UserAttraction))
            } else {
                registerUser(avatarUrl, inviteCode)
            }
        }
    }

    //华语区填充标签
    fun stagingAttractiveInfo(ids: List<Int>) {
        Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.EDIT_ATTRACTION, ABTest.Device.LoginUI))
        registerInfoState = registerInfoState.copy(tag_ids = ids)

        viewModelScope.launch {
            val body = registerInfoState.let {
                buildMap {
                    put("nickname", it.name)
                    put("gender", if (it.isBoy) 1 else 2)
                    put("birthday", it.birthday)
                    put("is_using_vpn", "${NetworkManager.isUsingVPN() || NetworkManager.isUsingProxy()}")
                    put("tag_ids", it.tag_ids.joinToString(","))
//                    if (!it.location.isNull) {
//                        put("region", it.location.value)
//                    }
//                    if (!it.life.isNull) {
//                        put("region_reason", it.life.value)
//                    }
                    if (it.cityCode.isNotBlank()) {
                        put("city_code", it.cityCode)
                    }
                    if (!it.isBoy) {
                        if (it.avatar.isNotEmpty()) {
                            put("avatar_url", it.avatar)
                        }
                    }
                    if (it.inviteCode.isNotEmpty()) {
                        put("invite_code", it.inviteCode)
                    }
                    val smId = AppInfo.smBoxId
                    if (!smId.isNullOrEmpty()) {
                        put("ism_device_id", smId)
                    }
                }
            }
            showLoading = true
            loginRepository.register(body).apply {
                onSuccess { ret ->
                    app.accountManager.accountTokenWithTemp?.copy(finishFillInfo = true)?.also {
                        if (ret.reportAdjustEvent) {
                            // 注册事件
                            Adjust.trackEvent(AdjustEvent("ei5x0w"))
                        }
                        val sayHi = it.native_region == 0 && (ret.roomId == null || ret.roomId == 0)

                        if (handleLoginSuccess(it, sayHi)) {
                            val guideInfo = ret.guideInfo
                            if (guideInfo != null) {
                                appCoroutineScope.launch {
                                    delay(1500)
                                    ActivityLifecycle.startTopActivity?.also { act ->
                                        MainNavigator.start(act, MainNavigator.NEW_USER_ON_BOARDING) { intent ->
                                            intent.putExtra(Const.KEY_DATA, guideInfo)
                                        }
                                    }
                                }
                            } else if (it.native_region == 0) {
                                if (ret.privateRoomId != null && ret.privateRoomId > 0 && ret.bestMatchUser != null) {
                                    appActivityTaskScheduler.addTask(JoinVoiceRoomTask(ret.privateRoomId, ret.bestMatchUser))
                                } else if (!sayHi) {
                                    appActivityTaskScheduler.addTask(JoinVoiceRoomTaskForSayHi(ret.roomId!!))
                                }
                                if (ret.hitVirtualLoverMatch) {
                                    appActivityTaskScheduler.addTask(VirtualFeatureGuideTask())
                                }
                            }
                        }
                    } ?: run {
                        _effectFlow.emit(
                            LoginViewEffect.Toast(
                                if (AppUserPartition.isUCOO) {
                                    app.getString(R.string.出错啦_请重新登录)
                                } else {
                                    app.getString(R.string.cpd出错啦_请重新登录)
                                }
                            )
                        )
                        _effectFlow.emit(LoginViewEffect.ReLogin)
                    }
                }
                onFailure {
                    handleError(it)
                }
            }
            showLoading = false
        }
    }

    //endregion

    //region 日区

    /**
     * 日区的注册
     *
     * @param name nickname
     * @param isBoy 性别
     * @param birthday 生日
     * @param selectCityCode 城市
     */
    fun stagingAccountInfo(name: String, isBoy: Boolean, birthday: String, selectCityCode: String) {
        if (selectCityCode.isEmpty()) {
            return
        }
        registerInfoState = registerInfoState.copy(name = name, isBoy = isBoy, birthday = birthday, cityCode = selectCityCode)
//        if (registerInfoState.isBoy) {
//            registerUser("")
//        } else {
        Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.EDIT_NAME_NEXT, ABTest.Device.LoginUI))
        viewModelScope.launch {
            _effectFlow.emit(LoginViewEffect.Navigation(CupidLoginDestination.UserInfoAvatar))
        }
//        }
    }

    fun registerUser(avatarUrl: String, inviteCode: String = "") {
        Analytics.appReportEvent(DataPoint.clickBody(DataTrace.Click.Login.ENTER_APP, ABTest.Device.LoginUI))
        registerInfoState = registerInfoState.copy(avatar = avatarUrl, inviteCode = inviteCode)
        viewModelScope.launch {
            val body = registerInfoState.let {
                buildMap {
                    put("nickname", it.name)
                    put("gender", if (it.isBoy) 1 else 2)
                    put("birthday", it.birthday)
                    put("is_using_vpn", "${NetworkManager.isUsingVPN() || NetworkManager.isUsingProxy()}")
//                    if (!it.location.isNull) {
//                        put("region", it.location.value)
//                    }
//                    if (!it.life.isNull) {
//                        put("region_reason", it.life.value)
//                    }
                    if (it.cityCode.isNotBlank()) {
                        put("city_code", it.cityCode)
                    }
                    if (!it.isBoy) {
                        if (it.avatar.isNotEmpty()) {
                            put("avatar_url", it.avatar)
                        }
                    }
                    if (it.inviteCode.isNotEmpty()) {
                        put("invite_code", it.inviteCode)
                    }
                    val smId = AppInfo.smBoxId
                    if (!smId.isNullOrEmpty()) {
                        put("ism_device_id", smId)
                    }
                }
            }
            showLoading = true
            loginRepository.register(body).apply {
                onSuccess { ret ->
                    ShushuUtils.userSet(
                        "channel" to (if (isPlayChannel) "gg" else "official"),
                        "device_id" to AppInfo.deviceId,
                        once = true
                    )
                    app.accountManager.accountTokenWithTemp?.copy(finishFillInfo = true)?.also {
                        if (ret.reportAdjustEvent) {
                            // 注册事件
                            Adjust.trackEvent(AdjustEvent("ei5x0w"))
                        }
                        val sayHi = it.native_region == 0 && (ret.roomId == null || ret.roomId == 0)

                        if (handleLoginSuccess(it, sayHi)) {
                            val guideInfo = ret.guideInfo
                            if (guideInfo != null) {
                                appCoroutineScope.launch {
                                    //2.40 专属女友/男友匹配
                                    delay(1500)
                                    ActivityLifecycle.startTopActivity?.also { act ->
                                        MainNavigator.start(act, MainNavigator.NEW_USER_ON_BOARDING) { intent ->
                                            intent.putExtra(Const.KEY_DATA, guideInfo)
                                        }
                                    }
                                }
                            } else if (it.native_region == 0) {
                                if (ret.privateRoomId != null && ret.privateRoomId > 0 && ret.bestMatchUser != null) {
                                    appActivityTaskScheduler.addTask(JoinVoiceRoomTask(ret.privateRoomId, ret.bestMatchUser))
                                } else if (!sayHi) {
                                    appActivityTaskScheduler.addTask(JoinVoiceRoomTaskForSayHi(ret.roomId!!))
                                }
                                if (ret.hitVirtualLoverMatch) {
                                    appActivityTaskScheduler.addTask(VirtualFeatureGuideTask())
                                }
                            }
                        }
                    } ?: run {
                        _effectFlow.emit(
                            LoginViewEffect.Toast(
                                if (AppUserPartition.isUCOO) {
                                    app.getString(R.string.出错啦_请重新登录)
                                } else {
                                    app.getString(R.string.cpd出错啦_请重新登录)
                                }
                            )
                        )
                        _effectFlow.emit(LoginViewEffect.ReLogin)
                    }
                }
                onFailure {
                    handleError(it)
                }
            }
            showLoading = false
        }
    }
    //endregion


    private fun login(request: LoginRequest, isRecover: Int = 0) {
        viewModelScope.launch {
            showLoading = true
            loginRepository.login(request, isRecover)
                .onSuccess {
                    handleTokenResult(it)
                    // 获取最新系统配置
                    SettingsRepository.fetchSystemSettings()
                }.onFailure { ex ->
                    handleError(ex)
                    //注销中
                    if (ex is ApiException) {
                        if (ex.code == -21) {
                            showComposeDialog { d ->
                                if (AppUserPartition.isCupid) {
                                    EnsureContent(
                                        title = stringResource(id = R.string.cpd_warn_tips),
                                        content = stringResource(id = R.string.cpd_login_when_destroy),
                                        leftButtonText = stringResource(id = R.string.cpd_recover_account),
                                        rightButtonText = stringResource(id = R.string.cpd_cancel_login),
                                        onLeftClick = {
                                            login(request, 1)
                                            d.dismiss()
                                        },
                                        onRightClick = { d.dismiss() })
                                } else {
                                    EnsureContent(
                                        title = stringResource(id = R.string.warm_tips),
                                        content = stringResource(id = R.string.login_when_destroy),
                                        leftButtonText = stringResource(id = R.string.recover_account),
                                        rightButtonText = stringResource(id = R.string.cancel_login),
                                        onLeftClick = {
                                            login(request, 1)
                                            d.dismiss()
                                        },
                                        onRightClick = { d.dismiss() },
                                        style = DialogUIStyle.ucoo
                                    )
                                }
                            }
                        }
                    }
                }
            showLoading = false
        }
    }

//    private fun login(request: LoginRequest) {
//        viewModelScope.launch {
//            showLoading = true
//            loginRepository.login(request)
//                .onSuccess { tokenResult ->
//                    //1. 登录完成后判断是否为Cupid
////                    if (AppUserPartition.isCupid && request.channel == LoginChannel.ONETAP) {
////                        AccessTokenInterceptor.setTempToken(tokenResult.authToken)
//                    //fixed 2023-8-27 一键登录后不再需要绑定三方账户, 以下注释
//                    //1.1 如果是cupid 且 本次为一键登录, 则直接设置临时token, 再请求获取绑定信息
////                        loginRepository.getBindAccountInfo().onSuccess {
////                            showLoading = false
////                            //2.1 如果需要绑定第三方, 则显示绑定第三方弹窗
////                            if (it.needBindOutsideAccount) {
////                                //3 显示绑定第三方弹窗
////                                showCupidBindDialog(it) { channel, token ->
////                                    //4 第三方弹窗获取第三方的token后调用bindThridAccount接口,并且进入登录成功流程
////                                    viewModelScope.launch {
////                                        AccessTokenInterceptor.setTempToken(tokenResult.authToken)
////                                        loginRepository.bindThirdAccount(channel, token)
////                                        requestLoginSucceed(tokenResult)
////                                    }
////                                }
////                            } else {
////                                //2.2 不需要则直接登录
////                                requestLoginSucceed(tokenResult)
////                            }
////                        }.onFailure {
////                            //2.3 获取失败时直接登录, 如果需要绑定的话就在其他页面弹出弹窗
////                            requestLoginSucceed(tokenResult)
////                            showLoading = false
////                        }
////                    } else {
//                    //1.2 如果不是Cupid,则直接登录成功
//                    showLoading = false
//                    requestLoginSucceed(tokenResult)
////                    }
//                }.onFailure {
//                    handleError(it)
//                    showLoading = false
//                }
//        }
//    }
//
//    private suspend fun requestLoginSucceed(tokenResult: AccountToken) {
//        handleTokenResult(tokenResult)
//        // 获取最新系统配置
//        SettingsRepository.fetchSystemSettings()
//    }

    private fun showCupidBindDialog(info: AccountBindInfo, onSucceed: (channelType: Int, token: String) -> Unit) {
        ActivityLifecycle.startTopActivity?.let { act ->
            SimpleComposeDialog(act, LayoutParams.WRAP_CONTENT) { dialog ->
                dialog.setCanceledOnTouchOutside(false)
                dialog.setCancelable(false)
                AccountBindContent(info, onSucceed) {
                    dialog.dismiss()
                }
            }.show()
        }
    }

    private fun bindPhone(request: LoginRequest) {
        viewModelScope.launch {
            showLoading = true
            loginRepository.bindPhone(request)
                .onSuccess {
                    handleTokenResult(
                        if (it.userId.isEmpty()) {
                            it.copy(userId = app.accountManager.accountTokenWithTemp?.userId.orEmpty())
                        } else {
                            it
                        }
                    )
                }.onFailure {
                    handleError(it)
                }
            showLoading = false
        }
    }

    private suspend fun handleTokenResult(token: AccountToken) {
        if (token.needVerifyPhone) {
            app.accountManager.stageToken(token)
            phoneInfoState =
                phoneInfoState.copy(isBindPhone = true, number = "", lastSendCodeTimestamp = 0, displayLeftSeconds = 0)
            _effectFlow.emit(
                LoginViewEffect.Navigation(
//                if(AppUserPartition.isCupid) CupidLoginDestination.PhoneInput else
                    LoginDestination.PhoneInput
                )
            )
        } else if (token.finishFillInfo) {
            handleLoginSuccess(token = token, sayHi = token.native_region == 0)
        } else {
            app.accountManager.stageToken(token)
            val locationList: List<SelectItem>
            val selectedLocation: SelectItem
            if (token.region != null) {
                locationList = listOf(
                    SelectItem(token.region, token.regionLabel.orEmpty()),
                    SelectItem("-1", app.getString(R.string.selected_other)),
                )
                selectedLocation = locationList[0]
            } else {
                locationList = listOf(
                    SelectItem("TW", app.getString(R.string.taiwan)),
                    SelectItem("US", app.getString(R.string.america)),
                    SelectItem("MY", app.getString(R.string.malaysia)),
                    SelectItem("SG", app.getString(R.string.singapore)),
                    SelectItem("CN", app.getString(R.string.china)),
                    SelectItem("HK", app.getString(R.string.hong_kong)),
                    SelectItem("AU", app.getString(R.string.australia)),
                    SelectItem("CA", app.getString(R.string.canada)),
                    SelectItem("-1", app.getString(R.string.selected_other)),
                )
                selectedLocation = locationList[4]
            }
            registerInfoState = RegisterInfo(
                locationList = locationList,
                location = selectedLocation,
                lifeList = listOf(
                    SelectItem("1", app.getString(R.string.item_play_type_1)),
                    SelectItem("2", app.getString(R.string.item_play_type_2)),
                    SelectItem("3", app.getString(R.string.item_play_type_3)),
                    SelectItem("4", app.getString(R.string.item_play_type_4)),
                    SelectItem("5", app.getString(R.string.item_play_type_5)),
                )
            )
            _effectFlow.emit(LoginViewEffect.Navigation(if (AppUserPartition.isCupid) CupidLoginDestination.UserInfoBase else LoginDestination.UserInfoBase))
        }
    }

    private suspend fun handleError(e: Throwable) {
        Analytics.login(false, e.message)
        if (e is com.qyqy.ucoo.http.ApiException) {
            if (e.code == -1 || e.extra != null) {
                _effectFlow.emit(LoginViewEffect.DialogTip(e.msg, app.getString(R.string.i_known)))
                return
            }

            if (e.code == -23 && e.errorMsg.isNullOrEmpty().not()) {
                _effectFlow.emit(LoginViewEffect.DialogTip(e.msg, app.getString(R.string.i_known)))
                return
            }
        }
        _effectFlow.emit(LoginViewEffect.Toast(e.message.orEmpty()))
    }

    private suspend fun handleLoginSuccess(token: AccountToken, sayHi: Boolean): Boolean {
        Analytics.login(true)
        ShushuUtils.userAdd("total_login_count" to 1)
        if (sayHi) {
            appCoroutineScope.launch {
                delay(5000)
                if (sIsSignIn) {
                    UserManager.sayHi()
                }
            }
        }
        app.accountManager.signIn(token)
        ShushuUtils.init(app)
        return true
//        return if (IMManager.connect(token)) {
//
//            true
//        } else {
//            _effectFlow.emit(LoginViewEffect.Toast(app.getString(R.string.im连接失败_请重试)))
//            false
//        }
    }
}
