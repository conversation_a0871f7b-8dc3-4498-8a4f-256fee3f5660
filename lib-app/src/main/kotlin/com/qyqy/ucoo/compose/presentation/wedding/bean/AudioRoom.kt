package com.qyqy.ucoo.compose.presentation.wedding.bean

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Parcelize
@Serializable
data class Audioroom(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("public_id")
    val publicId: String = "",
    @SerialName("room_locked")
    val roomLocked: Boolean = false,
    @SerialName("text_only")
    val textOnly: Boolean = false,
    @SerialName("title")
    val title: String = ""
) : Parcelable