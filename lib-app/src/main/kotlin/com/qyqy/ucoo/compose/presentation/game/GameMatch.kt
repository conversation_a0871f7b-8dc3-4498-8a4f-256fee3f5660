package com.qyqy.ucoo.compose.presentation.game

import android.app.Activity
import android.content.Context
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.ext.safeDismiss
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.BaseBottomSheetDialogFragment
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.data.GameInvite
import com.qyqy.ucoo.compose.data.MatchGameInfo
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.utils.requireInstance
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

object GameMatchScreenNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val gameType = bundle.getInt(Const.KEY_TYPE, -1)
        val gameId = bundle.getInt(Const.KEY_ID, -1)
        GameMatchScreen(gameType, gameId)
    }

    fun navigate(context: Context, gameType: Int, gameId: Int) {
        navigate(context) {
            putExtra(Const.KEY_TYPE, gameType)
            putExtra(Const.KEY_ID, gameId)
        }
    }
}

@Composable
private fun GameMatchScreen(gameType: Int, gameId: Int) {
    val activity = LocalContext.current.requireInstance<Activity>()
    if (gameType == -1 || gameId == -1) {
        activity.finish()
        return
    }
    val coroutineScope = rememberCoroutineScope()

    val gameDiamondApi = remember {
        createApi<GameDiamondApi>()
    }

    var canceled by remember { // 是否取消匹配
        mutableStateOf(false)
    }

    var matchId by remember {
        mutableIntStateOf(-1)
    }

    var successed by remember { // 是否匹配成功
        mutableStateOf(false)
    }

    LaunchedEffect(key1 = Unit) {
        val result = withContext(NonCancellable) {
            runApiCatching {
                gameDiamondApi.startMatch(buildMap {
                    put("game_type", gameType.toString())
                    put("game_id", gameId.toString())
                })
            }.onSuccess {
                it.getIntOrNull("match_id")?.apply {
                    if (isActive) {
                        matchId = this
                    } else if (!successed) { // 页面已关闭直接取消
                        runApiCatching {
                            gameDiamondApi.cancelMatch(mapOf("match_id" to this.toString()))
                        }
                    }
                }
            }
        }

        ensureActive()

        result.onFailure {
            if (!successed) {
                activity.finish()
            }
        }.toastError()
    }

    if (!canceled) {
        DisposableEffect(key1 = Unit) {
            val callback = object : IMCompatListener {

                override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
                    if (message.cmd != MsgEventCmd.GAME_MATCH_DONE) {
                        return
                    }
                    val matchInfo = message.parseDataJson<MatchGameInfo>() ?: return
                    if (matchId == -1) {
                        Log.w("GameMatchScreen", "接口还没返回不科学")
                    } else if (matchInfo.matchId != -1 && matchInfo.matchId != matchId) {
                        return
                    }
                    successed = true
                    activity.finish()
                    AppLinkManager.open(activity, matchInfo.toGameRouterInfo().toLink())
                }
            }

            IMCompatCore.addIMListener(callback)
            onDispose {
                IMCompatCore.removeIMListener(callback)
            }
        }
    }

    if (!successed) {
        DisposableEffect(key1 = Unit) { // 页面关闭直接取消
            onDispose {
                if (successed) {
                    return@onDispose
                }
                canceled = true
                coroutineScope.launch(NonCancellable) {
                    if (matchId != -1) {
                        runApiCatching {
                            gameDiamondApi.cancelMatch(mapOf("match_id" to matchId.toString()))
                        }
                    }
                }
            }
        }
    }

    GameMatchPage {
        if (successed) {
            return@GameMatchPage
        }
        canceled = true
        activity.finish()
    }
}

@Composable
private fun GameMatchPage(onCancel: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painter = painterResource(id = R.drawable.background_game_match), contentScale = ContentScale.Crop
            ), horizontalAlignment = Alignment.CenterHorizontally
    ) {

        Spacer(modifier = Modifier.fillMaxHeight(0.66f))

        Text(
            text = stringResource(id = R.string.正在为你匹配游戏对手),
            modifier = Modifier.padding(horizontal = 40.dp),
            color = colorResource(id = R.color.white),
            fontSize = 16.sp,
        )

        Spacer(modifier = Modifier.height(24.dp))

        Box(
            modifier = Modifier
                .size(260.dp, 48.dp)
                .clip(CircleShape)
                .background(brush = Brush.horizontalGradient(listOf(Color(0xFF5B15F0), Color(0xFFD46AF9))))
                .clickable(onClick = onCancel)
        ) {
            AutoSizeText(
                text = stringResource(id = R.string.cancel_math),
                modifier = Modifier
                    .align(Alignment.Center)
                    .padding(horizontal = 6.dp),
                color = colorResource(id = R.color.white),
                fontSize = 16.sp,
                maxLines = 2,
                lineSpacingRatio = 0.24f,
                alignment = Alignment.Center
            )
        }
    }
}

@Preview
@Composable
private fun PreviewGameMatchPage() {
    GameMatchPage()
}

class GameInviteDialog : BaseBottomSheetDialogFragment() {

    companion object {
        fun newInstance(game: GameInvite): GameInviteDialog {
            val args = Bundle()
            args.putParcelable(Const.KEY_DATA, game)
            val fragment = GameInviteDialog()
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        return ComposeView(requireContext()).apply {
            layoutParams = ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            // Dispose of the Composition when the view's LifecycleOwner
            // is destroyed
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
            setContent {
                GameInviteScreen(arguments?.getParcelable(Const.KEY_DATA)) {
                    safeDismiss()
                }
            }
        }
    }
}

@Composable
private fun GameInviteScreen(data: GameInvite?, onDismiss: () -> Unit) {
    data ?: return onDismiss()

    val context = LocalContext.current

    val coroutineScope = rememberCoroutineScope()

    val gameDiamondApi = remember {
        createApi<GameDiamondApi>()
    }

    var requesting by remember {
        mutableStateOf(false)
    }

    var enable by remember { // 按钮是否可点击
        mutableStateOf(true)
    }

    var agreed by remember {
        mutableStateOf(false)
    }

    var successed by remember { // 是否匹配成功
        mutableStateOf(false)
    }

    LaunchedEffect(key1 = Unit) {
        delay(5000)
        onDismiss()
    }

    if (agreed) {
        DisposableEffect(key1 = Unit) {
            val callback = object : IMCompatListener {

                override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
                    if (message.cmd != MsgEventCmd.GAME_MATCH_DONE) {
                        return
                    }
                    val matchInfo = message.parseDataJson<MatchGameInfo>() ?: return
                    if (matchInfo.matchId != -1 && matchInfo.matchId != data.matchId) {
                        return
                    }
                    successed = true
                    onDismiss()
                    AppLinkManager.open(context, matchInfo.toGameRouterInfo().toLink())
                }
            }

            IMCompatCore.addIMListener(callback)
            onDispose {
                IMCompatCore.removeIMListener(callback)
            }
        }
    }

    if (!successed) {
        DisposableEffect(key1 = Unit) { // 页面关闭直接取消
            onDispose {
                if (successed) {
                    return@onDispose
                }
                agreed = false
                coroutineScope.launch(NonCancellable) {
                    runApiCatching {
                        gameDiamondApi.refuseInvite(mapOf("match_id" to data.matchId.toString()))
                    }
                }
            }
        }
    }

    GameInvitePage(data, onAgree = {
        if (requesting || !enable) {
            return@GameInvitePage
        }
        agreed = true
        requesting = true
        coroutineScope.launch {
            runApiCatching {
                gameDiamondApi.agreeInvite(mapOf("match_id" to data.matchId.toString()))
            }.onSuccess {
                enable = false
            }.toastError()
            requesting = false
        }
    }) {
        if (!enable) {
            return@GameInvitePage
        }
        agreed = false
        onDismiss()
    }
}


@Composable
private fun GameInvitePage(data: GameInvite, onAgree: () -> Unit = {}, onReject: () -> Unit = {}) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 16.dp)
            .background(
                brush = Brush.verticalGradient(listOf(Color(0xFF2D1D4F), Color(0xFF222222))),
                shape = RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
            )
            .navigationBarsPadding(),
    ) {
        Column(
            modifier = Modifier
                .offset(y = (-16).dp)
                .fillMaxWidth(), horizontalAlignment = Alignment.CenterHorizontally
        ) {
            CircleComposeImage(
                model = data.user.avatarUrl, modifier = Modifier
                    .size(88.dp)
                    .clip(CircleShape)
                    .border(1.5.dp, Color.White, CircleShape)
            )

            Text(
                text = data.title,
                modifier = Modifier.padding(horizontal = 40.dp, vertical = 24.dp),
                color = colorResource(id = R.color.white),
                fontSize = 14.sp,
            )

            ComposeImage(
                model = data.gameInfo?.icon.orEmpty(),
                modifier = Modifier
                    .size(166.dp, 128.dp)
                    .clip(Shapes.extraSmall),
            )

            Box(
                modifier = Modifier
                    .padding(top = 20.dp, bottom = 30.dp)
                    .size(260.dp, 48.dp)
                    .clip(CircleShape)
                    .background(brush = Brush.horizontalGradient(listOf(Color(0xFF5B15F0), Color(0xFFD46AF9))))
                    .clickable(onClick = onAgree)
            ) {
                AutoSizeText(
                    text = data.btnText.ifEmpty { stringResource(id = R.string.接受邀请) },
                    modifier = Modifier
                        .align(Alignment.Center)
                        .padding(horizontal = 6.dp),
                    color = colorResource(id = R.color.white),
                    fontSize = 16.sp,
                    maxLines = 2,
                    lineSpacingRatio = 0.24f,
                    alignment = Alignment.Center
                )
            }
        }

        Image(
            painter = painterResource(id = R.drawable.ic_close_gray),
            contentDescription = "close",
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(16.dp)
                .clickable(onClick = onReject),
            colorFilter = ColorFilter.tint(Color.White)
        )
    }
}

@Preview
@Composable
private fun PreviewGameInvitePage() {
    GameInvitePage(GameInvite(0, userForPreview, stringResource(id = R.string.邀请你一起玩xxx游戏, "极速赛车")))
}