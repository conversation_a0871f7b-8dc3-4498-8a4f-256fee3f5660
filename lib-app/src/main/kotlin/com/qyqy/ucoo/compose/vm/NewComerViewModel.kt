package com.qyqy.ucoo.compose.vm

import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.data.NewbieRecommendUserItem
import com.qyqy.ucoo.compose.presentation.greets.GreetingsApi
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

class NewComerViewModel : StateViewModelWithIntPage<NewbieRecommendUserItem>() {

    private val api = createApi<GreetingsApi>()

    init {
        refresh()
    }

    override suspend fun loadData(pageNum: Int): Result<List<NewbieRecommendUserItem>> {
        val lastId = if (pageNum == firstPage) 0 else listFlow.value.lastOrNull()?.id ?: 0
        val result = runApiCatching { api.getRecommendUsers(lastId) }
        return if (result.isSuccess) {
            val jsonObject = result.getOrThrow()
            val list = jsonObject.getOrNull("infos")?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<NewbieRecommendUserItem>>(it)
            } ?: emptyList()
            Result.success(list)
        } else {
            Result.failure<List<NewbieRecommendUserItem>>(result.exceptionOrNull() ?: ApiException(msg = "error")).also {
                toast(it.exceptionOrNull()?.message ?: "")
            }
        }
    }

    fun clearBadge(userId:Int){
        updateItem({value-> value.user.userId == userId}){
            it.copy(isUnread = false)
        }
    }
}