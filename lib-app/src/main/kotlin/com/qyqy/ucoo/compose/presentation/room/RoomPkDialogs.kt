package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Matrix
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.PathFillType
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.ui.ComposeImage
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive


// --- Dialog Composable ---

@Composable
fun PkInvitationDialog(
    userName: String,
    userImageUrl: String,
    onlineCount: Int,
    initialCountdownSeconds: Int,
    onAccept: (Boolean) -> Unit = {},
    onReject: () -> Unit = {},
) {
    var countdown by remember { mutableIntStateOf(initialCountdownSeconds) }

    LaunchedEffect(Unit) { // Runs once when the composable enters the composition
        while (countdown > 0) {
            delay(1000) // Wait for 1 second
            countdown--
        }
        if (countdown == 0) {
            onAccept(true) // Auto-accept when timer reaches 0
        }
    }

    Column(
        modifier = Modifier
            .width(270.dp)
            .heightIn(min = 336.dp)
            .paint(painterResource(R.drawable.bg_dialog_cross_room_pk), contentScale = ContentScale.FillBounds)
            .padding(top = 72.dp, bottom = 16.dp, start = 24.dp, end = 24.dp), // Extra top padding to make space below overlapping banner
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        // User Image Area
        Box(
            contentAlignment = Alignment.BottomCenter // Align overlay to bottom
        ) {
            // --- Image Placeholder ---
            Box(
                modifier = Modifier
                    .size(128.dp) // Adjust size as needed
                    .clip(MaterialTheme.shapes.medium) // Rounded corners for image
                    .background(Color.Gray), // Placeholder color
                contentAlignment = Alignment.Center
            ) {

                ComposeImage(model = userImageUrl, modifier = Modifier.size(128.dp))

                // Text Overlay on Image
                Column(
                    modifier = Modifier
                        .align(Alignment.BottomStart)
                        .fillMaxWidth()
                        .padding(8.dp),
                ) {
                    Text(
                        text = stringResource(R.string.format_people_online, onlineCount),
                        color = Color.White,
                        fontSize = 10.sp
                    )
                    Text(
                        text = userName,
                        color = Color.White,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
            // --- End Image Placeholder ---
        }

        Spacer(modifier = Modifier.height(16.dp))

        // Invitation Text
        Text(
            text = stringResource(R.string.received_pk_invitation),
            color = Color.White,
            lineHeight = 16.sp,
            textAlign = TextAlign.Center
        )

        Spacer(modifier = Modifier.height(16.dp))

        // Action Buttons
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(8.dp) // Space between buttons
        ) {
            // Accept Button
            Button(
                onClick = {
                    onAccept(false)
                },
                modifier = Modifier
                    .widthIn(min = 180.dp)
                    .height(32.dp),
                contentPadding = PaddingValues(horizontal = 16.dp),
                shape = MaterialTheme.shapes.extraLarge, // Pill shape
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFFFD39C),
                    contentColor = Color(0xFF4C2D08)
                )
            ) {
                Text(
                    // Display countdown only if > 0
                    text = if (countdown > 0) stringResource(R.string.accept_invitation_with_countdown, countdown) else stringResource(R.string.accept_invitation),
                    fontSize = 12.sp,
                )
            }

            // Reject Button
            Button(
                onClick = onReject,
                modifier = Modifier
                    .widthIn(min = 180.dp)
                    .height(32.dp),
                contentPadding = PaddingValues(horizontal = 16.dp),
                shape = MaterialTheme.shapes.extraLarge, // Pill shape
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFFEAEAEA),
                    contentColor = Color(0xFF6A6A6A)
                )
            ) {
                Text(
                    text = stringResource(R.string.reject),
                    fontSize = 12.sp,
                )
            }
        }
    }
}


// --- Preview ---

@Preview(showBackground = true, uiMode = android.content.res.Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun PkInvitationDialogPreview() {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(MaterialTheme.colorScheme.background.copy(alpha = 0.8f)), // Simulate dimming
        contentAlignment = Alignment.Center
    ) {
        PkInvitationDialog(
            userName = "用户名称",
            userImageUrl = "", // Use placeholder in preview
            onlineCount = 122,
            initialCountdownSeconds = 10,
        )
    }
}

@Composable
fun CrossRoomPkResultDialog(
    roomName: String,
    roomIcon: String,
    contributorList: List<String>?,
    countDownSeconds: Int = 0,
    backgroundResId: Int,
    topBackgroundResId: Int,
    tintColor: Color,
    onDismiss: () -> Unit = {}
) {
    var countdown by remember { mutableIntStateOf(countDownSeconds) }

    if (countDownSeconds > 0) {
        val currentOnDismiss by rememberUpdatedState(onDismiss)
        LaunchedEffect(countDownSeconds) {
            while (countdown > 0) {
                delay(1000)
                countdown--
            }
            currentOnDismiss()
        }
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Box(contentAlignment = Alignment.TopCenter) {
            Image(
                painter = painterResource(backgroundResId),
                contentDescription = null,
                modifier = Modifier
                    .padding(top = 48.dp)
                    .size(327.dp, 288.dp),
                contentScale = ContentScale.FillBounds
            )

            Box(
                modifier = Modifier.height(140.dp),
                contentAlignment = Alignment.TopCenter
            ) {
                Image(
                    painter = painterResource(topBackgroundResId),
                    contentDescription = null,
                    modifier = Modifier.fillMaxHeight(),
                    contentScale = ContentScale.FillHeight
                )
                ComposeImage(
                    model = roomIcon,
                    modifier = Modifier
                        .padding(top = 29.dp)
                        .size(64.dp)
                        .clip(CircleShape)
                )
            }

            Column(
                modifier = Modifier.padding(top = 156.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(text = roomName, color = Color.White, fontSize = 16.sp)
                Image(
                    painter = painterResource(R.drawable.ic_dialog_cross_room_pk_tag),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(top = 16.dp, bottom = 8.dp)
                        .height(18.dp),
                    contentScale = ContentScale.FillHeight
                )
                Row(
                    verticalAlignment = Alignment.Bottom,
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    ContributeUserItem(
                        isMvp = false,
                        avatar = contributorList?.getOrNull(1),
                        size = 48.dp,
                        number = "2",
                        tintColor = tintColor,
                        textColor = Color(0xFF5D5D5D),
                        borderColor = Color(0xFFECECEC),
                        colors = listOf(Color(0xFF989898), Color(0xFFE4E4E4), Color(0xFF989898))
                    )
                    ContributeUserItem(
                        isMvp = true,
                        avatar = contributorList?.getOrNull(0),
                        size = 56.dp,
                        number = "1",
                        tintColor = tintColor,
                        textColor = Color(0xFFA57225),
                        borderColor = Color(0xFFFFF2A6),
                        colors = listOf(Color(0xFFFFE75D), Color(0xFFFFF5BD), Color(0xFFFFE75D))
                    )
                    ContributeUserItem(
                        isMvp = false,
                        avatar = contributorList?.getOrNull(2),
                        size = 48.dp,
                        number = "3",
                        tintColor = tintColor,
                        textColor = Color(0xFF9A561C),
                        borderColor = Color(0xFFFFE1C7),
                        colors = listOf(Color(0xFFE18A3D), Color(0xFFF1CEB0), Color(0xFFE18A3D))
                    )
                }
            }
        }

        Box(
            modifier = Modifier
                .padding(top = 16.dp)
                .height(32.dp)
                .widthIn(min = 80.dp)
                .background(Color(0xFF62546D), CircleShape)
                .noEffectClickable(onClick = onDismiss)
                .padding(horizontal = 10.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(text = stringResource(R.string.close_with_countdown, countdown), color = Color.White, fontSize = 14.sp)
        }
    }
}

@Composable
private fun ContributeUserItem(
    isMvp: Boolean,
    avatar: String?,
    size: Dp,
    number: String,
    tintColor: Color,
    textColor: Color,
    borderColor: Color,
    colors: List<Color>
) {
    val shape = remember {
        Rectangle7351Shape()
    }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.spacedBy((-8).dp)
    ) {
        Box(contentAlignment = Alignment.TopCenter) {
            Box(
                modifier = Modifier
                    .padding(top = if (isMvp) 12.dp else 0.dp)
                    .size(size)
                    .clip(CircleShape)
                    .background(if (avatar.isNullOrEmpty()) Color(0x4DFFFFFF) else Color.Transparent)
                    .border(1.dp, borderColor, CircleShape),
                contentAlignment = Alignment.Center
            ) {
                if (avatar.isNullOrEmpty()) {
                    Icon(
                        painter = painterResource(R.drawable.ic_svg_empty_seats),
                        contentDescription = null,
                        modifier = Modifier.size(20.dp),
                        tint = tintColor
                    )
                } else {
                    ComposeImage(model = avatar)
                }
            }

            if (isMvp) {
                Image(
                    painter = painterResource(R.drawable.ic_dialog_cross_room_pk_mvp),
                    contentDescription = stringResource(R.string.mvp),
                    modifier = Modifier.size(22.dp, 16.dp)
                )
            }
        }

        Box(
            modifier = Modifier
                .size(36.dp, 16.dp)
                .clip(shape)
                .background(Brush.horizontalGradient(colors))
                .border(0.5.dp, borderColor, shape),
            contentAlignment = Alignment.Center
        ) {
            Text(
                text = number,
                color = textColor,
                fontSize = 12.sp,
                lineHeight = 12.sp,
                fontFamily = FontFamily.D_DIN
            )
        }
    }
}

@Preview
@Composable
private fun PreviewWin() {
    CrossRoomPkResultDialog(
        roomName = "房间名称",
        roomIcon = "",
        contributorList = null,
        backgroundResId = R.drawable.bg_dialog_cross_room_pk_win,
        topBackgroundResId = R.drawable.bg_dialog_cross_room_pk_win_in_top,
        tintColor = Color(0xFFFF3E57)
    )
}

@Preview
@Composable
private fun PreviewLose() {
    CrossRoomPkResultDialog(
        roomName = "房间名称",
        roomIcon = "",
        contributorList = null,
        backgroundResId = R.drawable.bg_dialog_cross_room_pk_lose,
        topBackgroundResId = R.drawable.bg_dialog_cross_room_pk_lose_in_top,
        tintColor = Color(0xFF646C9B)
    )

}

@Preview
@Composable
private fun PreviewDraw() {
    CrossRoomPkResultDialog(
        roomName = "房间名称",
        roomIcon = "",
        contributorList = null,
        backgroundResId = R.drawable.bg_dialog_cross_room_pk_draw,
        topBackgroundResId = R.drawable.bg_dialog_cross_room_pk_draw_in_top,
        tintColor = Color(0xFF6A877C)
    )
}

private class Rectangle7351Shape : Shape {

    override fun createOutline(size: Size, layoutDirection: LayoutDirection, density: Density): Outline {
        val originalWidth = 36f
        val originalHeight = 16f

        val scaleX = size.width / originalWidth
        val scaleY = size.height / originalHeight

        val path = Path().apply {
            fillType = PathFillType.NonZero
            moveTo(2f, 0.25f)
            lineTo(34f, 0.25f)
            cubicTo(34.9665f, 0.25f, 35.75f, 1.0335f, 35.75f, 2f)
            lineTo(35.75f, 8.55848f)
            cubicTo(35.75f, 9.31173f, 35.268f, 9.98048f, 34.5534f, 10.2187f)
            lineTo(19.8183f, 15.1304f)
            cubicTo(18.638f, 15.5238f, 17.362f, 15.5238f, 16.1817f, 15.1304f)
            lineTo(1.4466f, 10.2187f)
            cubicTo(0.732001f, 9.98048f, 0.25f, 9.31174f, 0.25f, 8.55848f)
            lineTo(0.25f, 2f)
            cubicTo(0.25f, 1.0335f, 1.0335f, 0.25f, 2f, 0.25f)
            close()
        }

        val matrix = Matrix()
        matrix.scale(scaleX, scaleY)
        path.transform(matrix)
        return Outline.Generic(path)
    }
}