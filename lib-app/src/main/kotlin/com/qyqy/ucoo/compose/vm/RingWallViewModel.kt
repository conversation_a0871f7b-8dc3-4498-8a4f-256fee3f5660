package com.qyqy.ucoo.compose.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.RingWallBean
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.UserApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement

class RingWallViewModel(val userid: Int) : ViewModel() {
    private val _ringWallList: MutableStateFlow<List<RingWallBean>> = MutableStateFlow(listOf())
    val ringWallList = _ringWallList.asStateFlow()

    private val api by lazy {
        createApi<UserApi>()
    }

    /**
     * 刷新戒指墙信息
     */
    fun refreshWallInfo() {
        if (isPreviewOnCompose) {
            viewModelScope.launch {
                _ringWallList.emit(
                    mutableListOf<RingWallBean>().apply {
                        for (i in 0..3) {
                            add(
                                RingWallBean(
                                    ring = RingWallBean.Ring(icon = "", name = "星辰之诺"),
                                    count = if (i % 2 == 0) 5 else 0
                                )
                            )
                        }
                    }
                )
            }
        } else {
            viewModelScope.launch {
                runApiCatching {
                    api.getUserRingWall(userid.toString())
                }.onSuccess {
                    it.getOrNull("wall")?.let {
                        val list = sAppJson.decodeFromJsonElement<List<RingWallBean>>(it)
                        _ringWallList.emit(list)
                    }
                }.toastError()
            }
        }
    }

    private var isRequestDeleting = false

    /**
     * 重置戒指墙
     */
    fun resetRingWall() {
        if (isRequestDeleting) {
            return
        }
        isRequestDeleting = true
        viewModelScope.launch {
            runApiCatching {
                api.deleteAllRing()
            }.onSuccess {
                isRequestDeleting = false
                it.getStringOrNull("toast")?.apply { toast(this) }
                refreshWallInfo()
            }.onFailure {
                isRequestDeleting = false
            }.toastError()
        }
    }
}