@file:OptIn(ExperimentalFoundationApi::class)

package com.qyqy.ucoo.compose.presentation.moment

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeContentScale
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.MenuItem
import com.qyqy.ucoo.compose.ui.PopupActionMenu
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.compose.ui.photo.PhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.previewKeyModifier
import com.qyqy.ucoo.compose.ui.photo.rememberPhotoPreviewState
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.mine.isVideo
import com.qyqy.ucoo.mine.url
import com.qyqy.ucoo.moment.Moment
import com.qyqy.ucoo.moment.Topic


@Composable
fun MomentContentItem(
    user: User,
    moment: Moment,
    modifier: Modifier = Modifier,
    previewState: PhotoPreviewState = rememberPhotoPreviewState(),
    showMenu: Boolean = false,
    onToggleLike: (Moment) -> Unit = {},
    onDeleteMoment: (Moment) -> Unit = {},
    onReportMoment: (Moment) -> Unit = {},
    onTopicClicked: (Topic) -> Unit = {},
    onPreviewImageMoment: (Moment, Int) -> Unit = { _, _ -> },
) {
    val images = moment.mediaEntries
    BoxWithConstraints(modifier = modifier) {
        val maxSize = maxWidth
        Column {
            Row {
                Text(text = moment.text, modifier = Modifier.weight(1f), color = Color.White, fontSize = 14.sp)

                if (showMenu) {
                    val expanded = remember { mutableStateOf(false) }
                    Image(
                        painter = painterResource(id = R.drawable.ic_more_action),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(start = 16.dp)
                            .size(24.dp)
                            .clickable {
                                expanded.value = true
                            },
                    )

                    PopupActionMenu(
                        expanded = expanded,
                        modifier = Modifier
                            .shadow(
                                elevation = 2.dp, shape = RoundedCornerShape(3.dp)
                            )
                            .paint(painterResource(R.drawable.bg_popup_more))
                            .padding(top = 12.dp),
                        alignment = Alignment.TopEnd,
                        offset = LocalDensity.current.run {
                            IntOffset(6.dp.roundToPx(), 28.dp.roundToPx())
                        },
                    ) {
                        if (user.isSelf) {
                            MenuItem(R.string.delete, R.drawable.ic_popup_delete) {
                                onDeleteMoment(moment)
                                expanded.value = false
                            }
                        } else {
                            MenuItem(R.string.report, R.drawable.ic_report) {
                                onReportMoment(moment)
                                expanded.value = false
                            }
                        }
                    }
                }
            }

            if (images.isNotEmpty()) {
                Spacer(modifier = Modifier.height(12.dp))
                if (images.size == 1) {
                    val mediaInfo = images[0]
                    with(LocalDensity.current) {
                        val (itemModifier, contentScale) = if (mediaInfo.width > 0 && mediaInfo.height > 0) {
                            val itemRadio = mediaInfo.width.toFloat().div(mediaInfo.height)
                            if (itemRadio > 1f) {
                                Modifier
                                    .width(
                                        mediaInfo.width
                                            .toDp()
                                            .coerceAtMost(maxSize)
                                    )
                                    .aspectRatio(itemRadio)
                            } else {
                                Modifier
                                    .height(
                                        mediaInfo.height
                                            .toDp()
                                            .coerceAtMost(maxSize)
                                    )
                                    .aspectRatio(itemRadio)
                            } to ComposeContentScale.ProxyCrop
                        } else {
                            Modifier.sizeIn(maxWidth = maxSize, maxHeight = maxSize) to ContentScale.Fit
                        }
                        ImageItem(
                            url = mediaInfo.url,
                            modifier = itemModifier
                                .previewKeyModifier(previewState, mediaInfo.id)
                                .noEffectClickable {
                                    onPreviewImageMoment(moment, 0)
                                },
                            contentScale = contentScale,
                            isVideo = mediaInfo.isVideo
                        )
                    }
                } else {
                    VerticalGrid(
                        modifier = Modifier
                            .fillMaxWidth(),
                        columns = if (images.size == 2 || images.size == 4) 2 else 3,
                        horizontalSpace = 8.dp,
                        verticalSpace = 8.dp,
                    ) {
                        images.onEachIndexed { index, mediaInfo ->
                            ImageItem(
                                url = mediaInfo.url,
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .aspectRatio(if (images.size == 2) 0.84337f else 1f)
                                    .previewKeyModifier(previewState, mediaInfo.id)
                                    .noEffectClickable {
                                        onPreviewImageMoment(moment, index)
                                    },
                                isVideo = mediaInfo.isVideo
                            )
                        }
                    }
                }
            }


            val topic = remember(moment.topics) {
                moment.topics.firstOrNull()
            }

            if (topic != null) {
                Spacer(modifier = Modifier.height(12.dp))
                Row(
                    modifier = Modifier
                        .background(Color(0xFF2A2A2A), Shapes.chip)
                        .clickWithShape(onClick = {
                            onTopicClicked.invoke(topic)
                        })
                        .padding(10.dp, 6.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(painter = painterResource(id = R.drawable.ic_topic), contentDescription = "t", modifier = Modifier.size(16.dp))
                    Spacer(modifier = Modifier.width(2.dp))
                    AppText(text = topic.title, fontSize = 12.sp, color = Color.White)
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
            Spacer(modifier = Modifier.height(12.dp))
            //地址 ｜ 点赞
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {

                if (moment.locationLabel.isNotEmpty() /*|| (hideChina && isChinaMainland(location))*/) {
                    Image(painter = painterResource(id = R.drawable.ic_moment_location), contentDescription = null, modifier = Modifier.size(16.dp))
                    Text(
                        text = moment.locationLabel,
                        modifier = Modifier.padding(start = 2.dp),
                        color = colorResource(id = R.color.white_alpha_50),
                        fontSize = 12.sp
                    )
                }

                Spacer(modifier = Modifier.weight(1f))

                Image(
                    painter = painterResource(
                        id = if (moment.iHaveLiked) {
                            R.drawable.ic_bold_like_thumb
                        } else {
                            R.drawable.ic_like_thumb
                        }
                    ), contentDescription = null,
                    modifier = Modifier
                        .size(16.dp)
                        .clickable {
                            onToggleLike(moment)
                        }
                )
                Text(
                    text = moment.likeCnt.toString(),
                    modifier = Modifier
                        .padding(start = 4.dp)
                        .widthIn(min = 20.dp),
                    color = colorResource(id = R.color.white_alpha_50),
                    fontSize = 14.sp,
                )
            }
        }
    }
}

@Composable
private fun ImageItem(
    url: String,
    modifier: Modifier,
    contentScale: ContentScale = ComposeContentScale.ProxyCrop,
    isVideo: Boolean = false
) {
    Box {
        ComposeImage(
            model = url,
            modifier = Modifier
                .clip(Shapes.extraSmall)
                .then(modifier),
            contentScale = contentScale,
        )
        if (isVideo) {
            ComposeImage(model = R.drawable.ic_video_pause, modifier = Modifier.align(Alignment.Center))
        }
    }
}


@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun PreviewMomentContentItem() {
    MomentContentItem(
        user = userForPreview,
        moment = Moment(
            0, false, 0, false,
            listOf(
                MediaInfo(
                    "https://media.ucoofun.com/mobileclient/ios/2023-06-10/image_3157171378.png",
                    828, 620,
                ),
            ),
            null, 99, "美国·洛杉矶", text = "你的一切都想要，用心感受生活，用笔记录美好，留下值得回味的瞬间❤️",
            topics = listOf(Topic(title = "这个春天，你哟难忘")),
        ),
        modifier = Modifier.fillMaxWidth()
    )
}
