package com.qyqy.ucoo.compose.presentation.room

import android.content.Context
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.buildAnnotatedString
import com.qyqy.cupid.im.messages.C2CShareVoiceRoomContent
import com.qyqy.cupid.ui.relations.family.FamilyGiftContent
import com.qyqy.cupid.ui.relations.family.FamilyTipContent
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.presentation.config.LocalChatUIProvider
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.tribe.bean.Tribe

sealed interface FamilyUIData

data object FUnknown : FamilyUIData

data class FGift(
    val icon: String,
    val messageReceiver: AnnotatedString,
    val messageGift: AnnotatedString,
    val senderIsMe: Boolean,
    val isMultiReceiver: Boolean,
) : FamilyUIData

data class FInviteVoiceRoom(val entry: UIMessageEntry<UCCustomMessage>) : FamilyUIData

data class FTip(val icon: String?, val message: String, val user: User? = null) : FamilyUIData

fun transformEventData(context: Context, entry: UIMessageEntry<UCCustomMessage>): FamilyUIData {
    val message = entry.message
    when (message.cmd) {

        MsgEventCmd.MEMBER_JOIN -> {
            val sendUser = message.getJsonValue<AppUser>("user")
            val name = sendUser?.nickname.orEmpty()
            return FTip(
                sendUser?.avatarUrl.orEmpty(),
                context.getString(R.string.cpd_family_join, name),
                user = sendUser
            )
        }

        MsgEventCmd.MEMBER_QUIT -> {
            val sendUser = message.getJsonValue<AppUser>("user")
            val name = sendUser?.nickname.orEmpty()
            return FTip(
                sendUser?.avatarUrl.orEmpty(),
                context.getString(R.string.cpd_family_quit, name),
                sendUser
            )
        }

        MsgEventCmd.MEMBER_KICKED_OUT -> {
            val sendUser = message.getJsonValue<AppUser>("admin_user")
            val targetUser = message.getJsonValue<AppUser>("kicked_user")
            return FTip(
                sendUser?.avatarUrl.orEmpty(),
                context.getString(R.string.cpd_family_kick, targetUser?.nickname.orEmpty(), sendUser?.nickname.orEmpty()),
                sendUser
            )
        }

        MsgEventCmd.BULLETIN_UPDATE -> {
            val sendUser = message.getJsonValue<AppUser>("user")
            return FTip(
                sendUser?.avatarUrl.orEmpty(),
                context.getString(R.string.cpd_family_update_build, sendUser?.nickname.orEmpty()),
                sendUser
            )
        }

        MsgEventCmd.NAME_UPDATE -> {
            val sendUser = message.getJsonValue<AppUser>("user")
            val tribeInfo = message.getJsonValue<Tribe>("tribe")
            return FTip(
                sendUser?.avatarUrl.orEmpty(),
                context.getString(
                    R.string.cpd_family_update_title,
                    sendUser?.nickname.orEmpty(),
                    tribeInfo?.name.orEmpty()
                ),
                sendUser
            )
        }

        MsgEventCmd.INVITE_TO_ROOM -> {
            return FInviteVoiceRoom(entry)
        }

        MsgEventCmd.MEMBER_GET_REWARD -> {
            val targetUser = message.getJsonValue<AppUser>("user")
            val count = message.getJsonInt("reward_coins", 1)
            return FTip(
                targetUser?.avatarUrl.orEmpty(),
                context.getString(R.string.cpd从家族小金库中获得了金币, targetUser?.nickname.orEmpty(), count),
                targetUser
            )
        }

        MsgEventCmd.MEMBER_SIGN_IN -> {
            val targetUser = message.getJsonValue<AppUser>("user")
            return FTip(
                targetUser?.avatarUrl.orEmpty(),
                context.getString(R.string.cpd已完成家族签到任务, targetUser?.nickname.orEmpty()),
                targetUser
            )
        }

        else -> {}
    }
    return FUnknown
}

class FamilyEventMessageItem(
    private val context: Context,
    entry: UIMessageEntry<UCCustomMessage>,
    private val data: FamilyUIData = transformEventData(context, entry),
) : MessageItem.EventMessage(entry) {
    @Composable
    override fun LazyItemScope.Content() {
        when (data) {
            is FTip -> {
                FamilyTipContent(message = data.message, icon = data.icon, data.user)
            }

            is FInviteVoiceRoom -> {
                C2CShareVoiceRoomContent(data.entry, 1)
            }

            else -> {
                Text(text = "unknown ui:$data")
            }
        }
    }
}

class FamilGiftMessageItem(
    private val context: Context,
    entry: UIMessageEntry<UCGiftMessage>,
) : MessageItem.Base<UCGiftMessage>(entry, entry.message.cmd) {

    private val data: FamilyUIData = if (message.cmd == MsgEventCmd.GIVE_GIFT_COMBO_FINISHED) {
        val gm = message.gift
        val receivers = buildString {
            if (gm.receivers.isNotEmpty()) {
                append(gm.receivers.joinToString(separator = "、") { it.nickname })
            } else {
                append(app.getString(R.string.cpd_all_people))
            }
        }
        FTip(
            gm.sender.avatarUrl,
            context.getString(R.string.cpd家族房送幸运礼物, gm.sender.nickname, receivers, gm.count.toString(), gm.gift.name, gm.comboCoin.toString()),
            gm.sender
        )
    } else {
        val gm = message.gift
        val m = buildAnnotatedString {
            color(Color(0xFF1D2129)) {
                append(context.getString(R.string.cpd送给))
            }
            color(Color(0xFFFF5E8B)) {
                if (gm.receivers.isNotEmpty()) {
                    append(gm.receivers.joinToString(separator = "、") { it.nickname })
                } else {
                    append(context.getString(R.string.cpd_all_people))
                }
            }
        }
        FGift(gm.gift.icon, m, buildAnnotatedString {
            append("${gm.gift.name}x${gm.count}")
        }, gm.sender.isSelf, gm.receivers.size > 1)
    }

    @Composable
    override fun LazyItemScope.Content() {
        when (data) {
            is FGift -> {
                val up = LocalChatUIProvider.current
                up?.ItemLayout(entry) {
                    Box(modifier = Modifier.fillMaxWidth(), contentAlignment = Alignment.Center) {
                        FamilyGiftContent(
                            icon = data.icon,
                            messageReceiver = data.messageReceiver,
                            messageGift = data.messageGift,
                            senderIsMe = data.senderIsMe,
                            isMultiReceivers = data.isMultiReceiver
                        )
                    }
                }
            }

            is FTip -> {
                FamilyTipContent(message = data.message, icon = data.icon, data.user)
            }

            else -> {
                if (isProd && isRelease) {
                    return
                }
                Text(text = "unknown ui:${entry.message.toMsgString()}")
            }
        }
    }

}