package com.qyqy.ucoo.compose.vm.room

import com.qyqy.ucoo.compose.data.BackgroundProp
import com.qyqy.ucoo.compose.data.RoomBackgroundItem
import com.qyqy.ucoo.compose.state.MviViewModel

interface BackgroundSettingMvi {

    sealed interface ViewEvent : MviViewModel.MviEvent {

        object Fetch : ViewEvent

        data class SelectBackground(val index: Int) : ViewEvent

        object SetBackground : ViewEvent
    }

    sealed interface ViewResult : MviViewModel.MviViewResult {
        object NoOp : ViewResult

        data class BackgroundList(val list: List<RoomBackgroundItem>) : ViewResult

        data class ErrorResult(val toastError: String? = null) : ViewResult

        data class SelectBackgroundResult(val index: Int) : ViewResult

        data class SetBackgroundResult(val changed: Boolean, val id: Int) : ViewResult
    }

    data class ViewState(
        val currentUseId: Int = -1,
        val selectIndex: Int = -1,
        val list: List<BackgroundProp> = emptyList(),
    ) : MviViewModel.MviViewState


    interface ViewEffect : MviViewModel.MviSideEffect {

        data class Toast(val msg: String) : ViewEffect

        data class Back(val changed: Boolean, val id: Int) : ViewEffect

    }
}

interface ChatRoomExpansionMvi {

    sealed interface ViewEvent : MviViewModel.MviEvent {

        data class ChangeVideoEffect(val path: String?) : ViewEvent

    }

    sealed interface ViewResult : MviViewModel.MviViewResult {
        object NoOp : ViewResult

        data class VideoEffectResult(val path: String?) : ViewResult
    }

    class ViewState : MviViewModel.MviViewState


    interface ViewEffect : MviViewModel.MviSideEffect {

        data class Toast(val msg: String) : ViewEffect

        data class VideoEffect(val path: String?) : ViewEffect

    }
}

