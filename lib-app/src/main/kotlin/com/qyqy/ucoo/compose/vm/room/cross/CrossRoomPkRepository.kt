package com.qyqy.ucoo.compose.vm.room.cross

import com.qyqy.ucoo.compose.data.CrossPkConfigResponse
import com.qyqy.ucoo.compose.data.CrossRoomPkData
import com.qyqy.ucoo.compose.data.InvitePkResponse
import com.qyqy.ucoo.compose.data.RoomListResponse
import com.qyqy.ucoo.compose.data.SetMutedResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import kotlinx.serialization.json.JsonObject

// --- 3. Repository 实现 ---

// 假设你的 createApi 和 runApiCatching 实现存在
// declare function createApi<T>(clazz: Class<T>): T
// declare suspend fun <T> runApiCatching(block: suspend () -> ApiResponse<T>): Result<T>

class CrossRoomPkRepository {

    // 假设 createApi 是你创建 Retrofit Service 的方法
    private val pkApi: CrossRoomPkApi = createApi(CrossRoomPkApi::class.java)
    // 假设 runApiCatching 是你封装的统一处理网络请求和错误的方法
    // 它接收一个 suspend lambda，执行 API 调用，并返回 Result<T>

    /**
     * 获取跨房 PK 配置
     */
    suspend fun fetchCrossPkConfig(check: Boolean = true): Result<CrossPkConfigResponse> {
        return runApiCatching { pkApi.getCrossPkConfig(check) }
    }

    /**
     * 获取跨房 PK 房间列表
     */
    suspend fun fetchCrossPkRoomList(lastId: Int = 0): Result<RoomListResponse> {
        return runApiCatching { pkApi.getCrossPkRoomList(lastId) }
    }

    /**
     * 搜索跨房 PK 房间
     */
    suspend fun searchCrossPkRoom(publicId: String): Result<RoomListResponse> {
        return runApiCatching { pkApi.searchCrossPkRoom(publicId) }
    }

    /**
     * 邀请跨房 PK
     */
    suspend fun invitePk(targetRoomId: Int, duration: Int, pkType: Int): Result<InvitePkResponse> {
        return runApiCatching { pkApi.inviteCrossPk(targetRoomId, duration, pkType) }
    }

    /**
     * 接受/拒绝跨房 PK 邀请
     */
    suspend fun respondPkInvite(inviteCode: String, accept: Boolean, auto: Boolean): Result<JsonObject> {
        return runApiCatching { pkApi.acceptCrossPkInvite(inviteCode, accept, auto) }
    }

    /**
     * 设置对方房间静音
     */
    suspend fun setOpponentMuted(pkId: Int, targetRoomId: Int, muted: Boolean): Result<SetMutedResponse> {
        return runApiCatching { pkApi.setOpponentMicMuted(pkId, targetRoomId, muted) }
    }

    /**
     * 跨房 PK 加时
     * @param setType 后端定义的 "加时" 操作类型
     * @param extraDuration 加时时长 (秒)
     */
    suspend fun addPkTime(pkId: Int, setType: Int, extraDuration: Int): Result<JsonObject> {
        return runApiCatching { pkApi.setCrossPk(setType = setType, pkId = pkId, duration = extraDuration) }
    }

    /**
     * 终止跨房 PK
     * @param setType 后端定义的 "终止" 操作类型
     */
    suspend fun terminatePk(pkId: Int, setType: Int): Result<JsonObject> {
        return runApiCatching { pkApi.setCrossPk(setType = setType, pkId = pkId) }
    }

    /**
     * 获取进行中的 PK 信息 (通过房间详情接口)
     */
    suspend fun fetchCurrentPkInfo(): Result<CrossRoomPkData?> {
        // 注意：这里的 Result<CrossRoomPkData> 是转换后的，runApiCatching 可能返回 Result<RoomDetailCrossPkInfo>
        val result = runApiCatching { pkApi.getAudioRoomDetail() }
        return result.mapCatching { detailInfo ->
            detailInfo.crossPkInfo
        }
    }
}
