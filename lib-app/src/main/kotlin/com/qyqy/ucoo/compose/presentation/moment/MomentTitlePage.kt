package com.qyqy.ucoo.compose.presentation.moment

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.PlatformTextStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.isSelfOnCompose
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.MenuItem
import com.qyqy.ucoo.compose.ui.PopupActionMenu
import com.qyqy.ucoo.moment.Moment

@Composable
fun MomentTitleItem(
    moment: Moment,
    modifier: Modifier = Modifier,
    onStartChat: (User) -> Unit = {},
    onDeleteMoment: (Moment) -> Unit = {},
    onReportMoment: (Moment) -> Unit = {},
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(IntrinsicSize.Min),
        verticalAlignment = Alignment.CenterVertically,
    ) {

        CircleComposeImage(
            model = moment.realUser.avatarUrl,
            modifier = modifier.size(48.dp),
        )

        Column(
            modifier = Modifier
                .padding(start = 8.dp)
                .fillMaxHeight()
                .weight(1f),
            verticalArrangement = Arrangement.SpaceAround,
        ) {
            Text(
                text = moment.realUser.nickname,
                color = Color.White,
                fontSize = 16.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                style = LocalTextStyle.current.merge(
                    TextStyle(platformStyle = PlatformTextStyle(includeFontPadding = false))
                )
            )
            AgeGender(
                age = moment.realUser.age,
                isBoy = moment.realUser.isBoy
            )
        }

        if (!moment.realUser.isSelfOnCompose) {
            Row(
                modifier = Modifier
                    .padding(start = 10.dp)
                    .size(62.dp, 24.dp)
                    .background(Color(0x26FFFFFF), Shapes.corner12)
                    .clip(Shapes.corner12)
                    .clickable {
                        onStartChat(moment.realUser)
                    },
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_chat_icon),
                    contentDescription = null,
                    modifier = Modifier.size(20.dp),
                )
                Spacer(modifier = Modifier.width(2.dp))
                Text(text = stringResource(id = R.string.私聊), color = colorResource(id = R.color.white_alpha_50), fontSize = 13.sp)
            }
        }

        val expanded = remember { mutableStateOf(false) }
        Image(
            painter = painterResource(id = R.drawable.ic_more_action),
            contentDescription = null,
            modifier = Modifier
                .padding(start = 16.dp)
                .size(24.dp)
                .clickable {
                    expanded.value = true
                },
        )

        PopupActionMenu(
            expanded = expanded,
            modifier = Modifier
                .shadow(
                    elevation = 2.dp, shape = RoundedCornerShape(3.dp)
                )
                .paint(painterResource(R.drawable.bg_popup_more))
                .padding(top = 12.dp),
            alignment = Alignment.TopEnd,
            offset = LocalDensity.current.run {
                IntOffset(6.dp.roundToPx(), 42.dp.roundToPx())
            },
        ) {
            if (moment.realUser.isSelfOnCompose) {
                MenuItem(R.string.delete, R.drawable.ic_popup_delete) {
                    onDeleteMoment(moment)
                    expanded.value = false
                }
            } else {
                MenuItem(R.string.report, R.drawable.ic_report) {
                    onReportMoment(moment)
                    expanded.value = false
                }
            }
        }
    }
}

@Preview
@Composable
fun PreviewMomentTitleItem() {
    MomentTitleItem(
        Moment(
            0,
            false,
            0,
            false,
            listOf(),
            null,
            1,
            "",
            text = "用心感受生活，用笔记录美好，留下值得回味的瞬间❤️",
            user = userForPreview
        )
    )
}