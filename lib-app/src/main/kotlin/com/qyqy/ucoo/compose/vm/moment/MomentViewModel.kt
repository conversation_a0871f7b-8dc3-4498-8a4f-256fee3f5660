package com.qyqy.ucoo.compose.vm.moment

import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.compose.data.MomentPreviewItem
import com.qyqy.ucoo.compose.domain.usecase.moment.DeleteMomentUseCase
import com.qyqy.ucoo.compose.domain.usecase.moment.GetMomentListUseCase
import com.qyqy.ucoo.compose.domain.usecase.moment.LikeMomentUseCase
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.compose.ui.LoadResult
import com.qyqy.ucoo.compose.ui.PaginateState
import com.qyqy.ucoo.compose.vm.moment.MomentMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.moment.MomentMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.moment.MomentMvi.ViewResult
import com.qyqy.ucoo.compose.vm.moment.MomentMvi.ViewState
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.moment.MomentApi
import com.qyqy.ucoo.utils.ComposeState
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch


class MomentViewModel(
    initialState: ViewState,
    private val getMomentListUseCase: GetMomentListUseCase,
    private val likeMomentUseCase: LikeMomentUseCase,
    private val deleteMomentUseCase: DeleteMomentUseCase,
) : MviViewModel<ViewEvent, ViewResult, ViewState, ViewEffect>(initialState) {

    companion object {

        @Composable
        fun rememberViewModel(userId: String): MomentViewModel {
            return viewModel(initializer = {
                val api: MomentApi = createApi()
                MomentViewModel(
                    ViewState(userId, emptyList()), GetMomentListUseCase(api), LikeMomentUseCase(api), DeleteMomentUseCase(api)
                )
            })
        }
    }

    private val paginateState = PaginateState<Int>(nextEnable = false)
    private val _loaded: MutableState<Boolean> = mutableStateOf(false)
    val loaded: ComposeState<Boolean> = _loaded

    init {
        viewModelScope.launch {
            paginateState.nextEnable = false
            paginateState.reset()
            processEvent(ViewEvent.RefreshMoments(initialState.userId))
        }

        effects.onEach {
            if (it is ViewEffect.LoadPageEnd) {
                delay(100)
                if (it.pagingScope != null) {
                    it.pagingScope.loadEnd(it.result)
                } else if (it.result is LoadResult.Page && it.result.pageKey != null) {
                    paginateState.nextEnable = true
                }
            }
        }.launchIn(viewModelScope)
    }

    @Composable
    fun attach(
        listState: LazyListState = rememberLazyListState(),
    ) {
        paginateState.attach(listState) { pagingScope ->
            val state = states.value
            processEvent(ViewEvent.LoadMoments(state.userId, states.value.list.lastOrNull()?.id.orDefault(0), pagingScope))
        }
    }

    override fun Flow<ViewEvent>.toResults(): Flow<ViewResult> {
        return merge(
            filterIsInstance<ViewEvent.RefreshMoments>().toRefreshMoments(),
            filterIsInstance<ViewEvent.LoadMoments>().toLoadMoments(),
            filterIsInstance<ViewEvent.ToggleLikeMoment>().toToggleLikeMoment(),
            filterIsInstance<ViewEvent.DeleteMoment>().toDeleteMoment(),
            filterIsInstance<ViewEvent.PreviewMomentImage>().toPreviewMomentList(),
        )
    }

    override fun Flow<ViewResult>.filterStateResult(): Flow<ViewResult> {
        return filter {
            when (it) {
                is ViewResult.MomentsResult -> true
                is ViewResult.LikeMomentResult -> true
                is ViewResult.DeleteMomentResult -> true
                else -> false
            }
        }
    }

    override suspend fun ViewResult.reduce(state: ViewState): ViewState {
        return when (this) {
            is ViewResult.MomentsResult -> {
                if (append) {
                    if (state.userId != userId) {
                        return state
                    }
                    state.copy(list = buildList {
                        addAll(state.list)
                        addAll(list)
                    }.distinctBy { it.id })
                } else {
                    state.copy(userId = userId, list = list.distinctBy { it.id })
                }
            }

            is ViewResult.LikeMomentResult -> {
                val list = buildList {
                    for (item in state.list) {
                        if (item == moment) {
                            add(
                                item.copy(
                                    i_have_liked = liked, like_cnt = if (liked) item.like_cnt.plus(1) else item.like_cnt.minus(1).coerceAtLeast(0)
                                )
                            )
                        } else {
                            add(item)
                        }
                    }
                }
                state.copy(list = list)
            }

            is ViewResult.DeleteMomentResult -> {
                state.copy(list = state.list.filter {
                    it != moment
                })
            }

            else -> state
        }
    }

    override fun Flow<ViewResult>.toEffects(): Flow<ViewEffect> {
        return merge(filterIsInstance<ViewResult.ErrorResult>().filter {
            it.toastError.isNullOrEmpty().not()
        }.mapLatest { result ->
            ViewEffect.Toast(result.toastError!!)
        }, filterIsInstance<ViewResult.LoadPageResult>().mapLatest {
            ViewEffect.LoadPageEnd(it.result, it.pagingScope)
        }, filterIsInstance<ViewResult.PreviewMomentResult>().mapLatest {
            ViewEffect.PreviewImage(it.list, it.start)
        })
    }

    private fun Flow<ViewEvent.RefreshMoments>.toRefreshMoments(): Flow<ViewResult> {
        return flatMapLatest {
            flow {
                getMomentListUseCase.invoke(it.userId to 0).fold({ result ->
                    emit(ViewResult.MomentsResult(it.userId, false, result))
                    _loaded.value = true
                    if (result.size >= 20) {
                        paginateState.nextEnable = true
                    }
                    emit(ViewResult.LoadPageResult(LoadResult.Page(if (result.size >= 20) result.last().id else null), null))
                }) { throwable ->
                    emit(ViewResult.ErrorResult(throwable.message))
                }
            }
        }
    }

    private fun Flow<ViewEvent.LoadMoments>.toLoadMoments(): Flow<ViewResult> {
        return flatMapLatest {
            flow {
                getMomentListUseCase.invoke(it.userId to it.key).fold({ result ->
                    emit(ViewResult.MomentsResult(it.userId, true, result))
                    emit(ViewResult.LoadPageResult(LoadResult.Page(if (result.size >= 20) result.last().id else null), it.pagingScope))
                }) { throwable ->
                    emit(ViewResult.LoadPageResult(LoadResult.Error(throwable), it.pagingScope))
                }
            }
        }
    }

    private fun Flow<ViewEvent.ToggleLikeMoment>.toToggleLikeMoment(): Flow<ViewResult> {
        return mapLatest { event ->
            val like = event.moment.iHaveLiked.not()
            likeMomentUseCase.invoke(
                mapOf(
                    "moment_id" to event.moment.id.toString(), "like" to like.toString()
                )
            ).fold({ _ ->
                ViewResult.LikeMomentResult(event.moment, like)
            }) {
                ViewResult.ErrorResult(it.message)
            }
        }
    }

    private fun Flow<ViewEvent.DeleteMoment>.toDeleteMoment(): Flow<ViewResult> {
        return mapLatest { event ->
            deleteMomentUseCase.invoke(mapOf("moment_id" to event.moment.id.toString())).fold({ _ ->
                ViewResult.DeleteMomentResult(event.moment)
            }) {
                ViewResult.ErrorResult(it.message)
            }
        }
    }

    private fun Flow<ViewEvent.PreviewMomentImage>.toPreviewMomentList(): Flow<ViewResult> {
        return mapLatest { event ->
            var index = 0
            var start = 0
            val list = buildList {
                states.value.list.onEachIndexed { i, it ->
                    if (it.id == event.moment.id) {
                        start = index.plus(event.position)
                    }
                    addAll(it.mediaEntries.map { mediaInfo ->
                        MomentPreviewItem(it.id, i, mediaInfo)
                    })
                    index += it.mediaEntries.size
                }
            }
            ViewResult.PreviewMomentResult(list, start)
        }
    }

}
