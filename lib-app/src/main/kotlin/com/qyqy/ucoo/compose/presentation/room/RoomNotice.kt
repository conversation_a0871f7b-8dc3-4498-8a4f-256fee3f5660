package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.imePadding
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.material3.ColorScheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.dialog.AnimatedDialog
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch

//房间公告
@Composable
fun RoomNoticeEditor(
    titleResId: Int, hintResId: Int, buttonResId: Int,
    notice: String, onNoticeChange: (String) -> Unit, posting: Boolean, onSave: OnClick
) {
    Column(
        Modifier
            .fillMaxWidth()
            .background(
                MaterialTheme.colorScheme.background, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)
            )
            .padding(horizontal = 16.dp)
            .imePadding()
            .navigationBarsPadding(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp), contentAlignment = Alignment.Center
        ) {
            Text(text = stringResource(id = titleResId), color = MaterialTheme.colorScheme.onSurface, fontSize = 16.sp)
        }
        Spacer(modifier = Modifier.height(8.dp))
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(164.dp)
                .background(MaterialTheme.colorScheme.primaryContainer, Shapes.small)
                .padding(horizontal = 16.dp)
        ) {
            BasicTextField(
                value = notice,
                onValueChange = onNoticeChange,
                modifier = Modifier
                    .fillMaxSize()
                    .padding(vertical = 12.dp),
                textStyle = TextStyle(fontSize = 12.sp, color = MaterialTheme.colorScheme.onSurface),
                cursorBrush = SolidColor(MaterialTheme.colorScheme.primary),
                decorationBox = { fn ->
                    Box(modifier = Modifier.padding(top = 4.dp)) {
                        fn.invoke()
                    }
                    if (notice.isEmpty()) {
                        Text(
                            text = stringResource(id = hintResId),
                            fontSize = 12.sp,
                            color = MaterialTheme.colorScheme.onPrimaryContainer
                        )
                    }
                }
            )
            Text(
                text = "${notice.length}/150",
                fontSize = 12.sp,
                color = MaterialTheme.colorScheme.onSecondaryContainer,
                modifier = Modifier.align(Alignment.BottomEnd)
            )
        }
        Spacer(modifier = Modifier.height(16.dp))
        Box(
            modifier = Modifier
                .widthIn(min = 220.dp)
                .height(44.dp)
                .background(
                    MaterialTheme.colorScheme.primary,
                    Shapes.chip
                )
                .clip(Shapes.chip)
                .click(onClick = onSave, enabled = !posting),
            contentAlignment = Alignment.Center
        ) {
            Text(text = stringResource(id = buttonResId), fontSize = 16.sp, color = MaterialTheme.colorScheme.onPrimary)
        }
        Spacer(modifier = Modifier.height(16.dp))
    }
}


@Preview(showBackground = true, backgroundColor = 0xFF020202)
@Composable
private fun CupidRoomNoticeEditor() {
    MaterialTheme(
        colorScheme = MaterialTheme.colorScheme.copy(
            background = Color.White,
            onSurface = Color(0xFF1D2129),
            primary = Color(0xFFFF5E8B),
            onPrimary = Color.White,
            primaryContainer = Color(0xFFF5F7F9),
            onPrimaryContainer = Color(0xFF86909C),
            onSecondaryContainer = Color(0xFFC9CDD4)
        )
    ) {
        RoomNoticeEditor(R.string.cpd编辑房间公告, R.string.cpd请输入房间公告, R.string.cpd_save, "", {}, false) {

        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF020202)
@Composable
private fun PreviewUCOORoomNoticeEditor() {
    MaterialTheme(
        colorScheme = MaterialTheme.colorScheme.copy(
            background = Color(0xFF222222),
            onSurface = Color.White,
            primary = Color(0xFF8B56FC),
            onPrimary = Color.White,
            primaryContainer = Color(0x0DFFFFFF),
            onPrimaryContainer = Color(0xFF86909C),
            onSecondaryContainer = Color.White.copy(0.3f)
        )
    ) {
        RoomNoticeEditor(R.string.编辑房间公告, R.string.请输入房间公告, R.string.save, "", {}, false) {

        }
    }
}

@Composable
fun RoomNoticeEditorContent(
    roomId: Int,
    notice: String,
    titleResId: Int,
    hintResId: Int,
    buttonResId: Int,
    toastSuccessResId: Int,
    colorScheme: ColorScheme,
    onDismiss: OnClick
) {
    var textFieldValue by remember {
        mutableStateOf(TextFieldValue(notice, selection = TextRange(notice.length)))
    }
    val repo = remember {
        RoomRepository()
    }
    val scope = rememberCoroutineScope()
    MaterialTheme(
        colorScheme = colorScheme
    ) {
        var posting by remember {
            mutableStateOf(false)
        }
        RoomNoticeEditor(titleResId, hintResId, buttonResId, textFieldValue.text, {
            //保存
            textFieldValue = if (it.length > 150) {
                textFieldValue.copy(text = it.substring(0, 150), selection = TextRange(150))
            } else {
                textFieldValue.copy(text = it, TextRange(it.length))
            }
        }, posting) {
            scope.launch {
                posting = true
                repo.updateRoomInfo(roomId, "notice", textFieldValue.text)
                    .onSuccess {
                        onDismiss.invoke()
                        toastRes(toastSuccessResId)
                    }.toastError()
                posting = false
            }
        }
    }
}

class EditRoomNoticeDialog(
    val roomId: Int,
    val notice: String,
) : AnimatedDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        val colorScheme = MaterialTheme.colorScheme.copy(
            background = Color.White,
            onSurface = Color(0xFF1D2129),
            primary = Color(0xFFFF5E8B),
            onPrimary = Color.White,
            primaryContainer = Color(0xFFF5F7F9),
            onPrimaryContainer = Color(0xFF86909C),
            onSecondaryContainer = Color(0xFFC9CDD4)
        )
        RoomNoticeEditorContent(
            roomId = roomId,
            notice = notice,
            titleResId = R.string.cpd编辑房间公告,
            hintResId = R.string.cpd请输入房间公告,
            buttonResId = R.string.cpd_save,
            toastSuccessResId = R.string.cpd_notice_modify_success,
            colorScheme = colorScheme
        ) {
            dialog.dismiss()
        }
    }

}

@Composable
fun NoticePopup(notice: String, emptyMsg: String, modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
    ) {
        val path = remember {
            Path()
        }
        Row(modifier = Modifier.padding(start = 24.dp)) {
            Spacer(modifier = Modifier
                .size(8.dp, 4.dp)
                .noEffectClickable { }
                .drawWithContent {
                    val sz = this.size
                    path.reset()
                    path.moveTo(0f, sz.height)
                    path.lineTo(sz.width, sz.height)
                    path.lineTo(sz.width / 2f, 0f)
                    path.close()
                    drawPath(path, color = Color.White.copy(0.9f))
                }
            )
        }
        Text(
            text = notice.ifEmpty { emptyMsg }, color = Color(0xFF1D2129),
            fontSize = 12.sp,
            modifier = Modifier
                .then(if (notice.isNotEmpty()) Modifier.widthIn(max = 312.dp) else Modifier)
                .background(Color.White.copy(0.9f), Shapes.small)
                .noEffectClickable { }
                .padding(10.dp)
        )
    }
}

@Preview(showBackground = true)
@Composable
private fun NoticePreview() {
    NoticePopup(notice = "hahhaha", emptyMsg = "")
}