package com.qyqy.ucoo.compose.presentation.mine

import android.app.Activity
import android.content.Intent
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.Fragment
import androidx.fragment.app.findFragment
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.ext.suspendOnPostDelay
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.data.FirstClassBean
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.LocalTracePage
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TraceConst
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isHighQuality
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.data.CpGraph
import com.qyqy.ucoo.compose.data.CpZoneWrapper
import com.qyqy.ucoo.compose.data.ProfileTab
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.data.UserInfoItem
import com.qyqy.ucoo.compose.ifEmpty
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.member.MemberCenterScreenNavigator
import com.qyqy.ucoo.compose.presentation.profile.RingWallWidget
import com.qyqy.ucoo.compose.presentation.profile.UserInfoPageV2
import com.qyqy.ucoo.compose.presentation.sign_tasks.SignTaskManager
import com.qyqy.ucoo.compose.presentation.welfare_center.WelfareCenterNavigator
import com.qyqy.ucoo.compose.state.MviState
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.TabWithPager
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.compose.vm.profile.FirstClassViewModel
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewResult
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewState
import com.qyqy.ucoo.compose.vm.profile.UserProfileViewModel
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi
import com.qyqy.ucoo.compose.vm.relationship.RelationshipViewModel
import com.qyqy.ucoo.config.AppConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.h5UserIncomeUrl
import com.qyqy.ucoo.im.bean.CpTask
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.im.room.CpViewModel
import com.qyqy.ucoo.mine.FriendsActivity
import com.qyqy.ucoo.mine.cell.MineCell
import com.qyqy.ucoo.mine.cell.MineCellItemProvider
import com.qyqy.ucoo.mine.dressup.MineDressUpActivity
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.RechargeActivity
import com.qyqy.ucoo.utils.RouterHelper
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity.Companion.startJsWebActivity
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicator
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import eu.bambooapps.material3.pullrefresh.pullRefresh
import eu.bambooapps.material3.pullrefresh.rememberPullRefreshState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import me.jessyan.autosize.utils.ScreenUtils
import java.text.SimpleDateFormat

@Composable
fun MinePageRouter() {
    val context = LocalContext.current
    val fragment = LocalView.current.findFragment<Fragment>()
    val viewModel = viewModel<UserProfileViewModel>(
        factory = UserProfileViewModel.providerFactory(0, sUser as AppUser)
    )
    val uiState = viewModel.states.collectAsStateWithLifecycle().value
    val menuList = remember {
        MineCellItemProvider.getMineCellItems()
    }
    val lifecycleOwner = LocalLifecycleOwner.current
    val currentUserId by rememberUpdatedState(uiState.user.id)
    val firstClassViewModel = viewModel(modelClass = FirstClassViewModel::class)

    LaunchedEffect(Unit) {
        viewModel.processEvent(ViewEvent.GetUserProfileInfo(currentUserId))
        viewModel.effects.onEach { effect ->
            when (effect) {
                is ViewEffect.Toast -> {
                    toast(effect.msg)
                }

                is ViewEffect.RouterTo -> {
                    AppLinkManager.open(context, effect.link)
                }
            }
        }.launchIn(this)
    }

    DisposableEffect(lifecycleOwner) {
        var first = true
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // 刷新自己的用户信息
                if (!first && sUser.id == currentUserId) {
                    viewModel.processEvent(ViewEvent.GetUserInfo(currentUserId, null))
                }
                first = false
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }

    val visibleCheckIn by SignTaskManager.newbieVisibleFlow.collectAsStateWithLifecycle(initialValue = false)

    MinePage(
        uiState = uiState,
        menuList = menuList,
        visibleCheckIn = visibleCheckIn,
        eventFlow = viewModel.eventFlow,
        onRefresh = { tab ->
            firstClassViewModel.refresh()
            viewModel.processEvent(ViewEvent.GetUserProfileInfo(viewModel.userId))
            if (tab != null) {
                when (tab) {
                    is ProfileTab.Cp -> viewModel.processEvent(ViewEvent.GetCpZone(tab.userId))
//                    is ProfileTab.GiftWall -> viewModel.processEvent(ViewEvent.GetGiftWall(tab.userId))
                    else -> {
                        viewModel.processEvent(ViewEvent.OnRefreshEvent(tab))
                    }
                }
            }
        },
        onClickCheckInItem = {
            WelfareCenterNavigator.navigate(context)
        },
        onClickWithdrawal = {
            Analytics.appReportEvent(DataPoint.clickBody("mine_withdraw"))
            (context as? Activity)?.startJsWebActivity(h5UserIncomeUrl)
        }
    ) {
        when (it) {
            MineCellItemProvider.ID_MINE_FRIENDS -> {
                context.startActivity(FriendsActivity.createIntent(context, 0))
            }

            MineCellItemProvider.ID_MINE_VIP -> {
                Analytics.reportClickEvent(TracePoints.MY_PAGE_MEMBER_ENTRY)
                MemberCenterScreenNavigator.navigate(fragment, "mine")
            }

            MineCellItemProvider.ID_MINE_BILLING -> {//我的收益
                (context as? Activity)?.startJsWebActivity(h5UserIncomeUrl)
            }

            MineCellItemProvider.ID_MINE_WALLET -> {//我的钱包
                Analytics.reportClickEvent(TracePoints.MY_PAGE_WALLET)
                context.startActivity(Intent(context, RechargeActivity::class.java))
            }

            MineCellItemProvider.ID_MINE_DRESS_UP -> {
                Analytics.reportClickEvent(TracePoints.MY_PAGE_DRESS)
                context.startActivity(Intent(context, MineDressUpActivity::class.java))
            }

            MineCellItemProvider.ID_MINE_LEVEL -> {
                Analytics.reportClickEvent(TracePoints.MY_PAGE_RANGE)
                JsBridgeWebActivity.newIntent(context, AppConfig.URL_MINE_LEVEL).also { intent ->
                    context.startActivity(intent)
                }
            }

            else -> {
                return@MinePage false
            }
        }
        true
    }
}

@Composable
private fun FirstClassWidget(modifier: Modifier = Modifier) {
    val bean = if (isEditOnCompose) {
        FirstClassBean(
            showFirstClassInfo = true, firstClassInfo = FirstClassBean.FirstClassInfo(
                isFirstClassUser = true
            )
        )
    } else {
        val viewModel = viewModel(modelClass = FirstClassViewModel::class)

        LaunchedEffect(key1 = Unit) {
            viewModel.refresh()
        }
        val firstClassBean by viewModel.firstClassBean.collectAsStateWithLifecycle()
        firstClassBean
    }

    AnimatedVisibility(visible = bean.showFirstClassInfo) {
        Box(modifier.padding(top = 12.dp), contentAlignment = Alignment.BottomCenter) {
            val ctx = LocalContext.current
            Row(
                modifier = Modifier
                    .size(311.dp, 64.dp)
                    .paint(
                        painter = painterResource(id = R.drawable.ic_firstclass_bg),
                        contentScale = ContentScale.FillWidth
                    )
                    .padding(vertical = 8.dp, horizontal = 12.dp)
                    .click {
                        AppLinkManager.open(ctx, "${Const.Url.baseURL}/h5/zh/firstclass")
                    },
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Column(
                    verticalArrangement = Arrangement.SpaceBetween, modifier = Modifier
                        .fillMaxHeight()
                        .weight(1f)
                ) {
                    Text(buildAnnotatedString {
                        append(stringResource(id = R.string.firstclass_title))
                        if (bean.firstClassInfo.isFirstClassUser) {
                            withStyle(
                                SpanStyle(
                                    fontSize = 12.sp
                                )
                            ) {
                                append(
                                    stringResource(
                                        id = R.string.firstclass_joindays,
                                        bean.firstClassInfo.joinDays
                                    )
                                )
                            }
                        }
                    }, color = Color(0xFFFFF3CB), fontSize = 14.sp, lineHeight = 14.sp)
                    if (!bean.firstClassInfo.isFirstClassUser) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth(0.69f)
                                .height(6.dp)
                                .background(
                                    color = Color(0xFF38332e), shape = RoundedCornerShape(24.dp)
                                )
                        ) {
                            Spacer(
                                modifier = Modifier
                                    .fillMaxHeight()
                                    .fillMaxWidth(bean.firstClassInfo.paidCoins / bean.firstClassInfo.totalCoin.toFloat())
                                    .background(
                                        color = Color(0xFFFFF3CB),
                                        shape = RoundedCornerShape(24.dp)
                                    )
                            )
                        }
                        Text(
                            buildAnnotatedString {
                                append(stringResource(id = R.string.firstclass_recharge))
                                withStyle(
                                    SpanStyle(
                                        color = Color(0xFFF2BC65)
                                    )
                                ) {
                                    append(" ${bean.firstClassInfo.totalCoin - bean.firstClassInfo.paidCoins} ")
                                }
                                append(stringResource(id = R.string.firstclass_remain_coin_tojoin))
                            }, style = TextStyle(
                                fontSize = 12.sp, lineHeight = 12.sp, color = Color(0xFFFFF3CB)
                            )
                        )
                    } else {
                        val formater = remember {
                            SimpleDateFormat("YYYY-MM-dd")
                        }
                        Text(
                            text = stringResource(
                                id = R.string.firstclass_available_time,
                                formater.format(bean.firstClassInfo.endTime * 1000L)
                            ),
                            style = TextStyle(
                                fontSize = 12.sp, lineHeight = 12.sp, color = Color(0xFF645B4C)
                            )
                        )
                    }
                }

                ComposeImage(
                    model = if (bean.firstClassInfo.isFirstClassUser) R.drawable.ic_firstclass_actived else R.drawable.ic_firstclass_inactived,
                    modifier = Modifier
                        .padding(horizontal = 4.dp)
                        .size(54.dp)
                )
            }
        }
    }

    if (!bean.showFirstClassInfo) {
        Spacer(modifier = Modifier.height(12.dp))
    }
}


@Preview
@Composable
private fun FirstClassWidgetPreview() {
    FirstClassWidget()
}


@OptIn(ExperimentalMaterial3Api::class, ExperimentalFoundationApi::class)
@Composable
fun MinePage(
    uiState: ViewState,
    menuList: List<MineCell>,
    visibleCheckIn: Boolean = true,
    eventFlow: Flow<MviState<ViewResult.ProfileViewResult, ViewState>> = emptyFlow(),
    onRefresh: (ProfileTab?) -> Unit = {},
    onClickCheckInItem: () -> Unit = {},
    onClickWithdrawal: () -> Unit = {},
    onClickAnchorLevel: () -> Unit = {},
    onClickMenuItem: (Int) -> Boolean = { true },
) {
    val pg = TraceConst.MINE_PAGE
    val pagerState = rememberPagerState {
        uiState.tabs.size
    }

    val state = rememberPullRefreshState(refreshing = uiState.isLoading, onRefresh = {
        onRefresh(uiState.tabs.getOrNull(pagerState.fixCurrentPage))
    })

    val backgroundColor = Color(0xFF070D14)

    val density = LocalDensity.current
    val context = LocalContext.current
    val rootView = LocalView.current
    val configuration = LocalConfiguration.current

    var mineScreenHeightDp by remember(density) {   // 这个页面不知道为什么compose高度计算有问题，一直在循环重组，高度一直在变
        mutableStateOf(with(density) {
            ScreenUtils.getRawScreenSize(context)[1].toDp().minus(76.dp)
        })
    }

    LaunchedEffect(rootView, configuration) {
        rootView.suspendOnPostDelay()
        mineScreenHeightDp = with(density) {
            rootView.height.toDp()
        }
    }

    Box(
        modifier = Modifier
            .fillMaxWidth()
            .requiredHeight(mineScreenHeightDp)
            .background(backgroundColor)
            .pullRefresh(state)
    ) {
        val scrollState = rememberScrollState()
        var titleBarHeight by remember { mutableFloatStateOf(Float.NaN) }
        var topBoxHeight by remember { mutableFloatStateOf(Float.NaN) }
        var tabRowHeight by remember { mutableFloatStateOf(Float.NaN) }

        val scrollFraction by remember {
            derivedStateOf {
                if (topBoxHeight.isNaN() || titleBarHeight.isNaN()) {
                    return@derivedStateOf 0f
                }
                if (topBoxHeight <= 0) {
                    0f
                } else {
                    scrollState.value.div(topBoxHeight.minus(titleBarHeight)).coerceIn(0f, 1f)
                }
            }
        }

        val nestedScrollConnection = remember {
            object : NestedScrollConnection {
                override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                    if (available.y < 0 && scrollState.canScrollForward) {
                        val deltaY = -available.y
                        val consumedY = -scrollState.dispatchRawDelta(deltaY)
                        return available.copy(y = consumedY)
                    }
                    return Offset.Zero
                }
            }
        }

        val paddingSpace = 5.dp

        val pageHeight by with(density) {
            remember {
                derivedStateOf {
                    if (titleBarHeight.isNaN() || tabRowHeight.isNaN()) {
                        return@derivedStateOf mineScreenHeightDp.minus(paddingSpace)
                    }
                    mineScreenHeightDp.minus(titleBarHeight.toDp()).minus(tabRowHeight.toDp())
                        .minus(paddingSpace)
                }
            }
        }

        val editMode = isPreviewOnCompose

        val tabs by rememberUpdatedState(newValue = uiState.tabs)

        LaunchedEffect(Unit) {
            RouterHelper.mineTabFlow.filter {
                it.isNotEmpty()
            }.collectLatest { path ->
                tabs.indexOfFirst {
                    path == it.path
                }.takeIf {
                    it > -1
                }?.also { index ->
                    pagerState.scrollToPage(index, 0f)
                }
                RouterHelper.mineTabFlow.emit("")
            }
        }

        val isHighQuality = (uiState.user as User).isHighQuality

        Column(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(nestedScrollConnection)
                .verticalScroll(scrollState)
        ) {
            MineTopUserPage(user = uiState.user, modifier = Modifier.onSizeChanged {
                topBoxHeight = it.height.toFloat()
            })

            AnimatedVisibility(visible = visibleCheckIn) {
                MineCheckInPage(
                    modifier = Modifier
                        .padding(start = 16.dp, end = 16.dp, top = 12.dp)
                        .clickable {
                            onClickCheckInItem()
                        },
                    onClickCheckInItem
                )
            }

            AnimatedVisibility(visible = isHighQuality) {
                MyAppIncome(
                    balance = uiState.user.withdrawableValue.ifEmpty("0.00"),
                    unit = uiState.user.withdrawableUnit.ifEmpty(stringResource(R.string.元)),
                    modifier = Modifier
                        .padding(start = 16.dp, end = 16.dp, top = 12.dp)
                        .background(Color(0x0DFFFFFF), Shapes.corner12)
                        .clip(Shapes.corner12),
                    onClick = onClickWithdrawal,
                )
            }

            FirstClassWidget(modifier = Modifier.fillMaxWidth())

            MineMenuPage(
                list = menuList,
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp)
                    .background(Color(0xFF12151D), Shapes.small),
                onClickItem = onClickMenuItem,
            )

            TabWithPager(
                tabs = remember(context, uiState.tabs) {
                    uiState.tabs.map {
                        AppTab(
                            when (it) {
                                is ProfileTab.Cp -> context.getString(R.string.mine_cp)
                                is ProfileTab.Relationship -> context.getString(R.string.关系图谱)
                                is ProfileTab.UserInfo -> context.getString(R.string.个人资料)
                                is ProfileTab.GiftWall -> context.getString(R.string.gift_wall)
                                is ProfileTab.RingInfo -> context.getString(R.string.戒指墙)
                            }
                        )
                    }
                },
                pagerState = pagerState,
                tabModifier = Modifier
                    .padding(top = paddingSpace)
                    .align(Alignment.CenterHorizontally)
                    .onSizeChanged {
                        tabRowHeight = it.height.toFloat()
                    },
                pagerModifier = Modifier
                    .padding(top = 10.dp)
                    .height(pageHeight),
                //2.42.0 这里改成了0, 变成懒加载
                beyondBoundsPageCount = 0,
            ) { index ->
                when (val tabPage = uiState.tabs[index]) {
                    is ProfileTab.Cp -> {
                        if (editMode) {
                            PreviewCpZonePage()
                        } else {
                            val profileViewModel = viewModel<UserProfileViewModel>()
                            val cpViewModel = viewModel<CpViewModel>()

                            LaunchedEffect(key1 = tabPage.userId) {
                                if (tabPage.data.cpZone == null) {
                                    profileViewModel.processEvent(ViewEvent.GetCpZone(tabPage.userId))
                                }
                            }
                            CpZonePageRouter(
                                0,
                                tabPage.userId,
                                tabPage.data,
                                profileViewModel,
                                cpViewModel
                            )
                        }
                    }

                    is ProfileTab.Relationship -> {
                        if (editMode) {
                            PreviewRelationShipGraphPage()
                        } else {
                            val profileViewModel = viewModel<UserProfileViewModel>()
                            val viewModel =
                                RelationshipViewModel.rememberViewModel(tabPage, eventFlow)

                            LaunchedEffect(key1 = tabPage.userId) {
                                profileViewModel.effects.filterIsInstance<ViewEffect.Refresh>()
                                    .onEach {
                                        if (it.tab is ProfileTab.Relationship) {
                                            viewModel.processEvent(
                                                RelationshipMvi.ViewEvent.GetFamilyRelationInfo(
                                                    it.tab.userId
                                                )
                                            )
                                        }
                                    }.launchIn(this)
                            }
                            CompositionLocalProvider(LocalTracePage provides pg) {
                                RelationShipGraphRouter(uiState.user, viewModel)
                            }
                        }
                    }

                    is ProfileTab.UserInfo -> {
                        UserInfoPageV2(uiState.user, tabPage.data)
                    }


                    is ProfileTab.GiftWall -> {
                        if (editMode) {
                            PreviewGiftWallPage()
                        } else {
                            val refreshGiftWallFlag = remember {
                                mutableStateOf(0)
                            }
                            val profileViewModel = viewModel<UserProfileViewModel>()
                            LaunchedEffect(key1 = tabPage.userId) {
                                profileViewModel.effects.filterIsInstance<ViewEffect.Refresh>()
                                    .onEach {
                                        if (it.tab is ProfileTab.GiftWall) {
                                            refreshGiftWallFlag.value = (refreshGiftWallFlag.value % 10) + 1
                                        }
                                    }.launchIn(this)
                            }
                            GiftWallPagerV2(
                                userId = sUser.userId,
                                profileType = profileViewModel.profileType,
                                refreshFlag = refreshGiftWallFlag.value
                            )
                        }
                    }

                    is ProfileTab.RingInfo -> {
                        val refreshRingWallFlag = remember {
                            mutableStateOf(0)
                        }
                        val profileViewModel = viewModel<UserProfileViewModel>()
                        LaunchedEffect(key1 = tabPage.userId) {
                            profileViewModel.effects.filterIsInstance<ViewEffect.Refresh>()
                                .onEach {
                                    if (it.tab is ProfileTab.RingInfo) {
                                        refreshRingWallFlag.value = (refreshRingWallFlag.value % 10) + 1
                                    }
                                }.launchIn(this)
                        }
                        RingWallWidget(sUser.userId, profileType = 0, refreshRingWallFlag.value)
                    }
                }
            }
        }

        TopTitleBar(uiState.user, backgroundColor, scrollFraction, Modifier.onSizeChanged {
            titleBarHeight = it.height.toFloat()
        })

        PullRefreshIndicator(
            refreshing = uiState.isLoading,
            state = state,
            modifier = Modifier.align(Alignment.TopCenter),
            colors = PullRefreshIndicatorDefaults.colors(
                containerColor = colorResource(id = R.color.black_500),
                contentColor = colorResource(id = R.color.color_primary_pink),
            ),
            scale = true
        )
    }
}

@Preview
@Composable
fun PreviewMinePage() {
    val user = userForPreview
    MinePage(
        ViewState(
            false, user, listOf(
                ProfileTab.UserInfo(userId = "0", data = userForPreview.run {
                    buildList {
                        add(UserInfoItem.Moment(emptyList()))
                        add(UserInfoItem.AttractiveLabel(attractiveFlags))
                        add(UserInfoItem.Signature(shortIntro))
                        add(UserInfoItem.Tribe(tribe))
                    }.sortedDescending()
                }),
                ProfileTab.Cp(
                    userId = "0", data = CpZoneWrapper(
                        cpZone = CpZone(
                            60,
                            100,
                            233,
                            userForPreview,
                            userForPreview,
                            CpTask()
                        ),
                        cpRelationship = Relationship.Cp.previewSimple1()
                    )
                ),
                ProfileTab.Relationship(
                    userId = "0",
                    data = CpGraph(Relationship.Cp.previewSimple1(), "0", 0)
                ),
                ProfileTab.GiftWall(userId = "0", data = emptyList()),
                ProfileTab.RingInfo(userId = "0", data = emptyList()),
            )
        ),
        MineCellItemProvider.getMineCellItems()
    )
}


