package com.qyqy.ucoo.compose.presentation.voice

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.feat.audio.AudioPlayViewModel
import com.qyqy.ucoo.feat.audio.VirtualGirlFriendApi
import com.qyqy.ucoo.feat.audio.VoiceItem
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.utils.LogUtil

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

class VoiceViewModel : ViewModel() {

    companion object {
        private val _flowRefresh: MutableSharedFlow<Int> = MutableSharedFlow(extraBufferCapacity = 1)
        val flowRefresh = _flowRefresh.asSharedFlow()

        fun refresh(newVoiceId: Int = -1) {
            val ret = _flowRefresh.tryEmit(newVoiceId)
            LogUtil.w("refresh try emit :$ret")
        }
    }

    private val _flowVoiceList: MutableStateFlow<List<VoiceItem>> = MutableStateFlow(emptyList())
    val flowVoiceList = _flowVoiceList.asStateFlow()
    private val api = createApi<VirtualGirlFriendApi>()
    private var newVoiceId = -1

    var voiceListHasChanged = false
        private set

    init {
        refresh()
        viewModelScope.launch {
            flowRefresh.collectLatest {
                newVoiceId = it
                refresh()
            }
        }
    }

    fun refresh() {
        LogUtil.d("refresh voice list")
        viewModelScope.launch {
            runApiCatching { api.getVoiceList() }
                .onSuccess { jsonObject ->
                    jsonObject["voice_list"]?.jsonArray?.let { array ->
                        val list = sAppJson.decodeFromJsonElement<List<VoiceItem>>(array)
                        if (newVoiceId != -1) {
                            list.forEach {
                                it.isNew = it.id == newVoiceId
                            }
                            newVoiceId = -1
                        }
                        val listBefore = _flowVoiceList.value
                        if (listBefore.isNotEmpty()) {
                            voiceListHasChanged = list.size == listBefore.size
                        }
                        _flowVoiceList.emit(list)
                    }
                }.toastError()
        }
    }


    suspend fun deleteVoice(voiceId: Int) {
        runApiCatching { api.deleteVoice(mapOf("voice_id" to voiceId.toString())) }
            .onSuccess {
                voiceListHasChanged = true
                _flowVoiceList.emit(_flowVoiceList.value.filter { it.id != voiceId })
            }.toastError()
    }
}