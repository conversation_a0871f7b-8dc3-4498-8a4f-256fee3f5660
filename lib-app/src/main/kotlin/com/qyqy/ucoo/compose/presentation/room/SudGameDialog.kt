package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.message.entity.RichItem
import com.qyqy.ucoo.im.room.game.GameMode
import com.qyqy.ucoo.im.room.game.GameSettlementItem
import com.qyqy.ucoo.im.room.game.ModeOption


@Composable
fun GameModeDialogContent(modeOptions: List<ModeOption>, onDismiss: () -> Unit, onClick: (ModeOption) -> Unit) {
    Column(
        modifier = Modifier
            .width(315.dp)
            .heightIn(max = 478.dp)
            .background(Brush.verticalGradient(listOf(Color(0xFF062226), Color(0xFF3B8F9A))), Shapes.small)
    ) {
        Box {
            Box(
                modifier = Modifier
                    .align(Alignment.TopCenter)
                    .padding(top = 24.dp, start = 40.dp, end = 40.dp)
                    .fillMaxWidth()
                    .height(30.dp)
                    .paint(painterResource(id = R.drawable.bg_sud_game_mode_select_text)), contentAlignment = Alignment.Center
            ) {
                AutoSizeText(
                    text = stringResource(id = R.string.选择游戏模式),
                    fontSize = 18.sp,
                    modifier = Modifier.padding(horizontal = 10.dp),
                    style = LocalTextStyle.current.copy(
                        brush = Brush.verticalGradient(listOf(Color(0xFFE7FCFF), Color(0xFF68E3F2))), fontWeight = FontWeight.SemiBold
                    )
                )
            }

            Image(
                painter = painterResource(id = R.drawable.ic_close_gray),
                contentDescription = "close",
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(8.dp)
                    .clickable {
                        onDismiss()
                    },
                colorFilter = ColorFilter.tint(Color.White)
            )
        }

        LazyColumn(
            modifier = Modifier.padding(vertical = 24.dp), verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            items(modeOptions) {
                Box(
                    modifier = Modifier
                        .padding(horizontal = 20.dp)
                        .height(IntrinsicSize.Min)
                        .clickable {
                            onClick(it)
                        }
                ) {
                    ComposeImage(model = it.backgroundImage, modifier = Modifier.fillMaxSize(), contentScale = ContentScale.FillBounds)

                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(min = 82.dp)
                            .padding(start = 112.dp),
                        verticalArrangement = Arrangement.Center
                    ) {
                        if (it.inputDesc.isEmpty() || it.outputDesc.isEmpty()) {
                            AppText(
                                text = it.name, fontSize = 18.sp, fontWeight = FontWeight.SemiBold, color = Color.White
                            )
                        } else {
                            Spacer(modifier = Modifier.height(13.dp))

                            AppText(
                                text = it.name, fontSize = 18.sp, fontWeight = FontWeight.SemiBold, color = Color.White
                            )

                            Spacer(modifier = Modifier.height(6.dp))

                            Text(
                                text = it.inputDesc,
                                fontSize = 11.sp,
                                color = Color.White
                            )

                            AppText(
                                text = it.outputDesc,
                                fontSize = 11.sp,
                                color = Color.White
                            )

                            Spacer(modifier = Modifier.height(11.dp))
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewGameModeDialogContent() {
    GameModeDialogContent(listOf(
        ModeOption("", true, "sss", 2, "aaaa", "cccc", 1),
        ModeOption("", true, "bbbb", 2, "cccc", "dffdf", 1),
    ), {}, {})
}

@Composable
fun JoinSudGameDialog(gameMode: GameMode, onOk: () -> Unit) {
    Column(
        modifier = Modifier
            .width(270.dp)
            .background(Color(0xFF222222), Shapes.small)
            .padding(horizontal = 16.dp, vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(text = stringResource(id = R.string.温馨提示), fontSize = 17.sp, color = Color.White, fontWeight = FontWeight.Medium)

        RichText(rich = gameMode.descRich, modifier = Modifier.padding(top = 6.dp), fontSize = 14.sp, color = colorResource(id = R.color.white_alpha_50))

        Box(
            modifier = Modifier
                .padding(top = 20.dp)
                .fillMaxWidth()
                .height(36.dp)
                .clip(CircleShape)
                .background(color = Color(0xFF945EFF))
                .clickable(onClick = onOk),
            contentAlignment = Alignment.Center
        ) {
            RichText(rich = gameMode.btnRich, color = Color.White, fontSize = 14.sp)
        }
    }
}

@Preview
@Composable
private fun PreviewJoinSudGameDialog() {
    JoinSudGameDialog(
        GameMode(
            false,
            1,
            descRich = listOf(RichItem(type = 1, text = "顺丰打撒发的顺丰")),
            btnRich = listOf(RichItem(type = 1, text = "顺丰打撒发的顺丰"))
        ), {})
}


@Composable
fun SudGameSettlementDialog(result: List<GameSettlementItem>, onOk: () -> Unit) {
    Box(contentAlignment = Alignment.TopCenter) {
        Column(
            modifier = Modifier
                .padding(top = 30.dp)
                .width(288.dp)
                .clip(Shapes.small)
                .background(Color(0xFF2A2424))
                .border(1.dp, Color(0xFF4D4948), Shapes.small)
                .padding(top = 60.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            LazyColumn(
                modifier = Modifier
                    .padding(start = 12.dp, end = 8.dp)
                    .fillMaxWidth()
                    .heightIn(max = 268.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                items(result) {
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Box(contentAlignment = Alignment.TopCenter) {
                            CircleComposeImage(
                                model = it.avatarUrl,
                                modifier = Modifier
                                    .size(40.dp)
                                    .border(1.dp, Color.White, CircleShape)
                            )
                            CircleComposeImage(
                                model = it.resultIcon,
                                modifier = Modifier
                                    .padding(top = 32.dp)
                                    .size(48.dp, 16.dp)
                                    .clip(CircleShape)
                            )
                        }

                        Text(
                            text = it.nickname, modifier = Modifier
                                .padding(start = 8.dp)
                                .fillMaxWidth(0.42f),
                            fontSize = 14.sp,
                            color = Color.White,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        )

                        RichText(
                            rich = it.descLines,
                            modifier = Modifier.padding(start = 8.dp),
                            fontSize = 12.sp,
                            color = Color.White,
                        )
                    }
                }
            }
            Box(
                modifier = Modifier
                    .padding(vertical = 20.dp)
                    .size(220.dp, 40.dp)
                    .clip(CircleShape)
                    .background(Brush.verticalGradient(listOf(Color(0xFFFAF422), Color(0xFFD8B700))))
                    .clickable(onClick = onOk),
                contentAlignment = Alignment.Center
            ) {
                Text(text = stringResource(id = R.string.确认), color = Color(0xFF563300), fontSize = 16.sp)
            }
        }
        Box(
            modifier = Modifier
                .size(334.dp, 86.dp)
                .paint(painterResource(id = R.drawable.bg_sud_game_settlement_top_title), contentScale = ContentScale.Crop),
            contentAlignment = Alignment.TopCenter
        ) {
            Text(
                text = stringResource(id = R.string.游戏结算),
                modifier = Modifier.padding(top = 15.dp),
                fontSize = 20.sp, color = Color.White, fontWeight = FontWeight.Bold
            )
        }
    }
}

@Preview
@Composable
private fun PreviewSudGameSettlementDialog() {
    SudGameSettlementDialog(emptyList(), {})
}