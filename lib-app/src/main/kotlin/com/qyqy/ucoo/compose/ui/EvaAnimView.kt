package com.qyqy.ucoo.compose.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.util.ScaleType
import com.yy.yyeva.view.EvaAnimViewV3


@Stable
class EvaAnimState : IEvaAnimListener {

    var source by mutableStateOf<Source?>(null)
        private set

    var animState by mutableIntStateOf(0)
        private set

    val isShowing: Boolean
        get() = source != null

    fun show(evaSource: Source) {
        animState = 0
        source = evaSource
    }

    fun hide() {
        animState = 0
        source = null
    }

    internal fun startPlay(view: EvaAnimViewV3) {
        when (val evaSource = source) {
            is Source.Asset -> {
                if (view.isRunning()) {
                    view.stopPlay()
                }
                val assetManager = view.context.assets
                view.setAnimListener(this)
                view.setLastFrame(true)
                view.setScaleType(ScaleType.CENTER_CROP)
                view.startPlay(assetManager, evaSource.assetPath)
            }

            else -> {
                view.setAnimListener(null)
                view.stopPlay()
            }
        }
    }

    override fun onFailed(errorType: Int, errorMsg: String?) {
        source = null
        animState = 2
    }

    override fun onVideoComplete(lastFrame: Boolean) {
        source = null
        animState = 2
    }

    override fun onVideoDestroy() = Unit

    override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) {
        if (frameIndex >= 45) {
            animState = 2
        }
    }

    override fun onVideoRestart() = Unit

    override fun onVideoStart(isRestart: Boolean) {
        animState = 1
    }

    sealed interface Source {

        data class Asset(val assetPath: String) : Source
    }

}


@Composable
fun EvaVideoEffect(
    state: EvaAnimState,
    modifier: Modifier = Modifier,
    initializer: EvaAnimViewV3.() -> Unit = {},
) {
    if (!state.isShowing) {
        return
    }
    AndroidView(factory = {
        EvaAnimViewV3(it).apply(initializer)
    }, modifier = modifier, onReset = {
//        it.stopPlay()
//        it.setAnimListener(null)
    }, onRelease = {
        it.stopPlay()
        it.setAnimListener(null)
    }) {
        state.startPlay(it)
    }
}
