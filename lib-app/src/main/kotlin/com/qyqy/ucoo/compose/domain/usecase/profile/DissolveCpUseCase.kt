package com.qyqy.ucoo.compose.domain.usecase.profile

import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.JsonObject

class DissolveCpUseCase constructor(
    private val userRepository: UserRepository = UserRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Unit, JsonObject>(ioDispatcher) {
    override suspend fun execute(parameters: Unit): Result<JsonObject> {
        return userRepository.dissolveCp()
    }

}