package com.qyqy.ucoo.compose.ui.lazy

import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animate
import androidx.compose.animation.core.tween
import androidx.compose.foundation.MutatePriority
import androidx.compose.foundation.gestures.FlingBehavior
import androidx.compose.foundation.gestures.ScrollableDefaults
import androidx.compose.foundation.gestures.ScrollableState
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.cancellable
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.isActive
import java.util.UUID


private const val SCROLL_DX = 24f
private const val REQUIRED_CARD_COUNT = 8

private class AutoScrollItem<T>(
    val id: String = UUID.randomUUID().toString(),
    val data: T
)

@Composable
fun <T : Any> AutoScrollingLazyRow(
    list: List<T>,
    modifier: Modifier = Modifier,
    requiredCardCount: Int = REQUIRED_CARD_COUNT,
    dx: Float = SCROLL_DX,
    durationMillis: Int = 400,
    state: LazyListState = rememberLazyListState(),
    contentPadding: PaddingValues = PaddingValues(0.dp),
    reverseLayout: Boolean = false,
    horizontalArrangement: Arrangement.Horizontal =
        if (!reverseLayout) Arrangement.Start else Arrangement.End,
    verticalAlignment: Alignment.Vertical = Alignment.Top,
    flingBehavior: FlingBehavior = ScrollableDefaults.flingBehavior(),
    userScrollEnabled: Boolean = true,
    itemContent: @Composable LazyItemScope.(index: Int, item: T) -> Unit,
) {

    var items by remember { mutableStateOf(list.mapAutoScrollItem(requiredCardCount)) }

    var isOverFullRow by remember {
        mutableStateOf<Boolean?>(null)
    }

    LaunchedEffect(key1 = Unit, block = {
        snapshotFlow {
            state.layoutInfo.run {
                val lastItem = visibleItemsInfo.lastOrNull()
                if (isOverFullRow == null && lastItem != null) {
                    val viewportHeight = viewportEndOffset + viewportStartOffset
                    lastItem.offset + lastItem.size > viewportHeight
                } else {
                    null
                }
            }
        }.cancellable().collectLatest {
            if (it != null) {
                isOverFullRow = it
                cancel()
            }
        }
    })

    LazyRow(
        modifier = modifier,
        state = state,
        contentPadding = contentPadding,
        reverseLayout = reverseLayout,
        horizontalArrangement = horizontalArrangement,
        verticalAlignment = verticalAlignment,
        flingBehavior = flingBehavior,
        userScrollEnabled = userScrollEnabled,
    ) {
        itemsIndexed(
            items, key = { _, item -> "AutoScrollingLazyRow-${item.id}" }
        ) { index, item ->
            itemContent(index, item.data)
            if (isOverFullRow == true && index == items.lastIndex) {
                LaunchedEffect(key1 = Unit) {
                    snapshotFlow {
                        state.layoutInfo.visibleItemsInfo.last().index
                    }.distinctUntilChanged().filter {
                        items.lastIndex == it
                    }.map {
                        state.firstVisibleItemIndex
                    }.distinctUntilChanged().collectLatest {
                        val currentList = items
                        val secondPart = currentList.subList(0, it)
                        val firstPart = currentList.subList(it, currentList.size)
                        items = (firstPart + secondPart)
                        state.scrollToItem(
                            0,
                            maxOf(0, state.firstVisibleItemScrollOffset - dx.toInt())
                        )
                    }
                }

            }
        }
    }


//    val drag by state.interactionSource.collectIsDraggedAsState()
//        snapshotFlow {
//            !drag && !state.isScrollInProgress
//        }.filter {
//            it
//        }.collectLatest {
//            while (isActive) {
//                state.autoScroll(dx, tween(durationMillis = durationMillis, easing = LinearEasing))
//            }
//        }

    LaunchedEffect(Unit) {
        while (isActive) {
            state.autoScroll(dx = dx, animationSpec = tween(durationMillis = durationMillis, easing = LinearEasing))
        }
    }
}

private fun <T : Any> List<T>.mapAutoScrollItem(requiredCardCount: Int): List<AutoScrollItem<T>> {
    var newList = map { AutoScrollItem(data = it) }
    var index = 0
    if (this.size < requiredCardCount) {
        newList = newList.toMutableList()
        while (newList.size != requiredCardCount) {
            if (index > this.size - 1) {
                index = 0
            }
            newList.add(AutoScrollItem(data = this[index]))
            index++
        }
    }
    return newList
}

suspend fun ScrollableState.autoScroll(
    scrollPriority: MutatePriority = MutatePriority.PreventUserInput,
    dx: Float = SCROLL_DX,
    animationSpec: AnimationSpec<Float> = tween(durationMillis = 800, easing = LinearEasing)
) {
    var previousValue = 0f
    scroll(scrollPriority) {
        animate(0f, dx, animationSpec = animationSpec) { currentValue, _ ->
            previousValue += scrollBy(currentValue - previousValue)
        }
    }
}