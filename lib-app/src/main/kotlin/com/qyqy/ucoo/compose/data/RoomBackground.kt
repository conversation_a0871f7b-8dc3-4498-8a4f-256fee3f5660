package com.qyqy.ucoo.compose.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


/**
 * id": 121,
 *     "expire_timestamp": 1728901100,
 *     "in_use": False,
 *     "prop": {
 *         "id": 12,
 *         "name": "xxxx",
 *         "icon": "xxxx",
 *     }
 */
@Serializable
data class RoomBackgroundItem(
    @SerialName("id") val id: Int = 0,
    @SerialName("in_use") val inUse: Boolean = false,
    @SerialName("prop") val prop: BackgroundProp = BackgroundProp(),
)

@Parcelize
@Serializable
data class BackgroundProp(
    @SerialName("id") val id: Int = 0,
    @SerialName("name") val name: String = "",
    @SerialName("effect_file") val icon: String = "",
) : Parcelable


@Serializable
data class RoomBackgroundList(
    @SerialName("background_props") val list: List<RoomBackgroundItem> = emptyList(),
)