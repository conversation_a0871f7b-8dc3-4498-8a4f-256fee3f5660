package com.qyqy.ucoo.compose.ui.wish

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.profile.wish.C2CWishViewModel
import com.qyqy.cupid.ui.profile.wish.WishHelpEntry
import com.qyqy.cupid.ui.profile.wish.WishViewModel
import com.qyqy.cupid.widgets.CpdAppTabRow
import com.qyqy.cupid.widgets.state.CupidPullRefreshBox
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun WishHelpRecordPage(wishViewModel: WishViewModel, maxHeight: Dp = 300.dp, onBack: OnClick = {}, onNavProfile: (AppUser) -> Unit = {}) {
    Column(modifier = Modifier.fillMaxWidth()) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(32.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                contentDescription = "back",
                modifier = Modifier
                    .padding(start = 22.dp)
                    .noEffectClickable(onClick = onBack),
                tint = Color.White,
            )

            Text(
                text = stringResource(id = R.string.仅展示最近50条记录),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp),
                color = Color(0xFFEFE4FF),
                fontSize = 12.sp
            )
        }

        val pagerState = rememberPagerState {
            2
        }

        CpdAppTabRow(
            tabs = listOf(
                AppTab(stringResource(id = R.string.助力我的)), AppTab(stringResource(id = R.string.我助力的))
            ),
            pagerState = pagerState,
            modifier = Modifier.padding(horizontal = 50.dp),
            indicatorColor = Color.White,
            tabSelectedColor = Color.White,
            tabUnSelectedColor = Color.White.copy(0.5f),
            tabHeight = 28.dp
        )

        val scope = rememberCoroutineScope()

        HorizontalPager(
            state = pagerState, modifier = Modifier.fillMaxWidth(), verticalAlignment = Alignment.Top
        ) { page ->
            val list = if (page == 0) wishViewModel.helpMineWishEntries else wishViewModel.myHelpWishEntries

            var isRefreshing by remember {
                mutableStateOf(if (page == 0) wishViewModel.autoRefresh3 else wishViewModel.autoRefresh4)
            }

            val onRefresh: () -> Unit = {
                scope.launch {
                    if (page == 0) {
                        wishViewModel.queryHelpMineWishList()
                    } else {
                        wishViewModel.queryMyHelpWishList()
                    }
                    isRefreshing = false
                }
            }

            if (isRefreshing) {
                LaunchedEffect(key1 = Unit) {
                    if (isRefreshing) {
                        onRefresh()
                    }
                }
            }

            val listState = rememberLazyListState()

            val locale = LocalConfiguration.current.currentLocale

            val dateFormat = remember(locale) {
                SimpleDateFormat("yyyy.MM.dd HH:mm", locale)
            }

            CupidPullRefreshBox(
                isRefreshing = isRefreshing,
                onRefresh = onRefresh,
                modifier = Modifier
                    .padding(top = 16.dp)
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                contentAlignment = Alignment.TopCenter
            ) {
                if (list.isEmpty()) {
                    Column(
                        modifier = Modifier.verticalScroll(rememberScrollState()),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.ic_cpd_empty_wish),
                            contentDescription = null,
                            modifier = Modifier
                                .padding(top = 45.dp)
                                .size(120.dp)
                        )

                        Text(
                            text = stringResource(id = R.string.暂无记录),
                            modifier = Modifier.padding(top = 10.dp),
                            color = colorResource(id = R.color.white_alpha_50),
                            fontSize = 12.sp
                        )
                    }
                } else {
                    LazyColumn(
                        modifier = Modifier
                            .fillMaxWidth()
                            .heightIn(max = maxHeight),
                        state = listState,
                        verticalArrangement = Arrangement.spacedBy(10.dp),
                    ) {
                        items(list) {
                            HelpItem(page == 0, it, dateFormat) {
                                onNavProfile(it.user)
                            }
                        }
                    }
                }
            }
        }
    }
}


@Composable
private fun HelpItem(self: Boolean, entry: WishHelpEntry, dateFormat: SimpleDateFormat, onNavProfile: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .height(128.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .height(40.dp)
                .background(Color(0xFFCC7AFF))
                .padding(horizontal = 8.dp), verticalAlignment = Alignment.CenterVertically, horizontalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            CircleComposeImage(
                model = entry.user.avatarUrl,
                modifier = Modifier
                    .size(32.dp)
                    .noEffectClickable(onClick = onNavProfile),
                borderStroke = BorderStroke(0.5.dp, Color(0xFFFFFFFF))
            )
            Text(
                text = entry.user.nickname,
                modifier = Modifier
                    .padding(end = 8.dp)
                    .weight(1f)
                    .basicMarquee(),
                color = Color.White,
                fontSize = 14.sp,
                maxLines = 1,
            )

            val time = remember(dateFormat, entry) {
                entry.format(dateFormat)
            }
            Text(text = time, color = colorResource(id = R.color.white_alpha_50), fontSize = 12.sp)
        }

        Row(
            modifier = Modifier
                .fillMaxSize()
                .height(88.dp)
                .background(Color(0xFFD48EFF))
                .padding(horizontal = 8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Box(
                modifier = Modifier
                    .size(64.dp)
                    .background(Color(0xFFE0ADFF), RoundedCornerShape(4.dp))
            ) {
                ComposeImage(
                    model = entry.wishEntry.gift.icon, modifier = Modifier
                        .size(56.dp)
                        .align(Alignment.Center), loading = null
                )
            }

            Column(
                modifier = Modifier
                    .padding(start = 8.dp)
                    .weight(1f)
                    .height(60.dp), verticalArrangement = Arrangement.SpaceEvenly
            ) {
                if (self) {
                    Text(
                        text = stringResource(id = R.string.TA赠送了, entry.wishEntry.gift.name, entry.count),
                        color = Color.White,
                        fontSize = 14.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                } else {
                    Text(
                        text = stringResource(id = R.string.我赠送了, entry.wishEntry.gift.name, entry.count),
                        color = Color.White,
                        fontSize = 14.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }

                Text(
                    text = stringResource(id = R.string.感恩方式_, entry.wishEntry.thanks.orEmpty()),
                    modifier = Modifier.basicMarquee(),
                    color = Color.White,
                    fontSize = 12.sp,
                    maxLines = 1,
                )
            }
        }
    }
}

@Preview
@Composable
private fun PreviewWishHelpRecordPage() {
    WishPageScaffold {
        WishHelpRecordPage(C2CWishViewModel.Preview)
    }
}