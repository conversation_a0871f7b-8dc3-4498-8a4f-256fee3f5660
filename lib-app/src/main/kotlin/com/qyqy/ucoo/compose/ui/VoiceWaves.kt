package com.qyqy.ucoo.compose.ui

import androidx.compose.animation.core.FastOutLinearInEasing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.geometry.CornerRadius
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.util.lerp
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlin.math.PI
import kotlin.math.sin
import kotlin.random.Random

@Composable
fun VoiceWaves(
    count: Int,
    modifier: Modifier = Modifier,
    color: Color = Color.White,
    itemWidth: Dp = 2.dp,
    itemRadius: Dp = itemWidth,
    spaceWidth: Dp = 2.5.dp,
    minHeight: Dp = 2.dp,
) {
    NaturalSoundWaveAnimation(
        count = count,
        modifier = modifier,
        barWidth = itemWidth,
        barColor =color,
        cornerRadius = itemRadius,
        spacing = spaceWidth,
        minHeightFraction = 0.15f,
        maxHeightFraction = 1.0f,
        animationDurationMillis = 1000,
        naturalnessFactor = 0.1f // 降低自然度，更接近规则波形
    )
}

@Preview
@Composable
private fun Preview() {
    Box(modifier = Modifier.background(Color.Yellow)) {
        VoiceWaves(5, modifier = Modifier.size(22.dp, 12.dp), spaceWidth = 3.dp)
    }
}

/**
 * 一个模拟声波效果的无限动画组件，具有更自然的波动效果。
 *
 * @param count 圆角矩形的数量。
 * @param modifier Modifier 应用于整个组件布局。
 * @param barWidth 每个圆角矩形的宽度。
 * @param barColor 圆角矩形的颜色。
 * @param cornerRadius 圆角矩形的圆角半径。
 * @param spacing 圆角矩形之间的间距。
 * @param minHeightFraction 圆角矩形高度变化的最小值（相对于最大高度的比例，0.0 到 1.0）。
 * @param maxHeightFraction 圆角矩形高度变化的最大值（相对于最大高度的比例，0.0 到 1.0）。
 * @param animationDurationMillis 单个动画周期的时长（毫秒），影响基础波形速度。
 * @param naturalnessFactor 控制“自然度”的因子，影响次级波和随机性。值越高，变化越复杂。建议范围 0.0 (规则) 到 1.0 (较复杂)。
 */
@Composable
fun NaturalSoundWaveAnimation(
    count: Int,
    modifier: Modifier = Modifier,
    barWidth: Dp = 2.dp,
    barColor: Color = Color.Blue,
    cornerRadius: Dp = 2.dp,
    spacing: Dp = 2.5.dp,
    minHeightFraction: Float = 0.1f,
    maxHeightFraction: Float = 1.0f,
    animationDurationMillis: Int = 1200, // 稍微延长基础周期可能更自然
    naturalnessFactor: Float = 0.5f // 控制自然度的因子
) {
    require(minHeightFraction in 0f..1f) { "minHeightFraction must be between 0.0 and 1.0" }
    require(maxHeightFraction in 0f..1f) { "maxHeightFraction must be between 0.0 and 1.0" }
    require(minHeightFraction <= maxHeightFraction) { "minHeightFraction must be less than or equal to maxHeightFraction" }
    val clampedNaturalness = naturalnessFactor.coerceIn(0f, 1f) // 限制在 0 到 1

    val infiniteTransition = rememberInfiniteTransition(label = "NaturalSoundWaveTransition")

    // 基础动画进度 (0 to 2*PI)
    val progress by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2f * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(durationMillis = animationDurationMillis, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "BaseProgress"
    )

    // 为每个 Bar 独立管理一些缓慢变化的随机性
    val randomFactors = remember { mutableStateListOf<Float>().apply { repeat(count) { add(0.5f) } } }

    // 使用 LaunchedEffect 在后台缓慢更新随机因子，增加一些不可预测性
    // 注意：这个效果比较微妙，且依赖于 recomposition 时机
    LaunchedEffect(Unit) {
        while (isActive) {
            delay(animationDurationMillis / 4L) // 更新频率不要太快
            for (i in 0 until count) {
                // 缓慢地向一个新的随机目标移动
                val current = randomFactors[i]
                val target = Random.nextFloat() // 新的目标 [0, 1)
                randomFactors[i] = lerp(current, target, 0.1f) // 平滑过渡
            }
        }
    }

    val density = LocalDensity.current
    val cornerRadiusPx = with(density) { cornerRadius.toPx() }

    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(spacing)
    ) {
        repeat(count) { index ->
            Canvas(
                modifier = Modifier
                    .width(barWidth)
                    .fillMaxHeight()
            ) {
                val availableHeight = size.height
                val availableWidth = size.width

                // --- 计算更自然的高度因子 ---

                // 1. 基础波形 (与之前类似，但相位分布更广)
                val basePhaseShift = index * (4f * PI.toFloat() / count) // 让相位差更大，波浪更长
                val baseAngle = progress + basePhaseShift
                val baseSine = sin(baseAngle) * 0.6f // 基础波权重稍大

                // 2. 次级波形 (不同频率和相位)
                val secondaryFrequencyMultiplier = 1.7f // 不同的频率
                val secondaryPhaseOffset = PI.toFloat() / 3f // 不同的固定相位偏移
                val secondaryAngle = progress * secondaryFrequencyMultiplier + basePhaseShift + secondaryPhaseOffset
                val secondarySine = sin(secondaryAngle) * 0.4f * clampedNaturalness // 次级波受 naturalnessFactor 影响

                // 3. 缓慢的随机扰动 (基于 LaunchedEffect 更新的因子)
                // 将 randomFactor 从 [0, 1] 映射到 [-1, 1] 再乘以一个小的权重
                val randomPerturbation = (randomFactors[index] * 2f - 1f) * 0.2f * clampedNaturalness // 随机扰动也受 naturalnessFactor 影响

                // 4. 组合所有因子
                // 权重总和约为 0.6 + 0.4*nat + 0.2*nat = 0.6 + 0.6*nat
                // 最大可能振幅约为 1.0 + 0.2*nat (粗略估计，因为随机扰动有界)
                val combinedValue = baseSine + secondarySine + randomPerturbation

                // 5. 归一化
                // 将组合值大致映射回 [0, 1] 范围
                // 除以一个略大于1的数来压缩范围，避免频繁触顶/触底
                val normalizationFactor = 1.0f + 0.3f * clampedNaturalness // 稍微压缩范围
                val normalizedValue = (combinedValue / normalizationFactor + 1f) / 2f

                // 6. 安全钳制到 [0, 1]
                val clampedNormalizedValue = normalizedValue.coerceIn(0f, 1f)

                // --- 使用高度因子计算最终高度 ---
                val targetHeightFraction = lerp(minHeightFraction, maxHeightFraction, clampedNormalizedValue)
                val barHeight = availableHeight * targetHeightFraction
                val topOffset = availableHeight - barHeight

                drawRoundRect(
                    color = barColor,
                    topLeft = Offset(0f, topOffset),
                    size = Size(availableWidth, barHeight),
                    cornerRadius = CornerRadius(cornerRadiusPx)
                )
            }
        }
    }
}


// --- 预览 ---
@Preview(showBackground = true, widthDp = 250, heightDp = 100)
@Composable
private fun NaturalSoundWaveAnimationPreview() {
    NaturalSoundWaveAnimation(
        modifier = Modifier.padding(16.dp),
        count = 10,
        barWidth = 10.dp,
        barColor = Color(0xFF03A9F4), // Light Blue 500
        cornerRadius = 5.dp,
        spacing = 5.dp,
        minHeightFraction = 0.15f,
        maxHeightFraction = 1.0f,
        animationDurationMillis = 1500,
        naturalnessFactor = 0.7f // 增加自然度
    )
}

@Preview(showBackground = true, widthDp = 150, heightDp = 60)
@Composable
private fun LessNaturalSoundWaveAnimationPreview() {
    NaturalSoundWaveAnimation(
        modifier = Modifier.padding(8.dp),
        count = 5,
        barWidth = 8.dp,
        barColor = Color.Green,
        cornerRadius = 3.dp,
        spacing = 4.dp,
        minHeightFraction = 0.2f,
        maxHeightFraction = 0.8f,
        animationDurationMillis = 1000,
        naturalnessFactor = 0.1f // 降低自然度，更接近规则波形
    )
}