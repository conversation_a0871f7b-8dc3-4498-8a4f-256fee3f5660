package com.qyqy.ucoo.compose.presentation.voice

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.viewModels
import androidx.compose.animation.core.InfiniteRepeatableSpec
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.VectorConverter
import androidx.compose.animation.core.animateValue
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.graphics.drawscope.translate
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.ext.doOnDestroy
import com.overseas.common.utils.id2String
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.greets.GreetItem
import com.qyqy.ucoo.compose.presentation.greets.GreetListPage
import com.qyqy.ucoo.compose.presentation.greets.GreetViewModel
import com.qyqy.ucoo.compose.presentation.greets.Greetings
import com.qyqy.ucoo.compose.presentation.greets.durationText
import com.qyqy.ucoo.compose.presentation.greets.fileName
import com.qyqy.ucoo.compose.presentation.greets.ratioLength
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ConfirmDialog
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.feat.audio.AudioPlayViewModel
import com.qyqy.ucoo.feat.audio.VoiceGenerateActivity
import kotlinx.coroutines.launch


object VoiceListNav : ScreenNavigator {
    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        activity.run {
            val vmPlay by viewModels<AudioPlayViewModel>()
            doOnDestroy {
                vmPlay.release()
            }
            VoiceListScreen(vmPlay = vmPlay, onClickNewVoice = {
                startActivity(Intent(this, VoiceGenerateActivity::class.java))
            }) {
                activity.finish()
            }
        }
    }

    fun start(context: Context) {
        navigate(context)
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF0C0C0C)
@Composable
fun Previewer() {
    VoiceIcon()
}

@Composable
fun VoiceIcon(modifier: Modifier = Modifier, color: Color = Color.White, anim: Boolean = false, isRight: Boolean = false) {
    val strokeWidth = 4.dp.value
    val style = Stroke(width = strokeWidth)
    val angle = 100f
    val start = -(angle.div(2f))
    val num: State<Int> = if (anim) {
        val transition = rememberInfiniteTransition("anim_voice")
        transition.animateValue(
            initialValue = 0,
            targetValue = 3,
            typeConverter = Int.VectorConverter,
            animationSpec = InfiniteRepeatableSpec(TweenSpec(durationMillis = 600)), label = ""
        )
    } else {
        remember {
            mutableIntStateOf(2)
        }
    }

    Box(modifier = modifier
        .size(20.dp)
        .drawWithContent {
            drawContent()
            translate(left = (if (isRight) 6 else -10).dp.value) {
                rotate(if (isRight) 180f else 0f) {
                    drawCircle(color, radius = strokeWidth / 2f, style = style)
                    var s1 = this.size
                        .times(0.5f)
                        .div(2f)
                    if (num.value > 0) {
                        translate(s1.width, s1.height) {
                            drawArc(
                                color = color,
                                start,
                                angle,
                                false,
                                size = this.size.times(0.5f),
                                style = style
                            )
                        }
                    }
                    if (num.value > 1) {
                        s1 = this.size
                            .times(0.1f)
                            .div(2f)
                        translate(s1.width, s1.height) {
                            drawArc(
                                color = color,
                                start,
                                angle,
                                false,
                                size = this.size.times(0.9f),
                                style = style
                            )
                        }
                    }
                }
            }
        })
}

@Composable
fun VoiceListScreen(
    vmPlay: AudioPlayViewModel = viewModel(),
    onClickNewVoice: () -> Unit = {},
    onBack: () -> Unit = {},
) {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxSize(1f)
                .windowInsetsPadding(WindowInsets.Companion.systemBars)
        ) {
            AppTitleBar(title = stringResource(id = R.string.voice_hello_record), onBack = onBack)
            VoiceList(vmPlay, onClickNewVoice = onClickNewVoice)
        }
    }
}

@Composable
fun VoiceList(
    vmPlay: AudioPlayViewModel = viewModel(),
    vmVoice: GreetViewModel = viewModel(),
    colorSurface: Color = Color(0xFF333435),
    onClickNewVoice: () -> Unit = {},
) {
    val loadingState = LocalContentLoading.current
    val greetings = vmVoice.greetingsFlow.collectAsState()
    val greetingInfo by remember {
        derivedStateOf { greetings.value.tabs.firstOrNull { it.contentType == GreetItem.TYPE_VOICE } ?: Greetings.Tab() }
    }
    val list = greetingInfo.contentList
    val scope = rememberCoroutineScope()
    val downloadState = vmPlay.loadingAudioFlow.collectAsState()
    val playState = vmPlay.playingAudioFlow.collectAsState()
    val context = LocalContext.current
    val isEmpty = list.isEmpty()
    GreetListPage(buttonText = stringResource(id = R.string.insert_voice), isEmpty = isEmpty, onClick = onClickNewVoice) {
        LazyColumn(
            content = {
                items(list, key = { "VoiceList-${it.id}" }) { item: GreetItem ->
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(vertical = 8.dp)
                    ) {
                        Box(
                            modifier = Modifier
                                .size(24.dp)
                                .clip(CircleShape)
                                .background(colorSurface)
                                .clickable {
                                    ConfirmDialog(context, content = id2String(R.string.confrim_to_delete)) {
                                        scope.launch {
                                            loadingState.runWithLoading {
                                                vmVoice.deleteItem(item)
                                            }
                                        }
                                    }.show()
                                }
                        ) {
                            Box(
                                modifier = Modifier
                                    .size(12.dp, 1.5.dp)
                                    .background(Color.White)
                                    .align(Alignment.Center)
                            )
                        }
                        Spacer(modifier = Modifier.width(12.dp))
                        Row(
                            modifier = Modifier
                                .clickable {
                                    item.isNew = false
                                    vmPlay.onItemClick(item.content)
                                }
                                .background(colorSurface, Shapes.small)
                                .height(40.dp)
                                .padding(horizontal = 12.dp)
                                .fillMaxWidth(item.ratioLength),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            val playing by remember(item.content) {
                                derivedStateOf {
                                    playState.value == item.fileName
                                }
                            }
                            VoiceIcon(anim = playing)
                            Text(
                                text = item.durationText,
                                color = Color.White,
                                fontSize = 16.sp,
                                modifier = Modifier
                                    .align(Alignment.CenterVertically)
                                    .padding(start = 2.dp)
                            )
                            val download by remember(item.content) {
                                derivedStateOf {
                                    downloadState.value == item.content
                                }
                            }
                            Spacer(modifier = Modifier.weight(1f))
                            if (download) {
                                CircularProgressIndicator(
                                    modifier = Modifier.size(20.dp),
                                    color = Color.White,
                                    strokeWidth = 1.5.dp
                                )
                            } else {
                                if (item.isNew) {
                                    Box(
                                        modifier = Modifier
                                            .size(8.dp)
                                            .clip(CircleShape)
                                            .background(Color.Red)
                                    )
                                }
                            }

                        }
                    }
                }
                item {
                    GreetTips(tips = greetingInfo.tips)
                }
            }, modifier = Modifier
                .padding(16.dp)
        )
    }
}

@Composable
fun GreetTips(modifier: Modifier = Modifier, tips: String? = null) {
    AppText(
        text = tips ?: stringResource(id = R.string.tip_voice),
        fontSize = 12.sp,
        color = Color(0x80FFFFFF),
        modifier = modifier.padding(vertical = 8.dp)
    )
}