package com.qyqy.ucoo.compose.presentation.config

import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.staticCompositionLocalOf
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry


interface ChatUIProvider {

    val isFamilyConversation: Boolean

    val showBottomGiftIcon: Boolean

    @Composable
    fun ItemLayout(entry: UIMessageEntry<UCInstanceMessage>, content: @Composable ColumnScope.() -> Unit)

}

abstract class BaseChatUIProvider(
    override val isFamilyConversation: <PERSON>olean,
    override val showBottomGiftIcon: Boolean
) : ChatUIProvider


val LocalChatUIProvider = staticCompositionLocalOf<ChatUIProvider?> { null }