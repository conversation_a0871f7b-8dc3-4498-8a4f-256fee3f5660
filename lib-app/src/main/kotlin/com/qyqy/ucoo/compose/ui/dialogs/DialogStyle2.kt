package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorSecondBlack
import com.qyqy.ucoo.compose.ui.AppText

@Composable
fun DialogStyle2(
    content: String = "",
    buttonText: String = "ok",
    contentTextColor: Color = Color(0xFFE9C997),
    buttonTextColor: Color = Color(0xFF593A0C),
    iconRes: Int = R.drawable.icon_charge_heart,
    iconHeight: Int = 92,
    onClick: () -> Unit = {}
) {
    Box(Modifier.widthIn(270.dp)) {
        Column {
            Spacer(modifier = Modifier.height((iconHeight / 2).dp))
            Column(
                Modifier
                    .background(colorSecondBlack, Shapes.small)
                    .padding(horizontal = 15.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height((iconHeight / 2).dp))
                Spacer(modifier = Modifier.height(10.dp))
                AppText(
                    text = content,
                    color = contentTextColor,
                    fontSize = 15.sp,
                    modifier = Modifier.fillMaxWidth(1f),
                    textAlign = TextAlign.Center
                )
                Spacer(modifier = Modifier.height(20.dp))
                Box(
                    modifier = Modifier
                        .widthIn(160.dp)
                        .heightIn(36.dp)
                        .clickable(onClick = onClick)
                        .background(
                            Brush.horizontalGradient(
                                colors = listOf(
                                    Color(0xFFF2DBB5),
                                    Color(0xFFEDB664)
                                )
                            ),
                            RoundedCornerShape(50)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    AppText(text = buttonText, color = buttonTextColor, fontSize = 16.sp)
                }
                Spacer(modifier = Modifier.height(20.dp))
            }
        }
        Image(
            painter = painterResource(id = iconRes),
            contentDescription = "icon",
            Modifier
                .size(100.dp, iconHeight.dp)
                .align(Alignment.TopCenter)
        )
    }

}

@Preview()
@Composable
fun DialogStyle2Preview() {
    AppTheme {
        Dialog(onDismissRequest = { }) {
            DialogStyle2(content = "免费语音通话时长已用完，接下来通话20金币/分钟，充值金币后可继续发起匹配")
        }
    }
}