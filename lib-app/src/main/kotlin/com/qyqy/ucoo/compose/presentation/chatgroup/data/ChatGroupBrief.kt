package com.qyqy.ucoo.compose.presentation.chatgroup.data


import android.os.Parcelable
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 群聊简情
 */
@Parcelize
@Serializable
data class ChatGroupBrief(
    @SerialName("avatar_url")
    override val avatarUrl: String = "",
    @SerialName("i_enable_dont_disturb")
    override val iEnableDontDisturb: Boolean = false,
    override val id: Int = 0,
    override val intro: String = "",
    @SerialName("member_cnt")
    override val memberCnt: Int = 0,
    override val name: String = "",
    @SerialName("public_id")
    override val publicId: String = "",
    @SerialName("rc_group_id")
    override val rcGroupId: String = "",
    @SerialName("relation_with_me")
    override val relationWithMe: Int = 0,
    @SerialName("members_room_infos")
    override val membersRoomInfos: List<MemberRoomInfo> = emptyList(),
    override val role: Int = 0,
) : Parcelable, ChatGroupInfo {

    @IgnoredOnParcel
    override val memberApplyWaitCnt: Int = 0

    fun toDetail() = ChatGroupDetail(
        avatarUrl = avatarUrl,
        iEnableDontDisturb = iEnableDontDisturb,
        intro = intro,
        memberCnt = memberCnt,
        name = name,
        publicId = publicId,
        rcGroupId = rcGroupId,
        relationWithMe = relationWithMe,
        role = role,
        membersRoomInfos = membersRoomInfos,
    )

    fun conversationDisplayEquals(new: ChatGroupBrief): Boolean {
        val old = this
        return old.name == new.name && old.avatarUrl == new.avatarUrl && old.iEnableDontDisturb == new.iEnableDontDisturb && old.relationWithMe == new.relationWithMe
    }
}