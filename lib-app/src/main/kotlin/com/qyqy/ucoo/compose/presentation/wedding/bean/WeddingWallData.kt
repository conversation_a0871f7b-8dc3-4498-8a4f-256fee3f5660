package com.qyqy.ucoo.compose.presentation.wedding.bean


import com.qyqy.ucoo.account.AppUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class WeddingWallData(
    @SerialName("bg")
    val bg: String = "",
    @SerialName("count")
    val count: Int = 0,
    @SerialName("wedding_info")
    val lastRecord: LastRecord = LastRecord(),
    @SerialName("room_type")
    val roomType: Int = 0,
    @SerialName("room_type_desc")
    val roomTypeDesc: String = "",
) {
    @Serializable
    data class LastRecord(
        val id: Int = 0,
        @SerialName("audioroom")
        val audioroom: Audioroom = Audioroom(),
        @SerialName("booking_date")
        val bookingDate: String = "",
        @SerialName("end_time")
        val endTime: String = "",
        @SerialName("light_user")
        val lightUser: AppUser = AppUser(),
        @SerialName("ring")
        val ring: Ring = Ring(),
        @SerialName("room_type")
        val roomType: Int = 0,
        @SerialName("start_time")
        val startTime: String = "",
        @SerialName("target_user")
        val targetUser: AppUser = AppUser()
    )
}