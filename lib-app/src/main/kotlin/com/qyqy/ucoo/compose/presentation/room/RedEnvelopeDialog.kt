package com.qyqy.ucoo.compose.presentation.room

import android.os.Vibrator
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.ParagraphStyle
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.DpSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.getSystemService
import com.overseas.common.sntp.SNTPManager
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.data.RedEnvelope
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.utils.LogUtil
import kotlinx.coroutines.delay


private val redPacketSize = DpSize(280.dp, 368.dp)


@Composable
private fun SnatchRedEnvelope(data: RedEnvelope.Info, onSnatch: () -> Unit = {}) {

    Column(
        modifier = Modifier
            .size(redPacketSize)
            .paint(painterResource(id = R.drawable.background_red_envelope_cover)),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Spacer(modifier = Modifier.weight(1f))
        CircleComposeImage(
            model = data.user.avatarUrl,
            modifier = Modifier
                .size(64.dp)
                .border(1.dp, Color.White, CircleShape),
        )
        Spacer(modifier = Modifier.height(20.dp))
        Text(
            text = buildAnnotatedString {
                val text = stringResource(R.string.xxx的红包, data.user.nickname)
                withStyle(
                    style = ParagraphStyle(
                        lineHeight = 30.sp, lineHeightStyle = LineHeightStyle(
                            LineHeightStyle.Alignment.Top, LineHeightStyle.Trim.None
                        )
                    )
                ) {
                    withStyle(style = SpanStyle(fontSize = 18.sp)) {
                        append(text)
                    }
                }
                append(data.desc)
            },
            modifier = Modifier.padding(horizontal = 15.dp),
            color = Color.White,
            fontSize = 14.sp,
            textAlign = TextAlign.Center,
            maxLines = 3,
            overflow = TextOverflow.Ellipsis
        )

        val activeTimestamp = data.activeTimestamp
        TimeIndicator(Modifier.weight(1f), activeTimestamp, onSnatch)

        AppText(text = data.ps, color = Color.White, fontSize = 14.sp)

        Row(
            modifier = Modifier.padding(top = 10.dp, bottom = 20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_u_coin),
                contentDescription = null,
                modifier = Modifier
                    .padding(end = 5.dp)
                    .size(20.dp)
            )
            AppText(text = stringResource(R.string.红包余额_金币, data.balance), color = Color(0xFFFFE8AA), fontSize = 14.sp)
        }
    }
}

@Composable
fun ColumnScope.TimeIndicator(modifier: Modifier, activeTimestamp: Long, onSnatch: () -> Unit) {
    Spacer(modifier = Modifier.weight(0.5f))
    var activeTime by remember(activeTimestamp) {
        mutableStateOf("")
    }
    val lt = activeTimestamp.minus(SNTPManager.now())
    if (lt > 0) {
        LaunchedEffect(key1 = activeTimestamp, block = {
            var now = SNTPManager.now()
            while (activeTimestamp > now) {
                val left = activeTimestamp.minus(now)
                activeTime = formatTime(left)
//                LogUtil.d("remember time ==== $activeTimestamp $activeTime")
                if (left > 1500) {
//                    LogUtil.d("wait === 1000")
                    delay(1000)
                } else {
//                    LogUtil.d("wait === $left")
                    delay(left)
                    break
                }
                now = SNTPManager.now()
            }
            activeTime = ""
        })
    }
//    LogUtil.d("timestamp ===== $lt $activeTimestamp,$activeTime")
    if (activeTime.isNotEmpty()) {
        Box(
            modifier = Modifier
                .height(28.dp)
                .background(Color(0x26000000), CircleShape)
                .padding(horizontal = 8.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(text = stringResource(R.string._xx_xx后开抢, activeTime), color = Color.White, fontSize = 14.sp)
        }
    }
    Spacer(modifier = Modifier.weight(0.5f))
    val context = LocalContext.current
    Spacer(
        modifier = Modifier
            .size(80.dp)
            .noEffectClickable {
                //倒计时状态震动反馈
                if (activeTime.isNotEmpty()) {
                    context.getSystemService<Vibrator>()?.vibrate(500L)
                } else {
                    onSnatch()
                }
            }
    )
    Spacer(modifier = Modifier.height(10.dp))
}

@Composable
fun RedPacketLoading(text: String? = null) {
    Box(
        modifier = Modifier
            .size(redPacketSize),
        contentAlignment = Alignment.Center
    ) {

        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            CircularProgressIndicator(color = Color.White)
            if (text != null) {
                Text(text = text, color = Color.White, fontSize = 14.sp)
            }
        }
    }
}

@Composable
private fun ResultRedEnvelope(message: String, buttonText: String, onConfirm: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .size(280.dp, 368.dp)
            .paint(painterResource(id = R.drawable.background_red_envelope_result)),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {

        Image(
            painter = painterResource(id = R.drawable.ic_gold_coin_58),
            contentDescription = null,
            modifier = Modifier
                .padding(top = 64.dp, bottom = 20.dp)
                .size(80.dp),
            contentScale = ContentScale.Crop,
        )

        AppText(text = message, color = Color(0xFFDA7314), fontSize = 16.sp)

        Spacer(modifier = Modifier.weight(1f))

        Box(
            modifier = Modifier
                .padding(bottom = 36.dp)
                .size(200.dp, 44.dp)
                .background(Color(0xFFFEF9E2), RoundedCornerShape(3.dp))
                .noEffectClickable(onClick = onConfirm),
            contentAlignment = Alignment.Center
        ) {

            Text(text = buttonText, color = Color(0xFFFF2440), fontSize = 16.sp)
        }
    }
}

fun formatTime(time: Long): String {
    if (time <= 0) {
        return ""
    }
    val secondTime = time.plus(500).div(1000).toInt() // 四舍五入
    val minute = secondTime.div(60)
    val second = secondTime.rem(60)
    return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
}

@Preview
@Composable
fun RedEnvelopesPreview() {
    SnatchRedEnvelope(
        RedEnvelope.Info(
            userForPreview,
            "恭喜发财，大吉大利",
            "本部落成员可抢",
            1000,
            System.currentTimeMillis().plus(30_000),
        )
    )

//    ResultRedEnvelope(RedEnvelope.Result(true, 999))
}

@Composable
fun RedPacketUI(data: RedEnvelope, onSnatch: () -> Unit, onThank: () -> Unit, onConfirm: () -> Unit) {
    when (data) {
        is RedEnvelope.Info -> {
            SnatchRedEnvelope(data, onSnatch)
        }

        RedEnvelope.Loading -> {
            RedPacketLoading()
        }

        RedEnvelope.None -> {
            RedPacketLoading(stringResource(R.string.loading_failed))
        }

        RedEnvelope.Failed -> {
            ResultRedEnvelope(
                stringResource(id = R.string.抱歉_手速太慢没有抢到),
                stringResource(id = R.string.rc_confirm),
                onConfirm
            )
        }

        RedEnvelope.Expired -> {
            ResultRedEnvelope(
                message = stringResource(R.string.rp_expired),
                buttonText = stringResource(id = R.string.confirm),
                onConfirm
            )
        }

        is RedEnvelope.Success -> {
            ResultRedEnvelope(
                stringResource(id = R.string.恭喜_抢到了金币, data.goldCount),
                stringResource(id = R.string.收下并表示感谢),
                onThank
            )
        }

        is RedEnvelope.AlreadyCollected -> {
            ResultRedEnvelope(
                message = stringResource(R.string.rp_have_collected, data.coinCount),
                buttonText = stringResource(id = R.string.confirm),
                onConfirm
            )
        }
    }
}