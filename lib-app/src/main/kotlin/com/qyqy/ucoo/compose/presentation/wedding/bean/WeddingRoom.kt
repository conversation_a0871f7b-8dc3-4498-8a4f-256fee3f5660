package com.qyqy.ucoo.compose.presentation.wedding.bean


import android.os.Parcelable
import androidx.annotation.DrawableRes
import com.qyqy.ucoo.R
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

data object WeddingRoomType {
    //时光印记-紫色主题
    const val TIME_PRINT_PURPLE = 1

    //星辰之诺-蓝色主题
    const val STAR_PROMISE_BLUE = 2

    //臻爱永恒-红色主题
    const val LOVE_FOREVER_RED = 3

    fun getStringRes(roomType: Int) = when (roomType) {
        1 -> {}
        else -> {}
    }
}

/**
 * ```
 * {
 *     "rooms": [
 *         {
 *             "audioroom": {
 *                 "id": 123,
 *                 "public_id": 123,
 *                 "title": "123",
 *             },
 *             "room_type": 1,         // WeddingRoomType
 *             "gift": {    // 预约此婚房对应的购买的礼物
 *                 "id": 123,
 *                 "name": "123",
 *                 "icone": "123",
 *
 *             },
 *             "ring": {    // 预约此婚房对应的戒指
 *                 "id": 123,
 *                 "name": "123",
 *                 "icone": "123",
 *             },
 *             "bridesmaid_count": 2,   // 伴娘数量
 *             "bridegroom_count": 2,   // 伴郎数量
 *             "wedding_duration": 100,  // 婚礼时长 秒
 *             "booking_count": 1,     // 预约次数
 *             "room_type_desc": "臻爱永恒",
 *             "room_desc": "尊贵1314房间号举办",
 *             "booking_hint": "可预约次数(1)次"
 *         }
 *     ],
 *     "rule": "本玩法为用户自愿参与、自主选择的模拟体验场景。所有模拟内容仅为虚拟情境演绎，不具备任何法律效力或实际效力；场景设置及互动内容不构成对现实规则的指引或暗示，亦不代表平台的任何立场或观点；本产品所有功能及内容均基于娱乐属性开发，仅供用户休闲体验目的使用。"
 * }
 * ```
 */
@Parcelize
@Serializable
data class WeddingRoom(
    @SerialName("audioroom")
    val audioroom: Audioroom = Audioroom(),
    @SerialName("booking_count")
    val bookingCount: Int = 0,
    @SerialName("booking_hint")
    val bookingHint: String = "",
    @SerialName("bridegroom_count")
    val bridegroomCount: Int = 0,
    @SerialName("bridesmaid_count")
    val bridesmaidCount: Int = 0,
    @SerialName("gift")
    val gift: Gift = Gift(),
    @SerialName("ring")
    val ring: Ring = Ring(),
    @SerialName("room_desc")
    val roomDesc: String = "",
    @SerialName("room_type")
    val roomType: Int = 0,
    @SerialName("room_type_desc")
    val roomTypeDesc: String = "",
    @SerialName("wedding_duration")
    val weddingDuration: Int = 0
) : Parcelable {

    @Parcelize
    @Serializable
    data class Gift(
        @SerialName("icon")
        val icon: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("name")
        val name: String = ""
    ) : Parcelable

    @Parcelize
    @Serializable
    data class Ring(
        @SerialName("icon")
        val icon: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("name")
        val name: String = ""
    ) : Parcelable

    @get:DrawableRes
    val cardBgRes: Int
        get() = when (roomType) {
            WeddingRoomType.LOVE_FOREVER_RED -> R.drawable.bg_wed_thin_red
            WeddingRoomType.STAR_PROMISE_BLUE -> R.drawable.bg_wed_thin_blue
            else -> R.drawable.bg_wed_thin_purple
        }

    @get:DrawableRes
    val btnRes: Int
        get() = when (roomType) {
            WeddingRoomType.LOVE_FOREVER_RED -> R.drawable.wd_btn_1314
            WeddingRoomType.STAR_PROMISE_BLUE -> R.drawable.wd_btn_888
            else -> R.drawable.wd_btn_520
        }


    @IgnoredOnParcel
    val colorTitleBg: Long = when (roomType) {
        WeddingRoomType.LOVE_FOREVER_RED -> 0xFFFFBBEA
        WeddingRoomType.STAR_PROMISE_BLUE -> 0xFFBDCDFF
        else -> 0xFFDBBDFF
    }

    @IgnoredOnParcel
    val colorTitleText: Long = when (roomType) {
        WeddingRoomType.LOVE_FOREVER_RED -> 0xFFD5608F
        WeddingRoomType.STAR_PROMISE_BLUE -> 0xFF6378D0
        else -> 0xFF7A59D2
    }
}

@Parcelize
@Serializable
data class WeddingConf(val rooms: List<WeddingRoom>, val rule: String,
                       @SerialName("rule_url")
                       val ruleUrl:String) : Parcelable