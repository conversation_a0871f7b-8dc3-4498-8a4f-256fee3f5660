package com.qyqy.ucoo.compose.data

import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.mine.MediaInfo

interface IProfileRouterPath {

    val path: String
}

data class CpZoneWrapper(
    val cpZone: CpZone?,
    val cpRelationship: Relationship.Cp,
)

sealed interface UserInfoItem : Comparable<UserInfoItem> {

    val sortScore: Int

    override fun compareTo(other: UserInfoItem): Int {
        return sortScore.compareTo(other.sortScore)
    }

    data class Moment constructor(val list: List<MediaInfo>, val count: Int = list.size) : UserInfoItem {

        override val sortScore: Int
            get() = 50 + if (list.isNotEmpty()) 100 else 0

    }

    data class AttractiveLabel(val list: List<AttractiveFlag>) : UserInfoItem {

        override val sortScore: Int
            get() = 49 + if (list.isNotEmpty()) 100 else 0
    }

    data class Signature(val shortIntro: String) : UserInfoItem {

        override val sortScore: Int
            get() = 48 + if (shortIntro.isNotEmpty()) 100 else 0
    }

    data class Tribe(val tribe: com.qyqy.ucoo.tribe.bean.Tribe?) : UserInfoItem {

        override val sortScore: Int
            get() = 47 + if (tribe != null) 100 else 0
    }
}

sealed interface ProfileTab : IProfileRouterPath {

    data class Cp(
        val userId: String,
        val data: CpZoneWrapper,
    ) : ProfileTab {
        override val path: String = "cp_zone"

        val cpRelationship = data.cpRelationship
    }

    data class Relationship(
        val userId: String,
        val data: CpGraph,
    ) : ProfileTab {
        override val path: String = "relationship"
    }

    data class GiftWall(
        val userId: String,
        val data: List<CategoryGiftWall>,
    ) : ProfileTab {
        override val path: String = "gift_wall"
    }

    data class UserInfo(
        val userId: String,
        val data: List<UserInfoItem>,
    ) : ProfileTab {
        override val path: String = "user_info"
    }

    data class RingInfo(
        val userId: String,
        val data: List<String>,
    ) : ProfileTab {
        override val path: String = "ring_info"
    }
}


