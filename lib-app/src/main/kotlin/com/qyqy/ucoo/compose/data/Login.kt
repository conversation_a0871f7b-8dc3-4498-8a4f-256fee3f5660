package com.qyqy.ucoo.compose.data

import android.os.Parcelable
import kotlinx.parcelize.Parcelize


@Parcelize
data class PhoneInfo(
    val isBindPhone: Boolean = false,
    val country: String = "中国",
    val countryCode: String = "CN",
    val areaCode: String = "86",
    val number: String = "",
    val lastSendCodeTimestamp: Long = 0,
    val displayLeftSeconds: Int = 0,
) : Parcelable

@Parcelize
data class RegisterInfo(
    val name: String = "",
    val isBoy: Boolean = true,
    val birthday: String = "2000-01-01",
    val locationList: List<SelectItem> = emptyList(),
    val location: SelectItem = SelectedNull,
    val lifeList: List<SelectItem> = emptyList(),
    val life: SelectItem = SelectedNull,
    val inviteCode: String = "",
    val avatar: String = "",
    var cityCode: String = "",
    val tag_ids:List<Int> = emptyList()
) : Parcelable

@Parcelize
data class SelectItem(
    val id: String,
    val text: String,
    val value: String = id,
) : Parcelable {

    val isNull: Boolean
        get() = this == SelectedNull
}

val SelectedNull = SelectItem("", "")
