package com.qyqy.ucoo.compose.vm.profile

import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.hidePrivateRoomPendant
import com.qyqy.ucoo.account.isEmpty
import com.qyqy.ucoo.account.isHighQuality
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.privateRoomId
import com.qyqy.ucoo.account.showTaskPendant
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.data.CpGraph
import com.qyqy.ucoo.compose.data.CpZoneWrapper
import com.qyqy.ucoo.compose.data.ProfileTab
import com.qyqy.ucoo.compose.data.UserInfoItem
import com.qyqy.ucoo.compose.data.toCpRelationship
import com.qyqy.ucoo.compose.domain.usecase.profile.CpCheckInUseCase
import com.qyqy.ucoo.compose.domain.usecase.profile.DissolveCpUseCase
import com.qyqy.ucoo.compose.domain.usecase.profile.DoCpTaskUseCase
import com.qyqy.ucoo.compose.domain.usecase.profile.GetCpZoneUseCase
import com.qyqy.ucoo.compose.domain.usecase.profile.GetUserProfileUseCase
import com.qyqy.ucoo.compose.domain.usecase.profile.ToggleBlackUseCase
import com.qyqy.ucoo.compose.domain.usecase.profile.ToggleFollowUseCase
import com.qyqy.ucoo.compose.state.MviState
import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewResult
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewState
import com.qyqy.ucoo.im.bean.ProfileExtra
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.isSelf
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.user.UserRepository
import com.qyqy.ucoo.utils.findIsInstance
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asFlow
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.shareIn

class UserProfileViewModel(
    initialState: ViewState,
    val profileType: Int,
    private val getUserProfileUseCase: GetUserProfileUseCase,
    private val getCpZoneUseCase: GetCpZoneUseCase,
    private val cpCheckInUseCase: CpCheckInUseCase,
    private val dissolveCpUseCase: DissolveCpUseCase,
    private val toggleFollowUseCase: ToggleFollowUseCase,
    private val toggleBlackUseCase: ToggleBlackUseCase,
    private val doCpTaskUseCase: DoCpTaskUseCase,
) : MviViewModel<ViewEvent, ViewResult, ViewState, ViewEffect>(initialState) {

    companion object {

        val profileIdsFlow: MutableSharedFlow<Array<Int>> = MutableSharedFlow()

        fun providerFactory(profileType: Int, user: AppUser) = viewModelFactory {
            initializer {
                val uiState = ViewState(
                    user = user,
                    tabs = buildList {
                        if (user.cp != null && user.isSelf) {
                            add(
                                ProfileTab.Cp(
                                    userId = user.id, data = CpZoneWrapper(
                                        cpZone = null,
                                        cpRelationship = user.toCpRelationship()
                                    )
                                )
                            )
                        }
                        add(ProfileTab.UserInfo(userId = user.id, data = user.toUserInfoItems()))
                        add(ProfileTab.GiftWall(userId = user.id, data = emptyList()))
                        add(ProfileTab.RingInfo(userId = user.id, data = emptyList()))
                    }
                )
                val userRepository = UserRepository()
                UserProfileViewModel(
                    initialState = uiState,
                    profileType = profileType,
                    getUserProfileUseCase = GetUserProfileUseCase(userRepository),
                    getCpZoneUseCase = GetCpZoneUseCase(userRepository),
                    cpCheckInUseCase = CpCheckInUseCase(userRepository),
                    dissolveCpUseCase = DissolveCpUseCase(userRepository),
                    toggleFollowUseCase = ToggleFollowUseCase(userRepository),
                    toggleBlackUseCase = ToggleBlackUseCase(userRepository),
                    doCpTaskUseCase = DoCpTaskUseCase(userRepository)
                )
            }
        }

        private fun AppUser.toUserInfoItems(momentImages: List<MediaInfo> = emptyList(), momentTotalCount: Int = momentImages.size) = buildList {
            add(UserInfoItem.Moment(momentImages, momentTotalCount))
            add(UserInfoItem.AttractiveLabel(attractiveFlags))
            add(UserInfoItem.Signature(shortIntro))
            add(UserInfoItem.Tribe(tribe))
        }.sortedDescending()
    }

    val userId: String
        get() = states.value.user.id

    val hasMoment = MutableStateFlow(false)

    val eventFlow: SharedFlow<MviState<ViewResult.ProfileViewResult, ViewState>> = sharedFlow
        .filter {
            it.result == null || it.result is ViewResult.ProfileViewResult
        }.mapLatest {
            MviState(it.result as ViewResult.ProfileViewResult?, it.state)
        }.shareIn(
            scope = viewModelScope,
            started = SharingStarted.Eagerly,
            replay = 1,
        )

    init {
        if (profileType == 0) { // 表示个人中心，当切换账号时，应该更新user
            sUserFlow
                .filter {
                    !it.isEmpty() && it.id != cur.user.id
                }
                .distinctUntilChangedBy {
                    it.id
                }
                .onEach {
                    processEvent(ViewEvent.ResetUserInfo(it as AppUser))
                    processEvent(ViewEvent.GetUserProfileInfo(it.id))
                }.launchIn(viewModelScope)
        }

        profileIdsFlow.filter {
            it.contains(cur.user.userId)
        }.onEach {
            processEvent(ViewEvent.GetUserProfileInfo(cur.user.id))
        }.launchIn(viewModelScope)

        effects.onEach {
            if (it is ViewEffect.GetCpZoneTask) {
                processEvent(ViewEvent.GetCpZone(cur.user.id))
            }
        }.launchIn(viewModelScope)
    }

    override fun Flow<ViewEvent>.toResults(): Flow<ViewResult> {
        return merge(
            filterIsInstance<ViewEvent.ResetUserInfo>().toResetUserInfoResult(),
            filterIsInstance<ViewEvent.GetUserInfo>().toGetUserInfoResult(),
            filterIsInstance<ViewEvent.OnRefreshEvent>().toRefreshEventResult(),
            filterIsInstance<ViewEvent.GetUserProfileInfo>().toGetUserProfileInfoResult(),
            filterIsInstance<ViewEvent.GetCpZone>().toGetCpZoneResult(),
//            filterIsInstance<ViewEvent.GetGiftWall>().toGetGiftWallResult(),
            filterIsInstance<ViewEvent.OnCpCheckInClicked>().toCpCheckInResult(),
            filterIsInstance<ViewEvent.OnCpCheckInRemindClicked>().toCpCheckInRemindResult(),
            filterIsInstance<ViewEvent.OnDissolveCpClicked>().toDissolveCpResult(),
            filterIsInstance<ViewEvent.ToggleFollowUser>().toFollowUserResult(),
            filterIsInstance<ViewEvent.ToggleBlackUser>().toBlackUserResult(),
            filterIsInstance<ViewEvent.OnDoTaskEvent>().toRouterToResult(),
        )
    }

    override fun Flow<ViewResult>.filterStateResult(): Flow<ViewResult> {
        return filter {
            when (it) {
                is ViewResult.LoadingResult,
                is ViewResult.ErrorResult,
                is ViewResult.UserViewResult,
                is ViewResult.FollowUserResult,
                is ViewResult.BlackUserResult,
                is ViewResult.DissolveCpResult,
                is ViewResult.CpZoneResult,
//                is ViewResult.GiftWallResult,
                    -> true

                else -> false
            }
        }
    }

    override suspend fun ViewResult.reduce(state: ViewState): ViewState {
        return when (this) {
            is ViewResult.LoadingResult -> {
                state.copy(isLoading = true)
            }

            is ViewResult.ErrorResult -> {
                if (isLoading != null) {
                    state.copy(isLoading = isLoading)
                } else {
                    state
                }
            }

            is ViewResult.UserResult, is ViewResult.ResetProfileResult -> {
                val user = (this as ViewResult.UserViewResult).user
                val oldUser = state.user
                if (oldUser.id == user.id) {
                    // userInfo接口和userProfile接口返回的tribe信息不一致，userInfo返回的不全
                    val newUser = user.copy(
                        tribe = oldUser.tribe,
                        profileMedal = oldUser.profileMedal,
                        level = oldUser.level,
                        characteristicMedalList = oldUser.characteristicMedalList
                    )
                    state.copy(
                        user = newUser,
                        tabs = state.tabs.map {
                            if (it is ProfileTab.UserInfo) {
                                val item = it.data.findIsInstance<UserInfoItem.Moment>()
                                ProfileTab.UserInfo(it.userId, newUser.toUserInfoItems(item?.list.orEmpty(), item?.count ?: 0))
                            } else {
                                it
                            }
                        })
                } else {
                    val tabs = buildList {
                        if (user.cp != null && user.isSelf) {
                            add(
                                ProfileTab.Cp(
                                    userId = user.id, data = CpZoneWrapper(
                                        cpZone = null,
                                        cpRelationship = user.toCpRelationship()
                                    )
                                )
                            )
                        }
                        add(ProfileTab.UserInfo(userId = user.id, data = user.toUserInfoItems()))
                        add(ProfileTab.GiftWall(userId = user.id, data = emptyList()))
                        add(ProfileTab.RingInfo(userId = user.id, data = emptyList()))
                    }
                    state.copy(user = user, tabs = tabs, extra = ProfileExtra())
                }
            }


            is ViewResult.UserProfileResult -> {
                if (user.isSelf()) {
                    app.accountManager.setSelfUser(
                        user.copy(
                            balance = sUser.balance,
                            privateRoomId = sUser.privateRoomId,
                            isCoinTrade = sUser.isCoinTrade,
                            hidePrivateRoomPendant = sUser.hidePrivateRoomPendant,
                            showTaskPendant = sUser.showTaskPendant,
                            isHighQuality = sUser.isHighQuality,
                        )
                    )
                } else {
                    UserManager.updateUserIfExist(user.id) {
                        user
                    }
                }

                val tabs = buildList {

                    if (extra.showCpZone) {
                        val cpZoneTab = state.tabs.findIsInstance<ProfileTab.Cp>()
                        if (cpZoneTab != null) {
                            cpZoneTab.data.cpZone?.also {
                                it.hasNextLevel = extra.hasNextLevel
                                it.cpPublishSealUrl = extra.cpPublishSealUrl
                                it.cpPublishLevelUrl = extra.cpPublishLevelUrl
                                it.isPublishCp = extra.isPublishCp
                            }
                            add(
                                ProfileTab.Cp(
                                    userId = user.id,
                                    data = cpZoneTab.data.copy(cpRelationship = extra.cpRelationship)
                                )
                            )
                        } else {
                            add(
                                ProfileTab.Cp(
                                    userId = user.id,
                                    data = CpZoneWrapper(cpZone = null, cpRelationship = extra.cpRelationship)
                                )
                            )
                        }
                    }

                    fun checkAddRelationship() {
                        if (extra.showRelationship) {
                            add(
                                ProfileTab.Relationship(
                                    userId = user.id,
                                    data = CpGraph(extra.cpRelationship, user.id, profileType)
                                )
                            )
                        }
                    }

                    if (profileType == 0) {
                        checkAddRelationship()

                        add(ProfileTab.UserInfo(userId = user.id, data = user.toUserInfoItems(momentImages, extra.momentTotalCount)))
                    } else {
                        add(ProfileTab.UserInfo(userId = user.id, data = user.toUserInfoItems(momentImages, extra.momentTotalCount)))

                        checkAddRelationship()
                    }

                    val giftWallTab = state.tabs.findIsInstance<ProfileTab.GiftWall>()
                    if (giftWallTab != null) {
                        add(ProfileTab.GiftWall(userId = user.id, data = giftWallTab.data))
                    }

                    add(ProfileTab.RingInfo(userId = user.id, data = emptyList()))
                }

                state.copy(
                    isLoading = false,
                    user = user,
                    tabs = tabs,
                    extra = extra
                )
            }

            is ViewResult.CpZoneResult -> {
                state.copy(
                    isLoading = false,
                    tabs = state.tabs.map {
                        if (it is ProfileTab.Cp) {
                            ProfileTab.Cp(userId = state.user.id, data = it.data.copy(cpZone = cpZone.also {
                                state.apply {
                                    it.hasNextLevel = extra.hasNextLevel
                                    it.cpPublishSealUrl = extra.cpPublishSealUrl
                                    it.cpPublishLevelUrl = extra.cpPublishLevelUrl
                                    it.isPublishCp = extra.isPublishCp
                                }
                            }))
                        } else {
                            it
                        }
                    },
                )
            }

//            is ViewResult.GiftWallResult -> {
//                state.copy(
//                    isLoading = false,
//                    tabs = state.tabs.map {
//                        if (it is ProfileTab.GiftWall) {
//                            ProfileTab.GiftWall(userId = state.user.id, data = giftWall)
//                        } else {
//                            it
//                        }
//                    },
//                )
//            }

            is ViewResult.FollowUserResult -> state.let {
                val user = it.user.copy(followed = followed)
                UserManager.updateUserIfExist(user.id) { u ->
                    u.copy(followed = followed)
                }
                it.copy(user = user)
            }

            is ViewResult.BlackUserResult -> state.let {
                val user = it.user.copy(blacked = blacked)
                UserManager.updateUserIfExist(user.id) { u ->
                    u.copy(blacked = blacked)
                }
                it.copy(user = user)
            }

            is ViewResult.DissolveCpResult -> state.copy(tabs = state.tabs.filter {
                it !is ProfileTab.Cp
            }).also {
                processEvent(ViewEvent.GetUserProfileInfo(userId))
            }

            else -> state
        }
    }

    override fun Flow<ViewResult>.toEffects(): Flow<ViewEffect> {
        return merge(
            filterIsInstance<ViewResult.CpZoneTaskResult>().toGetCpZoneTaskEffects(),
            filterIsInstance<ViewResult.ErrorResult>().filter {
                it.toastError.isNullOrEmpty().not()
            }.mapLatest { result ->
                val toastError = result.toastError!!
                ViewEffect.Toast(if (toastError.startsWith("Child of the scoped flow")) "" else toastError)
            },
            filterIsInstance<ViewResult.RouterToResult>().toRouterToEffects(),
            filterIsInstance<ViewResult.RefreshEventResult>().toRefreshEffects(),
        )
    }

    private fun Flow<ViewEvent.ResetUserInfo>.toResetUserInfoResult(): Flow<ViewResult> {
        return mapLatest {
            ViewResult.ResetProfileResult(it.user)
        }
    }

    private fun Flow<ViewEvent.GetUserInfo>.toGetUserInfoResult(): Flow<ViewResult> {
        return onEach {
            UserManager.fetchUserById(it.userId, it.mode)
        }.flatMapLatest {
            if (it.userId == sUser.id) {
                sUserFlow.filter { user ->
                    !user.isInvalid()
                }
            } else {
                UserManager.getUserFlowById(it.userId)
            }
        }.distinctUntilChanged().map {
            ViewResult.UserResult(it as AppUser)
        }
    }

    private fun Flow<ViewEvent.OnRefreshEvent>.toRefreshEventResult(): Flow<ViewResult> {
        return mapLatest { event ->
            ViewResult.RefreshEventResult(event.tab)
        }
    }

    private fun Flow<ViewEvent.GetUserProfileInfo>.toGetUserProfileInfoResult(): Flow<ViewResult> {
        return merge(flowOf(ViewResult.LoadingResult), mapLatest { event ->
            getUserProfileUseCase(event.userId).fold({
                it
            }) {
                ViewResult.ErrorResult(it.message, isLoading = false)
            }
        })
    }

    private fun Flow<ViewEvent.GetCpZone>.toGetCpZoneResult(): Flow<ViewResult> {
        return merge(flowOf(ViewResult.LoadingResult), mapLatest { event ->
            getCpZoneUseCase(event.userId).fold({
                ViewResult.CpZoneResult(it)
            }) {
                ViewResult.ErrorResult(it.message, isLoading = false)
            }
        })
    }

//    private fun Flow<ViewEvent.GetGiftWall>.toGetGiftWallResult(): Flow<ViewResult> {
//        return merge(flowOf(ViewResult.LoadingResult), mapLatest { event ->
//            getGiftWallUseCase(event.userId).fold({
//                ViewResult.GiftWallResult(it)
//            }) {
//                ViewResult.ErrorResult(it.message, isLoading = false)
//            }
//        })
//    }

    private fun Flow<ViewEvent.OnCpCheckInClicked>.toCpCheckInResult(): Flow<ViewResult> {
        return flatMapLatest { event ->
            cpCheckInUseCase(Unit).fold({
                merge(
                    flowOf(ViewResult.CpZoneTaskResult),
                    if (it ?: event.cpTask.cpCheckedStatus) {
                        flowOf(
                            ViewResult.ErrorResult(
                                app.getString(
                                    R.string.format_success_add_value,
                                    event.cpTask.checkedHotDegree
                                )
                            )
                        )
                    } else {
                        flowOf(ViewResult.ErrorResult(app.getString(R.string.tip_signed_success)))
                    },
                )
            }) {
                flowOf(ViewResult.ErrorResult(it.message))
            }
        }
    }

    private fun Flow<ViewEvent.OnCpCheckInRemindClicked>.toCpCheckInRemindResult(): Flow<ViewResult> {
        return mapLatest { event ->
            IMCompatCore.sendMessage(event.targetId, ConversationType.C2C, MessageBundle.Text.create(app.getString(R.string.finish_task_honey)))
            ViewResult.ErrorResult(app.getString(R.string.remind_already))
        }
    }

    private fun Flow<ViewEvent.OnDissolveCpClicked>.toDissolveCpResult(): Flow<ViewResult> {
        return flatMapLatest { _ ->
            dissolveCpUseCase(Unit).fold({
                listOf(ViewResult.DissolveCpResult, ViewResult.ErrorResult(app.getString(R.string.already_cancel_cp))).asFlow()
            }) {
                flowOf(ViewResult.ErrorResult(it.message))
            }
        }
    }

    private fun Flow<ViewEvent.ToggleFollowUser>.toFollowUserResult(): Flow<ViewResult> {
        return flatMapLatest { _ ->
            val user = states.value.user
            val followed = !user.followed
            toggleFollowUseCase(user.id to followed).fold({
                if (followed) {
                    listOf(
                        ViewResult.FollowUserResult(true),
                        ViewResult.ErrorResult(app.getString(R.string.complete_follow))
                    ).asFlow()
                } else {
                    flowOf(ViewResult.FollowUserResult(false))
                }
            }) {
                flowOf(ViewResult.ErrorResult(it.message))
            }
        }
    }

    private fun Flow<ViewEvent.ToggleBlackUser>.toBlackUserResult(): Flow<ViewResult> {
        return flatMapLatest { _ ->
            val user = states.value.user
            val blacked = !user.blacked
            toggleBlackUseCase(user.id to blacked).fold({
                listOf(
                    ViewResult.BlackUserResult(blacked), ViewResult.ErrorResult(
                        if (blacked) {
                            app.getString(R.string.complete_add_backlist)
                        } else {
                            app.getString(R.string.complete_cancel_add_back)
                        }
                    )
                ).asFlow()
            }) {
                flowOf(ViewResult.ErrorResult(it.message))
            }
        }
    }

    private fun Flow<ViewEvent.OnDoTaskEvent>.toRouterToResult(): Flow<ViewResult> {
        return flatMapLatest { event ->
            doCpTaskUseCase(event.id).fold({
                if (it.action == 0) {
                    flowOf(ViewResult.ErrorResult(it.toast), ViewResult.CpZoneTaskResult)
                } else {
                    if (it.taskIsFinished) {
                        flowOf(ViewResult.CpZoneTaskResult, ViewResult.RouterToResult(it.clientJumpLink))
                    } else {
                        flowOf(ViewResult.RouterToResult(it.clientJumpLink))
                    }
                }
            }) {
                flowOf(ViewResult.ErrorResult(it.message))
            }
        }
    }

    private fun Flow<ViewResult.CpZoneTaskResult>.toGetCpZoneTaskEffects(): Flow<ViewEffect> {
        return mapLatest { _ -> ViewEffect.GetCpZoneTask }
    }

    private fun Flow<ViewResult.RouterToResult>.toRouterToEffects(): Flow<ViewEffect> {
        return mapLatest { result -> ViewEffect.RouterTo(result.link) }
    }

    private fun Flow<ViewResult.RefreshEventResult>.toRefreshEffects(): Flow<ViewEffect> {
        return mapLatest { result -> ViewEffect.Refresh(result.tab) }
    }

}