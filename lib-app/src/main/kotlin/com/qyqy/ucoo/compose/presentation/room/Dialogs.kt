

package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.constraintlayout.compose.ConstraintLayout
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.getNameColor
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.brushThemeButton
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.RelativeThreeChildren

/**
 * 赠送礼物弹窗
 */
@Composable
fun GiveGiftPanel(
    leftAvatar: String,
    rightAvatar: String,
    title: String,
    giftIcon: String,
    giftName: String,
    giftGoldCount: Int,
    buttonText: String,
    isFree: Boolean = false,
    onButtonClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 24.dp)
            .paint(painter = painterResource(id = R.drawable.bg_give_gift_dialog), contentScale = ContentScale.FillWidth)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 16.dp)
                .offset(y = (-24).dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .width((64 + 72).dp)
                    .height(72.dp)
            ) {
                val iconModifier = Modifier
                    .size(72.dp)
                    .clip(CircleShape)
                    .background(Color.White)
                    .padding(1.dp)
                CircleComposeImage(
                    model = leftAvatar, modifier = iconModifier
                )
                CircleComposeImage(
                    model = rightAvatar, modifier = iconModifier.align(Alignment.BottomEnd)
                )
            }
            Spacer(modifier = Modifier.height(16.dp))
            AppText(text = title, color = Color.White, fontSize = 12.sp)
            Spacer(modifier = Modifier.height(16.dp))
            Column(
                modifier = Modifier
                    .width(120.dp)
                    .background(
                        Brush.verticalGradient(listOf(Color(0xFF442F55), Color(0xFF3D3355))),
                        RoundedCornerShape(12.dp)
                    )
                    .padding(16.dp), horizontalAlignment = Alignment.CenterHorizontally
            ) {
                ComposeImage(model = giftIcon, contentScale = ContentScale.FillBounds, modifier = Modifier.size(88.dp))
                AppText(text = giftName, fontSize = 14.sp, color = Color.White, modifier = Modifier.padding(vertical = 8.dp))
                Row {
                    Image(
                        painter = painterResource(id = R.drawable.ic_u_coin),
                        contentDescription = "",
                        modifier = Modifier.size(12.dp)
                    )
                    Spacer(modifier = Modifier.width(3.dp))
                    AppText(text = giftGoldCount.toString(), fontSize = 12.sp, color = colorWhite50Alpha)
                }
            }
            Spacer(modifier = Modifier.height(24.dp))
            ConstraintLayout(
                modifier = Modifier
                    .height(44.dp)
                    .clip(RoundedCornerShape(50))
                    .clickable(onClick = onButtonClick)
                    .widthIn(min = 220.dp)
                    .background(brushThemeButton, RoundedCornerShape(50))
                    .padding(horizontal = 20.dp)

            ) {
                val btn = createRef()
                AppText(text = buttonText, fontSize = 16.sp, color = Color.White,
                    modifier = Modifier.constrainAs(btn) {
                        centerTo(parent)
                    })
                if (isFree) {
                    val textFree = createRef()
                    AppText(
                        text = "(${stringResource(id = R.string.free)})",
                        modifier = Modifier.constrainAs(textFree) {
                            start.linkTo(btn.end, margin = 4.dp)
                            centerVerticallyTo(parent)
                        }, color = Color(0xFFFFD97D), fontSize = 12.sp
                    )
                }
            }
            Spacer(modifier = Modifier.height(24.dp))
            Spacer(modifier = Modifier.navigationBarsPadding())
        }
    }
}

@Composable
fun BorderCircleImage(modifier: Modifier, model: Any, borderColor: Color = Color.White, borderWidth: Dp = 1.dp) {
    val iconModifier = modifier
        .clip(CircleShape)
        .background(borderColor)
        .padding(borderWidth)
    CircleComposeImage(
        model = model, modifier = iconModifier
    )
}

@Composable
fun EventOptionsPanel(
    modifier: Modifier = Modifier,
    user: User,
    ensureText: String,
    refuseText: String,
    title: String? = null,
    titleAnnotation: AnnotatedString? = title?.let { buildAnnotatedString { append(it) } },
    withNavigationBarBottom: Boolean = false,
    onEnsure: () -> Unit = {},
    onRefuse: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 24.dp)
                .then(modifier),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(68.dp))
            RelativeThreeChildren(center = {
                AppText(text = user.nickname, color = Color.White, fontSize = 16.sp)
            }, right = {
                Row {
                    Spacer(modifier = Modifier.width(4.dp))
                    AgeGender(age = user.age, isBoy = user.isBoy)
                }
            })
            Spacer(modifier = Modifier.height(32.dp))
            if (titleAnnotation != null) {
                AppText(text = titleAnnotation, color = Color.White, fontSize = 16.sp)
                Spacer(modifier = Modifier.height(32.dp))
            }
            Box(
                modifier = Modifier
                    .height(44.dp)
                    .widthIn(min = 220.dp)
                    .clickable(onClick = onEnsure)
                    .background(brushThemeButton, RoundedCornerShape(50)),
                contentAlignment = Alignment.Center
            ) {
                AppText(text = ensureText, color = Color.White, fontSize = 16.sp)
            }
            Spacer(modifier = Modifier.height(10.dp))
            AppText(
                text = refuseText,
                color = colorWhite50Alpha,
                fontSize = 16.sp,
                modifier = Modifier
                    .clickable(onClick = onRefuse)
                    .padding(10.dp)
            )
            Spacer(modifier = Modifier.height(10.dp))
            if (withNavigationBarBottom) {
                Spacer(modifier = Modifier.navigationBarsPadding())
            }
        }
        BorderCircleImage(
            modifier = Modifier
                .size(80.dp)
                .align(Alignment.TopCenter), model = user.avatarUrl
        )
    }
}

@Preview
@Composable
fun EventOptionsPanelBottomDialogPreview() {
    EventOptionsPanel(
        user = userForPreview,
        modifier = Modifier.paint(painterResource(id = R.drawable.bg_handle_bottom), contentScale = ContentScale.Fit),
        ensureText = stringResource(id = R.string.accept_inviete),
        refuseText = stringResource(id = R.string.text_refuse_hard),
        title = stringResource(id = R.string.text_invite_you_play_game),
        withNavigationBarBottom = true
    )
}


@Preview(device = "spec:width=900px,height=2340px,dpi=440")
@Composable
fun EventOptionsPanelCenterDialogPreview() {
    val u = userForPreview
    val title = stringResource(id = R.string.format_want_be_your_friends, u.nickname)
    val color = u.getNameColor()
    val titleAnnotation = remember {
        buildAnnotatedString {
            addStyle(SpanStyle(color), 0, u.nickname.length)
            append(title)
        }
    }
    EventOptionsPanel(
        user = u,
        modifier = Modifier.paint(painterResource(id = R.drawable.bg_handle_center), contentScale = ContentScale.FillWidth),
        titleAnnotation = titleAnnotation,
        ensureText = stringResource(id = R.string.add_friend_1),
        refuseText = stringResource(id = R.string.text_refuse_hard),
    )
}


@Preview
@Composable
fun GiftGiftDialogPreview() {
    GiveGiftPanel("", "", "赠送对方1个「礼物名称」礼物即可与她成为好友", "", "礼物名称", 9999, "发送好友申请", true)
}