package com.qyqy.ucoo.compose.ui.video.exo

import android.app.Application
import android.content.Context
import android.content.res.AssetFileDescriptor
import android.os.Handler
import android.view.Surface
import android.view.SurfaceHolder
import androidx.media3.common.PlaybackException
import androidx.media3.common.PlaybackParameters
import androidx.media3.common.Player
import androidx.media3.common.VideoSize
import androidx.media3.common.util.Clock
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.exoplayer.DefaultLoadControl
import androidx.media3.exoplayer.DefaultRenderersFactory
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.LoadControl
import androidx.media3.exoplayer.RenderersFactory
import androidx.media3.exoplayer.analytics.DefaultAnalyticsCollector
import androidx.media3.exoplayer.source.LoadEventInfo
import androidx.media3.exoplayer.source.MediaLoadData
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.MediaSourceEventListener
import androidx.media3.exoplayer.trackselection.DefaultTrackSelector
import androidx.media3.exoplayer.trackselection.MappingTrackSelector
import androidx.media3.exoplayer.trackselection.TrackSelector
import androidx.media3.exoplayer.upstream.DefaultBandwidthMeter
import androidx.media3.exoplayer.util.EventLogger
import site.xzwzz.video.kernel.inter.AbstractVideoPlayer
import site.xzwzz.video.kernel.utils.PlayerConstant
import site.xzwzz.video.kernel.utils.VideoLogUtils
import kotlin.concurrent.thread

@UnstableApi
class ExoMediaPlayer(context: Context) : AbstractVideoPlayer(), Player.Listener {
    protected var mAppContext: Context
    protected var mInternalPlayer: ExoPlayer? = null
    protected var mMediaSource: MediaSource? = null
    protected var mMediaSourceHelper: ExoMediaSourceHelper
    private var mSpeedPlaybackParameters: PlaybackParameters? = null
    private var mLastReportedPlaybackState = Player.STATE_IDLE
    private var mLastReportedPlayWhenReady = false
    private var mIsPreparing = false
    private var mIsBuffering = false
    private var mLoadControl: LoadControl? = null
    private var mRenderersFactory: RenderersFactory? = null
    private var mTrackSelector: TrackSelector? = null
    override fun initPlayer() {
        //创建exo播放器 create a exo player
        val builder = ExoPlayer.Builder(mAppContext)
        builder.setRenderersFactory((if (mRenderersFactory == null) DefaultRenderersFactory(mAppContext).also { mRenderersFactory = it } else mRenderersFactory)!!)
        builder.setTrackSelector((if (mTrackSelector == null) DefaultTrackSelector(mAppContext).also { mTrackSelector = it } else mTrackSelector)!!)
        builder.setLoadControl((if (mLoadControl == null) DefaultLoadControl().also { mLoadControl = it } else mLoadControl)!!)
        builder.setBandwidthMeter(DefaultBandwidthMeter.getSingletonInstance(mAppContext))
        builder.setAnalyticsCollector(DefaultAnalyticsCollector(Clock.DEFAULT))
        builder.setLooper(Util.getCurrentOrMainLooper())
//
//		mMediaSourceHelper,
//		(if (mTrackSelector == null) DefaultTrackSelector(mAppContext).also { mTrackSelector = it } else mTrackSelector)!!,
//		(if (mLoadControl == null) DefaultLoadControl().also { mLoadControl = it } else mLoadControl)!!,
//		DefaultBandwidthMeter.getSingletonInstance(mAppContext), DefaultAnalyticsCollector(Clock.DEFAULT
//		mInternalPlayer = SimpleExoPlayer.Builder(mAppContext,
//			,
//			(if (mLoadControl == null) DefaultLoadControl().also { mLoadControl = it } else mLoadControl)!!,
//			DefaultBandwidthMeter.getSingletonInstance(mAppContext),
//			Util.getLooper(),
//			AnalyticsCollector(Clock.DEFAULT), /* useLazyPreparation= */
//			true,
//			Clock.DEFAULT).build()

        mInternalPlayer = builder.build()

        setOptions()

        //播放器日志 player log setting
        if (VideoLogUtils.isIsLog && mTrackSelector is MappingTrackSelector) {
            mInternalPlayer!!.addAnalyticsListener(EventLogger(mTrackSelector as MappingTrackSelector?, "ExoPlayer"))
        }
        initListener()
    }

    /**
     * exo视频播放器监听监听器
     * exo player init listener
     */
    private fun initListener() {
        mInternalPlayer?.addListener(this)
    }

    fun setTrackSelector(trackSelector: TrackSelector?) {
        mTrackSelector = trackSelector
    }

    fun setRenderersFactory(renderersFactory: RenderersFactory?) {
        mRenderersFactory = renderersFactory
    }

    fun setLoadControl(loadControl: LoadControl?) {
        mLoadControl = loadControl
    }

    /***
     * 设置播放地址
     * @param 路径播放地址 path
     * @param headers 播放地址请求头 headers
     */
    override fun setDataSource(path: String, headers: Map<String, String>?) {
        //设置数据源 set video source
        if (path.isEmpty()) {
            if (mPlayerEventListener != null) {
                mPlayerEventListener!!.onInfo(PlayerConstant.MEDIA_INFO_URL_NULL, 0)
            }
            return
        }
        mMediaSource = mMediaSourceHelper.getMediaSource(path, headers)
    }

    override fun setDataSource(fd: AssetFileDescriptor?) {
        //no support
    }

    /*** 准备开始播放（异步）
     */
    override fun prepareAsync() {
        if (mInternalPlayer == null) {
            return
        }
        if (mMediaSource == null) {
            return
        }
        if (mSpeedPlaybackParameters != null) {
            mInternalPlayer!!.setPlaybackParameters(mSpeedPlaybackParameters!!)
        }
        mIsPreparing = true
        mMediaSource!!.addEventListener(Handler(), mMediaSourceEventListener)
        //准备播放 prepare to play
        mInternalPlayer!!.prepare(mMediaSource!!)
    }

    /*** 播放
     */
    override fun start() {
        mInternalPlayer?.playWhenReady = true
    }

    /*** 暂停
     */
    override fun pause() {
        mInternalPlayer?.playWhenReady = false
    }

    /*** 停止
     */
    override fun stop() {
        mInternalPlayer?.stop()
    }

    private val mMediaSourceEventListener: MediaSourceEventListener = object : MediaSourceEventListener {
//		override fun onReadingStarted(windowIndex: Int, mediaPeriodId: MediaPeriodId) {
//			if (mPlayerEventListener != null && mIsPreparing) {
//				mPlayerEventListener!!.onPrepared()
//			}
//		}

        override fun onLoadStarted(windowIndex: Int, mediaPeriodId: MediaSource.MediaPeriodId?, loadEventInfo: LoadEventInfo, mediaLoadData: MediaLoadData) {
            if (mPlayerEventListener != null && mIsPreparing) {
                mPlayerEventListener!!.onPrepared()
            }
        }

    }

    init {
        mAppContext = context as? Application ?: context.applicationContext
        mMediaSourceHelper = ExoMediaSourceHelper.getInstance(context)
    }

    /*** 重置播放器
     */
    override fun reset() {
        mInternalPlayer?.let { player ->
            player.stop()
            player.clearMediaItems()
            player.setVideoSurface(null)
            mIsPreparing = false
            mIsBuffering = false
            mLastReportedPlaybackState = Player.STATE_IDLE
            mLastReportedPlayWhenReady = false
        }
    }

    override fun isPlaying(): Boolean {
        val player = mInternalPlayer ?: return false
        val state = player.playbackState
        return when (state) {
            Player.STATE_BUFFERING, Player.STATE_READY -> player.playWhenReady
            Player.STATE_IDLE, Player.STATE_ENDED -> false
            else -> false
        }
    }

    /*** 调整图纸
     */
    override fun seekTo(time: Long) {
        mInternalPlayer?.seekTo(time)
    }

    /*** 释放播放器
     */
    override fun release() {
        mInternalPlayer?.let { player ->
            player.removeListener(this)
            val p = player
            //异步释放，防止卡顿
            p.release()
        }

        mIsPreparing = false
        mIsBuffering = false
        mLastReportedPlaybackState = Player.STATE_IDLE
        mLastReportedPlayWhenReady = false
        mSpeedPlaybackParameters = null
    }

    override fun getCurrentPosition(): Long = mInternalPlayer?.currentPosition ?: 0

    override fun getDuration(): Long = mInternalPlayer?.duration ?: 0

    override fun getBufferedPercentage(): Int = mInternalPlayer?.bufferedPercentage ?: 0

    /*** 设置渲染视频的View，主要用于SurfaceView
     */
    override fun setSurface(surface: Surface?) {
        if (surface != null) {
            try {
                mInternalPlayer?.setVideoSurface(surface)
            } catch (e: Exception) {
                mPlayerEventListener!!.onError(PlayerConstant.ErrorType.TYPE_UNEXPECTED, e.message)
            }
        }
    }

    override fun setDisplay(holder: SurfaceHolder?) {
        if (holder == null) {
            setSurface(null)
        } else {
            setSurface(holder.surface)
        }
    }

    /***音量设置
     */
    override fun setVolume(leftVolume: Float, rightVolume: Float) {
        mInternalPlayer?.volume = (leftVolume + rightVolume) / 2
    }

    /*** 设置是否循环播放
     */
    override fun setLooping(isLooping: Boolean) {
        mInternalPlayer?.repeatMode = if (isLooping) Player.REPEAT_MODE_ALL else Player.REPEAT_MODE_OFF
    }

    override fun setOptions() {
        //准备好就开始播放
        mInternalPlayer?.playWhenReady = true
    }

    override fun setSpeed(speed: Float) {
        val playbackParameters = PlaybackParameters(speed)
        mSpeedPlaybackParameters = playbackParameters
        mInternalPlayer?.setPlaybackParameters(playbackParameters)
    }

    override fun getSpeed(): Float = if (mSpeedPlaybackParameters != null) {
        mSpeedPlaybackParameters!!.speed
    } else 1f


    override fun getTcpSpeed(): Long = 0

    override fun onPlayerStateChanged(playWhenReady: Boolean, playbackState: Int) {
        if (mPlayerEventListener == null) {
            return
        }
        if (mIsPreparing) {
            return
        }
        if (mLastReportedPlayWhenReady != playWhenReady || mLastReportedPlaybackState != playbackState) {
            when (playbackState) {
                Player.STATE_IDLE -> {}
                Player.STATE_BUFFERING -> {
                    mPlayerEventListener!!.onInfo(PlayerConstant.MEDIA_INFO_BUFFERING_START, getBufferedPercentage())
                    mIsBuffering = true
                }

                Player.STATE_READY -> if (mIsBuffering) {
                    mPlayerEventListener!!.onInfo(PlayerConstant.MEDIA_INFO_BUFFERING_END, getBufferedPercentage())
                    mIsBuffering = false
                }

                Player.STATE_ENDED -> mPlayerEventListener!!.onCompletion()
                else -> {}
            }
            mLastReportedPlaybackState = playbackState
            mLastReportedPlayWhenReady = playWhenReady
        }
    }


    override fun onPlayerError(error: PlaybackException) {
        super.onPlayerError(error)
        mPlayerEventListener?.onError(PlayerConstant.ErrorType.TYPE_UNEXPECTED, error.errorCodeName + error.message)

//		if (mPlayerEventListener != null) {
//			val type = error.errorCode
//			if (type == ExoPlaybackException.TYPE_SOURCE) {
//				//错误的链接 error source
//			} else if (type == ExoPlaybackException.TYPE_RENDERER || type == ExoPlaybackException.TYPE_UNEXPECTED || type == ExoPlaybackException.TYPE_REMOTE) {
//				mPlayerEventListener!!.onError(PlayerConstant.ErrorType.TYPE_UNEXPECTED, error.message)
//			}
//		}
    }

    override fun onVideoSizeChanged(videoSize: VideoSize) {
        super.onVideoSizeChanged(videoSize)
        if (mPlayerEventListener != null) {
            mPlayerEventListener!!.onVideoSizeChanged(videoSize.width, videoSize.height)
            if (videoSize.unappliedRotationDegrees > 0) {
                mPlayerEventListener!!.onInfo(PlayerConstant.MEDIA_INFO_VIDEO_ROTATION_CHANGED, videoSize.unappliedRotationDegrees)
            }
        }
    }

    override fun onRenderedFirstFrame() {
        if (mPlayerEventListener != null && mIsPreparing) {
            mPlayerEventListener!!.onInfo(PlayerConstant.MEDIA_INFO_VIDEO_RENDERING_START, 0)
            mIsPreparing = false
        }
    }

}