package com.qyqy.ucoo.compose.domain.usecase.moment

import com.overseas.common.utils.getTimeZoneNum
import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.moment.Moment
import com.qyqy.ucoo.moment.MomentApi
import com.qyqy.ucoo.sUser
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import java.util.TimeZone


class GetMomentListUseCase constructor(
    private val api: MomentApi = createApi(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Pair<String?, Int>, List<Moment>>(ioDispatcher) {

    override suspend fun execute(parameters: Pair<String?, Int>): Result<List<Moment>> {
        return runApiCatching {
            if (parameters.first.isNullOrEmpty() || parameters.first == sUser.id) {
                api.getMyMoments(TimeZone.getDefault().getTimeZoneNum(), parameters.second)
            } else {
                api.getOthersMoments(parameters.first!!, TimeZone.getDefault().getTimeZoneNum(), parameters.second)
            }
        }.map { list ->
            list.moments.onEach {
                val id = it.id
                it.mediaEntries.onEachIndexed { index, mediaInfo ->
                    mediaInfo.id = "${id}@@@${index}"
                }
            }
        }
    }

}