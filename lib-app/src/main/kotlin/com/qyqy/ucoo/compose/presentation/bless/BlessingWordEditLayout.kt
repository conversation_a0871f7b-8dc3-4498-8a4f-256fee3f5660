package com.qyqy.ucoo.compose.presentation.bless

import android.content.Context
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.core.content.ContentProviderCompat.requireContext
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorSecondBlack
import com.qyqy.ucoo.compose.theme.themeButtonColors
import com.qyqy.ucoo.compose.ui.ComposeDialog


@Composable
fun BlessingWordEditLayout(
    lastBlessWord: String = "",
    title: String = stringResource(R.string.input_bless_word),
    placeholder: String = stringResource(R.string.hint_bless_word),
    hint: String = stringResource(R.string.tip_bless),
    onButtonClick: (String) -> Unit = {}
) {
    var blessWord by remember {
        mutableStateOf(lastBlessWord)
    }
    val colorText = Color(0x80FFFFFF)
    val textLimit by remember {
        derivedStateOf { "${blessWord.length}/20" }
    }
    Column(
        modifier = Modifier
            .background(colorSecondBlack, Shapes.small)
            .padding(16.dp, 20.dp)
    ) {
        Text(
            text = title,
            color = Color.White,
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(bottom = 12.dp),
            textAlign = TextAlign.Center,
            fontSize = 17.sp
        )
        Box(
            modifier = Modifier
                .background(color = Color(0x0DFFFFFF), shape = RoundedCornerShape(2.dp))
                .fillMaxWidth(1f)
                .height(96.dp),
        ) {
            MaterialTheme(colorScheme = MaterialTheme.colorScheme.copy(surfaceVariant = Color.Transparent, onBackground = Color.White)) {
                TextField(
                    value = blessWord,
                    onValueChange = {
                        blessWord = it.take(20)
                    },
                    placeholder = { Text(text = placeholder, fontSize = 14.sp, color = colorText) },
                    colors = TextFieldDefaults.colors(
                        focusedIndicatorColor = Color.Transparent, // 设置聚焦时的横线颜色为透明
                        unfocusedIndicatorColor = Color.Transparent, // 设置非聚焦时的横线颜色为透明
                        disabledIndicatorColor = Color.Transparent // 设置禁用时的横线颜色为透明
                    ),
                    textStyle = TextStyle(
                        textDecoration = TextDecoration.None,
                        color = Color.White,
                        background = Color.Transparent,
                        textAlign = TextAlign.Start
                    ),
                    modifier = Modifier
                        .fillMaxSize(1f),

                    )
            }

            Text(
                text = textLimit, color = Color(0xBFFFFFFF), modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .offset((-8).dp, (-8).dp)
            )
        }
        Text(
            text = hint,
            lineHeight = 16.sp, fontSize = 13.sp,
            color = Color(0x80FFFFFF),
            modifier = Modifier.padding(top = 12.dp, bottom = 20.dp)
        )
        Button(
            onClick = {
                onButtonClick(blessWord)
            },
            colors = ButtonDefaults.themeButtonColors(),
            enabled = blessWord.isNotEmpty(),
            modifier = Modifier
                .fillMaxWidth(1f)
//                .graphicsLayer {
//                    alpha = if (blessWord.isNotEmpty()) 1f else 0.5f
//                }
        ) {
            Text(text = stringResource(R.string.confirm_and_give), fontSize = 16.sp)
        }
    }
}

fun showBlessWordEditDialog(context: Context,
                            defaultText: String,
                            callback: (blessWord: String) -> Unit) {
    object : ComposeDialog(context) {
        @Composable
        override fun Content() {
            BlessingWordEditLayout(defaultText) { bless ->
                callback(bless)
                dismiss()
            }
        }
    }.show()
}

@Preview(device = "spec:width=270dp,height=850.9dp,dpi=440")
@Composable
fun BlessingWordEditLayoutPreviewer() {
    AppTheme {
        BlessingWordEditLayout()
    }
}