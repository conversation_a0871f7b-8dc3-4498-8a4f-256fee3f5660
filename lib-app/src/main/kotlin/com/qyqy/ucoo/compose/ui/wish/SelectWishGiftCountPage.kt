package com.qyqy.ucoo.compose.ui.wish

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.profile.wish.C2CWishViewModel
import com.qyqy.cupid.ui.profile.wish.EditWishEntry
import com.qyqy.cupid.ui.profile.wish.WishViewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import kotlin.math.abs

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun SelectWishGiftCountPage(
    editWishEntryState: MutableState<EditWishEntry>,
    wishViewModel: WishViewModel,
) {
    val wishConfig = wishViewModel.wishListConfig

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = Color.White, shape = Shapes.corner12)
            .padding(horizontal = 8.dp, vertical = 16.dp)
    ) {
        val editWishEntry = editWishEntryState.value

        FlowRow(
            modifier = Modifier
                .padding(top = 8.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp),
            maxItemsInEachRow = 2,
        ) {
            wishConfig.giftRecommendedCount.forEach {
                val selected = if (editWishEntry.inputCount == -1) {
                    editWishEntry.selectCount == it
                } else {
                    false
                }

                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(32.dp)
                        .clip(CircleShape)
                        .background(if (selected) Color(0xFFE2B2FF) else Color(0xFFF6E8FF))
                        .clickable {
                            editWishEntryState.value = editWishEntry.copy(
                                selectCount = it,
                                inputCount = -1,
                            )
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "$it",
                        color = if (selected) Color(0xFFBC4FFF) else Color(0xFFD594FF),
                        fontSize = 12.sp
                    )
                }
            }
        }
        Text(
            text = stringResource(id = R.string.选择自定义数量),
            modifier = Modifier.padding(top = 16.dp),
            color = Color(0xFF1D2129),
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium
        )

        Box(
            modifier = Modifier
                .padding(top = 10.dp, bottom = 6.dp)
                .fillMaxWidth()
                .height(32.dp)
                .background(Color(0xFFF1F2F3), CircleShape)
                .padding(horizontal = 16.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            AppBasicTextField(
                value = editWishEntry.inputCountText,
                onValueChange = { value ->
                    if (value.isEmpty()) {
                        editWishEntryState.value = editWishEntry.copy(inputCount = -1)
                    } else {
                        value.toIntOrNull()?.let {
                            abs(it)
                        }?.takeIf {
                            it <= wishConfig.giftCountLimit
                        }?.also {
                            editWishEntryState.value = editWishEntry.copy(inputCount = it)
                        }
                    }
                },
                modifier = Modifier.fillMaxWidth(),
                hintValue = stringResource(id = R.string.请输入数量, wishConfig.giftCountLimit),
                textStyle = TextStyle(
                    color = Color(0xFF1D2129),
                    fontSize = 14.sp,
                ),
                hintStyle = TextStyle(
                    color = Color(0xFF86909C),
                    fontSize = 14.sp,
                ),
                maxLines = 1,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Done),
            )
        }
    }
}


@Preview
@Composable
private fun PreviewSelectWishGiftCountPage() {
    PreviewCupidTheme {
        SelectWishGiftCountPage(remember {
            mutableStateOf(EditWishEntry())
        }, C2CWishViewModel.Preview)
    }
}