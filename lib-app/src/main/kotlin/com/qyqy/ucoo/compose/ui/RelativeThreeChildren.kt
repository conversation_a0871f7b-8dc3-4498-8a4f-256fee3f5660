package com.qyqy.ucoo.compose.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.Layout
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout

@Composable
fun RelativeThreeChildren(
    modifier: Modifier = Modifier,
    center: @Composable () -> Unit = {},
    left: @Composable () -> Unit = {},
    right: @Composable () -> Unit = {}
) {
    ConstraintLayout(modifier = modifier) {
        val (leftWidget, centerWidget, rightWidget) = createRefs()

        Box(modifier = Modifier.constrainAs(centerWidget) {
            centerTo(parent)
        }) {
            center()
        }

        Box(modifier = Modifier.constrainAs(leftWidget) {
            centerVerticallyTo(parent)
            end.linkTo(centerWidget.start)
        }) {
            left()
        }

        Box(modifier = Modifier.constrainAs(rightWidget) {
            start.linkTo(centerWidget.end)
            centerVerticallyTo(parent)
        }) {
            right()
        }
    }
}

@Preview
@Composable
fun ThreeLayoutPreview() {
    RelativeThreeChildren(modifier = Modifier.fillMaxWidth(), center = {
        Box(
            modifier = Modifier
                .size(160.dp, 50.dp)
                .background(Color.Blue), contentAlignment = Alignment.Center
        ) {
            AppText(text = "中", color = Color.White)

        }
    }, left = {
        Box(
            modifier = Modifier
                .size(20.dp, 50.dp)
                .background(Color.Red), contentAlignment = Alignment.Center
        ) {
            AppText(text = "左", color = Color.White)
        }
    }) {
        Box(
            modifier = Modifier
                .size(90.dp, 50.dp)
                .background(Color.Green), contentAlignment = Alignment.Center
        ) {
            AppText(text = "右", color = Color.White)
        }
    }
}


@Composable
fun RelativeThreeChildren2(
    modifier: Modifier = Modifier,
    center: @Composable BoxScope.() -> Unit = {},
    left: @Composable BoxScope.() -> Unit = {},
    right: @Composable BoxScope.() -> Unit = {},
) {
    Layout(
        content = {
            Box(contentAlignment = Alignment.CenterEnd) {
                left()
            }
            Box(contentAlignment = Alignment.Center) {
                center()
            }
            Box(contentAlignment = Alignment.CenterStart) {
                right()
            }
        },
        modifier = modifier
    ) { measurables, constraints ->
        check(measurables.size == 3)

        val contentConstraints = constraints.copy(minWidth = 0, minHeight = 0)

        val centerPlaceable = measurables[1].measure(contentConstraints)

        val sideMaxWidth = constraints.maxWidth.minus(centerPlaceable.width).div(2)

        val leftHeight = measurables.first().minIntrinsicHeight(sideMaxWidth)
        val rightHeight = measurables.last().minIntrinsicHeight(sideMaxWidth)

        val sideConstraints = contentConstraints.copy(
            maxWidth = sideMaxWidth,
            minHeight = maxOf(leftHeight, centerPlaceable.height, rightHeight),
            maxHeight = maxOf(leftHeight, centerPlaceable.height, rightHeight),
        )

        val leftPlaceable = measurables.first().measure(sideConstraints)

        val rightPlaceable = measurables.last().measure(sideConstraints)

        val layoutWidth = leftPlaceable.width.plus(centerPlaceable.width).plus(rightPlaceable.width)
            .coerceAtLeast(constraints.minWidth)

        val layoutHeight = maxOf(
            leftPlaceable.height,
            centerPlaceable.height,
            rightPlaceable.height
        ).coerceAtLeast(constraints.minHeight)

        layout(layoutWidth, layoutHeight) {
            leftPlaceable.placeRelative(sideMaxWidth.minus(leftPlaceable.width), 0)
            centerPlaceable.placeRelative(sideMaxWidth, layoutHeight.minus(centerPlaceable.height).div(2))
            rightPlaceable.placeRelative(layoutWidth.minus(sideMaxWidth), 0)
        }
    }
}


@Preview
@Composable
fun ThreeLayoutPreview2() {
    RelativeThreeChildren(modifier = Modifier.fillMaxWidth(), center = {
        Box(
            modifier = Modifier
                .size(160.dp, 50.dp)
                .background(Color.Blue), contentAlignment = Alignment.Center
        ) {
            AppText(text = "中", color = Color.White)

        }
    }, left = {
        Box(
            modifier = Modifier
                .size(20.dp, 50.dp)
                .background(Color.Red), contentAlignment = Alignment.Center
        ) {
            AppText(text = "左", color = Color.White)
        }
    }) {
        Box(
            modifier = Modifier
                .size(90.dp, 50.dp)
                .background(Color.Green), contentAlignment = Alignment.Center
        ) {
            AppText(text = "右", color = Color.White)
        }
    }
}