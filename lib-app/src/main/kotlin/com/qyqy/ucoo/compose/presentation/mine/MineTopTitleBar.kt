package com.qyqy.ucoo.compose.presentation.mine

import android.content.Intent
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.enableClick
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.mine.EditUserInfoActivity
import com.qyqy.ucoo.mine.UserProfileActivity
import com.qyqy.ucoo.setting.SettingActivity


@Composable
fun TopTitleBar(
    user: User,
    backgroundColor: Color,
    scrollFaction: Float,
    modifier: Modifier = Modifier,
) {
    val context = LocalContext.current
    Row(
        modifier = modifier
            .drawBehind {
                drawRect(backgroundColor.copy(alpha = scrollFaction))
            }
            .fillMaxWidth()
            .enableClick()
            .padding(vertical = 12.dp, horizontal = 16.dp)
            .windowInsetsPadding(WindowInsets.statusBars),
        verticalAlignment = Alignment.CenterVertically
    ) {
        AutoSizeText(
            text = user.nickname,
            modifier = Modifier
                .graphicsLayer {
                    alpha = if (scrollFaction < 0.9f) {
                        0f
                    } else {
                        scrollFaction
                            .minus(0.9f)
                            .div(0.1f)
                    }
                }
                .clickable(enabled = scrollFaction >= 0.9f) {
                    context.startActivity(UserProfileActivity.createIntent(context, user))
                },
            color = Color.White,
            fontSize = 17.sp,
            maxLines = 1,
            fontWeight = FontWeight.Bold,
            alignment = Alignment.CenterStart
        )
        Spacer(modifier = Modifier.weight(1f))
        Button(
            onClick = {
                context.startActivity(Intent(context, EditUserInfoActivity::class.java))
            },
            modifier = Modifier.size(68.dp, 28.dp),
            shape = Shapes.medium,
            colors = ButtonDefaults.buttonColors(containerColor = Color(0x806A6A6A)),
            contentPadding = PaddingValues(horizontal = 5.dp)
        ) {
            AutoSizeText(text = stringResource(id = R.string.edit_info), color = Color.White, fontSize = 12.sp)
        }
        IconButton(
            onClick = {
                context.startActivity(Intent(context, SettingActivity::class.java))
            },
            modifier = Modifier
                .padding(start = 5.dp)
                .size(24.dp, 24.dp)
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_settings),
                contentDescription = null,
                tint = Color.Unspecified
            )
        }
    }
}