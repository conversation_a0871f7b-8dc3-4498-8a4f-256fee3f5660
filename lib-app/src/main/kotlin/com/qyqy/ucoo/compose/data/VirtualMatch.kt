package com.qyqy.ucoo.compose.data


import android.graphics.drawable.Drawable
import android.os.Parcelable
import com.qyqy.ucoo.glide.isRecycled
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Parcelize
@Serializable
data class VirtualMatchInfo(
    @SerialName("match_cost")
    val matchCost: Int = 0,
    @SerialName("tags")
    val tags: List<Tag> = emptyList(),
    @SerialName("users")
    val users: List<String> = emptyList(),
) : Parcelable

@Parcelize
@Serializable
data class Tag(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("img_url")
    val imgUrl: String = "",
    @SerialName("name")
    val name: String = "",
    @SerialName("selected_img_url")
    val selectedImgUrl: String = ""
) : Parcelable {

    @Transient
    @IgnoredOnParcel
    var drawable: Drawable? = null
        get() {
            if (field != null && field?.isRecycled == false) {
                return field
            }
            field = null
            return null
        }

    @Transient
    @IgnoredOnParcel
    var selectedDrawable: Drawable? = null
        get() {
            if (field != null && field?.isRecycled == false) {
                return field
            }
            field = null
            return null
        }
}