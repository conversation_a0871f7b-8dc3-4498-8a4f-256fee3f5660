package com.qyqy.ucoo.compose.data

import android.os.Parcelable
import androidx.compose.runtime.Composable
import androidx.compose.runtime.ReadOnlyComposable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.CpCardEffectInfo
import com.qyqy.ucoo.account.CpHouseBasicInfo
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.ifEmpty
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.widget.LoadingDrawable
import kotlinx.collections.immutable.persistentListOf
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.Serializable

sealed class Relationship {

    sealed class Cp(
        open val self: User,
        open val target: User?,
        open val cpValue: String?,
        open val cpLevel: Int,
        open val cpDurationDay: String?,
        open val cpLogoUrl: String,
        open val gradientTopColor: Color = Color(0xFFF6E7EA),
        open val gradientBottomColor: Color = Color(0xFFFFCCD6),
        open val cardBgUrl: String?,
        open val cpCardEffect: CpCardEffectInfo?,
        open val cpHouse:CpHouseBasicInfo?
    ) {

        abstract val name1: String?

        abstract val name2: String?

        abstract val avatar1: Any?

        abstract val avatar2: Any?

        fun hasPublicCp() = if (this is None) {
            false
        } else {
            cpLevel > 0
        }

        companion object {
            fun previewSimple1(): Cp {
                return Normal(userForPreview, userForPreview, "857395", 0, "在一起34天", "", Color(0xFFF6E7EA), Color(0xFFFFCCD6), null,null,CpHouseBasicInfo())
            }

            fun previewSimple2(): Cp {
                return None(userForPreview, "")
            }
        }

        data class None(override val self: User, override val cardBgUrl: String? = self.cpCardBackground,override val cpCardEffect: CpCardEffectInfo? = self.cpCardRingInfo) :
            Cp(self, null, null, 0, null, "", cardBgUrl = cardBgUrl,cpCardEffect= cpCardEffect, cpHouse = null) {

            override val name1: String?
                get() = null

            override val name2: String?
                get() = null

            override val avatar1: Any
                get() = self.avatarUrl

            override val avatar2: Any?
                get() = null
        }

        data class Normal constructor(
            override val self: User,
            override val target: User,
            override val cpValue: String,
            override val cpLevel: Int,
            override val cpDurationDay: String,
            override val cpLogoUrl: String,
            override val gradientTopColor: Color,
            override val gradientBottomColor: Color,
            override val cardBgUrl: String?,
            override val cpCardEffect: CpCardEffectInfo?,
            override val cpHouse:CpHouseBasicInfo?
        ) : Cp(self, target, cpValue, cpLevel, cpDurationDay, cpLogoUrl, cardBgUrl = cardBgUrl,cpCardEffect=cpCardEffect, cpHouse = cpHouse) {

            override val name1: String
                get() = self.nickname

            override val name2: String
                get() = target.nickname

            override val avatar1: Any
                get() = self.avatarUrl

            override val avatar2: Any
                get() = target.avatarUrl
        }

        fun hasUser(userId:Int) = self.userId == userId || target?.userId == userId
    }


    sealed class Label(
        open val label: LabelItem?,
        open val user: User?,
        open val labelColor: Color?,
        open val textColor: Color,
        open val gradientTopColor: Color?,
        open val gradientBottomColor: Color?,
    ) {

        @get:Composable
        @get:ReadOnlyComposable
        abstract val name: String

        @get:Composable
        abstract val avatar: Any

        companion object {
            fun previewSimple(): Normal {
                return Normal(
                    LabelItem(name = "大哥", intimacyValue = "亲密度9999999999"),
                    AppUser(nickname = "幼儿园"),
                    Color(0xFF9CD6FF),
                    Color(0xFF549ACD),
                    Color(0xFFD8F1FF),
                    Color(0xFFAFDDFF)
                )
            }
        }


        object Placeholder : Label(null, null, null, Color(0xFF9184A3), null, null) {

            override val name: String
                @Composable
                @ReadOnlyComposable
                get() = "loading"

            override val avatar: Painter
                @Composable
                get() = rememberDrawablePainter(LoadingDrawable(LocalDensity.current.run {
                    3.dp.toPx()
                }))
        }

        object None : Label(null, null, null, Color(0xFF9184A3), null, null) {
            override val name: String
                @Composable
                @ReadOnlyComposable
                get() = stringResource(id = R.string.邀请亲友)

            override val avatar: Painter
                @Composable
                get() = painterResource(id = R.drawable.ic_user_relationship_add)
        }

        data class Normal(
            override val label: LabelItem,
            override val user: User,
            override val labelColor: Color,
            override val textColor: Color,
            override val gradientTopColor: Color,
            override val gradientBottomColor: Color,
        ) : Label(label, user, labelColor, textColor, gradientTopColor, gradientBottomColor) {

            override val name: String
                @Composable
                @ReadOnlyComposable
                get() = user.nickname

            override val avatar: Any
                @Composable
                get() = user.avatarUrl
        }
    }

}

fun AppUser.toCpRelationship(): Relationship.Cp {
    val user = this
    val cpExtraInfo = user.cpExtraInfo
    val cp = user.publicCP ?: user.cp
    return if (cp != null) {
        Relationship.Cp.Normal(
            user,
            cp,
            cpExtraInfo?.cpValue.orDefault(0).toString(),
            cpExtraInfo?.levelInfo?.level.orDefault(1),
            cpExtraInfo?.togatherDays.ifEmpty(app.getString(R.string.在一起N天, 1)),
            cpExtraInfo?.levelInfo?.headerImgUrl.orEmpty(),
            Color(0xFFF6E7EA),
            Color(0xFFFFCCD6),
            user.cpCardBackground,
            user.cpCardRingInfo,
            user.cpRoomInfo
        )
    } else {
        Relationship.Cp.None(user, user.cpCardBackground)
    }
}


data class RelationshipGraph constructor(
    val cpRelationship: Relationship.Cp,
    val envType: Int = 0, // mine = 0, self = 1 , other = 2
    val emptyCount: Int = 6,
    val totalCount: Int = emptyCount,
    val maxCount: Int = Int.MAX_VALUE,
    val pricePerLabel: Int = 5000,
    val labelList: List<Relationship.Label.Normal> = persistentListOf(),
    val labelTagList: List<LabelItem> = persistentListOf(),
    val ruleLink: String = "",
    val placeholderCpVisible: Boolean = false,
    val placeholderLabelVisible: Boolean = true,
) {
    constructor(
        cpGraph: CpGraph,
        familyGraph: FamilyGraph,
    ) : this(
        cpGraph.cpRelationship,
        cpGraph.envType,
        familyGraph.emptyCount,
        familyGraph.totalCount,
        familyGraph.maxCount,
        familyGraph.pricePerLabel,
        familyGraph.labelList,
        familyGraph.labelTagList,
        familyGraph.ruleLink,
        familyGraph.placeholderCpVisible,
        familyGraph.placeholderLabelVisible,
    )

    val hasLabel = totalCount > emptyCount || labelList.isNotEmpty()

    val placeholderCount = (totalCount - labelList.size - emptyCount).coerceAtLeast(0)

    val hasUnlockMore = totalCount < maxCount

    val hasBottomAction = envType < 2 && (hasLabel || hasUnlockMore)
}

data class CpGraph constructor(
    val cpRelationship: Relationship.Cp,
    val userId: String,
    val envType: Int = 0, // mine = 0, self = 1 , other = 2
)

data class FamilyGraph constructor(
    val emptyCount: Int = 6,
    val totalCount: Int = emptyCount,
    val maxCount: Int = Int.MAX_VALUE,
    val pricePerLabel: Int = 5000,
    val labelList: List<Relationship.Label.Normal> = persistentListOf(),
    val labelTagList: List<LabelItem> = persistentListOf(),
    val ruleLink: String = "",
    val placeholderCpVisible: Boolean = false,
    val placeholderLabelVisible: Boolean = true,
)

@Serializable
@Parcelize
data class LabelItem constructor(
    val relativeId: Int = 0,
    val id: Int = 0,
    val name: String,
    val intimacyValue: String = "亲密度0",
    val levelValue: String = "Lv.1",
) : Parcelable {

    @IgnoredOnParcel
    val labelName = "$name$levelValue"

}