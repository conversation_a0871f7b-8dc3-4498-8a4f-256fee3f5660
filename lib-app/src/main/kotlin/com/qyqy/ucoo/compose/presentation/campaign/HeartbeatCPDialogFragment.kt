package com.qyqy.ucoo.compose.presentation.campaign

import android.app.Dialog
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.appcompat.app.AppCompatDialog
import androidx.compose.runtime.Composable
import androidx.core.view.WindowCompat
import androidx.core.view.setPadding
import androidx.fragment.app.viewModels
import com.overseas.common.ext.findParentOwner
import com.overseas.common.ext.safeDismiss
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.ComposeDialogFragment
import com.qyqy.ucoo.im.bean.CPRightPageInfo
import com.qyqy.ucoo.im.chat.IChatViewModelStoreOwner
import com.qyqy.ucoo.im.room.CpViewModel
import com.qyqy.ucoo.widget.dialog.CPRightPage

class HeartbeatCPDialogFragment : ComposeDialogFragment(), CPRightPage {
    companion object {
        private const val KEY_CP_RIGHT_INFO = "cp_right_info"
        fun newInstance(cpRightPageInfo: CPRightPageInfo) = HeartbeatCPDialogFragment().apply {
            arguments = Bundle().also {
                it.putParcelable(KEY_CP_RIGHT_INFO, cpRightPageInfo)
            }
        }
    }

    override val cpViewModel by viewModels<CpViewModel>({
        findParentOwner<IChatViewModelStoreOwner>() ?: requireActivity()
    })
    private val cpRightPageInfo: CPRightPageInfo? by lazy { arguments?.getParcelable(KEY_CP_RIGHT_INFO) }

    override fun configWindow(window: Window) {
        super.configWindow(window)
        window.decorView.setPadding(0)
        window.decorView.background = ColorDrawable(Color.TRANSPARENT)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        return object :AppCompatDialog(requireContext(), R.style.AppBaseBottomDialog){
            override fun onCreate(savedInstanceState: Bundle?) {
                super.onCreate(savedInstanceState)
                window?.also {
                    WindowCompat.setDecorFitsSystemWindows(it,false)
                    it.setLayout(WindowManager.LayoutParams.MATCH_PARENT, WindowManager.LayoutParams.MATCH_PARENT)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        cpRightPageInfo ?: safeDismiss()
    }

    override val content: @Composable () -> Unit = {
        cpRightPageInfo?.also { info ->
            HeartBeatCP(info = info, onClick = {
                performCPAction(info.targetUser.id, info.inviteCode)
                safeDismiss()
            }, onBack = {
                safeDismiss()
            })
        }
    }
}