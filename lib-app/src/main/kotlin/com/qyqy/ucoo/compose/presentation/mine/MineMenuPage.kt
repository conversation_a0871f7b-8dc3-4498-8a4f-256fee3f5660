package com.qyqy.ucoo.compose.presentation.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.clickable
import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.layout
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.mine.cell.MineCell
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.map
import kotlin.math.roundToInt

@Composable
fun MineMenuPage(
    list: List<MineCell>,
    modifier: Modifier = Modifier,
    onClickItem: (Int) -> Boolean,
) {
    val context = LocalContext.current
    BoxWithConstraints(
        modifier = modifier.fillMaxWidth(),
    ) {
        val itemWidth = constraints.maxWidth.div(4f).roundToInt()
        val scrollState = rememberScrollState()
        LaunchedEffect(scrollState) {
            snapshotFlow { scrollState.isScrollInProgress to scrollState.value }
                .distinctUntilChangedBy {
                    it.first
                }
                .filter {
                    it.first.not()
                }
                .map {
                    it.second
                }
                .collectLatest { value ->
                    val offset = value.rem(itemWidth)
                    if (offset > 0) {
                        val index = value.div(itemWidth)
                        if (offset >= itemWidth.div(2)) {
                            scrollState.animateScrollTo(itemWidth.times(index.plus(1)))
                        } else {
                            scrollState.animateScrollTo(itemWidth.times(index))
                        }
                    }
                }
        }

        Row(
            modifier = Modifier
                .horizontalScroll(scrollState, enabled = list.size > 4),
        ) {
            val itemModifier = Modifier
                .height(86.dp)
                .layout { measurable, constraints ->
                    val placeable = measurable.measure(
                        constraints.copy(
                            minWidth = itemWidth,
                            maxWidth = itemWidth
                        )
                    )
                    layout(placeable.width, placeable.height) {
                        placeable.placeRelative(0, 0)
                    }
                }
            list.forEach {
                MenuItem(itemModifier.clickable {
                    if (onClickItem(it.id).not()) {
                        it.onClick(context)
                    }
                }, it.iconRes, stringResource(it.textRes))
            }
        }
    }
}

@Composable
private fun MenuItem(modifier: Modifier, icon: Int, text: String) {
    Column(
        modifier = modifier,
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        Image(painter = painterResource(id = icon), contentDescription = null)
        AutoSizeText(text = text, color = Color.White, fontSize = 14.sp)
    }
}
