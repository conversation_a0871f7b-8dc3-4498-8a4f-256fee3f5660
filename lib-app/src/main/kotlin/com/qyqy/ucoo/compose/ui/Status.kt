package com.qyqy.ucoo.compose.ui

import android.app.Activity
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

@Composable
fun AppearanceStatusBars(isLight: <PERSON>olean, autoRestore: Boolean = false) {
    val view = LocalView.current
    if (!view.isInEditMode) {
        val window = (view.context as? Activity)?.window ?: return
        SideEffect {
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = isLight
        }

        if (autoRestore) {
            DisposableEffect(key1 = Unit) {
                val previous = WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars
                onDispose {
                    WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = previous
                }
            }
        }
    }
}