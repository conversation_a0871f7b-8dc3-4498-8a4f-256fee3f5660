package com.qyqy.ucoo.compose.presentation.ff

import com.qyqy.ucoo.AppUserPartition
import com.overseas.common.utils.id2String
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toast
import kotlinx.serialization.json.booleanOrNull
import kotlinx.serialization.json.jsonPrimitive


interface InviteInterface {

    suspend fun invite(userId: Int, tagId: Int): Result<Boolean>

    suspend fun acceptInvite(inviteId: Int): Result<Boolean>

    suspend fun giveupReleationship(id: Int): Result<Boolean>
}

object InviteManager : InviteInterface {

    private val api = createApi<FamilyFriendApi>()

    override suspend fun invite(userId: Int, tagId: Int): Result<Boolean> {
        return runApiCatching {
            api.inviteUser(
                mapOf(
                    "relative_uid" to userId.toString(),
                    "relative_tag_id" to tagId.toString()
                )
            )
        }.map { it["ok"]?.jsonPrimitive?.booleanOrNull ?: false }.onSuccess {
            if (AppUserPartition.isUCOO) {
                toast(id2String(R.string.已发送邀请))
            } else {
                toast(id2String(R.string.cpd已发送邀请))
            }
        }.toastError()
    }

    override suspend fun acceptInvite(inviteId: Int): Result<Boolean> {
        return runApiCatching { api.acceptInvite(mapOf("invited_id" to inviteId.toString())) }
            .map { true }.toastError()
    }

    override suspend fun giveupReleationship(id: Int): Result<Boolean> {
        return runApiCatching { api.giveupRelationship(mapOf("relationship_id" to id.toString())) }
            .map { true }.toastError().onSuccess {
                toast(id2String(R.string.broke_family))
            }
    }
}