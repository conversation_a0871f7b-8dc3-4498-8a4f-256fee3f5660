package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.platform.LocalContext
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.qyqy.ucoo.im.compat.chat.utils.VoiceMessagePlayController


val LocalMsgAudioPlayer = staticCompositionLocalOf<VoiceMessagePlayController> {
    error("AudioMessagePlayDelegate is null")
}

@Composable
fun rememberMsgAudioPlayer(cancelIfOnStop: Boolean = true): VoiceMessagePlayController {
    val context = LocalContext.current
    val scope = rememberCoroutineScope()
    val lifecycleOwner = LocalLifecycleOwner.current
    return remember(context, scope, lifecycleOwner) {
        VoiceMessagePlayController(lifecycleOwner, context, cancelIfOnStop)
    }
}