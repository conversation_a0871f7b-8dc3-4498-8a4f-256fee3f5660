package com.qyqy.ucoo.compose.presentation.cp_house.bean

import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.cp_house.house.IHouseIntimateRankItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class HRank(
    val loading: Boolean = false,
    @SerialName("my_room")
    val myRoom: Room? = null,
    @SerialName("ranking_data")
    val rankingData: List<RankItem> = emptyList(),
    val rule: String = ""
) {

    @Serializable
    data class Room(
        @SerialName("female_user")
        val femaleUser: AppUser,
        @SerialName("male_user")
        val maleUser: AppUser,
        val ranking: Int,
        @SerialName("intimacy_score")
        val intimacyScore: Int,
        val tips: String
    )

    @Serializable
    data class RankItem(
        override val id: Int,
        @SerialName("female_user")
        val femaleUser: AppUser,
        @SerialName("male_user")
        val maleUser: AppUser,
        @SerialName("intimacy_score")
        val intimacyScore: Int,
        @SerialName("is_hidden")
        override val isHidden: Boolean,
        val house: House,
    ) : IHouseIntimateRankItem {
        override val userLeft: User = maleUser
        override val userRight: User = femaleUser
        override val effectPath: String = house.icon
        override val value: Int = intimacyScore
    }

    @Serializable
    data class House(
        val icon: String,
        @SerialName("effect_file")
        val effectFile: String
    )
}
