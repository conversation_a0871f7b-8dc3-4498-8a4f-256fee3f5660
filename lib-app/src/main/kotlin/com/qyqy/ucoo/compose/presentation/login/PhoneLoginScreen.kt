package com.qyqy.ucoo.compose.presentation.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.jay.phone_text_watcher.PhoneTextWatcher
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.clearFocusOnKeyboardDismiss
import com.qyqy.ucoo.compose.data.PhoneInfo
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import kotlinx.coroutines.delay


@Composable
fun PhoneInputLoginScreen(
    phoneInfo: PhoneInfo = PhoneInfo(),
    onConfirm: (String) -> Unit = { },
    onBack: () -> Unit = {},
) {
    var hasFocus by rememberSaveable { mutableStateOf(false) }
    var phoneNumber by rememberSaveable {
        mutableStateOf("")
    }
    val focusRequester = remember { FocusRequester() } //焦点
    val focusManager = LocalFocusManager.current
    val resources = LocalContext.current.resources

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .drawWithCache {
                val imageBitmap = ImageBitmap.imageResource(resources, id = R.drawable.header_home)
                val dstSize = IntSize(
                    size.width.toInt(),
                    size.width
                        .div(1125)
                        .times(840)
                        .toInt()
                )
                onDrawBehind {
                    drawImage(image = imageBitmap, dstSize = dstSize)
                }
            }
            .systemBarsPadding()
    ) {

        AppTitleBar("", modifier = Modifier.padding(start = 5.dp), onBack = {
            if (hasFocus) {
                focusManager.clearFocus(true)
            }
            onBack()
        })

        Image(
            painter = painterResource(id = R.drawable.ic_ucoo_logo),
            contentDescription = null,
            modifier = Modifier
                .padding(start = 24.dp, top = 32.dp)
                .width(120.dp),
            contentScale = ContentScale.FillWidth
        )

        Text(
            text = stringResource(id = R.string.please_input_your_phone_num),
            fontSize = 24.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.padding(start = 24.dp, top = 16.dp)
        )

        LoginInputCard(
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .padding(top = 32.dp)
                .fillMaxWidth()
                .height(56.dp),
            prefix = stringResource(id = R.string.title_country_area),
            content = phoneInfo.country,
            enabled = false
        )

        LoginInputNumberCard(
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .padding(top = 24.dp)
                .fillMaxWidth()
                .height(56.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { hasFocus = it.isFocused }
                .clearFocusOnKeyboardDismiss(),
            prefix = "${phoneInfo.countryCode}+${phoneInfo.areaCode}",
            content = phoneNumber,
            keyboardActions = KeyboardActions(
                onDone = {
                    if (phoneNumber.length >= 11) {
                        onConfirm(phoneNumber)
                    }
                }
            )
        ) {
            phoneNumber = it
            if ((!isRelease || !isProd) && phoneNumber.length >= 11) {
                onConfirm(phoneNumber)
            }
        }

        LaunchedEffect(Unit) {
            delay(150)
            if (!hasFocus) {
                focusRequester.requestFocus()
            }
        }

        Text(
            text = stringResource(id = R.string.tip_input_phone_num),
            modifier = Modifier
                .padding(top = 24.dp)
                .padding(horizontal = 48.dp),
            fontSize = 12.sp,
            color = Color.White.copy(0.35f),
        )

        LoginButton(
            text = stringResource(id = R.string.next_step),
            modifier = Modifier
                .padding(top = 64.dp)
                .padding(horizontal = 48.dp)
                .fillMaxWidth(),
            enabled = phoneNumber.length >= 11,
            onClick = {
                onConfirm(phoneNumber)
            }
        )
    }
}

@Preview(
    "手机号输入登录页面",
    widthDp = 375,
    heightDp = 812,
)
@Composable
fun PhoneInputLoginPreview() {
    MaterialTheme {
        PhoneInputLoginScreen()
    }
}

@Composable
fun PhoneVerifyLoginScreen(
    phoneInfo: PhoneInfo = PhoneInfo(),
    onConfirm: (String) -> Unit = { },
    onResend: () -> Unit = {},
    onStartTimer: suspend () -> Unit = {},
    onPopupToStart: () -> Unit = {},
    onBack: () -> Unit = {},
) {
    var code by rememberSaveable {
        mutableStateOf("")
    }
    var hasFocus by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() } //焦点
    val focusManager = LocalFocusManager.current
    val resources = LocalContext.current.resources

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .drawWithCache {
                val imageBitmap = ImageBitmap.imageResource(resources, id = R.drawable.header_home)
                val dstSize = IntSize(
                    size.width.toInt(),
                    size.width
                        .div(1125)
                        .times(840)
                        .toInt()
                )
                onDrawBehind {
                    drawImage(image = imageBitmap, dstSize = dstSize)
                }
            }
            .systemBarsPadding()
    ) {

        AppTitleBar("", modifier = Modifier.padding(start = 5.dp), onBack = onBack)

        Text(
            text = stringResource(id = R.string.verify_code),
            modifier = Modifier.padding(start = 24.dp, top = 32.dp),
            fontSize = 24.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
        )

        Box(
            modifier = Modifier
                .padding(start = 24.dp, end = 24.dp, top = 10.dp)
                .fillMaxWidth()
                .height(28.dp),
        ) {

            val phoneFormatter = remember {
                PhoneTextWatcher()
            }

            Text(
                text = "+${phoneInfo.areaCode} ${phoneFormatter.format(phoneInfo.number)}",
                modifier = Modifier.align(Alignment.CenterStart),
                fontSize = 16.sp,
                color = Color.White,
            )

            val display = phoneInfo.displayLeftSeconds
            if (display > 0) {
                Text(
                    text = "${display}s",
                    modifier = Modifier.align(Alignment.CenterEnd),
                    fontSize = 16.sp,
                    color = Color.White,
                )
                LaunchedEffect(phoneInfo.lastSendCodeTimestamp) {
                    onStartTimer()
                }
            } else {
                Box(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .fillMaxHeight()
                        .background(Color(0x26945EFF), CircleShape)
                        .clip(CircleShape)
                        .clickable(onClick = onResend)
                        .padding(horizontal = 8.dp),
                    contentAlignment = Alignment.Center,
                ) {
                    AppText(
                        text = stringResource(id = R.string.retry_obtain),
                        fontSize = 12.sp,
                        color = Color(0xFF945EFF),
                    )
                }
            }
        }

        Box(
            modifier = Modifier
                .padding(start = 24.dp, end = 24.dp, top = 48.dp)
                .height(80.dp)
        ) {
            val pattern = remember { Regex("^\\d+\$") }
            AppBasicTextField(
                value = code,
                onValueChange = {
                    if (it.isEmpty() || it.matches(pattern)) {
                        val newCode = it.take(6)
                        if (newCode != code) {
                            code = newCode
                            if ((!isRelease || !isProd) && newCode.length == 6) {
                                onConfirm(newCode)
                            }
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxSize()
                    .alpha(0f)
                    .focusRequester(focusRequester)
                    .onFocusChanged { hasFocus = it.isFocused }
                    .clearFocusOnKeyboardDismiss(),
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Done),
                keyboardActions = KeyboardActions(
                    onDone = {
                        if (code.length >= 6) {
                            onConfirm(code)
                        }
                    }
                )
            )

            Row(
                modifier = Modifier
                    .fillMaxSize()
                    .noEffectClickable {
                        focusRequester.requestFocus()
                    },
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                repeat(6) {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .fillMaxHeight()
                            .background(Color(0x0DFFFFFF), Shapes.small),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(text = code.getOrNull(it)?.toString().orEmpty(), color = Color.White, fontSize = 32.sp, fontWeight = FontWeight.Bold)
                    }
                }
            }
        }

        LaunchedEffect(Unit) {
            delay(150)
            if (!hasFocus) {
                focusRequester.requestFocus()
            }
        }

        Text(
            text = stringResource(id = R.string.try_login_witch_another_way),
            modifier = Modifier
                .align(Alignment.End)
                .padding(top = 24.dp, end = 24.dp)
                .clickable {
                    if (hasFocus) {
                        focusManager.clearFocus(true)
                    }
                    onPopupToStart()
                },
            fontSize = 14.sp,
            color = Color(0xFF945EFF),
        )

        LoginButton(
            text = stringResource(id = R.string.next_step),
            modifier = Modifier
                .padding(top = 150.dp)
                .padding(horizontal = 48.dp)
                .fillMaxWidth(),
            enabled = code.length >= 6,
            onClick = {
                onConfirm(code)
            }
        )
    }
}

@Preview(
    "手机号验证登录页面",
    widthDp = 375,
    heightDp = 812,
)
@Composable
fun PhoneVerifyLoginPreview() {
    MaterialTheme {
        PhoneVerifyLoginScreen()
    }
}

