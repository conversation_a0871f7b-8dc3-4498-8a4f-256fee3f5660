/*
 * Copyright 2022 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.qyqy.ucoo.compose.theme

import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.ui.unit.dp

object Shapes {

    val extraSmall = RoundedCornerShape(4.dp)

    val moreSmall = RoundedCornerShape(6.dp)

    val small = RoundedCornerShape(8.dp)

    val medium = RoundedCornerShape(16.dp)

    val large = RoundedCornerShape(24.dp)

    val extraLarge = RoundedCornerShape(32.dp)

    val corner12 = RoundedCornerShape(12.dp)

    val chip = RoundedCornerShape(50)
}


