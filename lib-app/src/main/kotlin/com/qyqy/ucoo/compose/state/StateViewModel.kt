package com.qyqy.ucoo.compose.state

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.widget.orElse
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch


sealed interface PageResult<Page, T> {

    val data: List<T>

    val nexPageKey: Page?

    val hasNext: Boolean

    data class HasNext<Page, T>(
        override val data: List<T>,
        override val nexPageKey: Page?,
    ) : PageResult<Page, T> {
        override val hasNext: Boolean = true
    }

    data class NoNext<Page, T>(
        override val data: List<T>,
    ) : PageResult<Page, T> {
        override val nexPageKey: Page? = null

        override val hasNext: Boolean = false
    }
}

abstract class StateViewModel<Page, T> : ViewModel() {

    companion object {
        const val TAG = "StateViewModel"
    }

    private var nextPage: Page? = null

    val contentState = ContentState(StateValue.Idle) {
        _isRefreshingFlow.value = it == StateValue.Refreshing
        _isLodeMoreFlow.value = it == StateValue.LoadMore
    }

    protected val _listFlow: MutableStateFlow<List<T>> = MutableStateFlow(emptyList())
    val listFlow = _listFlow.asStateFlow()

    init {
        viewModelScope.launch {
            listFlow.collectLatest {
                if (contentState.current.isSuccess && it.isEmpty()) {
                    contentState.setValue(StateValue.Empty(emptyMessage))
                }
            }
        }
    }

    private val _isRefreshingFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val isRefreshingFlow = _isRefreshingFlow.asStateFlow()

    private val _isLodeMoreFlow: MutableStateFlow<Boolean> = MutableStateFlow(false)
    val isLodeMoreFlow = _isLodeMoreFlow.asStateFlow()

    private val _hasMoreFlow: MutableStateFlow<Boolean> = MutableStateFlow(true)
    val hasMoreFlow = _hasMoreFlow.asStateFlow()

    var hasMoreData: Boolean
        protected set(value) {
            _hasMoreFlow.value = value
        }
        get() = hasMoreFlow.value

    val loadMoreVisibleFlow = _listFlow.map {
        it.isNotEmpty() && it.size > 6
    }.stateIn(viewModelScope, SharingStarted.Lazily, true)

    protected fun getItem(index: Int): T? {
        if (index >= listFlow.value.size) {
            return null
        }
        try{
            return listFlow.value[index]
        }catch (e:Exception){
            return null
        }
    }

    protected fun lastItem(): T? {
        return listFlow.value.lastOrNull()
    }

    private fun onStateSuccess(dataList: List<T>, replace: Boolean = false) {
        if (replace) {
            replaceList(dataList)
        } else {
            appendList(dataList)
        }
    }

    private fun showRefresh() {
        contentState.setValue(StateValue.Refreshing)
    }

    protected fun showLoading() {
        contentState.setValue(StateValue.LoadMore)
    }

    private fun onStateError(errorMessage: String? = null) {
        contentState.setValue(StateValue.Error(errorMessage))
    }


    private fun replaceList(list: List<T>) {
        viewModelScope.launch {
            emitList(list)
        }
    }

    private fun appendList(list: List<T>) {
        viewModelScope.launch {
            emitList(buildList {
                addAll(_listFlow.value)
                addAll(list)
            })
        }
    }

    fun filterItems(predicate: (T) -> Boolean) {
        viewModelScope.launch {
            emitList(_listFlow.value.filter(predicate))
        }
    }

    fun removeItems(predicate: (T) -> Boolean) {
        viewModelScope.launch {
            emitList(_listFlow.value.filterNot(predicate))
        }
    }

    fun updateItem(predicate: (T) -> Boolean, block: (T) -> T) {
        transformList {
            val newList = this.toMutableList()
            val pos = newList.indexOfFirst(predicate)
            newList[pos] = block(newList[pos])
            newList
        }
    }

    fun transformList(transform: List<T>.() -> List<T>) {
        viewModelScope.launch {
            val list = _listFlow.value
            val newList = transform(list)
            if (newList !== list) {
                emitList(newList)
            }
        }
    }

    protected open suspend fun emitList(newList: List<T>) = run {
        _listFlow.emit(newList)
        if (newList.isEmpty()) {
            contentState.setValue(StateValue.Empty(emptyMessage))
        } else {
            contentState.setValue(StateValue.Success)
        }
    }

    open fun refresh() {
        val current = contentState.current
        if (current.isLoading || current.isRefreshing) {
            return
        }
        showRefresh()
        nextPage = null
        performLoadData(true, firstPageKey())
    }

    open fun loadMore() {
        val current = contentState.current
        if (current.isLoading || current.isRefreshing) {
            return
        }
        if (hasMoreData.not()) {
            return
        }
        showLoading()
        performLoadData(false, nextPage)
    }

    private fun performLoadData(isRefresh: Boolean, page: Page?) {
        viewModelScope.launch {
            loadPageData(page)
                .onSuccess {
                    onStateSuccess(it.data, isRefresh)
                    hasMoreData = it.hasNext
                    if (it.hasNext) {
                        nextPage = it.nexPageKey
                    }
                }.onFailure {
                    if (isRefresh) {
                        onStateError(it.message)
                    } else {
                        toast(it.message ?: "")
                    }
                }
        }
    }

    protected open val emptyMessage: String? = null

    protected open fun firstPageKey(): Page? = null

    protected abstract suspend fun loadPageData(pageNum: Page?): Result<PageResult<Page, T>>
}

abstract class StateViewModelWithIntPage<T> : StateViewModel<Int, T>() {

    protected val firstPage: Int
        get() = firstPageKey()

    override fun firstPageKey(): Int = 1

    final override suspend fun loadPageData(pageNum: Int?): Result<PageResult<Int, T>> {
        val currentPage = pageNum.orElse(firstPageKey())
        return loadData(currentPage).map {
            if (currentPage == firstPageKey() || it.isNotEmpty()) {
                PageResult.HasNext(it, currentPage.plus(1))
            } else {
                PageResult.NoNext(it)
            }
        }
    }

    protected abstract suspend fun loadData(pageNum: Int): Result<List<T>>

}

