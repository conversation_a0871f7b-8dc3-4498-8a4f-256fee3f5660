package com.qyqy.ucoo.compose.ui.photo

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.UiComposable

data class PhotoGestureBoxScope(
    val boxScope: BoxScope,
    val gestureScope: PhotoGestureScope,
) : BoxScope by boxScope, PhotoGestureScope by gestureScope


@Composable
fun PhotoGestureBox(
    modifier: Modifier = Modifier,
    enable: Boolean = true,
    gestureState: PhotoGestureState = rememberPhotoGestureState(),
    content: @Composable @UiComposable PhotoGestureBoxScope.() -> Unit
) {
    gestureState.apply {
        Box(
            modifier = modifier
                .photoGesture(scope = rememberCoroutineScope(), key = Unit, enable = {
                    enable
                }),
            contentAlignment = Alignment.Center
        ) {
            content(PhotoGestureBoxScope(this, gestureState))
        }
    }
}