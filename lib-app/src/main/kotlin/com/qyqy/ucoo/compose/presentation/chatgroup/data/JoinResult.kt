package com.qyqy.ucoo.compose.presentation.chatgroup.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class JoinResult(
    val apply: Apply = Apply(),
    val msg: String = ""
) {
    @Serializable
    data class Apply(
        @SerialName("chatgroup_id")
        val chatgroupId: Int = 0,
        val id: Int = 0,
        val status: Int = 0
    )
}