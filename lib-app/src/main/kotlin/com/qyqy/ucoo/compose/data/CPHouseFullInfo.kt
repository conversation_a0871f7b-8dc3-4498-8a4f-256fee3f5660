package com.qyqy.ucoo.compose.data


import android.os.Parcelable
import androidx.annotation.Keep
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.CpExtraInfo
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.sUser
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
{
"cp_extra_info": {
"cp_value": 22980,
"togather_days": "在一起23天"
},
"balance": 1000,
"intimacy_score": 100,
"intimacy_ranking": 100,
"car": {
"name": "xxx",
"icon": "xxxx",
"effect_file": "xxx",
"profit": 100
},
"house": {
"name": "xxx",
"icon": "xxxx",
"effect_file": "xxx",
"profit": 100
},
"background": "xxxxxx",
"private_room_id": 100,
"is_hidden": true
}
 */
@Keep
@Serializable
data class CPHouseFullInfo(
    @SerialName("id")
    val id: Int = 0,
    @SerialName("female_user")
    val femaleUser: AppUser = AppUser(),
    @SerialName("male_user")
    val maleUser: AppUser = AppUser(),
    @SerialName("background")
    val background: String = "",
    @SerialName("balance")
    val balance: Int = 0,
    @SerialName("cp_extra_info")
    val cpExtraInfo: CpExtraInfo = CpExtraInfo(),
    @SerialName("house")
    val house: CPHouseSimplePropItem? = null,
    @SerialName("car")
    val car: CPHouseSimplePropItem? = null,
    @SerialName("intimacy_ranking")
    val intimacyRanking: Int = 0,
    @SerialName("intimacy_score")
    val intimacyScore: Int = 0,
    @SerialName("is_hidden")
    val isHidden: Boolean = false,
    @SerialName("private_room_id")
    val privateRoomId: Int = 0,
    val rule: String = ""
) {

    companion object {
        val EMPTY = CPHouseFullInfo()
    }

    interface ICPHouseProp {
        val id: Int
        val effectFile: String
        val icon: String
    }

    @Serializable
    @Parcelize
    data class CPHouseSimplePropItem(
        @SerialName("id")
        override val id: Int = 0,
        @SerialName("effect_file")
        override val effectFile: String = "",
        @SerialName("icon")
        override val icon: String = "",
        @SerialName("name")
        val name: String = "",
        @SerialName("profit")
        val profit: Int = 0
    ) : Parcelable, ICPHouseProp

    @Serializable
    @Parcelize
    data class CPHouseFullPropItem(
        @SerialName("id")
        override val id: Int = 0,
        @SerialName("effect_file")
        override val effectFile: String = "",
        @SerialName("icon")
        override val icon: String = "",
        @SerialName("gain_type")
        val gainType: Int = 0,//获取方式 3亲密度解锁 4宝箱抽取 5系统赠送
        @SerialName("sub_type")
        val subType: Int = 0,//1: 专属小屋 2:限定小屋 3: 专属车辆
        @SerialName("sub_type_name")
        val subTypeName: String = "",
        @SerialName("intimacy_score_limit")
        val intimacyScoreLimit: Int = 0,//亲密度解锁门槛
        @SerialName("name")
        val name: String = "",
        @SerialName("obtained")
        val obtained: Boolean = false,//是否已获得
        @SerialName("profit")
        val profit: Int = 0,//收益
        @SerialName("t")
        val t: Int = 0, //装扮种类
        @SerialName("is_use")
        var isUsing: Boolean = false//是否使用中
    ) : Parcelable, ICPHouseProp {
        companion object {
            const val TYPE_HOUSE = 17
            const val TYPE_CAR = 18
        }
    }

    fun hasUser(userId: Int): Boolean {
        return femaleUser.userId == userId || maleUser.userId == userId
    }

    fun MyCpUser(userId: Int) =
        if (femaleUser.userId == userId) maleUser else if (maleUser.userId == userId) femaleUser else null

    fun iAmAdmin() = hasUser(sUser.userId)

    fun inValid() = this != EMPTY
}