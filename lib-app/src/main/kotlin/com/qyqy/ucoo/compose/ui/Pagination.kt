package com.qyqy.ucoo.compose.ui

import android.os.SystemClock
import androidx.annotation.IntRange
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.Stable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.listSaver
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.overseas.common.utils.ResultContinuation
import com.qyqy.ucoo.widget.orElse
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.launch

sealed class LoadResult<Key : Any> {
    data class Page<Key : Any>(
        val pageKey: Key?,
    ) : LoadResult<Key>()

    /**
     * [retryDuration] 单位毫秒, [Int.MAX_VALUE]表示不自动重试，需要主动调用retry.
     */
    data class Error<Key : Any>(
        val throwable: Throwable,
        @IntRange(from = 500) val retryDuration: Int = Int.MAX_VALUE,
    ) : LoadResult<Key>() {

        init {
            require(retryDuration >= 500)
        }
    }

    val isSuccess: Boolean
        get() = this is Page<*>
}

sealed class LoadState {

    data object Idea : LoadState()

    data class Loading<Key : Any>(val key: Key?) : LoadState()

    @Stable
    data class Failure<Key : Any>(
        val key: Key?,
        val throwable: Throwable,
        val retryDuration: Int,
        var retryed: Boolean = false,
    ) : LoadState()

    data object End : LoadState()


    val isLoading: Boolean
        get() = this is Loading<*>

    val isFailure: Boolean
        get() = this is Failure<*>

    val canLoad: Boolean
        get() = this is Idea

    val isEnd: Boolean
        get() = this is End
}

@Composable
fun <Key : Any> rememberPaginateState(
    initialNextKey: Key? = null,
    initialPrevKey: Key? = null,
    nextEnable: Boolean = true,
    prevEnable: Boolean = false,
    buffer: Int = 12,
): PaginateState<Key> {
    return rememberSaveable(saver = PaginateState.createSaver()) {
        PaginateState(
            initialNextKey = initialNextKey,
            initialPrevKey = initialPrevKey,
            nextEnable = nextEnable,
            prevEnable = prevEnable,
            buffer = buffer
        )
    }
}

data class PagingScope<Key : Any>(
    val key: Key?,
    val next: Boolean,
) {
    private val resultContinuation = ResultContinuation<LoadResult<Key>>()

    @Deprecated("use LaunchAttach")
    fun loadEnd(result: LoadResult<Key>) {
        resultContinuation.resume(result)
    }

    @Deprecated("use LaunchAttach")
    suspend fun suspendResult(
        onLoad: (PagingScope<Key>) -> Unit,
    ): LoadResult<Key> {
        onLoad(this)
        return resultContinuation.suspendUntil()
    }
}

/**
 * 支持LazyColumn分页容器，支持向前分页和向后分页
 */
@Stable
class PaginateState<Key : Any> internal constructor(
    initialNextKey: Key? = null,
    initialPrevKey: Key? = null,
    nextEnable: Boolean = true,
    prevEnable: Boolean = false,
    private val buffer: Int = 12,
) {
    var nextEnable: Boolean by mutableStateOf(nextEnable)

    var prevEnable: Boolean by mutableStateOf(prevEnable)

    var nextLoadState: LoadState by mutableStateOf(LoadState.Idea)
        private set

    var prevLoadState: LoadState by mutableStateOf(LoadState.Idea)
        private set

    private var nextKey: Key? = initialNextKey

    private var prevKey: Key? = initialPrevKey

    fun resetPrev(key: Key? = null, loadState: LoadState = LoadState.Idea) {
        prevKey = key
        prevLoadState = loadState
    }

    fun resetNext(key: Key? = null, loadState: LoadState = LoadState.Idea) {
        nextKey = key
        nextLoadState = loadState
    }

    fun resetState(prevEnable: Boolean?, prevKey: Key?, nextEnable: Boolean?, nextKey: Key?) {
        if (prevEnable != null) {
            this.prevEnable = prevEnable
            if (prevEnable) {
                this.prevKey = prevKey
                this.prevLoadState = LoadState.Idea
            } else {
                this.prevLoadState = LoadState.End
            }
        }

        if (nextEnable != null) {
            this.nextEnable = nextEnable
            if (nextEnable) {
                this.nextKey = nextKey
                this.nextLoadState = LoadState.Idea
            } else {
                this.nextLoadState = LoadState.End
            }
        }
    }

    fun reset(
        prevKey: Key? = null,
        nextKey: Key? = null,
    ) {
        this.prevKey = prevKey
        this.prevLoadState = LoadState.Idea
        this.nextKey = nextKey
        this.nextLoadState = LoadState.Idea
    }

    fun retryNext(): Boolean {
        if (nextEnable && nextLoadState.isFailure) {
            nextLoadState = LoadState.Idea
            return true
        }
        return false
    }

    fun retryPrev(): Boolean {
        if (prevEnable && prevLoadState.isFailure) {
            prevLoadState = LoadState.Idea
            return true
        }
        return false
    }

    fun retry(): Boolean {
        if (retryNext()) {
            return true
        }
        if (retryPrev()) {
            return true
        }
        return false
    }

    suspend fun loadTriggerOnce(
        next: Boolean,
        key: Key? = getLoadKey(next),
        ignoreEnable: Boolean = false,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        if (!ignoreEnable && !getLoadEnable(next)) {
            return
        }
        if (getLoadState(next).isLoading) {
            return
        }
        loadPageData(next, key, onLoad)
    }

    @Deprecated("use LaunchAttach")
    @Composable
    fun attach(
        listState: LazyListState = rememberLazyListState(),
        onLoad: (PagingScope<Key>) -> Unit,
    ) {

        val triggerNext by remember {
            derivedStateOf {
                val layoutInfo = listState.layoutInfo
                val totalItemsNumber = layoutInfo.totalItemsCount
                val lastVisibleItemIndex = layoutInfo.visibleItemsInfo.lastOrNull()?.index.orElse(0).plus(1)
                lastVisibleItemIndex > (totalItemsNumber - buffer)
            }
        }

        val triggerPrev by remember {
            derivedStateOf {
                val layoutInfo = listState.layoutInfo
                val firstVisibleItemIndex = layoutInfo.visibleItemsInfo.firstOrNull()?.index.orElse(0)
                firstVisibleItemIndex < buffer
            }
        }

        LaunchedEffect(listState) {
            snapshotFlow {
                if (triggerNext && nextEnable && nextLoadState is LoadState.Idea && prevLoadState !is LoadState.Loading<*>) {
                    Action.TriggerNext
                } else if (triggerPrev && prevEnable && prevLoadState is LoadState.Idea && nextLoadState !is LoadState.Loading<*>) {
                    Action.TriggerPrev
                } else if ((!nextEnable && nextLoadState is LoadState.Loading<*>) || (!prevEnable && prevLoadState is LoadState.Loading<*>)) {
                    Action.Cancel// 取消当前的加载
                } else {
                    Action.Nothing
                }
            }.filter {
                it !is Action.Nothing
            }.collectLatest {
                val scope = when (it) {
                    is Action.TriggerNext -> {
                        nextLoadState = LoadState.Loading(nextKey)
                        PagingScope(nextKey, true)
                    }

                    is Action.TriggerPrev -> {
                        prevLoadState = LoadState.Loading(prevKey)
                        PagingScope(prevKey, false)
                    }

                    is Action.Cancel -> {
                        // 取消加载更多
                        if (nextLoadState is LoadState.Loading<*>) {
                            nextLoadState = LoadState.Idea
                        } else if (prevLoadState is LoadState.Loading<*>) {
                            prevLoadState = LoadState.Idea
                        }
                        return@collectLatest
                    }

                    else -> error("不会发生的事情")
                }
                val result = scope.suspendResult(onLoad)
                when (it) {
                    is Action.TriggerNext -> {
                        val state = nextLoadState
                        if (state is LoadState.Loading<*>) {
                            var delayTime: Int? = null
                            nextLoadState = if (result is LoadResult.Page) {
                                nextKey = result.pageKey
                                if (result.pageKey == null) {
                                    LoadState.End
                                } else {
                                    LoadState.Idea
                                }
                            } else {
                                result as LoadResult.Error
                                if (result.retryDuration != Int.MAX_VALUE) {
                                    delayTime = result.retryDuration
                                }
                                LoadState.Failure(scope.key, result.throwable, result.retryDuration)
                            }

                            if (delayTime != null) {
                                delay(delayTime.toLong())
                                if (nextEnable && nextLoadState is LoadState.Failure<*>) {
                                    nextLoadState = LoadState.Idea
                                }
                            }
                        }
                    }

                    is Action.TriggerPrev -> {
                        val state = prevLoadState
                        if (state is LoadState.Loading<*>) {
                            var delayTime: Int? = null
                            prevLoadState = if (result is LoadResult.Page) {
                                prevKey = result.pageKey
                                if (result.pageKey == null) {
                                    LoadState.End
                                } else {
                                    LoadState.Idea
                                }
                            } else {
                                result as LoadResult.Error
                                if (result.retryDuration != Int.MAX_VALUE) {
                                    delayTime = result.retryDuration
                                }
                                LoadState.Failure(scope.key, result.throwable, result.retryDuration)
                            }

                            if (delayTime != null) {
                                delay(delayTime.toLong())
                                if (prevEnable && prevLoadState is LoadState.Failure<*>) {
                                    prevLoadState = LoadState.Idea
                                }
                            }
                        }
                    }

                    else -> error("nothing")
                }


            }
        }
    }

    @Composable
    fun LaunchAttach(
        listState: LazyListState,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        if (nextEnable) {
            LaunchedEffect(listState) {
                val triggerNext by derivedStateOf {
                    val layoutInfo = listState.layoutInfo
                    val totalItemsNumber = layoutInfo.totalItemsCount
                    val lastVisibleItemIndex =
                        layoutInfo.visibleItemsInfo.lastOrNull()?.index.orElse(0).plus(1)
                    lastVisibleItemIndex > (totalItemsNumber - buffer)
                }

                snapshotFlow {
                    if (nextLoadState.canLoad && triggerNext) {
                        Action.Load
                    } else if (nextLoadState.isLoading) {
                        Action.Nothing
                    } else { // 取消当前的加载
                        Action.Cancel
                    }
                }.filter {
                    it !is Action.Nothing
                }.collectLatest {
                    if (it is Action.Cancel) {
                        return@collectLatest
                    }

                    loadPageData(next = true, key = nextKey, onLoad = onLoad)
                }
            }

            (getLoadState(true) as? LoadState.Failure<*>)?.takeIf {
                !it.retryed && it.retryDuration != Int.MAX_VALUE
            }?.also {
                it.retryed = true
                LaunchedEffect(key1 = Unit) {
                    delay(it.retryDuration.toLong())
                    if (getLoadState(true).isFailure) {
                        setLoadState(true, LoadState.Idea)
                    }
                }
            }
        }

        if (prevEnable) {
            LaunchedEffect(listState) {
                val triggerPrev by derivedStateOf {
                    val layoutInfo = listState.layoutInfo
                    val firstVisibleItemIndex =
                        layoutInfo.visibleItemsInfo.firstOrNull()?.index.orElse(0)
                    firstVisibleItemIndex < buffer
                }

                snapshotFlow {
                    if (prevLoadState.canLoad && triggerPrev) {
                        Action.Load
                    } else if (prevLoadState.isLoading) {
                        Action.Nothing
                    } else { // 取消当前的加载
                        Action.Cancel
                    }
                }.filter {
                    it !is Action.Nothing
                }.collectLatest {
                    if (it is Action.Cancel) {
                        return@collectLatest
                    }

                    loadPageData(next = false, key = prevKey, onLoad = onLoad)
                }
            }

            (getLoadState(false) as? LoadState.Failure<*>)?.takeIf {
                !it.retryed && it.retryDuration != Int.MAX_VALUE
            }?.also {
                it.retryed = true
                LaunchedEffect(key1 = Unit) {
                    delay(it.retryDuration.toLong())
                    if (getLoadState(false).isFailure) {
                        setLoadState(false, LoadState.Idea)
                    }
                }
            }
        }
    }

    suspend fun refresh(
        key: Key,
        next: Boolean,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        loadPageData(next, key, onLoad)
    }

    private suspend fun loadPageData(
        next: Boolean,
        key: Key?,
        onLoad: suspend (PagingScope<Key>) -> LoadResult<Key>,
    ) {
        setLoadState(next, LoadState.Loading(key))
        val scope = PagingScope(key, next)
        val start = SystemClock.elapsedRealtime()
        val result = onLoad(scope)

        // 确保数据分页加载状态变化不再同一次组合中，因为state是从idea->loading->idea，如果在变化同一次组合中连续改变，等于没有发生改变
        delay(16.minus(SystemClock.elapsedRealtime().minus(start)))

        val loadState = if (result is LoadResult.Page) {
            setLoadKey(next, result.pageKey)
            if (result.pageKey == null) {
                LoadState.End
            } else {
                LoadState.Idea
            }
        } else {
            result as LoadResult.Error
            LoadState.Failure(scope.key, result.throwable, result.retryDuration)
        }

        setLoadState(next, loadState)
    }

    private fun getLoadEnable(next: Boolean) = if (next) {
        nextEnable
    } else {
        prevEnable
    }

    private fun setLoadState(next: Boolean, state: LoadState) {
        if (next) {
            nextLoadState = state
        } else {
            prevLoadState = state
        }
    }

    private fun getLoadState(next: Boolean) = if (next) {
        nextLoadState
    } else {
        prevLoadState
    }

    private fun setLoadKey(next: Boolean, key: Key?) {
        if (next) {
            nextKey = key
        } else {
            prevKey = key
        }
    }

    private fun getLoadKey(next: Boolean) = if (next) {
        nextKey
    } else {
        prevKey
    }

    private sealed class Action {

        abstract class AbsLoad : Action()

        data object TriggerNext : AbsLoad()

        data object TriggerPrev : AbsLoad()

        data object Load : AbsLoad()

        data object Cancel : Action()
        data object Nothing : Action()
    }

    companion object {

        fun <Key : Any> createSaver() = listSaver(
            save = {
                listOf(
                    it.nextKey,
                    it.prevKey,
                    it.nextEnable,
                    it.prevEnable,
                    it.buffer
                )
            },
            restore = {
                PaginateState(
                    initialNextKey = it[0] as? Key?,
                    initialPrevKey = it[1] as? Key?,
                    nextEnable = it[2] as Boolean,
                    prevEnable = it[3] as Boolean,
                    buffer = it[4] as Int,
                )
            })
    }
}

@Preview
@Composable
fun PreviewSimplePaging() {
    val list = remember {
        mutableStateListOf<Int>()
    }
    val lazyListState = rememberLazyListState()
    val paginateState =
        rememberPaginateState(initialNextKey = 10, nextEnable = true, prevEnable = false)
    paginateState.LaunchAttach(lazyListState) { pagingScope ->
        val key = if (pagingScope.next) {
            pagingScope.key.orElse(list.lastOrNull()?.div(10)?.plus(1) ?: 1)
        } else {
            pagingScope.key.orElse(list.firstOrNull()?.minus(1)?.div(10) ?: 10)
        }
        val start = key.minus(1).times(10)
        val result = buildList {
            repeat(10) { index ->
                add(start.plus(index).plus(1))
            }
        }
        if (pagingScope.next) {
            list.addAll(result)
            LoadResult.Page(key.plus(1))
        } else {
            if (key > 0) {
                list.addAll(0, result)
            }
            if (key <= 1) {
                LoadResult.Page(null)
            } else {
                LoadResult.Error(IllegalStateException("哈哈哈"))
            }
        }
    }

    val isFailure by remember {
        derivedStateOf {
            paginateState.prevLoadState.isFailure
        }
    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color.Black)
    ) {
        LazyColumn(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f), state = lazyListState
        ) {
            if (isFailure) {
                item {
                    Box(
                        modifier = Modifier
                            .padding(start = 15.dp)
                            .fillMaxWidth()
                            .height(30.dp)
                            .clickable {
                                paginateState.retryPrev()
                            }, contentAlignment = Alignment.CenterStart
                    ) {
                        Text(text = "重试", color = Color.White, fontSize = 15.sp)
                    }
                }
            }
            items(list) {
                Box(
                    modifier = Modifier
                        .padding(start = 15.dp)
                        .fillMaxWidth()
                        .height(30.dp),
                    contentAlignment = Alignment.CenterStart
                ) {
                    Text(text = "我是第${it}项", color = Color.White, fontSize = 15.sp)
                }
            }
        }

        Row {
            val scope = rememberCoroutineScope()
            Button(onClick = {
                scope.launch {
                    lazyListState.animateScrollToItem(10)
                }
            }) {
                Text(text = "测试1")
            }

            Button(onClick = {
                paginateState.nextEnable = !paginateState.nextEnable
            }) {
                Text(text = "测试2")
            }
        }
    }

}