package com.qyqy.ucoo.compose.state

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.compose.state.MviViewModel.MviEvent
import com.qyqy.ucoo.compose.state.MviViewModel.MviSideEffect
import com.qyqy.ucoo.compose.state.MviViewModel.MviViewResult
import com.qyqy.ucoo.compose.state.MviViewModel.MviViewState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.SharingStarted
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.scan
import kotlinx.coroutines.flow.shareIn
import kotlinx.coroutines.flow.stateIn
import kotlinx.coroutines.launch

abstract class MviViewModel<Event : MviEvent, Result : MviViewResult, State : MviViewState, Effect : MviSideEffect>(
    initialState: State
) : ViewModel() {
    val states: StateFlow<State>
    val effects: Flow<Effect>
    private val events = MutableSharedFlow<Event>()
    protected val sharedFlow: Flow<MviState<Result, State>>

    protected val cur: State
        get() = states.value

    init {
        @Suppress("LeakingThis")
        events.toResults()
            .share()
            .also { results ->
                sharedFlow = results.toStates(initialState).share()
                states = sharedFlow
                    .mapLatest {
                        it.state
                    }
                    .stateIn(
                        scope = viewModelScope,
                        started = SharingStarted.Lazily,
                        initialValue = initialState
                    )
                effects = results.toEffects()
            }
    }

    fun processEvent(event: Event) {
        viewModelScope.launch {
            events.emit(event)
        }
    }

    /**
     * 避免访问子类属性，因为此时子类属性没有初始化
     */
    protected abstract fun Flow<Event>.toResults(): Flow<Result>
    protected abstract suspend fun Result.reduce(state: State): State
    protected open fun Flow<Result>.toEffects(): Flow<Effect> = emptyFlow()

    protected open fun Flow<Result>.filterStateResult(): Flow<Result> = this

    private fun Flow<Result>.toStates(initialState: State): Flow<MviState<Result, State>> {
        return filterStateResult().scan(MviState(null, initialState)) { state, result -> MviState(result, result.reduce(state.state)) }
    }

    private fun <T> Flow<T>.share(): Flow<T> {
        return shareIn(scope = viewModelScope, started = SharingStarted.Eagerly)
    }

    interface MviViewState
    interface MviSideEffect
    interface MviEvent
    interface MviViewResult
}

data class MviState<Result : MviViewResult, State>(
    val result: Result?,
    val state: State,
)