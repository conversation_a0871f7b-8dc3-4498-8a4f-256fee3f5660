package com.qyqy.ucoo.compose.ui

import android.graphics.drawable.Drawable
import android.util.Log
import androidx.compose.animation.core.AnimationSpec
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.UiComposable
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties
import com.bumptech.glide.integration.compose.placeholder
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.composePlaceholderDrawable
import com.qyqy.ucoo.compose.ui.photo.PhotoGesturePreviewer
import com.qyqy.ucoo.compose.ui.photo.PhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.PhotoTransform
import com.qyqy.ucoo.compose.ui.photo.PreviewerDefault
import com.qyqy.ucoo.compose.ui.video.XVideoViewWidget
import site.xzwzz.video.XPlayer


@Composable
fun AppPhotoPreviewer(
    previewState: PhotoPreviewState,
    modifier: Modifier = Modifier,
    pageSpacing: Dp = 10.dp,
    enableGesture: Boolean = true,
    animationSpec: AnimationSpec<PhotoTransform>? = spring(
        dampingRatio = 0.9f,
        stiffness = 1000f,
    ),
    behindContent: @Composable @UiComposable BoxScope.() -> Unit = PreviewerDefault.behindContent(previewState = previewState),
    inFrontContent: @Composable @UiComposable BoxScope.() -> Unit = {},
) {
    PhotoGesturePreviewer(
        previewState = previewState,
        modifier = modifier,
        pageSpacing = pageSpacing,
        enableGesture = enableGesture,
        animationSpec = animationSpec,
        behindContent = behindContent,
        inFrontContent = inFrontContent,
    ) { isTransition, preview, imageModifier, contentScale ->
        Box(
            modifier = imageModifier,
            contentAlignment = Alignment.Center
        ) {
            if (preview.isVideo) {
                XVideoViewWidget(preview.previewSource.model.toString())
            } else {
                if (preview.transitionSource.model == preview.previewSource.model) {
                    ComposeImage(
                        model = preview.transitionSource.model,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = contentScale,
                        loading = null,
                        failure = placeholder(composePlaceholderDrawable()),
                    )
                } else {
                    var previewLoadFinish by remember {
                        mutableStateOf(false)
                    }

                    if (!previewLoadFinish) {
                        ComposeImage(
                            model = preview.transitionSource.model,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = contentScale,
                            loading = null,
                            failure = placeholder(composePlaceholderDrawable()),
                        )
                    }

                    ComposeImage(
                        model = preview.previewSource.model,
                        modifier = Modifier.fillMaxSize(),
                        contentScale = contentScale,
                        loading = null,
                    ) {
                        it.listener(object : RequestListener<Drawable> {
                            override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable>, isFirstResource: Boolean): Boolean {
                                previewLoadFinish = true
                                return false
                            }

                            override fun onResourceReady(
                                resource: Drawable,
                                model: Any,
                                target: Target<Drawable>?,
                                dataSource: DataSource,
                                isFirstResource: Boolean,
                            ): Boolean {
                                previewLoadFinish = true
                                return false
                            }
                        })
                    }
                }
            }
        }
    }
}


@Composable
fun AppPhotoPreviewerInDialog(
    previewState: PhotoPreviewState,
    modifier: Modifier = Modifier,
    pageSpacing: Dp = 10.dp,
    enableGesture: Boolean = true,
    animationSpec: AnimationSpec<PhotoTransform>? = tween(durationMillis = 5000),
    behindContent: @Composable @UiComposable BoxScope.() -> Unit = PreviewerDefault.behindContent(previewState = previewState),
    inFrontContent: @Composable @UiComposable BoxScope.() -> Unit = {},
) {
    if (!previewState.isPreviewing) {
        return
    }
    FullDialog(
        onDismissRequest = {
            previewState.endPreview()
        },
        properties = DialogProperties(
            dismissOnClickOutside = false,
            dismissOnBackPress = false,
        )
    ) {
        PhotoGesturePreviewer(
            previewState = previewState,
            modifier = modifier,
            pageSpacing = pageSpacing,
            enableGesture = enableGesture,
            animationSpec = animationSpec,
            behindContent = behindContent,
            inFrontContent = inFrontContent,
        ) { isTransition, preview, imageModifier, contentScale ->
            Box(modifier = imageModifier) {
                if (preview.isVideo) {
                    XVideoViewWidget(preview.previewSource.model.toString())
                } else {
                    if (preview.transitionSource.model == preview.previewSource.model) {
                        ComposeImage(
                            model = preview.transitionSource.model,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = contentScale,
                            loading = null,
                            failure = placeholder(composePlaceholderDrawable()),
                        )
                    } else {
                        var previewLoadFinish by remember {
                            mutableStateOf(false)
                        }

                        if (!previewLoadFinish) {
                            ComposeImage(
                                model = preview.transitionSource.model,
                                modifier = Modifier.fillMaxSize(),
                                contentScale = contentScale,
                                loading = null,
                                failure = placeholder(composePlaceholderDrawable()),
                            )
                        }

                        ComposeImage(
                            model = preview.previewSource.model,
                            modifier = Modifier.fillMaxSize(),
                            contentScale = contentScale,
                            loading = null,
                        ) {
                            it.listener(object : RequestListener<Drawable> {
                                override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable>, isFirstResource: Boolean): Boolean {
                                    previewLoadFinish = true
                                    return false
                                }

                                override fun onResourceReady(
                                    resource: Drawable,
                                    model: Any,
                                    target: Target<Drawable>?,
                                    dataSource: DataSource,
                                    isFirstResource: Boolean,
                                ): Boolean {
                                    previewLoadFinish = true
                                    return false
                                }
                            })
                        }
                    }
                }
            }
        }
    }
}