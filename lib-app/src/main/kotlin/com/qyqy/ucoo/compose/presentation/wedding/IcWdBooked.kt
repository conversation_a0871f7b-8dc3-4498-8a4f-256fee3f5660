package com.qyqy.ucoo.compose.presentation.wedding

import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.graphics.vector.path
import androidx.compose.ui.unit.dp

val IcWdBooked: ImageVector
    get() {
        if (_WdBooked != null) {
            return _WdBooked!!
        }
        _WdBooked = ImageVector.Builder(
            name = "WdBooked",
            defaultWidth = 34.dp,
            defaultHeight = 28.dp,
            viewportWidth = 34f,
            viewportHeight = 28f
        ).apply {
            path(fill = SolidColor(Color(0xFFFBBBDD))) {
                moveTo(15.949f, 23.565f)
                curveTo(14.849f, 23.565f, 13.786f, 23.403f, 12.783f, 23.102f)
                lineTo(10.035f, 23.965f)
                curveTo(11.805f, 24.883f, 13.818f, 25.403f, 15.949f, 25.403f)
                curveTo(20.891f, 25.403f, 25.188f, 22.611f, 27.322f, 18.533f)
                lineTo(24.565f, 19.399f)
                curveTo(22.559f, 21.934f, 19.443f, 23.565f, 15.949f, 23.565f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFBBBDD))) {
                moveTo(15.95f, 1.838f)
                curveTo(20.174f, 1.838f, 23.847f, 4.222f, 25.673f, 7.705f)
                lineTo(27.462f, 7.142f)
                curveTo(25.38f, 2.917f, 21.002f, 0f, 15.95f, 0f)
                curveTo(8.89f, 0f, 3.146f, 5.698f, 3.146f, 12.701f)
                curveTo(3.146f, 13.392f, 3.201f, 14.071f, 3.309f, 14.732f)
                lineTo(5.098f, 14.17f)
                curveTo(5.032f, 13.689f, 4.999f, 13.199f, 4.999f, 12.701f)
                curveTo(4.999f, 6.711f, 9.912f, 1.838f, 15.95f, 1.838f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFBBBDD))) {
                moveTo(11.724f, 19.633f)
                curveTo(11.759f, 19.783f, 11.793f, 19.93f, 11.826f, 20.073f)
                curveTo(11.856f, 20.211f, 11.885f, 20.373f, 11.911f, 20.56f)
                curveTo(11.956f, 20.847f, 11.973f, 21.091f, 11.961f, 21.291f)
                curveTo(11.952f, 21.497f, 11.901f, 21.674f, 11.809f, 21.823f)
                curveTo(11.72f, 21.978f, 11.578f, 22.111f, 11.383f, 22.223f)
                curveTo(11.196f, 22.339f, 10.945f, 22.451f, 10.63f, 22.56f)
                curveTo(9.272f, 23.019f, 7.908f, 23.458f, 6.538f, 23.879f)
                curveTo(6.231f, 23.971f, 5.96f, 24.023f, 5.726f, 24.035f)
                curveTo(5.5f, 24.052f, 5.3f, 24.019f, 5.125f, 23.935f)
                curveTo(4.956f, 23.849f, 4.808f, 23.708f, 4.681f, 23.511f)
                curveTo(4.553f, 23.314f, 4.436f, 23.054f, 4.33f, 22.731f)
                lineTo(2.867f, 18.264f)
                lineTo(4.149f, 17.904f)
                lineTo(4.466f, 18.873f)
                lineTo(8.778f, 17.483f)
                lineTo(8.211f, 15.754f)
                lineTo(2.51f, 17.591f)
                lineTo(2.106f, 16.356f)
                lineTo(9.035f, 14.123f)
                lineTo(10.404f, 18.304f)
                lineTo(4.865f, 20.09f)
                lineTo(5.487f, 21.99f)
                curveTo(5.541f, 22.155f, 5.594f, 22.285f, 5.647f, 22.38f)
                curveTo(5.699f, 22.475f, 5.76f, 22.543f, 5.829f, 22.584f)
                curveTo(5.905f, 22.622f, 5.997f, 22.635f, 6.105f, 22.621f)
                curveTo(6.219f, 22.605f, 6.361f, 22.57f, 6.533f, 22.515f)
                curveTo(7.649f, 22.176f, 8.76f, 21.818f, 9.864f, 21.441f)
                curveTo(10.048f, 21.382f, 10.191f, 21.325f, 10.292f, 21.271f)
                curveTo(10.394f, 21.218f, 10.469f, 21.158f, 10.518f, 21.094f)
                curveTo(10.565f, 21.022f, 10.588f, 20.942f, 10.586f, 20.851f)
                curveTo(10.591f, 20.758f, 10.581f, 20.643f, 10.556f, 20.503f)
                curveTo(10.534f, 20.371f, 10.503f, 20.212f, 10.464f, 20.029f)
                curveTo(10.425f, 19.845f, 10.392f, 19.702f, 10.365f, 19.598f)
                lineTo(11.724f, 19.633f)
                close()
                moveTo(16.557f, 12.854f)
                lineTo(15.034f, 13.345f)
                lineTo(14.682f, 12.271f)
                lineTo(19.08f, 10.854f)
                lineTo(19.431f, 11.928f)
                lineTo(17.652f, 12.501f)
                curveTo(17.664f, 12.602f, 17.676f, 12.714f, 17.688f, 12.836f)
                curveTo(17.704f, 12.95f, 17.714f, 13.066f, 17.717f, 13.184f)
                lineTo(19.431f, 12.632f)
                lineTo(20.878f, 17.051f)
                lineTo(19.822f, 17.391f)
                lineTo(18.723f, 14.037f)
                lineTo(16.6f, 14.721f)
                lineTo(17.699f, 18.075f)
                lineTo(16.709f, 18.394f)
                lineTo(15.262f, 13.975f)
                lineTo(16.623f, 13.537f)
                lineTo(16.557f, 12.854f)
                close()
                moveTo(14.864f, 17.676f)
                curveTo(14.873f, 17.512f, 14.878f, 17.345f, 14.879f, 17.177f)
                curveTo(14.88f, 17.009f, 14.873f, 16.836f, 14.857f, 16.659f)
                lineTo(14.438f, 16.794f)
                lineTo(15.416f, 19.778f)
                curveTo(15.497f, 20.025f, 15.549f, 20.229f, 15.574f, 20.389f)
                curveTo(15.598f, 20.549f, 15.584f, 20.687f, 15.53f, 20.802f)
                curveTo(15.483f, 20.915f, 15.39f, 21.015f, 15.25f, 21.103f)
                curveTo(15.112f, 21.196f, 14.92f, 21.296f, 14.675f, 21.403f)
                lineTo(14.231f, 21.589f)
                lineTo(13.498f, 20.606f)
                lineTo(13.965f, 20.424f)
                curveTo(14.064f, 20.385f, 14.144f, 20.349f, 14.203f, 20.316f)
                curveTo(14.26f, 20.276f, 14.298f, 20.233f, 14.318f, 20.184f)
                curveTo(14.335f, 20.13f, 14.336f, 20.059f, 14.322f, 19.973f)
                curveTo(14.308f, 19.886f, 14.277f, 19.77f, 14.229f, 19.625f)
                lineTo(13.41f, 17.125f)
                lineTo(12.23f, 17.506f)
                lineTo(11.869f, 16.403f)
                lineTo(13.278f, 15.949f)
                curveTo(13.021f, 15.808f, 12.766f, 15.683f, 12.513f, 15.576f)
                curveTo(12.267f, 15.466f, 12.061f, 15.382f, 11.894f, 15.323f)
                lineTo(12.267f, 14.436f)
                curveTo(12.475f, 14.516f, 12.735f, 14.625f, 13.047f, 14.763f)
                curveTo(13.13f, 14.568f, 13.216f, 14.379f, 13.303f, 14.197f)
                curveTo(13.397f, 14.012f, 13.471f, 13.894f, 13.524f, 13.842f)
                lineTo(11.554f, 14.477f)
                lineTo(11.211f, 13.432f)
                lineTo(14.39f, 12.407f)
                lineTo(14.767f, 13.557f)
                curveTo(14.735f, 13.588f, 14.686f, 13.664f, 14.62f, 13.783f)
                curveTo(14.56f, 13.9f, 14.494f, 14.041f, 14.421f, 14.204f)
                curveTo(14.355f, 14.366f, 14.285f, 14.539f, 14.212f, 14.723f)
                curveTo(14.138f, 14.902f, 14.07f, 15.07f, 14.01f, 15.23f)
                lineTo(14.323f, 15.413f)
                lineTo(14.191f, 15.655f)
                lineTo(15.486f, 15.238f)
                lineTo(15.837f, 16.311f)
                curveTo(15.859f, 16.549f, 15.868f, 16.788f, 15.862f, 17.028f)
                curveTo(15.861f, 17.26f, 15.846f, 17.482f, 15.817f, 17.694f)
                lineTo(14.864f, 17.676f)
                close()
                moveTo(19.053f, 17.198f)
                curveTo(19.262f, 17.257f, 19.495f, 17.325f, 19.752f, 17.403f)
                curveTo(20.015f, 17.479f, 20.277f, 17.56f, 20.536f, 17.644f)
                curveTo(20.793f, 17.723f, 21.037f, 17.805f, 21.269f, 17.891f)
                curveTo(21.505f, 17.969f, 21.7f, 18.04f, 21.854f, 18.102f)
                lineTo(21.571f, 19.359f)
                curveTo(21.403f, 19.274f, 21.208f, 19.182f, 20.987f, 19.086f)
                curveTo(20.772f, 18.987f, 20.548f, 18.891f, 20.314f, 18.798f)
                curveTo(20.087f, 18.703f, 19.854f, 18.614f, 19.616f, 18.529f)
                curveTo(19.384f, 18.443f, 19.166f, 18.366f, 18.961f, 18.299f)
                curveTo(18.851f, 18.713f, 18.655f, 19.119f, 18.372f, 19.518f)
                curveTo(18.089f, 19.918f, 17.711f, 20.337f, 17.237f, 20.777f)
                lineTo(16.194f, 19.968f)
                curveTo(16.652f, 19.59f, 17.008f, 19.254f, 17.263f, 18.962f)
                curveTo(17.516f, 18.663f, 17.691f, 18.383f, 17.788f, 18.12f)
                curveTo(17.888f, 17.85f, 17.923f, 17.58f, 17.89f, 17.31f)
                curveTo(17.864f, 17.038f, 17.799f, 16.74f, 17.693f, 16.417f)
                lineTo(17.176f, 14.84f)
                lineTo(18.292f, 14.522f)
                lineTo(18.831f, 16.166f)
                curveTo(18.945f, 16.514f, 19.019f, 16.858f, 19.053f, 17.198f)
                close()
                moveTo(22.549f, 12.215f)
                lineTo(21.388f, 12.589f)
                lineTo(20.8f, 10.793f)
                lineTo(24.065f, 9.741f)
                lineTo(23.667f, 9.008f)
                lineTo(24.879f, 8.564f)
                lineTo(25.312f, 9.339f)
                lineTo(28.443f, 8.33f)
                lineTo(29.031f, 10.126f)
                lineTo(27.899f, 10.491f)
                lineTo(27.662f, 9.769f)
                lineTo(22.313f, 11.493f)
                lineTo(22.549f, 12.215f)
                close()
                moveTo(27.457f, 15.76f)
                curveTo(28.055f, 15.596f, 28.642f, 15.417f, 29.218f, 15.224f)
                curveTo(29.793f, 15.032f, 30.363f, 14.824f, 30.928f, 14.6f)
                lineTo(30.975f, 15.835f)
                curveTo(30.446f, 16.04f, 29.889f, 16.237f, 29.306f, 16.425f)
                curveTo(28.73f, 16.618f, 28.171f, 16.795f, 27.628f, 16.956f)
                curveTo(27.014f, 17.139f, 26.44f, 17.205f, 25.904f, 17.154f)
                curveTo(25.368f, 17.103f, 24.865f, 16.928f, 24.396f, 16.631f)
                curveTo(24.341f, 16.957f, 24.26f, 17.278f, 24.153f, 17.592f)
                curveTo(24.046f, 17.907f, 23.911f, 18.234f, 23.749f, 18.574f)
                lineTo(22.545f, 18.111f)
                curveTo(22.715f, 17.754f, 22.854f, 17.426f, 22.959f, 17.126f)
                curveTo(23.068f, 16.818f, 23.143f, 16.51f, 23.182f, 16.203f)
                curveTo(23.226f, 15.888f, 23.233f, 15.564f, 23.201f, 15.231f)
                curveTo(23.175f, 14.896f, 23.115f, 14.519f, 23.02f, 14.101f)
                lineTo(24.244f, 13.823f)
                curveTo(24.306f, 14.097f, 24.354f, 14.361f, 24.389f, 14.617f)
                curveTo(24.427f, 14.863f, 24.453f, 15.104f, 24.467f, 15.337f)
                curveTo(24.742f, 15.557f, 25.02f, 15.719f, 25.3f, 15.825f)
                curveTo(25.587f, 15.929f, 25.893f, 15.977f, 26.221f, 15.97f)
                lineTo(25.193f, 12.834f)
                lineTo(22.138f, 13.818f)
                lineTo(21.783f, 12.735f)
                lineTo(28.827f, 10.465f)
                lineTo(29.181f, 11.548f)
                lineTo(26.374f, 12.453f)
                lineTo(26.701f, 13.451f)
                lineTo(29.251f, 12.629f)
                lineTo(29.606f, 13.712f)
                lineTo(27.055f, 14.535f)
                lineTo(27.457f, 15.76f)
                close()
            }
            path(fill = SolidColor(Color(0xFFFFC5EE))) {
                moveTo(13.508f, 5.441f)
                curveTo(13.774f, 5.033f, 14.184f, 4.742f, 14.655f, 4.629f)
                curveTo(15.126f, 4.516f, 15.622f, 4.589f, 16.04f, 4.833f)
                curveTo(16.458f, 5.077f, 16.767f, 5.473f, 16.904f, 5.94f)
                curveTo(17.041f, 6.406f, 16.996f, 6.909f, 16.777f, 7.344f)
                lineTo(14.876f, 10.651f)
                lineTo(11.604f, 8.739f)
                curveTo(11.201f, 8.472f, 10.914f, 8.058f, 10.804f, 7.584f)
                curveTo(10.694f, 7.109f, 10.768f, 6.609f, 11.012f, 6.187f)
                curveTo(11.255f, 5.765f, 11.65f, 5.453f, 12.114f, 5.314f)
                curveTo(12.578f, 5.175f, 13.077f, 5.221f, 13.508f, 5.441f)
                close()
            }
        }.build()

        return _WdBooked!!
    }

@Suppress("ObjectPropertyName")
private var _WdBooked: ImageVector? = null
