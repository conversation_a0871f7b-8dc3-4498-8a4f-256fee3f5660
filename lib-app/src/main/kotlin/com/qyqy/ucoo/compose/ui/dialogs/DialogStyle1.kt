package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText


@Composable
fun DialogStyle1(
    message: String,
    buttonText: String,
    bgRes: Int = R.drawable.bg_dialog_add_gold,
    buttonBgModifier: Modifier = Modifier.background(
        Brush.verticalGradient(
            listOf(Color(0xFFFF93A6), Color(0xFFFF523A)),
        ),
        RoundedCornerShape(50)
    ),
    buttonTextColor: Color = Color.White,
    height: Int = 219,
    onButtonClick: () -> Unit = {}
) {
    Box(
        modifier = Modifier
            .size(270.dp, height.dp)
            .paint(painterResource(id = bgRes), contentScale = ContentScale.Fit)
    ) {

        AppText(
            text = message,
            fontSize = 15.sp,
            color = Color(0xFF593A0C),
            textAlign = TextAlign.Center,
            lineSpacingRatio = 0.25f,
            modifier = Modifier
                .padding(horizontal = 10.dp)
                .offset(y = 8.dp)
                .align(Alignment.Center)
        )

        AppText(
            modifier = Modifier
                .noEffectClickable(onClick = onButtonClick)
                .offset(y = ((-20).dp))
                .then(buttonBgModifier)
                .padding(48.dp, 7.dp)
                .align(Alignment.BottomCenter),
            text = buttonText, fontSize = 16.sp, color = buttonTextColor
        )
    }
}

@Preview(device = "spec:width=375dp,height=640dp")
@Composable
fun NotifyPreview() {
    DialogStyle1(message = "恭喜获得5金币", buttonText = "收下奖励")
}

@Preview
@Composable
fun AnchorRewardPreview() {
    DialogStyle1(
        message = "恭喜完成进阶任务，获得25元现金奖励",
        buttonText = "去查看",
        bgRes = R.drawable.bg_dialog_new_brc,
        buttonBgModifier = Modifier.background(
            Brush.horizontalGradient(
                listOf(Color(0xFFFFF2AC), Color(0xFFFFFDF0)),
            ),
            RoundedCornerShape(50)
        ),
        buttonTextColor = Color(0xFFB32E00)
    )
}

