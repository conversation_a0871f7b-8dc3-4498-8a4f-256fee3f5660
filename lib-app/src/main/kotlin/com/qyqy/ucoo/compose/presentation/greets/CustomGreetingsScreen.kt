package com.qyqy.ucoo.compose.presentation.greets

import android.os.Bundle
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.windowInsetsPadding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.ext.startActivity
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.voice.VoiceList
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.TabWithPager
import com.qyqy.ucoo.feat.audio.VoiceGenerateActivity

object CustomGreetingsNav : ScreenNavigator {
    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        AppTheme {
            CustomGreetingsScreen(activity, onBack = { activity.onBackPressedDispatcher.onBackPressed() }) {
                activity.run {
                    startActivity<VoiceGenerateActivity>()
                }
            }
        }
    }
}

@OptIn(ExperimentalFoundationApi::class)
@Composable
fun CustomGreetingsScreen(
    activity: BaseActivity,
    vm: GreetViewModel = viewModel(),
    onBack: () -> Unit = {},
    onCreateVoiceGreet: () -> Unit
) {
    val greetings by vm.greetingsFlow.collectAsState()
    val tabs = greetings.tabs
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .windowInsetsPadding(WindowInsets.systemBars)
    ) {
        AppTitleBar(title = stringResource(id = R.string.custom_greet), onBack = onBack)
        TabWithPager(tabs = tabs, pagerModifier = Modifier.fillMaxSize(1f)) { index ->
            when (tabs[index].contentType) {
                GreetItem.TYPE_IMAGE -> ImageGreetings(
                    activity,
                    stringResource(id = R.string.format_add_new, tabs[index].name),
                    vm = vm
                )

                GreetItem.TYPE_VOICE -> VoiceList(onClickNewVoice = onCreateVoiceGreet, vmVoice = vm)
                else -> TextGreetings(stringResource(id = R.string.format_add_new, tabs[index].name), vm = vm)
            }
        }
    }
    val loading by vm.loading.collectAsState()
    LocalContentLoading.current.value = loading
}
