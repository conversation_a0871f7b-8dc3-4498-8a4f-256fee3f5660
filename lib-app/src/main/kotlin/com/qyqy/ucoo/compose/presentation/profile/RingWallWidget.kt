package com.qyqy.ucoo.compose.presentation.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.data.RingWallBean
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.compose.ui.dialogs.AlertContent
import com.qyqy.ucoo.compose.vm.OnlineMemberListViewModel
import com.qyqy.ucoo.compose.vm.RingWallHistoryViewModel
import com.qyqy.ucoo.compose.vm.RingWallViewModel
import com.qyqy.ucoo.utils.ToastUtils


/**
 *
 */
@Composable
fun RingWallWidget(userId: Int, profileType: Int = 0, refreshFlag: Int = 0) {
    var showingRingInfo by remember {
        mutableStateOf<RingWallBean?>(null)
    }
    var showResetRingDialog by remember {
        mutableStateOf(false)
    }

    val viewModel = viewModel(modelClass = RingWallViewModel::class, factory = viewModelFactory {
        initializer {
            RingWallViewModel(userId)
        }
    })

    LaunchedEffect(refreshFlag) {
        viewModel.refreshWallInfo()
    }

    val ringList by viewModel.ringWallList.collectAsStateWithLifecycle()

    Column(
        modifier = Modifier
            .padding(16.dp)
            .fillMaxWidth()
            .background(color = Color(0xff12151d), shape = RoundedCornerShape(12.dp))
            .padding(vertical = 16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(modifier = Modifier.padding(top = 4.dp, bottom = 12.dp)) {
            ComposeImage(model = R.drawable.line_right_star)
            Text(
                stringResource(id = R.string.婚礼戒指), fontSize = 14.sp, lineHeight = 14.sp, color = Color(0xffffe589),
                modifier = Modifier.padding(horizontal = 8.dp)
            )
            ComposeImage(model = R.drawable.line_left_star)
        }

        val colorMatrix = remember {
            ColorMatrix().apply {
                // 设置颜色矩阵为黑白
                setToSaturation(0f)
            }
        }

        VerticalGrid(columns = 3) {
            ringList.forEach {
                val isActive = it.count > 0
                Box(
                    modifier = Modifier
                        .padding(horizontal = 4.dp)
                        .size(101.dp, 160.dp)
                        .click(noEffect = true) {
//                            if (profileType == 0) {
                            showingRingInfo = it
//                            }
                        }
                ) {
                    ComposeImage(
                        model = if (isActive)
                            R.drawable.ic_ring_active_bg
                        else R.drawable.ic_ring_inactive_bg,
                        contentScale = ContentScale.FillWidth,
                        modifier = Modifier
                            .padding(top = 12.dp)
                            .fillMaxSize()
                    )
                    if (isActive) {
                        ComposeImage(
                            model = it.ring.effectFile,
                            modifier = Modifier
                                .size(64.dp)
                                .align(Alignment.TopCenter),
                        )
                    } else {
                        ComposeImage(
                            model = it.ring.effectFile, modifier = Modifier
                                .size(64.dp)
                                .align(Alignment.TopCenter),
                            colorFilter = ColorFilter.colorMatrix(colorMatrix)
                        )
                    }
                    Column(
                        modifier = Modifier
                            .align(Alignment.TopCenter)
                            .padding(top = 42.dp), horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        if (it.lastLightUser != null) {
                            ComposeImage(
                                model = it.lastLightUser.avatarUrl, modifier = Modifier
                                    .size(24.dp)
                                    .border(
                                        1.dp, color = Color(0xffffe589),
                                        shape = CircleShape
                                    )
                                    .clip(CircleShape)
                            )
                        } else {
                            Spacer(modifier = Modifier.height(24.dp))
                        }
                        Spacer(modifier = Modifier.height(8.dp))
                        Text(it.ring.name, color = Color(if (isActive) 0xffffe589 else 0x4DFFFFFF), fontSize = 14.sp, lineHeight = 14.sp)
                        Spacer(modifier = Modifier.height(8.dp))
                        Text("x${it.count}", color = Color(if (isActive) 0xffffe589 else 0x4DFFFFFF), fontSize = 14.sp, lineHeight = 14.sp)
                    }
                    Text(

                        if (isActive) stringResource(id = R.string.点亮时间, it.formatTime) else stringResource(id = R.string.未点亮),
                        modifier = Modifier
                            .fillMaxWidth()
                            .align(Alignment.BottomCenter)
                            .background(
                                color = Color(if (isActive) 0xff1f2145 else 0x4D1F2145), shape = RoundedCornerShape(
                                    bottomStart = 10.dp, bottomEnd = 10.dp
                                )
                            ),
                        fontSize = 11.sp,
                        color = Color(if (isActive) 0x99FFFFFF else 0x4DFFFFFF),
                        textAlign = TextAlign.Center,
                        lineHeight = 28.sp,
                    )
                }
            }
        }

        if (profileType == 0) {
            Text(
                stringResource(id = R.string.重新开始收集),
                modifier = Modifier
                    .padding(top = 24.dp)
                    .paint(painter = painterResource(id = R.drawable.ic_ring_reset_bg), contentScale = ContentScale.FillWidth)
                    .click {
                        if (false) {
                            ToastUtils.showShort(R.string.还没有点亮戒指)
                        } else {
                            showResetRingDialog = true
                        }
                    },
                textAlign = TextAlign.Center,
                color = Color(0xffd3d4fb),
                fontSize = 14.sp,
                lineHeight = 40.sp
            )
        }
    }

    //region 戒指详情弹窗
//    if (showingRingInfo != null) {
//        AnimatedDialog(
//            onDismiss = { showingRingInfo = null },
//            properties = AnyPopDialogProperties(direction = DirectionState.BOTTOM)
//        ) {
//            RingDetailWidget(onBack = {
//                showingRingInfo = null
//            })
//        }
//    }
    RingDetailDialog(userId, showingRingInfo) {
        showingRingInfo = null
    }

//    val sheetState = rememberModalBottomSheetState()
//    LaunchedEffect(key1 = showingRingInfo) {
//        if (showingRingInfo == null) {
//            sheetState.hide()
//        } else {
//            sheetState.show()
//        }
//    }
//    if (showingRingInfo != null) {
//        ModalBottomSheet(
//            sheetState = sheetState,
//            onDismissRequest = {
//                showingRingInfo = null
//            },
//            containerColor = Color.Black
//        ) {
//            RingDetailWidget(onBack = {
//                showingRingInfo = null
//            })
//        }
//    }
    //endregion

    if (showResetRingDialog) {
        AnimatedDialog(
            onDismiss = { showResetRingDialog = false },
            properties = AnyPopDialogProperties(direction = DirectionState.CENTER)
        ) {
            //注意: 这里的confirm和cancel反转了,
            AlertContent(
                title = stringResource(id = R.string.戒指重置确认标题),
                content = stringResource(id = R.string.戒指重置确认内容),
                buttonCancelText = stringResource(id = R.string.确认),
                buttonConfirm = stringResource(id = R.string.取消),
                onCancel = {
                    showResetRingDialog = false
                    viewModel.resetRingWall()
                },
                onConfirm = {
                    showResetRingDialog = false
                }
            )
        }
    }
}

@Composable
fun RingDetailDialog(userId: Int, ringBean: RingWallBean?, onDismiss: () -> Unit) {
    if (ringBean != null) {
        var isActiveClose = remember {
            mutableStateOf(false)
        }
        AnimatedDialog(
            isActiveClose = isActiveClose.value,
            onDismiss = { onDismiss() },
            properties = AnyPopDialogProperties(direction = DirectionState.BOTTOM)
        ) {
            RingDetailWidget(userId, ringBean, onBack = {
                isActiveClose.value = true
            })
        }
    }
}

@Composable
private fun RingDetailWidget(userId: Int, ringBean: RingWallBean, modifier: Modifier = Modifier, onBack: () -> Unit = {}) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .height(520.dp)
            .background(
                brush = Brush.verticalGradient(
                    listOf(Color(0xff202247), Color(0xff101024))
                ), shape = RoundedCornerShape(12.dp)
            ),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        AppTitleBar(title = ringBean.ring.name, onBack = onBack)


        val vm = viewModel(modelClass = RingWallHistoryViewModel::class)

        val list by vm.dataState

        LaunchedEffect(key1 = ringBean.ring.id, key2 = userId) {
            vm.updateParams(userId, ringBean.ring.id)
        }

        LazyColumn(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .weight(1f)
                .padding(horizontal = 16.dp)
        ) {
            item {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    if (ringBean.count > 0) {
                        ComposeImage(
                            model = ringBean.ring.effectFile,
                            modifier = Modifier
                                .padding(vertical = 24.dp)
                                .size(120.dp)
                        )
                    } else {
                        val colorMatrix = remember {
                            ColorMatrix().apply {
                                // 设置颜色矩阵为黑白
                                setToSaturation(0f)
                            }
                        }
                        ComposeImage(
                            model = ringBean.ring.icon,
                            modifier = Modifier
                                .padding(vertical = 24.dp)
                                .size(120.dp),
                            colorFilter = ColorFilter.colorMatrix(colorMatrix)
                        )
                    }

                    Text(
                        "x${ringBean.count}",
                        color = Color(0xffffe589),
                        fontSize = 24.sp,
                        modifier = Modifier.padding(bottom = 16.dp)
                    )

                    Text(
                        stringResource(id = R.string.当前点亮总数),
                        color = Color(0x90ffffff),
                        fontSize = 12.sp,
                        modifier = Modifier.padding(bottom = 24.dp)
                    )

                }
            }

            if (vm.isLoaded) {
                item {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Center,
                        modifier = Modifier
                            .fillMaxWidth()
                            .background(Color(0xff1F2145), RoundedCornerShape(topStart = 8.dp, topEnd = 8.dp))
                            .padding(vertical = 16.dp)
                    ) {
                        Spacer(
                            modifier = Modifier
                                .padding(end = 12.dp)
                                .width(80.dp)
                                .height(0.5.dp)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        listOf(Color(0x00ffe589), Color(0xffffe589))
                                    )
                                )
                        )
                        Text(
                            stringResource(id = R.string.最近点亮),
                            color = Color(0xffffe589),
                            fontSize = 14.sp,
                        )
                        Spacer(
                            modifier = Modifier
                                .padding(start = 12.dp)
                                .width(80.dp)
                                .height(0.5.dp)
                                .background(
                                    brush = Brush.horizontalGradient(
                                        listOf(Color(0xffffe589), Color(0x00ffe589))
                                    )
                                )
                        )
                    }

                    if (list.isNotEmpty()) {

                        Column(
                            modifier = Modifier
                                .background(Color(0xff1F2145))
                                .padding(horizontal = 16.dp)
                        ) {
                            list.firstOrNull()?.let {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    modifier = Modifier
                                        .background(color = Color(0x08ffffff), shape = RoundedCornerShape(6.dp))
                                        .padding(vertical = 6.dp, horizontal = 12.dp)
                                ) {
                                    ComposeImage(
                                        model = it.fromUser.avatarUrl,
                                        modifier = Modifier
                                            .size(32.dp)
                                            .border(1.dp, Color(0xffffe589), CircleShape)
                                            .clip(CircleShape)
                                    )
                                    Text(
                                        it.fromUser.nickname,
                                        color = Color.White,
                                        fontSize = 14.sp,
                                        modifier = Modifier
                                            .weight(1f)
                                            .padding(horizontal = 12.dp),
                                        overflow = TextOverflow.Ellipsis
                                    )
                                    Text(
                                        stringResource(id = R.string.点亮时间, it.formatTime),
                                        color = Color(0x90ffffff),
                                        fontSize = 12.sp
                                    )
                                }

                            }

                            if (list.size > 1) {
                                Row(
                                    verticalAlignment = Alignment.CenterVertically,
                                    horizontalArrangement = Arrangement.Center,
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(vertical = 16.dp)
                                ) {
                                    Spacer(
                                        modifier = Modifier
                                            .padding(end = 12.dp)
                                            .width(80.dp)
                                            .height(0.5.dp)
                                            .background(
                                                brush = Brush.horizontalGradient(
                                                    listOf(Color(0x00ffe589), Color(0xffffe589))
                                                )
                                            )
                                    )
                                    Text(
                                        stringResource(id = R.string.历史点亮),
                                        color = Color(0xffffe589),
                                        fontSize = 14.sp,
                                    )
                                    Spacer(
                                        modifier = Modifier
                                            .padding(start = 12.dp)
                                            .width(80.dp)
                                            .height(0.5.dp)
                                            .background(
                                                brush = Brush.horizontalGradient(
                                                    listOf(Color(0xffffe589), Color(0x00ffe589))
                                                )
                                            )
                                    )
                                }
                            }
                        }
                    } else {
                        Text(
                            stringResource(id = R.string.暂无点亮记录), fontSize = 14.sp, lineHeight = 156.sp,
                            color = Color(0x80ffffff),
                            fontWeight = FontWeight.Medium,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .fillMaxWidth()
                                .background(
                                    Color(0xff1F2145),
                                    RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp)
                                )
                        )
                    }
                }

                itemsIndexed(list) { index, it ->
                    if (index > 0) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier
                                .background(Color(0xff1F2145))
                                .padding(vertical = 8.dp, horizontal = 16.dp)
                        ) {
                            Text(
                                it.fromUser.nickname,
                                color = Color.White,
                                fontSize = 14.sp,
                                modifier = Modifier
                                    .weight(1f)
                                    .padding(horizontal = 12.dp),
                                overflow = TextOverflow.Ellipsis
                            )
                            Text(
                                stringResource(id = R.string.点亮时间, it.formatTime),
                                color = Color(0x90ffffff),
                                fontSize = 12.sp
                            )
                        }
                    }
                }

                item {
                    Spacer(
                        modifier = Modifier
                            .padding(bottom = 8.dp)
                            .height(16.dp)
                            .background(
                                Color(0xff1F2145),
                                RoundedCornerShape(bottomStart = 8.dp, bottomEnd = 8.dp)
                            )
                    )
                }
            }

            itemLoadMore(list.isNotEmpty() && vm.allowLoad, vm.isLoadingNow, vm.hasMore) {
                vm.loadMore()
            }
        }
    }
}

@Preview
@Composable
private fun previewRingWallWidget() {
    RingWallWidget(-1)
}

@Preview
@Composable
private fun previewRingDetailWidget() {
    RingDetailWidget(-1, RingWallBean(ring = RingWallBean.Ring(name = "这是戒指名称")))
}

