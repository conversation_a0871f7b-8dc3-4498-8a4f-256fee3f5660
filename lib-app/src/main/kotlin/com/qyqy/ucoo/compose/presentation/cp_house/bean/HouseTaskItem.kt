package com.qyqy.ucoo.compose.presentation.cp_house.bean


import com.qyqy.ucoo.im.message.entity.RichItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

enum class TaskType(val code: Int, val description: String) {
    CHECK_IN(1, "签到"),
    TODAY_FIRST_GIVE_GIFT(2, "今日首次送礼"),
    TODAY_GIVE_GIFT_COIN(3, "今日互赠礼物"),
    TODAY_COMMON_MIC_IN_PRIVATE_ROOM_DURATION(4, "今日私密小屋共同在麦"),
    GIVE_GIFT(5, "送礼物")
}


@Serializable
data class TaskSeries(
    val id: Int,
    val name: String,
    val type: Int,
    @SerialName("tasks")
    val taskList: List<HouseTaskItem>
)


@Serializable
data class HouseTaskItem(
    @SerialName("task_bonus")
    val bonus: String = "",
    @SerialName("task_bonus_rich_desc")
    val bonusRichDesc: List<RichItem> = listOf(),
    @SerialName("task_btn_label")
    val btnLabel: String = "",
    @SerialName("task_icon")
    val icon: String = "",
    @SerialName("task_id")
    val id: Int = 0,
    @SerialName("task_is_finished")
    val isFinished: Boolean = false,
    @SerialName("task_name")
    val name: String = "",
    @SerialName("task_process")
    val process: Process = Process(),
    @SerialName("task_type")
    val type: Int = 0
) {
    @Serializable
    data class Bonus(
        @SerialName("cp_room_coin")
        val cpRoomCoin: Int = 0,
        @SerialName("intimacy_score")
        val intimacyScore: Int = 0
    )

    @Serializable
    data class Process(
        @SerialName("current")
        val current: Int = 0,
        @SerialName("total")
        val total: Int = 0
    )
}