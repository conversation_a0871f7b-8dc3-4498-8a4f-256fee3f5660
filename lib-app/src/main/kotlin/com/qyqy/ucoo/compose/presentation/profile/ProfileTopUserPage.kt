@file:OptIn(ExperimentalFoundationApi::class)

package com.qyqy.ucoo.compose.presentation.profile

import android.app.Activity
import android.content.Intent
import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.LifecycleStartEffect
import com.bumptech.glide.integration.compose.placeholder
import com.coolerfall.download.Priority
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.greets.GreetingsApi
import com.qyqy.ucoo.compose.presentation.wedding.WeddingNavigator
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.rememberAnimatedDrawablePainter
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi
import com.qyqy.ucoo.feat.audio.VoiceGenerateActivity
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.compat.chat.utils.VoiceMessagePlayController
import com.qyqy.ucoo.im.inputpanel.audio.AudioPlayManager
import com.qyqy.ucoo.im.inputpanel.audio.IAudioPlayListener
import com.qyqy.ucoo.mine.EditAlbumActivity
import com.qyqy.ucoo.utils.download.DownloadHelper
import com.qyqy.ucoo.utils.download.isHttpUrl
import kotlinx.coroutines.delay
import kotlinx.coroutines.ensureActive
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch

@Composable
fun ProfileTopPage(
    state: ProfileMvi.ViewState,
    modifier: Modifier = Modifier,
    onRefresh: () -> Unit = {},
) {
    val user = state.user
    val context = LocalContext.current
    val pagerState = rememberPagerState {
        user.albumList.size.plus(1)
    }
    LaunchedEffect(pagerState) {
        while (isActive) {
            delay(2500)
            if (pagerState.currentPage < pagerState.pageCount.minus(1)) {
                pagerState.animateScrollToPage(pagerState.currentPage.plus(1))
            } else {
                pagerState.animateScrollToPage(0)
            }
        }
    }
    val scope = rememberCoroutineScope()
    Box(
        modifier = modifier
            .fillMaxWidth()
            .height(375.dp)
    ) {
        HorizontalPager(
            state = pagerState,
            verticalAlignment = Alignment.Top,
            modifier = Modifier
                .fillMaxSize()
                .drawWithContent {
                    drawContent()
                    drawRect(Brush.verticalGradient(listOf(Color(0x66000000), Color(0xBF000000))))
                },
        ) { index ->
            if (index == 0) {
                ComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier.fillMaxSize(),
                )
            } else {
                ComposeImage(
                    model = user.albumList[index.minus(1)].url,
                    modifier = Modifier.fillMaxSize(),
                )
            }
        }

        Column(
            modifier = Modifier
                .align(Alignment.BottomStart)
                .padding(start = 16.dp, bottom = 20.dp), verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {
            val cpPublish = user.publicCP
            if (cpPublish != null) {
                Column(
                    modifier = Modifier.noEffectClickable {
                        UserProfileNavigator.navigate(context, cpPublish)
                    }, horizontalAlignment = Alignment.CenterHorizontally, verticalArrangement = Arrangement.spacedBy((-14).dp)
                ) {
                    CircleComposeImage(
                        model = cpPublish.avatarUrl,
                        modifier = Modifier
                            .size(100.dp)
                            .border(2.dp, Color.White, CircleShape),
                    )
                    val url = user.cpExtraInfo?.levelInfo?.normalImgUrl
                    val model = if (url.isNullOrEmpty()) {
                        R.drawable.ic_show_love_bar
                    } else {
                        url
                    }
                    ComposeImage(
                        model = model,
                        modifier = Modifier
                            .height(28.dp)
                            .widthIn(max = 100.dp),
                        contentScale = ContentScale.FillHeight,
                        loading = placeholder(R.drawable.ic_show_love_bar)
                    )
                }
            }

            val sound = state.extra.sound?.takeIf {
                it.url?.isHttpUrl == true
            }

            if ((user.isSelf && user.isHighQuality == true) || sound != null) {

                val playState = if (sound == null) {
                    null
                } else {
                    // 不是自己就自动播放语音
                    rememberPlayState(sound.requireUrl, if (user.isSelf) null else 1)
                }

                val launcher = rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) {
                    if (it.resultCode == Activity.RESULT_OK) { // 刷新语音
                        onRefresh()
                    }
                }

                Row(verticalAlignment = Alignment.CenterVertically) {
                    Row(
                        modifier = Modifier
                            .size(110.dp, 32.dp)
                            .background(Color(0xFF7D3E9A), CircleShape)
                            .noEffectClickable {
                                if (playState == null) {
                                    launcher.launch(Intent(context, VoiceGenerateActivity::class.java).also {
                                        it.putExtra("isSoundBrand", true)
                                    })
                                } else {
                                    playState.intValue = if (playState.intValue == 0) 1 else 0
                                }
                            }, verticalAlignment = Alignment.CenterVertically
                    ) {
                        Image(
                            painter = if (playState?.intValue.orDefault(0) == 0) {
                                painterResource(id = R.drawable.ic_user_voice_paused)
                            } else {
                                painterResource(id = R.drawable.ic_user_voice_playing)
                            }, contentDescription = null, modifier = Modifier
                                .padding(end = 3.dp)
                                .requiredSize(34.dp)
                        )

                        if (sound == null) {
                            AutoSizeText(
                                text = stringResource(id = R.string.添加语音签名),
                                modifier = Modifier.padding(end = 12.dp),
                                color = Color.White,
                                fontSize = 14.sp
                            )
                        } else {

                            val isPlayingState = remember {
                                derivedStateOf {
                                    playState?.intValue == 2
                                }
                            }

                            val painter = rememberAnimatedDrawablePainter(
                                resId = R.drawable.ic_webp_sound, isPlayingState = isPlayingState, maybeReset = true
                            )

                            Image(
                                painter = painter,
                                contentDescription = null,
                                modifier = Modifier
                                    .padding(horizontal = 10.dp)
                                    .weight(1f),
                                contentScale = ContentScale.FillWidth
                            )

                            AutoSizeText(
                                text = "${sound.duration}″", modifier = Modifier.padding(end = 12.dp), color = Color.White, fontSize = 14.sp
                            )
                        }
                    }

                    if (user.isSelf && sound != null) {

                        var showEditDialog by remember {
                            mutableStateOf(false)
                        }
                        IconButton(
                            onClick = {
                                showEditDialog = true
                            }, modifier = Modifier
                                .padding(start = 4.dp)
                                .size(18.dp)
                        ) {
                            Icon(
                                painter = painterResource(id = R.drawable.ic_edit_info), contentDescription = null, tint = Color.White
                            )
                        }

                        if (showEditDialog) {
                            Dialog(onDismissRequest = {
                                showEditDialog = false
                            }) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .background(Color(0xFF222222), shape = Shapes.small),
                                    horizontalAlignment = Alignment.CenterHorizontally
                                ) {
                                    Text(
                                        text = stringResource(id = R.string.是否要删除或者修改声音名片),
                                        modifier = Modifier.padding(vertical = 30.dp, horizontal = 15.dp),
                                        color = Color(0x80FFFFFF),
                                        fontSize = 15.sp
                                    )
                                    Row(
                                        modifier = Modifier.padding(top = 5.dp, bottom = 20.dp), horizontalArrangement = Arrangement.spacedBy(12.dp)
                                    ) {
                                        Text(
                                            text = stringResource(id = R.string.delete),
                                            modifier = Modifier
                                                .size(113.dp, 36.dp)
                                                .background(Color(0xFF464646), CircleShape)
                                                .wrapContentHeight()
                                                .noEffectClickable {
                                                    showEditDialog = false
                                                    scope.launch {
                                                        runApiCatching { createApi<GreetingsApi>().deleteSound() }
                                                            .onSuccess {
                                                                onRefresh()
                                                            }
                                                            .toastError()
                                                    }
                                                },
                                            color = Color(0xFFA3A3A3),
                                            fontSize = 16.sp,
                                            textAlign = TextAlign.Center
                                        )

                                        Text(
                                            text = stringResource(id = R.string.修改),
                                            modifier = Modifier
                                                .size(113.dp, 36.dp)
                                                .background(Color(0xFF945EFF), CircleShape)
                                                .wrapContentHeight()
                                                .noEffectClickable {
                                                    showEditDialog = false
                                                    launcher.launch(Intent(context, VoiceGenerateActivity::class.java).also {
                                                        it.putExtra("isSoundBrand", true)
                                                    })
                                                },
                                            color = Color.White,
                                            fontSize = 16.sp,
                                            textAlign = TextAlign.Center
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        Column(
            Modifier
                .align(Alignment.BottomEnd)
                .padding(bottom = 18.dp),
            horizontalAlignment = Alignment.End
        ) {
            user.ringInfo?.let {
                val context = LocalContext.current
                Column(
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .background(
                            color = Color(0x4d000000),
                            shape = RoundedCornerShape(8.dp)
                        )
                        .padding(4.dp)
                        .click {
                            WeddingNavigator.navigate(context, from = 3)
                        },
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    ComposeImage(model = it.effect_file, modifier = Modifier.size(48.dp), contentScale = ContentScale.FillWidth)
                    Text(
                        it.name, color = Color(0xFFFFE589), fontSize = 10.sp, lineHeight = 10.sp,
                        maxLines = 1, overflow = TextOverflow.Ellipsis,
                        modifier = Modifier.widthIn(max = 56.dp)
                    )
                }
            }

            if (user.isSelf || user.albumList.isNotEmpty()) {
                val lazyListState = rememberLazyListState()
                LaunchedEffect(key1 = pagerState.currentPage) {
                    lazyListState.animateScrollToItem(pagerState.currentPage)
                }
                LazyRow(
                    modifier = Modifier
                        .fillMaxWidth(0.53333f),
                    state = lazyListState,
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    contentPadding = PaddingValues(end = 8.dp)
                ) {
                    if (user.isSelf) {
                        item(
                            key = "add", contentType = 0
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_add),
                                contentDescription = null,
                                modifier = Modifier
                                    .size(44.dp)
                                    .background(Color(0xFF2E2E35), Shapes.corner12)
                                    .clip(Shapes.corner12)
                                    .clickable {
                                        context.startActivity(Intent(context, EditAlbumActivity::class.java))
                                    },
                                contentScale = ContentScale.Inside,
                            )
                        }
                    }
                    item(
                        key = user.avatarUrl,
                        contentType = 1,
                    ) {
                        ComposeImage(
                            model = user.avatarUrl, modifier = Modifier
                                .size(44.dp)
                                .clip(Shapes.corner12)
                                .noEffectClickable {
                                    scope.launch {
                                        pagerState.animateScrollToPage(0)
                                    }
                                }, preview = painterResource(id = R.drawable.ic_google_pay)
                        )
                    }
                    itemsIndexed(user.albumList, key = { _, it ->
                        it.url
                    }, contentType = { _, _ ->
                        1
                    }) { index, it ->
                        ComposeImage(
                            model = it.url, modifier = Modifier
                                .size(44.dp)
                                .clip(Shapes.corner12)
                                .noEffectClickable {
                                    scope.launch {
                                        pagerState.animateScrollToPage(index.plus(1))
                                    }
                                }, preview = painterResource(id = R.drawable.ic_google_pay)
                        )
                    }
                }
            }
        }
    }
}

/**
 * 0: 初始状态
 * 1: 触发播放，但还未播放
 * 2: 正在播放
 */
@Composable
private fun rememberPlayState(url: String, initialValue: Int? = null): MutableIntState {

    val context = LocalContext.current

    val playState = remember {
        mutableIntStateOf(initialValue ?: if (AudioPlayManager.getInstance().isPlaying) 2 else 0)
    }

    if (playState.intValue == 1) {
        LaunchedEffect(Unit) {
            val file = DownloadHelper.executeDownload(
                requestWrapper = DownloadHelper.createRequestWrapper(url, VoiceMessagePlayController.AUDIO_TYPE) {
                    priority(Priority.HIGH)
                }, reDownloadIfPending = true, cancelDownloadIfCancelCoroutine = false
            )

            ensureActive()

            if (file != null) {
                AudioPlayManager.getInstance().stopPlay()

                AudioPlayManager.getInstance().startPlay(context, Uri.fromFile(file), object : IAudioPlayListener {
                    override fun onStart(uri: Uri?) {
                        playState.intValue = 2
                    }

                    override fun onStop(uri: Uri?) {
                        playState.intValue = 0
                    }

                    override fun onComplete(uri: Uri?) {
                        playState.intValue = 0
                    }
                })
            } else {
                playState.intValue = 0
            }
        }
    }

    if (playState.intValue > 0) {
        LifecycleStartEffect(key1 = Unit) {
            onStopOrDispose {
                AudioPlayManager.getInstance().stopPlay()
            }
        }
    }

    return playState
}


@Preview(widthDp = 375)
@Composable
fun PreviewProfileTopPage() {
    ProfileTopPage(ProfileMvi.ViewState(user = userForPreview, tabs = emptyList()))
}