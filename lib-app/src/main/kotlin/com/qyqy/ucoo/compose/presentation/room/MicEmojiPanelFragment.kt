package com.qyqy.ucoo.compose.presentation.room

import android.os.Bundle
import android.os.Parcelable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.activityViewModels
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseFragment
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.state.Empty
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.loadingUiState
import com.qyqy.ucoo.compose.state.toUIData
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LoadingWidget
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.im.compat.IIMEnvironmentScope
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.ChatRoomViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import kotlin.time.Duration.Companion.seconds

/**
 * ```
 * {
 * "emoji_list": [
 * {
 * "id": 1,
 * "name": "笑脸",
 * "icon": "icon url",
 * "effect_file": "effect file url",
 * "type": 1,
 * "is_locked": true
 * }
 * ],
 * "is_show": false
 * }
 * ```
 */
@Serializable
data class MicEmojiList(
    @SerialName("emoji_list")
    val emojiList: List<Emoji> = listOf(),
    @SerialName("is_show")
    val isShow: Boolean = false // false
)

@Parcelize
@Serializable
data class Emoji(
    @SerialName("icon")
    val icon: String = "", // icon url
    @SerialName("id")
    val id: Int = 0, // 1
    @SerialName("is_locked")
    val isLocked: Boolean = false, // true
    @SerialName("name")
    val name: String = "", // 笑脸
) : Parcelable


class MicEmojiPanelFragment : BaseFragment() {

    private val chatViewModel by activityViewModels<ChatRoomViewModel>()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        return ComposeView(requireContext()).apply {
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT).also {
                it.gravity = Gravity.BOTTOM
            }
            // Dispose of the Composition when the view's LifecycleOwner
            // is destroyed
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)

            setContent {
                PanelContent()
            }
        }
    }


    @Preview
    @Composable
    private fun PanelContent() {

        var state: UIState<List<Emoji>> by remember {
            mutableStateOf(loadingUiState())
        }

        if (state.isLoading) {
            LaunchedEffect(Unit) {
                val list = chatViewModel.getMicEmojiList()
                state = list?.toUIData() ?: UIState.Error(Empty(R.string.加载失败点击重试, 0), null)
            }
        }

        var timeLimit by rememberSaveable {
            mutableIntStateOf(0)
        }

        if (timeLimit > 0) {
            LaunchedEffect(Unit) {
                delay(timeLimit.seconds)
                timeLimit = 0
            }
        }

        val pagerState = rememberPagerState {
            val list = state.data
            if (list.isNullOrEmpty()) {
                1
            } else {
                (list.size - 1) / 10 + 1
            }
        }

        val scope = rememberCoroutineScope()

        Column(
            modifier = Modifier
                .fillMaxWidth()
                .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .background(Color(0xFF222222))
                .drawWithContent {
                    drawContent()
                    if (timeLimit > 0) {
                        drawRect(Color(0x80222222))
                    }
                }
                .navigationPadding(minimumPadding = 12.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val emojiList = state.data

            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 160.dp),
                beyondViewportPageCount = 2,
                verticalAlignment = Alignment.Top,
            ) { page ->
                if (emojiList?.isEmpty() == true) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(160.dp),
                        verticalArrangement = Arrangement.Center,
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Image(
                            painter = painterResource(R.drawable.ic_task_empty),
                            contentDescription = null,
                            modifier = Modifier
                                .padding(top = 20.dp, bottom = 10.dp)
                                .size(72.dp)
                        )
                        Text(
                            text = stringResource(id = R.string.什么都没有),
                            color = colorResource(id = R.color.white_alpha_50),
                            fontSize = 14.sp
                        )
                    }
                } else {
                    VerticalGrid(
                        modifier = Modifier
                            .padding(top = 10.dp, bottom = 6.dp)
                            .fillMaxWidth()
                            .padding(horizontal = 12.dp),
                        columns = 5,
                        horizontalSpace = 5.dp,
                        wrapRowHeight = true
                    ) {
                        if (emojiList == null) {
                            repeat(10) {
                                Column(
                                    modifier = Modifier.padding(vertical = 6.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.Center
                                ) {
                                    Spacer(
                                        modifier = Modifier
                                            .size(40.dp)
                                            .clip(CircleShape)
                                            .background(Color(0x22F1F1F1))
                                    )

                                    Spacer(modifier = Modifier.height(8.dp))

                                    Spacer(
                                        modifier = Modifier
                                            .size(40.dp, 12.dp)
                                            .clip(RoundedCornerShape(2.dp))
                                            .background(Color(0x22F1F1F1))
                                    )
                                }
                            }
                        } else {
                            val startIndex = page * 10
                            val count = (emojiList.size - startIndex).coerceAtMost(10)
                            repeat(count) {
                                val item = emojiList[startIndex + it]
                                Column(
                                    modifier = Modifier
                                        .clip(RoundedCornerShape(8.dp))
                                        .clickable(!item.isLocked && timeLimit <= 0) {
                                            timeLimit = 5
                                            scope.launch {
                                                timeLimit = chatViewModel.sendMicEmoji(item.id) ?: 0
                                            }
                                        }
                                        .padding(vertical = 6.dp),
                                    horizontalAlignment = Alignment.CenterHorizontally,
                                    verticalArrangement = Arrangement.Center
                                ) {

                                    Box(
                                        modifier = Modifier
                                            .size(40.dp)
                                            .clip(CircleShape)
                                    ) {
                                        ComposeImage(
                                            model = item.icon,
                                            modifier = Modifier.fillMaxSize(),
                                            contentScale = ContentScale.Fit
                                        )

                                        if (item.isLocked) {
                                            Image(
                                                painter = painterResource(id = R.drawable.ic_mic_emoji_locked),
                                                contentDescription = null,
                                                modifier = Modifier
                                                    .fillMaxSize()
                                                    .background(Color(0x80000000))
                                                    .padding(10.dp),
                                            )
                                        }
                                    }

                                    Spacer(modifier = Modifier.height(8.dp))

                                    AutoSizeText(
                                        text = item.name,
                                        color = Color.White,
                                        fontSize = 12.sp,
                                        maxLines = 3,
                                        minTextSize = 10.sp,
                                        maxTextSize = 12.sp,
                                        alignment = Alignment.Center
                                    )
                                }
                            }
                        }
                    }
                }
            }

            if (pagerState.pageCount > 1) {

                val indicatorScrollState = rememberLazyListState()

                val scope = rememberCoroutineScope()

                val density = LocalDensity.current

                LaunchedEffect(key1 = pagerState.currentPage, key2 = density, block = {
                    try {
                        with(density) {
                            indicatorScrollState.animateScrollToItem(pagerState.currentPage, -32.dp.roundToPx())
                        }
                    } catch (e: Exception) {
                    }
                })

                LazyRow(
                    state = indicatorScrollState,
                    modifier = Modifier
                        .height(24.dp)
                        .width(80.dp), // I'm hard computing it to simplify
                    horizontalArrangement = Arrangement.Center,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    repeat(pagerState.pageCount) { iteration ->
                        val color = if (pagerState.currentPage == iteration) Color.White else Color.White.copy(0.3f)
                        item(key = "item$iteration") {
                            Box(
                                modifier = Modifier
                                    .padding(horizontal = 4.dp)
                                    .size(8.dp)
                                    .clip(CircleShape)
                                    .background(color)
                                    .clickable(timeLimit <= 0) {
                                        scope.launch {
                                            pagerState.animateScrollToPage(iteration)
                                        }
                                    }
                            )
                        }
                    }
                }
            } else {
                Box(
                    modifier = Modifier.height(24.dp),
                    contentAlignment = Alignment.Center
                ) {
                    if (state.isLoading) {
                        LoadingWidget()
                    } else if (state.isError) {
                        val error = state as UIState.Error
                        Text(
                            text = error.text.orEmpty(),
                            modifier = Modifier.noEffectClickable(onClick = {
                                state = loadingUiState()
                            }),
                            color = colorResource(id = R.color.red_300),
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }
    }

    private fun sendMessage(type: Int, result: String) {

    }
}