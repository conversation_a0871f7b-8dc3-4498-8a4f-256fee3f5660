package com.qyqy.ucoo.compose.ui

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.ripple.rememberRipple
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonColors
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ButtonElevation
import androidx.compose.material3.ElevatedButton
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.contentColorFor
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.RectangleShape
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.compose.noEffectClickable

@Composable
fun CommonButton(
    modifier: Modifier = Modifier,
    gradient: Brush? = null,
    colors: ButtonColors = ButtonDefaults.buttonColors(Color.Transparent),
    shape: Shape = RectangleShape,
    border: BorderStroke? = null,
    enabled: Boolean = true,
    contentPadding: PaddingValues = PaddingValues(),
    onClick: () -> Unit = { },
    content: @Composable BoxScope.() -> Unit,
) {
    Button(
        modifier = modifier,
        shape = shape,
        colors = colors,
        border = border,
        enabled = enabled,
        contentPadding = PaddingValues(),
        onClick = onClick,
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .run {
                    if (gradient == null) {
                        this
                    } else {
                        background(gradient)
                    }
                }
                .padding(contentPadding),
            contentAlignment = Alignment.Center,
            content = content
        )
    }
}


@Composable
fun CommonElevatedButton(
    modifier: Modifier = Modifier,
    gradient: Brush? = null,
    colors: ButtonColors = ButtonDefaults.buttonColors(Color.Transparent),
    shape: Shape = RectangleShape,
    border: BorderStroke? = null,
    enabled: Boolean = true,
    contentPadding: PaddingValues = PaddingValues(),
    elevation: ButtonElevation? = ButtonDefaults.elevatedButtonElevation(),
    onClick: () -> Unit = { },
    content: @Composable BoxScope.() -> Unit,
) {
    ElevatedButton(
        modifier = modifier,
        shape = shape,
        colors = colors,
        border = border,
        enabled = enabled,
        elevation = elevation,
        contentPadding = PaddingValues(),
        onClick = onClick,
    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .run {
                    if (gradient == null) {
                        this
                    } else {
                        background(gradient)
                    }
                }
                .padding(contentPadding),
            contentAlignment = Alignment.Center,
            content = content
        )
    }
}

@Composable
fun AppButton(
    modifier: Modifier = Modifier,
    text: String,
    color: Color = Color.White,
    fontSize: TextUnit = 14.sp,
    paddingValues: PaddingValues = PaddingValues(8.dp),
    fontWeight: FontWeight? = null,
    isEnable: Boolean = true,
    onClick: () -> Unit = {},
) {
    AppText(
        text = text,
        color = color,
        fontWeight = fontWeight,
        textAlign = TextAlign.Center,
        fontSize = fontSize,
        modifier = modifier
            .noEffectClickable(enabled = isEnable, onClick = onClick)
            .padding(paddingValues)
            .graphicsLayer {
                alpha = if (isEnable) 1f else 0.5f
            }
    )
}


@Composable
fun AppButton(
    text: String,
    modifier: Modifier = Modifier,
    background: Color = MaterialTheme.colorScheme.primary,
    color: Color = MaterialTheme.colorScheme.contentColorFor(background),
    fontSize: TextUnit = 14.sp,
    maxLines: Int = 1,
    textStyle: TextStyle = LocalTextStyle.current,
    enabled: Boolean = true,
    shape: Shape = CircleShape,
    border: BorderStroke? = null,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    onLayoutContent: @Composable RowScope.(Boolean) -> Unit = {},
    onClick: () -> Unit,
) {
    ElevatedButton(
        modifier = modifier,
        onClick = onClick,
        enabled = enabled,
        shape = shape,
        colors = ButtonDefaults.elevatedButtonColors(
            containerColor = background,
            contentColor = color,
            disabledContainerColor = background,
            disabledContentColor = color.copy(0.5f),
        ),
        elevation = null,
        border = border,
        contentPadding = contentPadding,
        interactionSource = interactionSource,
    ) {
        onLayoutContent(true)
        AutoSizeText(text = text, fontSize = fontSize, fontWeight = FontWeight.Medium, maxLines = maxLines, style = textStyle)
        onLayoutContent(false)
    }
}


@Composable
fun AppButton(
    text: String,
    brush: Brush,
    modifier: Modifier = Modifier,
    color: Color = MaterialTheme.colorScheme.onPrimary,
    fontSize: TextUnit = 14.sp,
    maxLines: Int = 1,
    textStyle: TextStyle = LocalTextStyle.current,
    enabled: Boolean = true,
    shape: Shape = CircleShape,
    border: BorderStroke? = null,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    onClick: () -> Unit,
) {
    ElevatedButton(
        modifier = modifier
            .background(brush = brush, shape = shape)
            .clipToBounds(),
        onClick = onClick,
        enabled = enabled,
        shape = shape,
        colors = ButtonDefaults.elevatedButtonColors(
            containerColor = Color.Transparent,
            contentColor = color,
            disabledContainerColor = Color.Transparent,
            disabledContentColor = color.copy(0.5f),
        ),
        elevation = null,
        border = border,
        contentPadding = contentPadding,
        interactionSource = interactionSource,
    ) {
        AutoSizeText(text = text, fontSize = fontSize, fontWeight = FontWeight.Medium, maxLines = maxLines, style = textStyle)
    }
}

@Composable
fun AppButton(
    text: String,
    modifier: Modifier = Modifier,
    colors: ButtonColors = ButtonDefaults.elevatedButtonColors(
        containerColor = MaterialTheme.colorScheme.primary,
        contentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.primary),
        disabledContainerColor = MaterialTheme.colorScheme.primary,
        disabledContentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.primary),
    ),
    fontSize: TextUnit = 14.sp,
    maxLines: Int = 1,
    textStyle: TextStyle = LocalTextStyle.current,
    enabled: Boolean = true,
    shape: Shape = CircleShape,
    border: BorderStroke? = null,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    onClick: () -> Unit,
) {
    ElevatedButton(
        modifier = modifier,
        onClick = onClick,
        enabled = enabled,
        shape = shape,
        colors = colors,
        elevation = null,
        border = border,
        contentPadding = contentPadding,
        interactionSource = interactionSource,
    ) {
        AutoSizeText(text = text, fontSize = fontSize, fontWeight = FontWeight.Medium, maxLines = maxLines, style = textStyle)
    }
}


@Composable
fun AppButton(
    text: AnnotatedString,
    modifier: Modifier = Modifier,
    colors: ButtonColors = ButtonDefaults.elevatedButtonColors(
        containerColor = MaterialTheme.colorScheme.primary,
        contentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.primary),
        disabledContainerColor = MaterialTheme.colorScheme.primary,
        disabledContentColor = MaterialTheme.colorScheme.contentColorFor(MaterialTheme.colorScheme.primary),
    ),
    fontSize: TextUnit = 14.sp,
    maxLines: Int = 1,
    textStyle: TextStyle = LocalTextStyle.current,
    enabled: Boolean = true,
    shape: Shape = CircleShape,
    border: BorderStroke? = null,
    contentPadding: PaddingValues = PaddingValues(0.dp),
    interactionSource: MutableInteractionSource = remember { MutableInteractionSource() },
    onClick: () -> Unit,
) {
    ElevatedButton(
        modifier = modifier,
        onClick = onClick,
        enabled = enabled,
        shape = shape,
        colors = colors,
        elevation = null,
        border = border,
        contentPadding = contentPadding,
        interactionSource = interactionSource,
    ) {
        AutoSizeText(text = text, fontSize = fontSize, fontWeight = FontWeight.Medium, maxLines = maxLines, style = textStyle)
    }
}
