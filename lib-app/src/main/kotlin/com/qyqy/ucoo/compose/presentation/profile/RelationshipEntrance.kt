package com.qyqy.ucoo.compose.presentation.profile

import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.CpHouseBasicInfo
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.pages.CPHouseNavigator
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.sUser

//0 主页Mine  1、个人主页（自己看）。2、个人主页（他人看）。3、语音房资料卡。4、部落资料卡。5、群组资料卡。
val LocalRelationShipEntranceFrom = staticCompositionLocalOf { 1 }

val LocalRelationShipCPFrom = staticCompositionLocalOf<Relationship.Cp?> { null }
val LocalRelationShipLabelFrom = staticCompositionLocalOf<Relationship.Label?> { null }

/**
 * @param roomId 小屋id
 * @param from 0 主页Mine 1、个人主页（自己看）。2、个人主页（他人看）。3、语音房资料卡。4、部落资料卡。5、群组资料卡。
 * @param modifier
 */
@Composable
fun CPHouseEntranceWidget(modifier: Modifier = Modifier) {
    val traceType = LocalRelationShipEntranceFrom.current
    val cpInfo = LocalRelationShipCPFrom.current

    cpInfo?.cpHouse?.let { cpRoomInfo ->
        if (!cpRoomInfo.isHidden || cpInfo.hasUser(sUser.userId)) {
            val ctx = LocalContext.current

            ReportExposureCompose(onExposureStart = {
                if (traceType > 0) {
                    Analytics.reportExposureEvent(
                        TracePoints.CP_ROOM_ENTRY_SHOW,
                        extra = "{\"cp_room_entry_pos_type\":${traceType},\"cp_room_entry_user_id\":${cpInfo.self.userId},\"cp_room_id\":${cpRoomInfo.id}}",
                        useShushu = true
                    )
                }
            }) {
                Box(
                    modifier =
                    modifier
                        .fillMaxWidth()
                        .aspectRatio(3.89772727f)
                        .paint(
                            painterResource(id = R.drawable.ic_house_entrance_bg),
                            contentScale = ContentScale.FillWidth,
                        )
                        .clip(RoundedCornerShape(12.dp))
                ) {
                    Row(
                        modifier = Modifier
                            .padding(8.dp)
                            .align(Alignment.CenterStart),
                        horizontalArrangement = Arrangement.Center,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        cpRoomInfo.getPropItems().forEach {
                            Column(
                                modifier = Modifier
                                    .padding(end = 8.dp)
                                    .size(72.dp)
                                    .background(
                                        color = Color(0xFFFF9AD7),
                                        shape = RoundedCornerShape(8.dp)
                                    )
                                    .border(1.dp, Color(0xFFFFD5EE), RoundedCornerShape(8.dp)),
                                verticalArrangement = Arrangement.Center,
                                horizontalAlignment = Alignment.CenterHorizontally
                            ) {
                                ComposeImage(model = it.icon, modifier = Modifier.size(48.dp))
                                Text(
                                    it.name, color = Color.White, fontSize = 12.sp, lineHeight = 12.sp,
                                    fontWeight = FontWeight.Medium, maxLines = 1, overflow = TextOverflow.Ellipsis
                                )
                            }
                        }
                        Spacer(modifier = Modifier.weight(1f))
                        Spacer(modifier = Modifier.width(72.dp))

                    }

                    if (cpRoomInfo.isExpired) {
                        Spacer(
                            modifier = Modifier
                                .fillMaxSize()
                                .background(color = Color(0x66000000))
                        )
                    }

                    Text(
                        stringResource(id = R.string.进入小屋), modifier = Modifier
                            .padding(end = 8.dp)
                            .align(Alignment.CenterEnd)
                            .background(
                                brush = Brush.verticalGradient(
                                    colors = listOf(
                                        Color(0xffff7ba0), Color(0xffff41b3)
                                    )
                                ), shape = CircleShape
                            )
                            .border(1.dp, brush = Brush.verticalGradient(listOf(Color(0x99FFFFFF), Color(0x00ffffff))), shape = CircleShape)
                            .padding(vertical = 7.dp, horizontal = 14.dp)
                            .click(noEffect = true) {
                                if (traceType > 0) {
                                    Analytics.reportExposureEvent(
                                        TracePoints.CP_ROOM_ENTRY_CLICK,
                                        extra = "{\"cp_room_entry_pos_type\":${traceType},\"cp_room_entry_user_id\":${cpInfo.self.userId},\"cp_room_id\":${cpRoomInfo.id}}",
                                        useShushu = true
                                    )
                                }
                                CPHouseNavigator.navigate(ctx, cpRoomInfo.id, extra = Bundle().apply {
                                    putInt("user_id", cpInfo.self.userId)
                                    putInt("from", traceType)
                                })
                            },
                        color = Color.White, fontSize = 14.sp, lineHeight = 14.sp, fontWeight = FontWeight.Medium
                    )
                    if (cpRoomInfo.isHidden) {
                        Text(
                            stringResource(id = R.string.已隐藏), color = Color(0xff865409), fontSize = 12.sp, lineHeight = 12.sp, fontWeight = FontWeight.Medium,
                            modifier = Modifier
                                .background(color = Color(0xFFFFD977), shape = RoundedCornerShape(bottomStart = 12.dp))
                                .padding(vertical = 4.dp, horizontal = 10.dp)
                                .align(Alignment.TopEnd)
                        )
                    }
                }
            }
        }
    }
}

//region 待定 亲友小屋
@Composable
fun RelationShipEntranceWidget(
    cpInfo: Relationship.Cp,
    infos: List<Relationship.Label.Normal>,
    modifier: Modifier = Modifier
) {
    Row(
        modifier =
        modifier
            .fillMaxWidth()
            .aspectRatio(3.89772727f)
            .paint(
                painterResource(id = R.drawable.ic_house_entrance_bg),
                contentScale = ContentScale.FillWidth,
            )
            .clip(RoundedCornerShape(12.dp))
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceBetween
    ) {

        val bottomText = remember {
            derivedStateOf {
                if (cpInfo is Relationship.Cp.None) {
                    "虚位以待"
                } else {
                    "cp类型"
                }
            }
        }
        ItemRelationShipWidget(
            bottomText = bottomText.value,
            bgImageUrl = "",
            avatars = listOf(
                cpInfo.avatar1, cpInfo.avatar2
            ),
            avatarSize = if (cpInfo is Relationship.Cp.None) 36.dp else 24.dp,
            avatarCenter = cpInfo is Relationship.Cp.None,
            modifier = Modifier
                .width(76.dp)
                .aspectRatio(1.056f)
        )
        Row {
            val friendList = remember {
                derivedStateOf {
                    infos.take(3)
                }
            }
            for (item in friendList.value) {
                ItemRelationShipWidget(
                    bottomText = "兄弟Lv16", avatars = listOf(
                        item.avatar
                    ),
                    bgImageUrl = item.avatar.toString(),
                    modifier = Modifier
                        .padding(end = 4.dp)
                        .width(76.dp)
                        .aspectRatio(1.056f)
                        .background(
                            color = Color(0x99ffffff),
                            shape = RoundedCornerShape(6.dp)
                        )
                        .fillMaxHeight()
                )
            }
            for (i in friendList.value.size..2) {
                ItemRelationShipWidget(
                    bottomText = "虚位以待", avatars = listOf(R.drawable.ic_relation_add), avatarSize = 36.dp,
                    avatarCenter = true,
                    modifier = Modifier
                        .width(76.dp)
                        .aspectRatio(1.056f)
                        .background(
                            color = Color(0x99ffffff),
                            shape = RoundedCornerShape(6.dp)
                        )
                        .fillMaxHeight()
                )
            }
        }
    }
}

@Composable
private fun ItemRelationShipWidget(
    bottomText: String,
    avatars: List<Any?>,
    bgImageUrl: String? = null,
    avatarSize: Dp = 24.dp,
    avatarDivider: Dp = 4.dp,
    avatarCenter: Boolean = false,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
    ) {
        bgImageUrl?.let {
            ComposeImage(
                model = it, modifier = Modifier
                    .padding(
                        top = 4.dp, bottom = 12.dp, start = 8.dp, end = 8.dp
                    )
                    .fillMaxSize()
            )
        }

        Box(
            modifier = Modifier
                .align(if (avatarCenter) Alignment.Center else Alignment.BottomCenter)
                .padding(bottom = 16.dp)
        ) {
            avatars.forEachIndexed { index, avatar ->
                ComposeImage(
                    model = avatar ?: R.drawable.ic_relation_add,
                    modifier = Modifier
                        .padding(start = if (index != 0) (avatarSize - avatarDivider) else 0.dp)
                        .size(avatarSize)
                        .clip(CircleShape)
                        .border(0.5.dp, color = Color.White, CircleShape)
                )
            }
        }

        AutoSizeText(
            bottomText,
            modifier = Modifier
                .padding(horizontal = 4.dp)
                .padding(bottom = 2.dp)
                .fillMaxWidth()
                .background(color = Color(0xfff768ca), shape = CircleShape)
                .padding(vertical = 3.dp, horizontal = 14.dp)
                .align(Alignment.BottomCenter),

            fontSize = 10.sp,
            alignment = Alignment.BottomCenter,
            color = Color.White
        )
    }
}

@Composable
@Preview
private fun PreviewRelationShipEntrance() {
    RelationShipEntranceWidget(
        cpInfo = Relationship.Cp.previewSimple1(),
        infos = remember {
            mutableListOf<Relationship.Label.Normal>().apply {
                for (i in 0..1) {
                    add(Relationship.Label.previewSimple())
                }
            }
        })
}
//endregion

@Composable
@Preview
private fun CPHouseEntranceWidgetPreview() {
    CompositionLocalProvider(
        LocalRelationShipCPFrom provides Relationship.Cp.previewSimple1()
    ) {
        CPHouseEntranceWidget()
    }
}