package com.qyqy.ucoo.compose.ui.photo

import android.os.Parcelable
import androidx.annotation.DrawableRes
import kotlinx.parcelize.Parcelize


interface IImageSource : Parcelable {

    val model: Any

    @Parcelize
    @JvmInline
    value class Uri(private val uri: android.net.Uri) : IImageSource {
        override val model: Any
            get() = uri
    }

    @Parcelize
    @JvmInline
    value class Url(private val url: String) : IImageSource {
        override val model: Any
            get() = url
    }

    @Parcelize
    @JvmInline
    value class Path(private val path: String) : IImageSource {
        override val model: Any
            get() = path
    }

    @Parcelize
    @JvmInline
    value class Res(@DrawableRes private val id: Int) : IImageSource {
        override val model: Any
            get() = id
    }
}

interface IPhotoPreviewModel : Parcelable {

    val key: String

    val aspectRatio: Float // 图片实际的宽高比

    val transitionSource: IImageSource

    val previewSource: IImageSource

    val originSource: IImageSource?

    val isVideo: Boolean
}

@Parcelize
data class PhotoPreviewModel(
    override val key: String,
    override var aspectRatio: Float, // 图片实际的宽高比, 未知可以传-1
    override val transitionSource: IImageSource,
    override val previewSource: IImageSource,
    override val originSource: IImageSource? = null,
    override val isVideo: Boolean = false,
) : IPhotoPreviewModel {


    init {
        if (aspectRatio <= 0 || aspectRatio.isNaN()) {
            aspectRatio = -1f
        }
    }

}