package com.qyqy.ucoo.compose.domain.usecase.moment

import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.moment.MomentApi
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.JsonObject

class DeleteMomentUseCase constructor(
    private val api: MomentApi = createApi(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Map<String, String>, JsonObject>(ioDispatcher) {

    override suspend fun execute(parameters: Map<String, String>): Result<JsonObject> {
        return runApiCatching {
            api.deleteMoment(parameters)
        }
    }

}