package com.qyqy.ucoo.compose.vm.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.cupid.data.FirstClassBean
import com.qyqy.cupid.data.TargetUserClassBean
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch

class FirstClassViewModel : ViewModel() {
    private val userRepository: UserRepository by lazy {
        UserRepository()
    }

    private val _firstClassBean = MutableStateFlow<FirstClassBean>(FirstClassBean(showFirstClassInfo = false))
    val firstClassBean = _firstClassBean.asStateFlow()

    private val _targetUserClassBean = MutableStateFlow<TargetUserClassBean?>(null)
    val targetUserClassBean = _targetUserClassBean.asStateFlow()

    fun refresh() {
        viewModelScope.launch {
            userRepository.getFirstClassInfo().onSuccess {
                _firstClassBean.emit(it)
            }
        }
    }

    fun fetchTargetUserInfo(userId: Int) {
        viewModelScope.launch {
            userRepository.getTargetUserClassInfo(userId).onSuccess {
                _targetUserClassBean.emit(it)
            }
        }
    }
}