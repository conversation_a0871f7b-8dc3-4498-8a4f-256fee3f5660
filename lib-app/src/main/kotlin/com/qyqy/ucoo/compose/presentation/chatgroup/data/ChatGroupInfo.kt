package com.qyqy.ucoo.compose.presentation.chatgroup.data


interface ChatGroupInfo {
    val avatarUrl: String
    val iEnableDontDisturb: Boolean
    val id: Int
    val intro: String
    val memberCnt: Int
    val name: String
    val publicId: String
    val rcGroupId: String
    val relationWithMe: Int
    val membersRoomInfos: List<MemberRoomInfo>
    val role: Int
    val memberApplyWaitCnt: Int
}