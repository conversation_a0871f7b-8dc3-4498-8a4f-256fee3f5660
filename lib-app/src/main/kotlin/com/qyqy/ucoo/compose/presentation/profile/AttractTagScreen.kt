@file:OptIn(ExperimentalLayoutApi::class)

package com.qyqy.ucoo.compose.presentation.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.toMutableStateList
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.compose.data.RegisterInfo
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.presentation.login.LoginButton
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.UCOOScreen
import com.qyqy.ucoo.compose.vm.AttractionViewModel
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.user.UserApi
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

@Composable
fun AttractionTagWidget(
    registerInfo: RegisterInfo = RegisterInfo(isBoy = false),
    onConfirm: (List<Int>) -> Unit = { _ -> },
    onBack: () -> Unit = {},
) {
    val context = LocalContext.current
    val resources = context.resources


    val viewModel: AttractionViewModel = viewModel(factory = viewModelFactory {
        initializer {
            AttractionViewModel(if (registerInfo.isBoy) 1 else 2)
        }
    })
    val attractivetags by viewModel.attractiveTags.collectAsStateWithLifecycle()

    var selectedTags by remember {
        mutableStateOf(registerInfo.tag_ids)
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .drawWithCache {
                val imageBitmap = ImageBitmap.imageResource(resources, id = R.drawable.header_home)
                val dstSize = IntSize(
                    size.width.toInt(),
                    size.width
                        .div(1125)
                        .times(840)
                        .toInt()
                )
                onDrawBehind {
                    drawImage(image = imageBitmap, dstSize = dstSize)
                }
            }
            .systemBarsPadding()
    ) {
        AppTitleBar("", modifier = Modifier.padding(start = 5.dp), onBack = {
            onBack()
        })
        Text(
            text = buildAnnotatedString {
                append(stringResource(id = R.string.选择引力签))
                append("\n")
                withStyle(
                    SpanStyle(
                        color = Color(0xff96939E),
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Normal
                    )
                ) {
                    append(stringResource(id = R.string.选择引力签desc))
                }
            },
            modifier = Modifier.padding(start = 24.dp, top = 32.dp),
            fontSize = 24.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
            lineHeight = 40.sp
        )
        Box(modifier = Modifier.weight(1f)) {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp, top = 40.dp, bottom = 148.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                attractivetags.forEach {
                    ItemAttractTag(
                        tag = it,
                        modifier = Modifier
                            .height(32.dp)
                            .background(
                                color = if (!selectedTags.contains(it.id)) Color(0xFF342A4A) else Color(0xff945EFF),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .click {
                                val pos = selectedTags.indexOf(it.id)
                                if (pos == -1) {
                                    selectedTags = selectedTags
                                        .toMutableList()
                                        .apply {
                                            add(it.id)
                                        }
                                } else {
                                    selectedTags = selectedTags
                                        .toMutableList()
                                        .apply {
                                            removeAt(pos)
                                        }
                                }
                            }
                            .padding(horizontal = 9.dp)
                    )
                }
                Spacer(modifier = Modifier.height(80.dp))
            }
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .background(brush = Brush.verticalGradient(listOf(Color(0x001c1d1e), Color(0xff1c1d1e))))
                    .padding(top = 40.dp, bottom = 108.dp)
                    .fillMaxWidth()
            ) {
                LoginButton(
                    text = stringResource(id = R.string.finish_register),
                    modifier = Modifier
                        .padding(horizontal = 48.dp)
                        .fillMaxWidth(),
                    enabled = selectedTags.isNotEmpty(),
                    onClick = {
                        onConfirm(selectedTags)
                    }
                )
            }
        }
    }
}

@Composable
fun AttractionEditWidget(onSaved: () -> Unit = {}) {
    val viewModel: AttractionViewModel = viewModel(factory = viewModelFactory {
        initializer {
            AttractionViewModel()
        }
    })

    val attractivetags by viewModel.attractiveTags.collectAsStateWithLifecycle()

    val selectedTags = remember(attractivetags) {
        attractivetags.filter { item ->
            sUser.attractiveFlags.any { item.id == it.id }
        }.toMutableStateList()
    }

    UCOOScreen(title = stringResource(id = R.string.选择引力签)) {
        Box(modifier = Modifier.weight(1f)) {
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier
                    .padding(start = 16.dp, end = 16.dp, top = 40.dp, bottom = 148.dp)
                    .verticalScroll(rememberScrollState())
            ) {
                attractivetags.forEach { item ->
                    ItemAttractTag(
                        tag = item,
                        modifier = Modifier
                            .height(32.dp)
                            .background(
                                color = if (!selectedTags.any { it.id == item.id }) Color(0xFF342A4A) else Color(0xff945EFF),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .click {
                                val pos = selectedTags.indexOfFirst {
                                    it.id == item.id
                                }
                                if (pos == -1) {
                                    selectedTags.add(item)
                                } else {
                                    selectedTags.removeAt(pos)
                                }
                            }
                            .padding(horizontal = 9.dp)
                    )
                }
                Spacer(modifier = Modifier.height(40.dp))
            }
            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .background(brush = Brush.verticalGradient(listOf(Color(0x001c1d1e), Color(0xff1c1d1e))))
                    .padding(top = 40.dp, bottom = 20.dp)
                    .fillMaxWidth()
            ) {
                val scope = rememberCoroutineScope()
                LoginButton(
                    text = stringResource(id = R.string.save),
                    modifier = Modifier
                        .padding(horizontal = 48.dp)
                        .fillMaxWidth(),
                    enabled = selectedTags.isNotEmpty(),
                    onClick = {
                        scope.launch {
                            viewModel.updateTags(selectedTags).onSuccess {
                                onSaved()
                            }
                        }
                    }
                )
            }
        }
    }
}


@Composable
private fun ItemAttractTag(modifier: Modifier = Modifier, tag: AttractiveFlag) {
    Row(
        modifier = modifier,
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(2.dp)
    ) {
        if (tag.url.isNullOrEmpty().not()) {
            ComposeImage(model = tag.url, modifier = Modifier.size(12.dp))
        }
        Text(tag.label, color = Color.White, fontSize = 12.sp, lineHeight = 12.sp, fontWeight = FontWeight.Medium)
    }
}


@Composable
@Preview
private fun AttractTagScreenPreview() {
    AttractionTagWidget()
}

@Composable
@Preview
private fun AttractTagScreen2Preview() {
    AttractionEditWidget()
}