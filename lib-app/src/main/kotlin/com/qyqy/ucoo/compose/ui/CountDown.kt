package com.qyqy.ucoo.compose.ui

import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableLongStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.qyqy.ucoo.compose.formatTimeWithHours
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive

/**
 * [deadline] 截止时间毫秒ms
 */
@Composable
fun CountdownBar(
    modifier: Modifier = Modifier,
    key: Any = Unit,
    onComplete: () -> Unit = {},
    deadline: Long,
    timeUI: @Composable RowScope.(text: String) -> Unit, splitUI: @Composable RowScope.() -> Unit,
) {
    val list = remember {
        mutableStateListOf<String>().apply {
            addAll(
                formatTimeWithHours(
                    maxOf(
                        (deadline - System.currentTimeMillis()),
                        0
                    )
                ).split(":")
            )
        }
    }

    LaunchedEffect(key) {
        while (isActive) {
            delay(1000L)
            list.clear()
            val time = maxOf((deadline - System.currentTimeMillis()), 0L)
            val str = formatTimeWithHours(time)
            list.addAll(str.split(":"))
            if (time == 0L) {
                onComplete()
                break
            }
        }
    }

    Row(modifier = modifier, verticalAlignment = Alignment.CenterVertically) {
        list.forEachIndexed { index, s ->
            timeUI(s)
            if (index < 2) {
                splitUI()
            }
        }
    }
}

/**
 * [leftMillis] 剩余的毫秒
 */
@Composable
fun TickDownEffect(
    leftMillis: Long,
    key: Any = Unit,
    onComplete: () -> Unit = {},
    content: @Composable (leftMillis: Long) -> Unit,
) {
    var left by remember {
        mutableLongStateOf(leftMillis)
    }
    LaunchedEffect(key) {
        val rem = left % 1000L
        if (rem != 0L) {
            delay(rem)
            left -= rem
        }
        while (isActive) {
            delay(1000L)
            left -= 1000L
            if (left <= 0) {
                onComplete()
                break
            }
        }
    }
    content(left)
}