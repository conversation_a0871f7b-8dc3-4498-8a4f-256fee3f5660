package com.qyqy.ucoo.compose.presentation.cp_house.bean


import com.qyqy.ucoo.compose.presentation.cp_house.house.IHouseDressUPItem
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlin.time.DurationUnit
import kotlin.time.toDuration

@Serializable
data class HouseDressUpItem(
    @SerialName("expire_timestamp")
    override val expireTimestamp: Long? = null,// 过期时间戳。expire_timestamp为null代表为永不过期
    @SerialName("in_use")
    val inUse: Boolean = false,
    @SerialName("prop")
    val prop: Prop = Prop()
) : IHouseDressUPItem {
    val id: Int = prop.id

    override val t: Int = prop.t

    @Serializable
    data class Prop(
        @SerialName("effect_file")
        val effectFile: String = "",
        @SerialName("gain_type")
        val gainType: Int = 0,// 获取方式 3: 亲密度解锁，4: 宝箱抽取 5: 系统赠送
        @SerialName("icon")
        val icon: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("intimacy_score_limit")
        val intimacyScoreLimit: Int = 0,
        @SerialName("name")
        val name: String = "",
        @SerialName("profit")
        val profit: Int = 0,// 小屋币收益
        @SerialName("t")
        val t: Int = 0
    )

    override val icon: String = prop.icon

    override val title: String = prop.name

    override val benefit: String = prop.profit.toString()

    override val using: Boolean = inUse


}