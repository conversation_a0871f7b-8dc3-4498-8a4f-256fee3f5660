package com.qyqy.ucoo.compose.presentation.wedding.bean


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class WeddingEventDat(
    @SerialName("btn_desc")
    val btnDesc: String = "",
    @SerialName("content")
    val content: String = "",
    @SerialName("jump_link")
    val jumpLink: String = "",
    @SerialName("room_type")
    val roomType: Int = 0,
    @SerialName("room_type_desc")
    val roomTypeDesc: String = "",
    @SerialName("title")
    val title: String = ""
)