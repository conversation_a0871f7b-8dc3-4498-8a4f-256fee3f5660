package com.qyqy.ucoo.compose.presentation.game

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.state.Empty
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.UIStateContent
import com.qyqy.ucoo.compose.state.isError
import com.qyqy.ucoo.compose.state.toUIData
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.LoadResult
import com.qyqy.ucoo.compose.ui.PaginateState
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior
import com.qyqy.ucoo.compose.ui.rememberPaginateState
import kotlinx.coroutines.launch

@Composable
fun DiamondHistoryPage(request: suspend (Int?) -> Result<DiamondHistory>, modifier: Modifier = Modifier) {
    val paginateState = rememberPaginateState<Int>(null)
    val lazyListState = rememberLazyListState()
    val scope = rememberCoroutineScope()
    var uiState by remember {
        mutableStateOf<UIState<DiamondHistory>>(UIState.Loading(R.string.加载中))
    }
    paginateState.attach(lazyListState) { pagingScope ->
        val key = pagingScope.key
        scope.launch {
            val result = request(key)
            result.onSuccess {
                val data = uiState
                uiState = if (data is UIState.Data) {
                    data.value.run {
                        copy(items = buildList {
                            addAll(items)
                            addAll(it.items)
                        })
                    }
                } else {
                    it
                }.toUIData()
                pagingScope.loadEnd(LoadResult.Page(if (it.hasMore) it.items.lastOrNull()?.id else null))
            }.onFailure {
                if (uiState !is UIState.Data) {
                    uiState = UIState.Error(Empty(R.string.加载失败点击重试, R.drawable.ic_empty_for_fans), throwable = it)
                }
                pagingScope.loadEnd(LoadResult.Error(it))
            }
        }
    }
    DiamondHistoryPage(uiState, modifier, paginateState, lazyListState) {
        if (it is UIAction.Retry) {
            if (uiState !is UIState.Data) {
                uiState = UIState.Loading(R.string.加载中)
            }
            paginateState.retryNext()
        }
    }
}


@Composable
private fun DiamondHistoryPage(
    uiState: UIState<DiamondHistory>,
    modifier: Modifier = Modifier,
    paginateState: PaginateState<Int> = rememberPaginateState(),
    lazyListState: LazyListState = rememberLazyListState(),
    onAction: (UIAction) -> Unit = {},
) {
    Column(
        modifier = modifier.fillMaxSize()
    ) {
        val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
        AppTitleBar(title = stringResource(id = R.string.历史记录), onBack = {
            onBackPressedDispatcher?.onBackPressed()
        })
        UIStateContent(uiState = uiState, onEmptyOrError = {
            if (it.isError) {
                onAction(UIAction.Retry)
            }
        }) {
            val isLoading by remember {
                derivedStateOf {
                    paginateState.nextLoadState.isLoading
                }
            }

            val isFailure by remember {
                derivedStateOf {
                    paginateState.nextLoadState.isFailure
                }
            }

            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .overScrollVertical(),
                state = lazyListState,
                flingBehavior = rememberOverscrollFlingBehavior { lazyListState }
            ) {
                itemsIndexed(value.items, contentType = { _, _ -> 0 }) { index, item ->
                    Row(
                        modifier = Modifier.padding(horizontal = 16.dp, vertical = 20.dp), verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column(
                            modifier = Modifier
                                .padding(end = 10.dp)
                                .weight(1f)
                        ) {
                            Text(text = item.title, color = Color.White, fontSize = 14.sp, maxLines = 1, overflow = TextOverflow.Ellipsis)
                            Spacer(modifier = Modifier.height(12.dp))
                            Text(text = item.formatTime, color = Color.White.copy(0.5f), fontSize = 12.sp)
                        }
                        Text(text = stringResource(id = R.string.多少钻石, item.changeAmount), color = Color.White, fontSize = 14.sp)
                    }
                    if (index != value.items.lastIndex) {
                        HorizontalDivider(modifier = Modifier.padding(horizontal = 16.dp), thickness = 0.5.dp, color = Color(0x14FFFFFF))
                    }
                }

                if (isLoading) {
                    item(key = "isLoading", contentType = 1) {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(30.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(5.dp, Alignment.CenterHorizontally),
                        ) {
                            CircularProgressIndicator(modifier = Modifier.size(16.dp), color = colorResource(id = R.color.white), strokeWidth = 1.5.dp)
                            Text(text = stringResource(id = R.string.加载中), color = colorResource(id = R.color.white), fontSize = 14.sp)
                        }
                    }
                } else if (isFailure) {
                    item(key = "isFailure", contentType = 2) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(30.dp)
                                .noEffectClickable {
                                    paginateState.retry()
                                }, contentAlignment = Alignment.Center
                        ) {
                            Text(text = stringResource(id = R.string.加载失败点击重试), color = colorResource(id = R.color.red_300), fontSize = 14.sp)
                        }
                    }
                }
            }
        }
    }
}