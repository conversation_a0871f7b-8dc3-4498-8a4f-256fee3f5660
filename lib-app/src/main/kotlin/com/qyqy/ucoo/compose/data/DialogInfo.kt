package com.qyqy.ucoo.compose.data

import androidx.compose.runtime.Stable

@Stable
data class DialogDelegate(
    var visible: Boolean = false,
    val title: String = "",
    var content: String = "",
    val buttonText: String = "",
    val confirmText: String = "",
    val cancelText: String = "",
) {
    var onConfirm: () -> Unit = {}
    var onCancel: () -> Unit = {}
    var onButtonClick: () -> Unit = {}
}