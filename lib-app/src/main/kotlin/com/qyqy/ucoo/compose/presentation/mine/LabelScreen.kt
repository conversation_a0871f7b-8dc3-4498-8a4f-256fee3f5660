package com.qyqy.ucoo.compose.presentation.mine

import android.content.Intent
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.account.AttractiveFlagColors
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.isSelfOnCompose
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.mine.EditAttractionActivity


@Composable
fun UserLabelPageRouter(profileType: Int, user: User, list: List<AttractiveFlag>) {
    UserLabelPage(profileType, user, list)
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun UserLabelPage(profileType: Int, user: User, list: List<AttractiveFlag>) {
    val context = LocalContext.current
    if (list.isEmpty()) {
        Column(
            modifier = Modifier.fillMaxWidth().padding(horizontal = 15.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(30.dp))
            Image(painter = painterResource(id = R.drawable.ic_empty_for_attraction), contentDescription = null)
            Spacer(modifier = Modifier.height(10.dp))
            Text(
                text = stringResource(
                    id = when {
                        user.isSelfOnCompose -> R.string.add_attraction_to_upgrade
                        user.isBoy -> R.string.he_has_not_attraction
                        else -> R.string.her_has_no_attraction
                    }
                ),
                fontSize = 12.sp, color = colorResource(id = R.color.white_alpha_30),
                textAlign = TextAlign.Center,
            )
            if (user.isSelfOnCompose) {
                Spacer(modifier = Modifier.height(10.dp))
                Button(
                    modifier = Modifier.height(30.dp),
                    shape = Shapes.medium,
                    colors = ButtonDefaults.buttonColors(Color.Transparent),
                    border = BorderStroke(1.dp, Color(0xFF383838)),
                    contentPadding = PaddingValues(horizontal = 15.dp),
                    onClick = {
                        context.startActivity(Intent(context, EditAttractionActivity::class.java))
                    },
                ) {
                    Image(painter = painterResource(id = R.drawable.ic_add), contentDescription = null)
                    Spacer(modifier = Modifier.width(10.dp))
                    Text(text = stringResource(id = R.string.add_attraction), color = Color.White)
                }
            }
        }
    } else {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 4.dp)
        ) {
            FlowRow(
                modifier = Modifier
                    .padding(horizontal = 30.dp)
                    .fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(15.dp),
                verticalArrangement = Arrangement.spacedBy(15.dp),
            ) {
                list.onEachIndexed { index, flag ->
                    Text(
                        text = flag.label,
                        modifier = Modifier
                            .border(1.dp, Color(AttractiveFlagColors[index.rem(5)]), Shapes.medium)
                            .padding(horizontal = 15.dp, vertical = 5.dp),
                        color = Color.White,
                        fontSize = 14.sp
                    )
                }
            }

            when (profileType) {
                0 -> Spacer(modifier = Modifier.height(25.dp))
                1 -> Spacer(modifier = Modifier.height(25.dp))
                else -> Spacer(modifier = Modifier.height(111.dp))
            }
        }
    }
}

@Preview
@Composable
fun PreviewLabelScreenPage() {
    UserLabelPage(0, userForPreview, List(5) {
        AttractiveFlag(0, 0, "啥时候大事","https://media.ucoofun.com/opsite/common/icon1_0uTh8o1.png")
    })
}