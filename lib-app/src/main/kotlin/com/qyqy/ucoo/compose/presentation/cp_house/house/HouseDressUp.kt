package com.qyqy.ucoo.compose.presentation.cp_house.house

import android.widget.Toast
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.key
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.cp_house.bean.HouseDressUpItem
import com.qyqy.ucoo.compose.presentation.cp_house.bean.HouseDressUpTabList
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.state.StatePage
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorDarkScreenBackground
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppScrollableTabRow
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.viewModelOfActivity
import com.qyqy.ucoo.compose.vm.cp_house.CPHouseViewModel
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale


interface IHouseDressUPItem {
    val icon: String
    val title: String
    val benefit: String
    val using: Boolean
    val t: Int
    val expireTimestamp: Long?
}

val IHouseDressUPItem.isHouseType: Boolean
    get() = t == 17
val IHouseDressUPItem.isCarType: Boolean
    get() = t == 18
val IHouseDressUPItem.isBackgroundType: Boolean
    get() = t == 19

@Composable
private fun getAvailableDurationString(item: IHouseDressUPItem): String {
    val text = LocalHouseText.current
    return item.expireTimestamp?.let {
        val dateStr = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(Date(it*1000))
        stringResource(id = text.formatDurationDay, dateStr)
    } ?: stringResource(id = text.durationForever)
}

@Composable
fun HouseDressUpItemUI(item: IHouseDressUPItem, using: Boolean, onClick: OnClick = {}) {
    val colorBlue = Color(0xFF945EFF)
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFF272829), Shapes.small)
            .then(
                if (using) Modifier.border(
                    1.dp, colorBlue,
                    Shapes.small
                ) else Modifier
            )
    ) {
        val text = LocalHouseText.current
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Spacer(modifier = Modifier.height(32.dp))
            ComposeImage(model = item.icon, modifier = Modifier.size(80.dp))
            Spacer(modifier = Modifier.height(6.dp))
            Text(
                text = item.title,
                fontSize = 14.sp,
                color = Color.White,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 2.dp),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
                textAlign = TextAlign.Center
            )
            Spacer(modifier = Modifier.height(4.dp))
            val benefit = when {
                item.isHouseType -> stringResource(id = text.formatBenefitHouse, item.benefit)
                item.isCarType -> stringResource(id = text.formatBenefitCar, item.benefit)
                else -> ""
            }
            AppText(text = benefit, fontSize = 12.sp, color = colorWhite50Alpha)
            Spacer(modifier = Modifier.height(4.dp))
            AppText(
                text = getAvailableDurationString(item = item),
                fontSize = 12.sp,
                color = colorWhite50Alpha
            )
            Spacer(modifier = Modifier.height(12.dp))
            Box(
                modifier = Modifier
                    .widthIn(min = 72.dp)
                    .click(onClick = onClick)
                    .background(
                        if (using) SolidColor(Color(0xFF606161)) else Brush.horizontalGradient(
                            listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))
                        ),
                        Shapes.chip
                    )
                    .padding(horizontal = 12.dp)
                    .height(28.dp),
                contentAlignment = Alignment.Center
            ) {
                AppText(
                    text = stringResource(id = if (using) text.using else text.use),
                    fontSize = 12.sp,
                    color = Color.White
                )
            }
            Spacer(modifier = Modifier.height(8.dp))
        }
        if (using) {
            AppText(
                modifier = Modifier
                    .background(
                        colorBlue,
                        RoundedCornerShape(topStart = 8.dp, bottomEnd = 8.dp)
                    )
                    .padding(horizontal = 8.dp),
                text = stringResource(id = text.use),
                lineHeight = 18.sp,
                fontSize = 10.sp,
                color = Color.White
            )
        }
    }
}

@Composable
private fun HouseBgItemUI(item: IHouseDressUPItem, using: Boolean, onClick: OnClick = {}) {
    val colorBlue = Color(0xFF945EFF)
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(106 / 185f)
                .clip(Shapes.small)
                .click(onClick = onClick)
                .background(Color.Black)
                .then(
                    if (using) Modifier.border(
                        1.dp, colorBlue,
                        Shapes.small
                    ) else Modifier
                )
        ) {
            val text = LocalHouseText.current
            ComposeImage(model = item.icon, modifier = Modifier.fillMaxSize())
            if (using) {
                Box(
                    modifier = Modifier
                        .height(20.dp)
                        .background(
                            colorBlue,
                            RoundedCornerShape(topStart = 8.dp, bottomEnd = 8.dp)
                        )
                        .padding(horizontal = 8.dp),
                    contentAlignment = Alignment.Center
                ) {
                    AppText(
                        text = stringResource(id = text.use),
                        fontSize = 10.sp,
                        color = Color.White
                    )
                }
                Image(
                    painter = painterResource(id = R.drawable.ic_checked),
                    contentDescription = "",
                    modifier = Modifier
                        .align(
                            Alignment.BottomEnd
                        )
                        .padding(8.dp)
                        .size(16.dp)
                )
            }
        }
        Spacer(modifier = Modifier.height(6.dp))
        AppText(
            text = item.title,
            fontSize = 14.sp,
            color = Color.White,
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = 2.dp),
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(6.dp))
        AppText(
            text = getAvailableDurationString(item = item),
            fontSize = 12.sp,
            color = colorWhite50Alpha
        )
        Spacer(modifier = Modifier.height(12.dp))
    }
}

@Preview
@Composable
private fun HouseDressUpItemPreview() {
    val item =
        HouseDressUpItem(1, prop = HouseDressUpItem.Prop(profit = 9, t = 17, name = "自行车"))
    Row(modifier = Modifier.background(Color.Black)) {
        Column(modifier = Modifier.width(165.dp)) {
            HouseDressUpItemUI(item = item, using = false)
            Spacer(modifier = Modifier.height(10.dp))
            HouseDressUpItemUI(item = item, using = true)
        }
        Spacer(modifier = Modifier.width(10.dp))
        Column(modifier = Modifier.width(165.dp)) {
            HouseBgItemUI(item = item, using = false)
            Spacer(modifier = Modifier.height(10.dp))
            HouseBgItemUI(item = item, using = true)
        }
    }
}

@Composable
fun HouseDressUpScreen() {
    val text = LocalHouseText.current
    val api = remember {
        createApi<HouseDressUpApi>()
    }
    val vm = viewModelOfActivity<CPHouseViewModel>()
    val backOwner = LocalOnBackPressedDispatcherOwner.current
    Scaffold(
        modifier = Modifier
            .fillMaxWidth()
            .systemBarsPadding(), topBar = {
            AppTitleBar(title = stringResource(id = text.dressUpCenter), onBack = {
                backOwner?.onBackPressedDispatcher?.onBackPressed()
            })
        }, containerColor = colorDarkScreenBackground
    ) { pd ->
        StatePage(
            modifier = Modifier.fillMaxSize(),
            dataFetcher = { runApiCatching { api.getTabList() } }) { tabs ->
            if (tabs.isEmpty()) {
                EmptyView(
                    modifier = Modifier.fillMaxSize()
                )
            } else {
                key(tabs) {
                    val pagerState = rememberPagerState {
                        tabs.size
                    }
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(pd),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        Spacer(modifier = Modifier.height(10.dp))
                        Box(
                            modifier = Modifier.fillMaxWidth(),
                            contentAlignment = Alignment.Center
                        ) {
                            AppScrollableTabRow(
                                tabs = tabs,
                                pagerState = pagerState,
                                modifier = if (tabs.size < 4) Modifier.fillMaxWidth(0.75f) else Modifier.fillMaxWidth()
                            )
                        }
                        HorizontalPager(state = pagerState) { pageIndex ->
                            val tab = tabs[pageIndex]
                            DressUpListPage(tab.t) {
                                vm?.refreshHouseInfo()
                            }
                        }
                    }
                }
            }
        }
    }
}

@Composable
private fun DressUpListPage(
    t: Int,
    vm: HouseDressUpListViewModel = viewModel(key = "vmdressup-$t", factory = viewModelFactory {
        initializer {
            HouseDressUpListViewModel(t)
        }
    }),
    onRefreshHouseInfo: OnClick = {}
) {
    val text = LocalHouseText.current
    val column = if (t == 19) 3 else 2
    val list by vm.dataState
    val usingPropId by vm.usingPropId
    val scope = rememberCoroutineScope()
    PullRefreshBox(
        modifier = Modifier.fillMaxSize(),
        isRefreshing = vm.isRefreshing,
        onRefresh = { vm.refresh() }) {
        LazyVerticalGrid(
            modifier = Modifier.padding(16.dp),
            columns = GridCells.Fixed(column),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            if (vm.isLoaded) {
                if (list.isEmpty()) {
                    item(span = { GridItemSpan(column) }) {
                        EmptyView(
                            modifier = Modifier
                                .fillMaxWidth()
                                .aspectRatio(0.75f),
                            textRes = text.nodata
                        )
                    }
                } else {
                    items(list) { item ->
                        val using = if (usingPropId != -1) {
                            item.id == usingPropId
                        } else {
                            item.inUse
                        }
                        if (t == 19) {
                            HouseBgItemUI(item = item, using = using) {
                                if (!using) {
                                    scope.launch {
                                        vm.changeUseState(item)
                                        onRefreshHouseInfo()
                                    }
                                }
                            }
                        } else {
                            HouseDressUpItemUI(item = item, using = using) {
                                if (!using) {
                                    scope.launch {
                                        vm.changeUseState(item)
                                        onRefreshHouseInfo()
                                    }
                                }
                            }
                        }
                    }
                }
            }
            itemLoadMore(
                vm.allowLoad && list.isNotEmpty(),
                vm.isLoadingNow,
                vm.hasMore,
                span = { GridItemSpan(column) })
        }
    }
}

private class HouseDressUpListViewModel(val t: Int) : LiStateViewModel<HouseDressUpItem>() {

    private val houseDressUpApi = createApi<HouseDressUpApi>()

    private var pageNo = 0

    private val _usingPropId: MutableState<Int> = mutableIntStateOf(-1)
    val usingPropId: ComposeState<Int> = _usingPropId

    init {
        refresh()
    }

    override fun onStartRequest(isRefresh: Boolean) {
        if (isRefresh) {
            pageNo = 0
        } else {
            pageNo += 1
        }
    }

    override suspend fun fetch(): List<HouseDressUpItem> {
        return runApiCatching { houseDressUpApi.getDressUpList(t, pageNo) }
            .onSuccess { l ->
                l.firstOrNull { it.inUse }?.also {
                    _usingPropId.value = it.id
                }
            }
            .getOrNull().orEmpty()
    }

    suspend fun changeUseState(item: HouseDressUpItem) {
        runApiCatching {
            houseDressUpApi.changeUseState(
                mapOf(
                    "t" to item.prop.t,
                    "prop_id" to item.prop.id,
                )
            )
        }.onSuccess {
            _usingPropId.value = item.id
            it.parseValue<String>("toast", "").also {
                toast(it, Toast.LENGTH_LONG)
            }
        }.toastError()
    }

}


@Composable
private fun PreviewDressUpGrid() {
    val preview = LocalInspectionMode.current
    if (preview) {
        val prop = HouseDressUpItem.Prop()
        val list = listOf(
            HouseDressUpItem(), HouseDressUpItem(), HouseDressUpItem(), HouseDressUpItem()
        )
        HouseDUGrid(list = list)
    }
}

@Composable
private fun HouseDUGrid(list: List<IHouseDressUPItem>, modifier: Modifier = Modifier) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(2),
        modifier = modifier.padding(16.dp),
        horizontalArrangement = Arrangement.spacedBy(12.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp)
    ) {
        items(list) {
            HouseDressUpItemUI(item = it, using = it.using)
        }
    }
}


@Preview
@Composable
private fun HouseDUPreview() {
    HouseDressUpScreen()
}

interface HouseDressUpApi {
    /**
     * ```
     * # 装扮tab
     * 17:小屋模型 18: cp小屋车辆, 19: cp小屋背景
     * ```
     */
    @GET("api/friendship/v1/cp_room/prop/tab")
    suspend fun getTabList(): ApiResponse<List<HouseDressUpTabList.Tab>>


    @GET("api/friendship/v1/cp_room/prop/obtained")
    suspend fun getDressUpList(
        @Query("t") t: Int,
        @Query("page_no") pageNo: Int,
        @Query("page_size") pageSize: Int = 20
    ): ApiResponse<List<HouseDressUpItem>>

    @POST("api/friendship/v1/cp_room/prop/use")
    suspend fun changeUseState(@Body map: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>
}