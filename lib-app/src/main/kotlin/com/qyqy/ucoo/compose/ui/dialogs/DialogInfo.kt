package com.qyqy.ucoo.compose.ui.dialogs

import android.app.Activity
import androidx.compose.runtime.Composable
import com.overseas.common.utils.dpF
import com.qyqy.ucoo.compose.ui.ComposeDialog
import com.qyqy.ucoo.compose.ui.SimpleComposeDialog
import com.qyqy.ucoo.runWithTopActivity

data class DialogInfo(
    val title: String = "",
    val message: String = "",
    val centerButtonText: String = "",
    val leftButtonText: String = "",
    val rightButtonText: String = ""
)

fun showComposeDialog(
    activity: Activity? = null,
    dialogWidth: Int = 270.dpF.toInt(),
    initBlock: (ComposeDialog) -> Unit = {},
    content: @Composable (ComposeDialog) -> Unit
) {
    if (activity != null) {
        if (!(activity.isDestroyed || activity.isFinishing)) {
            SimpleComposeDialog(activity, dialogWidth) {
                content(it)
            }.also(initBlock).show()
        }
    } else {
        runWithTopActivity {
            SimpleComposeDialog(this, dialogWidth) {
                content(it)
            }.also(initBlock).show()
        }
    }
}