package com.qyqy.ucoo.compose.ui.wish

import android.os.Parcelable
import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.background
import androidx.compose.foundation.basicMarquee
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.ui.profile.wish.AddWishPage
import com.qyqy.cupid.ui.profile.wish.C2CWishViewModel
import com.qyqy.cupid.ui.profile.wish.EditWishEntry
import com.qyqy.cupid.ui.profile.wish.WishViewModel
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.clearFocusOnKeyboardDismiss
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.utils.OnClick
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.parcelize.RawValue

@Composable
fun EditWishHostPage(wishViewModel: WishViewModel, onBack: OnClick = {}, onSave: (EditWishEntry) -> Unit = {}) {

    val editWishEntryState = remember {
        mutableStateOf(EditWishEntry())
    }

    val tempEditWishEntryState = remember {
        mutableStateOf(EditWishEntry())
    }

    var addWishPage: AddWishPage by remember {
        mutableStateOf(AddWishPage.Home)
    }

    val editWishEntry by editWishEntryState

    val enabled by remember {
        derivedStateOf {
            when (addWishPage) {
                AddWishPage.Home -> {
                    editWishEntry.isOk
                }

                AddWishPage.Gift -> {
                    editWishEntry.giftId != -1 || tempEditWishEntryState.value.giftId != -1
                }

                AddWishPage.GiftCount -> {
                    editWishEntry.count > 0 || tempEditWishEntryState.value.count > 0
                }
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
    ) {
        Box(
            modifier = Modifier
                .padding(bottom = 16.dp)
                .fillMaxWidth()
                .height(32.dp),
            contentAlignment = Alignment.CenterStart
        ) {
            Icon(
                painter = painterResource(id = R.drawable.ic_cpd_navigation_back),
                contentDescription = "back",
                modifier = Modifier
                    .padding(start = 6.dp)
                    .noEffectClickable {
                        if (addWishPage == AddWishPage.Home) {
                            onBack()
                        } else {
                            addWishPage = AddWishPage.Home
                        }
                    },
                tint = Color.White,
            )

            AppButton(
                text = stringResource(id = R.string.确定),
                brush = if (enabled) {
                    Brush.verticalGradient(listOf(Color(0xFFFD8EFF), Color(0xFF8247FF)))
                } else {
                    SolidColor(Color(0xFF8854D0))
                },
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .size(56.dp, 24.dp),
                enabled = enabled,
                color = Color.White,
                fontSize = 12.sp,
                onClick = {
                    if (addWishPage == AddWishPage.Home) {
                        onSave(editWishEntry)
                    } else {
                        editWishEntryState.value = tempEditWishEntryState.value
                        addWishPage = AddWishPage.Home
                    }
                }
            )
        }

        BackHandler(addWishPage != AddWishPage.Home) {
            addWishPage = AddWishPage.Home
        }

        AnimatedContent(targetState = addWishPage, label = "host") {
            when (it) {

                AddWishPage.Home -> EditWishPage(
                    editWishEntryState = editWishEntryState,
                    wishViewModel = wishViewModel,
                    onSelectGift = {
                        tempEditWishEntryState.value = editWishEntry
                        addWishPage = AddWishPage.Gift
                    },
                    onSelectGiftCount = {
                        tempEditWishEntryState.value = editWishEntry
                        addWishPage = AddWishPage.GiftCount
                    }
                )

                AddWishPage.Gift -> SelectWishGiftPage(
                    editWishEntryState = tempEditWishEntryState,
                    wishViewModel = wishViewModel
                )

                AddWishPage.GiftCount -> SelectWishGiftCountPage(
                    editWishEntryState = tempEditWishEntryState,
                    wishViewModel = wishViewModel
                )
            }
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EditWishPage(
    editWishEntryState: MutableState<EditWishEntry>,
    wishViewModel: WishViewModel,
    onSelectGift: OnClick = {},
    onSelectGiftCount: OnClick = {},
) {

    val editWishEntry = editWishEntryState.value

    val context = LocalContext.current
    Column(modifier = Modifier.fillMaxWidth()) {

        val editThanks = remember {
            EditContent.Input(editWishEntryState, context.getString(R.string.点击填写报恩方式, wishViewModel.wishListConfig.thanksWordCount))
        }

        Column(
            modifier = Modifier.fillMaxWidth(),
            verticalArrangement = Arrangement.spacedBy(8.dp)
        ) {

            val editGift = remember {
                EditContent.Select(editWishEntry.giftName, context.getString(R.string.点击选择礼物))
            }

            val editCount = remember {
                EditContent.Select(editWishEntry.countText, context.getString(R.string.点击选择个数))
            }

            SideEffect { // 必须要有这个
                editThanks.endIndex = null
            }

            EditWishItem(stringResource(id = R.string.心愿礼物), editGift, onSelectGift)
            EditWishItem(stringResource(id = R.string.礼物数量), editCount, onSelectGiftCount)
            EditWishItem(stringResource(id = R.string.感恩方式), editThanks)
        }

        Text(
            text = stringResource(id = R.string.选择感恩方式),
            modifier = Modifier.padding(top = 15.dp),
            color = Color(0xFFEFE4FF),
            fontSize = 12.sp
        )

        FlowRow(
            modifier = Modifier
                .padding(top = 8.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalArrangement = Arrangement.spacedBy(8.dp),
            maxItemsInEachRow = 2,
        ) {
            wishViewModel.wishListConfig.recommendedThanks.forEach {
                Box(
                    modifier = Modifier
                        .weight(1f)
                        .height(32.dp)
                        .clip(CircleShape)
                        .background(Color(0xFFF6E8FF))
                        .clickable {
                            editThanks.value = it
                            editThanks.endIndex = it.length
                        },
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = it,
                        color = Color(0xFFD594FF),
                        fontSize = 12.sp
                    )
                }
            }
        }

        Spacer(modifier = Modifier.height(20.dp))
    }
}

sealed interface EditContent : Parcelable {

    @Parcelize
    data class Select(val content: String, val hint: String) : EditContent

    @Parcelize
    data class Input(
        val editWishEntryState: @RawValue MutableState<EditWishEntry>,
        val hint: String,
    ) : EditContent {


        var value: String
            get() = editWishEntryState.value.thanks.orEmpty()
            set(value) {
                editWishEntryState.value = editWishEntryState.value.copy(thanks = value)
            }


        @IgnoredOnParcel
        private val selectionEndIndex = mutableStateOf(if (value.isEmpty()) null else value.length)


        var endIndex: Int?
            get() = selectionEndIndex.value
            set(value) {
                selectionEndIndex.value = value
            }
    }
}

@Composable
private fun EditWishItem(editName: String, editContent: EditContent, onClick: OnClick = {}) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .height(40.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0xFFBD8EFF))
            .border(
                width = 0.5.dp, brush = Brush.verticalGradient(listOf(Color(0x4DFFFFFF), Color(0x1AFFFFFF))), shape = RoundedCornerShape(8.dp)
            )
            .padding(start = 12.dp, end = 8.dp), verticalAlignment = Alignment.CenterVertically
    ) {
        Text(text = "${editName}：", color = Color.White, fontSize = 14.sp)

        Box(
            modifier = Modifier
                .padding(end = 5.dp)
                .weight(1f)
                .fillMaxHeight()
                .noEffectClickable(onClick = onClick),
            contentAlignment = Alignment.CenterStart
        ) {
            when (editContent) {
                is EditContent.Select -> {
                    if (editContent.content.isNotEmpty()) {
                        Text(
                            text = editContent.content,
                            modifier = Modifier.basicMarquee(),
                            color = Color.White,
                            fontSize = 14.sp,
                            maxLines = 1
                        )
                    } else {
                        Text(
                            text = editContent.hint,
                            modifier = Modifier.basicMarquee(),
                            color = Color.White.copy(0.5f),
                            fontSize = 12.sp,
                            maxLines = 1
                        )
                    }
                }

                is EditContent.Input -> {
                    AppBasicTextField(
                        value = editContent.value,
                        onValueChange = {
                            editContent.value = it
                        },
                        modifier = Modifier
                            .fillMaxWidth()
                            .clearFocusOnKeyboardDismiss(),
                        hintValue = editContent.hint,
                        selection = editContent.endIndex?.let {
                            TextRange(it)
                        },
                        textStyle = TextStyle(
                            color = Color.White,
                            fontSize = 14.sp,
                        ),
                        hintStyle = TextStyle(
                            color = Color.White.copy(0.5f),
                            fontSize = 12.sp,
                        ),
                        cursorBrush = SolidColor(Color.White),
                        maxLines = 1,
                    )
                }
            }
        }


        Icon(
            painter = painterResource(id = R.drawable.ic_cpd_arrow_right), contentDescription = null, tint = Color.White
        )
    }
}

@Preview
@Composable
private fun PreviewEditWishHostPage() {
    WishPageScaffold {
        EditWishHostPage(C2CWishViewModel.Preview)
    }
}