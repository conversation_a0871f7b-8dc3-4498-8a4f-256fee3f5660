package com.qyqy.ucoo.compose.vm.room.cross

import android.view.WindowManager
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import com.overseas.common.sntp.SNTPManager
import com.overseas.common.utils.dpF
import com.qyqy.ucoo.R
import com.qyqy.ucoo.UserPartition
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.CrossPkConfigResponse
import com.qyqy.ucoo.compose.data.CrossRoomPkAddTimeEvent
import com.qyqy.ucoo.compose.data.CrossRoomPkData
import com.qyqy.ucoo.compose.data.CrossRoomPkMicMutedEvent
import com.qyqy.ucoo.compose.data.CrossRoomPkResultEvent
import com.qyqy.ucoo.compose.data.RefuseCrossRoomPkInviteEvent
import com.qyqy.ucoo.compose.data.SetMutedResponse
import com.qyqy.ucoo.compose.map
import com.qyqy.ucoo.compose.presentation.room.CrossRoomPkResultDialog
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.MsgFilter
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.ChatRoomViewModel
import com.qyqy.ucoo.loginCoroutineScope
import com.qyqy.ucoo.runWithTopActivity
import com.qyqy.ucoo.utils.toast
import com.qyqy.ucoo.widget.dialog.AppDialog
import com.qyqy.ucoo.widget.dialog.dialogCommonWidth
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch

// --- 4. PK 状态管理和消息处理 ---
class CrossRoomPkManager(
    private val repository: CrossRoomPkRepository,
    private val chatRoomViewModel: ChatRoomViewModel,
    private val coroutineScope: CoroutineScope
) : IMCompatListener {

    // 当前 PK 的详细数据 (进行中或结束后)
    val pkData: StateFlow<CrossRoomPkData?> = chatRoomViewModel.uiState.map(coroutineScope) {
        it.roomState.crossPkInfo?.takeIf { info -> info.checkIsValid() }
    }

    override val filter: MsgFilter = MsgFilter(chatRoomViewModel.room.rongCloudId, UserPartition.UCOO)

    private val currentRoomId = chatRoomViewModel.room.roomId

    private var job: Job? = null

    init {
        IMCompatCore.addIMListener(this)
//        coroutineScope.launch {
//            delay(3000)
//            restorePkStateOnEnterRoom()
//        }
    }

    fun release() {
        IMCompatCore.removeIMListener(this)
    }

    override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
        handlePkMessage(message)
    }

    /**
     * 设置对方静音
     */
    fun setOpponentMuted() {
        val currentPkData = pkData.value
        val pkId = currentPkData?.pkInfo?.pkId
        val opponentRoomId = currentPkData?.getOpponentRoomBasicInfo(currentRoomId)?.id
        val currentRoom = currentPkData?.getCurrentRoomPkDetail(currentRoomId)

        if (pkId == null || opponentRoomId == null || currentRoom == null) {
            return
        }

        coroutineScope.launch {
            repository.setOpponentMuted(pkId, opponentRoomId, !currentRoom.isMicMuted).toastError()
        }
    }

    /**
     * PK 加时
     * @param setType 后端定义的 "加时" 操作类型 ID
     * @param extraDuration 加时时长 (秒)
     */
    fun addPkTime(extraDuration: Int) {
        val pkId = pkData.value?.pkInfo?.pkId ?: return
        // 状态更新依赖 cross_room_pk_add_time_event 事件
        coroutineScope.launch {
            repository.addPkTime(pkId, 1, extraDuration).toastError()
        }
    }

    /**
     * 提前终止 PK
     * @param setType 后端定义的 "终止" 操作类型 ID
     */
    fun terminatePk() {
        val pkId = pkData.value?.pkInfo?.pkId ?: return
        // 状态更新依赖 cross_room_pk_result_event 事件
        coroutineScope.launch {
            repository.terminatePk(pkId, 2).toastError()
        }
    }

    suspend fun getCrossPkConfig() = repository.fetchCrossPkConfig(false).getOrNull()

    /**
     * 当用户进入房间时，检查并恢复当前的 PK 状态 (如果存在)
     */
    private suspend fun restorePkStateOnEnterRoom() {
        val result = repository.fetchCurrentPkInfo()
        result.onSuccess { pkData ->
            // 根据房间详情里的 PK 数据恢复状态
            if (pkData != null) {
                updateCrossRoomPkData(pkData)
            }
        }
    }

    // --- 消息处理 ---

    /**
     * 处理收到的 PK 相关 WebSocket 消息
     * @param cmd 消息命令字
     * @param jsonData 消息内容的 JSON 字符串
     */
    private fun handlePkMessage(message: UCCustomMessage) {
        val cmd = message.cmd
        try {
            when (cmd) {
                MsgEventCmd.CMD_REFUSE_CROSS_ROOM_PK_INVITE -> handleRefuseInviteEvent(message.parseDataJson<RefuseCrossRoomPkInviteEvent>())
                MsgEventCmd.CMD_CROSS_ROOM_PK_START -> handlePkStartEvent(message.parseDataJson<CrossRoomPkData>())
                MsgEventCmd.CMD_CROSS_ROOM_PK_SYNC -> handlePkSyncEvent(message.parseDataJson<CrossRoomPkData>())
                MsgEventCmd.CMD_CROSS_ROOM_PK_MIC_MUTED -> handleMicMutedEvent(message.parseDataJson<CrossRoomPkMicMutedEvent>())
                MsgEventCmd.CMD_CROSS_ROOM_PK_ADD_TIME -> handleAddTimeEvent(message.parseDataJson<CrossRoomPkAddTimeEvent>())
                MsgEventCmd.CMD_CROSS_ROOM_PK_RESULT -> {
                    handlePkResultEvent(message.parseDataJson<CrossRoomPkData>()?.let {
                        CrossRoomPkResultEvent(message.getJsonInt("countdown", 0), it)
                    })
                }
                // 可以添加其他需要处理的命令
                else -> { /* 不是 PK 相关消息，忽略 */
                }
            }
        } catch (e: Exception) {
            // 处理 JSON 解析错误或其他异常
            println("Error handling PK message ($cmd): ${e.message}")
            // 考虑是否需要重置状态或上报错误
        }
    }


    private fun handleRefuseInviteEvent(eventData: RefuseCrossRoomPkInviteEvent?) {
//        eventData ?: return
//        toast(eventData.toast)
    }

    private fun handlePkStartEvent(pkData: CrossRoomPkData?) {
        pkData ?: return
        job?.cancel()
        job = null
        updateCrossRoomPkData(pkData)
    }

    private fun handlePkSyncEvent(newPkData: CrossRoomPkData?) {
        newPkData ?: return
        // 只有在 PK 进行中才处理同步事件
        updateCrossRoomPkData(newPkData)
    }

    private fun handleMicMutedEvent(eventData: CrossRoomPkMicMutedEvent?) {
        eventData ?: return
        val currentPk = pkData.value ?: return
        // 更新 PkData 中对应房间的 isMicMuted 状态
        // 注意：这个事件可能是对方操作了对己方的静音，也可能是己方操作了对对方的静音的回执
        // 需要根据 target_room.id 判断是被静音还是主动静音了对方
        val isMuted = eventData.isMicMuted

        // 正常情况下只需要关心有没有静音对方的音频，不需要关心自己有没有被对方静音
        // 现在静音是服务端操作，客户端其实可以不处理
        val updatedPkData = if (eventData.targetRoom.id == currentPk.targetRoom.id) {
            currentPk.copy(
                _pkInfo = currentPk.pkInfo.copy(
                    fromRoomPkInfo = currentPk.pkInfo.fromRoomPkInfo?.copy(isMicMuted = isMuted, thirdPushStreamId = eventData.thirdPushStreamId) // 更新对方的被静音状态
                )
            )
        } else {
            currentPk.copy(
                _pkInfo = currentPk.pkInfo.copy(
                    targetRoomPkInfo = currentPk.pkInfo.targetRoomPkInfo?.copy(isMicMuted = isMuted, thirdPushStreamId = eventData.thirdPushStreamId) // 更新对方的被静音状态
                )
            )
        }
        updateCrossRoomPkData(updatedPkData)
    }

    private fun handleAddTimeEvent(eventData: CrossRoomPkAddTimeEvent?) {
        eventData ?: return
        val currentPk = pkData.value ?: return

        // 更新 PkInfo 中的结束时间和总时长 (如果需要的话，虽然总时长可能不直接用)
        val updatedPkInfo = currentPk.pkInfo.copy(
            endTime = eventData.endTime,
            duration = currentPk.pkInfo.duration + eventData.addTimeDuration // 更新总时长
        )

        updateCrossRoomPkData(currentPk.copy(_pkInfo = updatedPkInfo))
    }

    private fun handlePkResultEvent(resultEvent: CrossRoomPkResultEvent?) {
        resultEvent ?: return
        // 不论当前状态如何，收到结果事件都应该处理
        // 因为可能因为网络问题等没收到 start 或 sync，直接收到了 result
        val pkData = resultEvent.pkData
        val pkId = pkData.pkInfo.pkId

        try {
            if (pkId == this.pkData.value?.pkInfo?.pkId) {
                // 不需要更高mode和background
                chatRoomViewModel.setState {
                    copy(roomState = roomState.copy(crossPkInfo = pkData.copy(_pkInfo = pkData.pkInfo.copy(endTime = SNTPManager.now().div(1000).toInt()))))
                }
            }

            val room = pkData.getCurrentRoomBasicInfo(currentRoomId)

            val roomName: String = room.title
            val roomIcon: String = room.avatarUrl
            val contributorList: List<String>? = pkData.getCurrentRoomPkDetail(currentRoomId)?.cheers?.map { it.avatarUrl }

            val backgroundResId: Int
            val topBackgroundResId: Int
            val tintColor: Color

            when (pkData.pkInfo.winnerRoom) {
                null -> {
                    backgroundResId = R.drawable.bg_dialog_cross_room_pk_draw
                    topBackgroundResId = R.drawable.bg_dialog_cross_room_pk_draw_in_top
                    tintColor = Color(0xFF6A877C)
                }

                currentRoomId -> {
                    backgroundResId = R.drawable.bg_dialog_cross_room_pk_win
                    topBackgroundResId = R.drawable.bg_dialog_cross_room_pk_win_in_top
                    tintColor = Color(0xFFFF3E57)
                }

                else -> {
                    backgroundResId = R.drawable.bg_dialog_cross_room_pk_lose
                    topBackgroundResId = R.drawable.bg_dialog_cross_room_pk_lose_in_top
                    tintColor = Color(0xFF646C9B)
                }
            }

            job = coroutineScope.launch {
                delay(resultEvent.countdown.times(1000L))
                if (pkId == <EMAIL>?.pkInfo?.pkId) {
                    updateCrossRoomPkData(pkData, true)
                }
            }

            runWithTopActivity {
                val composeView = ComposeView(this)
                val dialog = AppDialog.Builder(this)
                    .setView(composeView)
                    .setWidth(WindowManager.LayoutParams.MATCH_PARENT)
                    .setOnDismissListener {
                        job?.cancel()
                        job = null
                        if (pkId == <EMAIL>?.pkInfo?.pkId) {
                            updateCrossRoomPkData(pkData, true)
                        }
                    }
                    .show()

                composeView.setContent {
                    CrossRoomPkResultDialog(
                        roomName = roomName,
                        roomIcon = roomIcon,
                        contributorList = contributorList,
                        countDownSeconds = resultEvent.countdown,
                        backgroundResId = backgroundResId,
                        topBackgroundResId = topBackgroundResId,
                        tintColor = tintColor,
                        onDismiss = {
                            dialog.dismiss()
                        }
                    )
                }
            }
        } catch (e: Exception) {
            println("Error parsing PK Result event: ${e.message}")
            // 解析失败也尝试重置状态
            if (pkId == this.pkData.value?.pkInfo?.pkId) {
                updateCrossRoomPkData(pkData, true)
            }
        }
    }

    private fun updateCrossRoomPkData(data: CrossRoomPkData, isFinish: Boolean = false) {
        chatRoomViewModel.setState {
            copy(roomState = data.getCurrentRoomBasicInfo(currentRoomId).let {
                roomState.copy(crossPkInfo = if (isFinish) null else data, roomMode = it.roomMode, roomBackground = it.roomBackground)
            })
        }
    }

}