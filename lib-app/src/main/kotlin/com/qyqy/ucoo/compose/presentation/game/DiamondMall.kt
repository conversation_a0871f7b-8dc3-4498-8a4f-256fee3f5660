package com.qyqy.ucoo.compose.presentation.game

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Icon
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.UIStateContent
import com.qyqy.ucoo.compose.state.isError
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.overScrollVertical
import com.qyqy.ucoo.compose.ui.rememberOverscrollFlingBehavior


@Composable
fun DiamondMallPage(
    diamond: Int,
    uiState: UIState<ExchangeList>,
    onAction: (UIAction) -> Unit = {},
) {
    Column(modifier = Modifier.fillMaxSize()) {
        val onBackPressedDispatcher = LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher
        AppTitleBar(title = stringResource(id = R.string.diamond_store), onBack = {
            onBackPressedDispatcher?.onBackPressed()
        }){
            Text(
                text = stringResource(id = R.string.历史记录),
                modifier = Modifier
                    .align(Alignment.CenterEnd)
                    .padding(end = 16.dp)
                    .clickable {
                        onAction(UIAction.OnHistory)
                    },
                color = Color.White,
                fontSize = 14.sp
            )
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp)
        ) {
            DiamondBalanceCard(diamond = diamond, modifier = Modifier.padding(top = 24.dp)) {
                Row(
                    modifier = Modifier.clickable {
                        uiState.data?.diamondAccount?.ruleLink?.takeIf {
                            it.isNotEmpty()
                        }?.also {
                            onAction(UIAction.OnRouterByLink(it))
                        }
                    },
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppText(
                        text = uiState.data?.diamondAccount?.ruleText.orEmpty().ifEmpty {
                            stringResource(R.string.查看如何获得钻石)
                        },
                        color = colorResource(id = R.color.white_alpha_30), fontSize = 12.sp
                    )
                    Icon(
                        painter = painterResource(id = R.drawable.ic_cp_zone_qa),
                        contentDescription = null,
                        modifier = Modifier
                            .padding(start = 3.dp)
                            .size(12.dp),
                        tint = colorResource(id = R.color.white_alpha_30)
                    )
                }
            }

            Row(
                modifier = Modifier
                    .padding(top = 40.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                HorizontalDivider(modifier = Modifier.weight(1f), thickness = 0.5.dp, color = Color(0x26FFFFFF))
                Text(
                    text = stringResource(R.string.钻石兑换),
                    modifier = Modifier.widthIn(max = 240.dp),
                    color = Color.White,
                    fontSize = 16.sp,
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
                HorizontalDivider(modifier = Modifier.weight(1f), thickness = 0.5.dp, color = Color(0x26FFFFFF))
            }

            Spacer(modifier = Modifier.height(15.dp))

            UIStateContent(
                uiState = uiState,
                onEmptyOrError = {
                    if (it.isError) {
                        onAction(UIAction.Retry)
                    }
                }
            ) {
                val scrollState = rememberLazyGridState()
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    modifier = Modifier
                        .fillMaxSize()
                        .overScrollVertical(),
                    state = scrollState,
                    contentPadding = PaddingValues(top = 10.dp),
                    verticalArrangement = Arrangement.spacedBy(12.dp),
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    flingBehavior = rememberOverscrollFlingBehavior { scrollState }
                ) {
                    items(value.items, key = {
                        it.id
                    }) {
                        Column(
                            modifier = Modifier
                                .clip(Shapes.small)
                                .background(Color(0x0DFFFFFF)), horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            ComposeImage(
                                model = it.icon, modifier = Modifier
                                    .fillMaxWidth()
                                    .height(72.dp),
                                contentScale = ContentScale.Fit
                            )

                            AppText(text = it.name, fontSize = 13.sp, color = Color.White)

                            Spacer(modifier = Modifier.height(4.dp))
                            AppText(text = it.desc, fontSize = 11.sp, color = colorResource(id = R.color.white_alpha_50))

                            Spacer(modifier = Modifier.height(4.dp))
                            Row(
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(id = R.drawable.icon_diamond),
                                    contentDescription = null,
                                    modifier = Modifier.size(13.dp)
                                )
                                Spacer(modifier = Modifier.width(3.dp))
                                Text(text = it.price.toString(), fontSize = 13.sp, color = Color.White, fontFamily = D_DIN)
                            }

                            Spacer(modifier = Modifier.height(8.dp))
                            Box(
                                modifier = Modifier
                                    .padding(horizontal = 16.dp)
                                    .fillMaxWidth()
                                    .height(24.dp)
                                    .background(
                                        brush = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))),
                                        shape = CircleShape
                                    )
                                    .clickable {
                                        onAction(UIAction.OnExchangeOfDiamond(it.id))
                                    }
                                    .padding(horizontal = 5.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                AutoSizeText(text = stringResource(R.string.兑换), fontSize = 12.sp, color = Color.White, maxLines = 2)
                            }
                            Spacer(modifier = Modifier.height(8.dp))
                        }
                    }
                }
            }
        }
    }
}

@Preview
@Composable
private fun PreviewDiamondMallPage() {
    DiamondMallPage(
        1000, UIState.Data(
            ExchangeList(
                listOf(
                    ExchangeItem(1, "极速赛车", "x99", 100),
                    ExchangeItem(2, "极速赛车", "x99", 100),
                    ExchangeItem(3, "极速赛车", "x99", 100),
                    ExchangeItem(4, "极速赛车", "x99", 100),
                    ExchangeItem(5, "极速赛车", "x99", 100),
                )
            )
        )
    )
}