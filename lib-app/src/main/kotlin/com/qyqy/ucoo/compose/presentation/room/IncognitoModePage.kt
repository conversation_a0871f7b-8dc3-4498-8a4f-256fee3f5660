package com.qyqy.ucoo.compose.presentation.room

import com.qyqy.ucoo.widget.Switch
import com.qyqy.ucoo.widget.SyncSwitchButton
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import com.bumptech.glide.integration.compose.placeholder
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.loadingUiState
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LoadingWidget
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Preview
@Composable
private fun PreviewIncognitoModeCard() {
    IncognitoModeCard(loadingUiState())
}


@Composable
fun IncognitoModeCard(
    uiState: UIState<IncognitoMode>,
    modifier: Modifier = Modifier,
    onRetry: () -> Unit = {},
    onBuy: (Int) -> Unit = {},
    onSyncSwitch: suspend (Switch) -> Boolean = { true },
) {
    var readyGood by remember {
        mutableStateOf<IncognitoModeGood?>(null)
    }
    Column(
        modifier = modifier
            .fillMaxWidth()
            .animateContentSize()
            .background(Brush.verticalGradient(listOf(Color(0xFF170F26), Color(0xFF311F45))), RoundedCornerShape(topStart = 24.dp, topEnd = 24.dp))
            .navigationPadding(minimumPadding = 15.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Row(
            modifier = Modifier.padding(top = 20.dp),
            horizontalArrangement = Arrangement.spacedBy(10.dp, Alignment.CenterHorizontally),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_room_incognito_mode),
                contentDescription = null,
                modifier = Modifier.height(9.dp),
                contentScale = ContentScale.FillHeight
            )
            Text(
                text = stringResource(id = R.string.房间隐身侠特权), fontSize = 16.sp, style = LocalTextStyle.current.copy(
                    fontWeight = FontWeight.SemiBold, brush = Brush.horizontalGradient(
                        listOf(Color(0xFFFDE9DB), Color(0xFFF6BD90))
                    )
                )
            )
            Image(
                painter = painterResource(id = R.drawable.ic_room_incognito_mode), contentDescription = null, modifier = Modifier
                    .height(9.dp)
                    .graphicsLayer {
                        rotationY = 180f
                    }, contentScale = ContentScale.FillHeight
            )
        }

        Image(
            painter = painterResource(id = R.drawable.background_room_incognito_mode),
            contentDescription = null,
            modifier = Modifier
                .padding(top = 24.dp, start = 20.dp, end = 20.dp)
                .fillMaxWidth(),
            contentScale = ContentScale.FillWidth
        )

        Text(
            text = stringResource(R.string.开启隐身侠模式),
            modifier = Modifier.padding(vertical = 16.dp, horizontal = 28.dp),
            fontSize = 14.sp,
            color = Color(0xFFE3C0A2),
            textAlign = TextAlign.Center,
        )

        AnimatedContent(targetState = uiState, label = "action") { data ->
            when (data) {
                is UIState.Loading -> {
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(80.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        LoadingWidget()
                    }
                }

                is UIState.Error -> {
                    Box(
                        modifier = Modifier
                            .height(80.dp)
                            .noEffectClickable(onClick = onRetry), contentAlignment = Alignment.Center
                    ) {
                        Text(text = data.text.orEmpty(), color = colorResource(id = R.color.red_300), fontSize = 14.sp)
                    }
                }

                is UIState.Data -> {
                    when (val it = data.value) {
                        is IncognitoMode.Already -> {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .heightIn(min = 172.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.SpaceBetween
                            ) {
                                SimpleItem(
                                    modifier = Modifier
                                        .padding(horizontal = 16.dp)
                                        .fillMaxWidth()
                                        .background(Brush.verticalGradient(listOf(Color(0xFF260E31), Color(0xFF1A0C23))), Shapes.small)
                                        .border(0.5.dp, Color(0xFF806D89), Shapes.small)
                                        .padding(horizontal = 16.dp, vertical = 20.dp), startContent = {
                                    Image(
                                        painter = painterResource(id = R.drawable.ic_room_incognito_logo),
                                        contentDescription = null,
                                        modifier = Modifier.size(48.dp)
                                    )
                                }, centerContent = {
                                    Text(
                                        text = stringResource(R.string.隐身侠模式),
                                        fontSize = 16.sp,
                                        color = Color(0xFFE3C0A2),
                                        modifier = Modifier.padding(horizontal = 12.dp),
                                        overflow = TextOverflow.Ellipsis,
                                        maxLines = 1
                                    )

                                    Spacer(modifier = Modifier.height(4.dp))

                                    Text(
                                        text = it.remainingTime,
                                        fontSize = 14.sp,
                                        color = Color(0x80E3C0A2),
                                        modifier = Modifier.padding(horizontal = 12.dp),
                                        overflow = TextOverflow.Ellipsis,
                                        maxLines = 1
                                    )
                                }, endContent = {
                                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                        val switchState = remember { mutableStateOf(Switch.valueOf(it.on)) }
                                        SyncSwitchButton(
                                            switchState = switchState,
                                            onSync = onSyncSwitch,
                                            width = 44.dp,
                                            height = 26.dp,
                                            uncheckedTrackColor = Color(0xFF4D2062),
                                            checkedTrackColor = Color(0xFFF4B75A),
                                        )
                                        Text(
                                            text = if (switchState.value == Switch.ON) stringResource(R.string.已开启) else stringResource(R.string.已关闭),
                                            fontSize = 13.sp,
                                            color = Color(0xFFE3C0A2),
                                            modifier = Modifier.padding(top = 3.dp),
                                        )
                                    }
                                })

                                Text(
                                    text = it.tips,
                                    modifier = Modifier.padding(vertical = 10.dp, horizontal = 28.dp),
                                    fontSize = 12.sp,
                                    color = Color(0xFFE3C0A2),
                                    textAlign = TextAlign.Center,
                                )
                            }
                        }

                        is IncognitoMode.Buy -> {
                            Column(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .heightIn(min = 172.dp),
                                horizontalAlignment = Alignment.CenterHorizontally,
                                verticalArrangement = Arrangement.SpaceBetween
                            ) {
                                Column(
                                    modifier = Modifier
                                        .padding(horizontal = 16.dp)
                                        .fillMaxWidth()
                                        .background(Brush.verticalGradient(listOf(Color(0xFF260E31), Color(0xFF1A0C23))), Shapes.small)
                                        .border(0.5.dp, Color(0xFF806D89), Shapes.small)
                                        .padding(horizontal = 16.dp),
                                ) {
                                    it.goods.forEachIndexed { index, item ->
                                        SimpleItem(modifier = Modifier.padding(vertical = 12.dp), startContent = {
                                            ComposeImage(
                                                model = item.icon, contentDescription = "icon", modifier = Modifier.size(48.dp),
                                                loading = placeholder(painterResource(id = R.drawable.ic_room_incognito_logo)),
                                            )
                                        }, centerContent = {
                                            Text(
                                                text = item.name,
                                                fontSize = 16.sp,
                                                color = Color(0xFFE3C0A2),
                                                modifier = Modifier.padding(horizontal = 12.dp),
                                                overflow = TextOverflow.Ellipsis,
                                                maxLines = 1
                                            )

                                            Spacer(modifier = Modifier.height(4.dp))

                                            Row(
                                                modifier = Modifier.padding(horizontal = 12.dp),
                                                verticalAlignment = Alignment.CenterVertically,
                                                horizontalArrangement = Arrangement.spacedBy(4.dp)
                                            ) {
                                                Image(
                                                    painter = painterResource(id = R.drawable.ic_u_coin),
                                                    contentDescription = "coin",
                                                    modifier = Modifier.size(16.dp)
                                                )
                                                Text(
                                                    text = item.price.toString(), fontSize = 14.sp, color = Color(0xFFFFCF69),

                                                    overflow = TextOverflow.Ellipsis, maxLines = 1
                                                )
                                            }
                                        }, endContent = {
                                            Box(
                                                modifier = Modifier
                                                    .size(84.dp, 36.dp)
                                                    .clip(CircleShape)
                                                    .background(Brush.horizontalGradient(listOf(Color(0xFFF2DBB5), Color(0xFFEDB664))))
                                                    .clickable {
                                                        readyGood = item
                                                    }) {
                                                Text(
                                                    text = stringResource(R.string.立即开通),
                                                    modifier = Modifier.align(Alignment.Center),
                                                    fontSize = 15.sp,
                                                    color = Color(0xFF593A0C),
                                                    fontWeight = FontWeight.SemiBold,
                                                )
                                            }
                                        })
                                        if (index != it.goods.lastIndex) {
                                            HorizontalDivider(thickness = 0.5.dp, color = Color(0x26FFFFFF))
                                        }
                                    }
                                }

                                Text(
                                    text = it.tips,
                                    modifier = Modifier.padding(vertical = 10.dp, horizontal = 28.dp),
                                    fontSize = 12.sp,
                                    color = Color(0xFFE3C0A2),
                                    textAlign = TextAlign.Center,
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    readyGood?.apply {
        Dialog(onDismissRequest = { readyGood = null }) {
            Column(
                modifier = Modifier
                    .width(270.dp)
                    .background(Color(0xFF222222), Shapes.small)
                    .padding(vertical = 20.dp, horizontal = 16.dp)
            ) {
                Text(text = stringResource(id = R.string.本次需花费xx金币确定开通吗, price), color = Color(0xFFE3C0A2), fontSize = 15.sp)
                Row(modifier = Modifier.padding(top = 20.dp)) {
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .height(36.dp)
                            .clip(CircleShape)
                            .background(Color(0xFF464646))
                            .clickable {
                                readyGood = null
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(text = stringResource(id = R.string.取消), color = Color(0xFFA3A3A3), fontSize = 16.sp)
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                    Box(
                        modifier = Modifier
                            .weight(1f)
                            .height(36.dp)
                            .clip(CircleShape)
                            .background(Color(0xFFF0C88A))
                            .clickable {
                                onBuy(id)
                                readyGood = null
                            },
                        contentAlignment = Alignment.Center
                    ) {
                        Text(text = stringResource(id = R.string.确定), color = Color(0xFF593A0C), fontSize = 16.sp)
                    }
                }
            }
        }
    }
}

/**
 * 无痕模式
 */
sealed interface IncognitoMode {
    data class Already(val on: Boolean, val remainingTime: String, val tips: String) : IncognitoMode

    data class Buy(val goods: List<IncognitoModeGood>, val tips: String) : IncognitoMode
}


@Serializable
data class IncognitoModeInfo(
    @SerialName("in_use") val on: Boolean,
    @SerialName("validity_period") val validityPeriod: Int,
    @SerialName("is_expired") val isExpired: Boolean,
    @SerialName("is_free") val isFree: Boolean,
    val tips: String,
)

@Serializable
data class IncognitoModeGoods(
    val tips: String,
    @SerialName("hidden_man_list") val goods: List<IncognitoModeGood>,
)

@Serializable
data class IncognitoModeGood(
    val id: Int,
    val name: String,
    val icon: String,
    val price: Int,
)


