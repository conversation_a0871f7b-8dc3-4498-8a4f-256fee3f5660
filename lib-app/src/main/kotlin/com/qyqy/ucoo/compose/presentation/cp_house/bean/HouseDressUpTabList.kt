package com.qyqy.ucoo.compose.presentation.cp_house.bean


import com.qyqy.ucoo.compose.ui.ITab
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class HouseDressUpTabList(
    @SerialName("tab_list")
    val tabList: List<Tab> = listOf()
) {
    @Serializable
    data class Tab(
        @SerialName("name")
        override val name: String = "",
        @SerialName("order")
        val order: Int = 0,
        @SerialName("t")
        val t: Int = 0
    ) : ITab
}