package com.qyqy.ucoo.compose.presentation.wedding

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.cpUrl
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick

@Composable
fun WallScreen(title: String, onBack: OnClick, onQuestion: OnClick, content: @Composable ColumnScope.() -> Unit) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(painterResource(id = R.drawable.bg_wedding_wall), contentScale = ContentScale.Crop)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(top = 44.dp)
                .height(44.dp)
                .padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically,
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_wedding_back),
                contentDescription = "",
                modifier = Modifier
                    .size(28.dp)
                    .clickWithShape(onClick = onBack)
            )
            AppText(text = title, fontSize = 16.sp, color = Color.White)
            Image(
                painter = painterResource(id = R.drawable.ic_wedding_question),
                contentDescription = "",
                modifier = Modifier
                    .size(28.dp)
                    .clickWithShape(onClick = onQuestion)
            )
        }
        content()
    }
}

@Preview
@Composable
private fun Preview() {
    WallScreen(title = "婚礼纪念墙", onBack = { /*TODO*/ }, onQuestion = { /*TODO*/ }) {

    }
}

@Composable
fun UserColumn(
    user: AppUser,
    modifier: Modifier = Modifier,
    color: Color = Color.White,
    borderColor: Color = color.copy(alpha = 0.8f),
    onClick: OnClick = {}
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box(modifier = Modifier.size(66.dp, 70.dp)) {
            ComposeImage(
                model = user.avatarUrl, modifier = Modifier
                    .size(66.dp)
                    .clip(CircleShape)
                    .click(onClick = onClick)
                    .border(3.dp, borderColor, CircleShape)
            )
            val cpUrl = user.cpExtraInfo?.levelInfo?.normalImgUrl
            if (!cpUrl.isNullOrEmpty()) {
                ComposeImage(
                    model = cpUrl,
                    contentScale = ContentScale.Inside,
                    modifier = Modifier
                        .size(56.dp,18.dp)
                        .align(Alignment.BottomCenter),
                )
            }
        }
        AppText(
            modifier = Modifier.widthIn(max = 66.dp),
            text = user.nickname,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis,
            color = color,
            fontSize = 12.sp,
            lineHeight = 18.sp,
        )
    }
}

enum class CheckMode {
    SELF, ALL
}

@Composable
fun CheckModeSwitchButton(mode: CheckMode, modifier: Modifier = Modifier, onChange: (newMode: CheckMode) -> Unit) {
    val text = if (mode == CheckMode.ALL)
        stringResource(R.string.check_self)
    else
        stringResource(R.string.check_all)
    AppText(
        text = text, modifier = modifier
            .background(Brush.verticalGradient(listOf(Color(0xFFFF87E3), Color(0xFFFFA9B6))), Shapes.chip)
            .border(1.dp, Color(0xFFFFF9DA), Shapes.chip)
            .widthIn(min = 78.dp)
            .clickWithShape {
                onChange(if (mode == CheckMode.SELF) CheckMode.ALL else CheckMode.SELF)
            }
            .padding(10.dp), textAlign = TextAlign.Center, fontSize = 12.sp, color = Color.White,
        style = TextStyle(shadow = Shadow(color = Color.LightGray, blurRadius = 5.dp.value))
    )
}

@Preview
@Composable
private fun UserColumnPreview() {
    Column {
        UserColumn(user = userForPreview)
        Spacer(modifier = Modifier.height(10.dp))
        CheckModeSwitchButton(mode = CheckMode.ALL) {

        }
    }
}

@Composable
fun WeddingEmpty(modifier: Modifier = Modifier, emptyMessage: String = stringResource(R.string.empty_wedding_list)) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Image(
            painter = painterResource(id = R.drawable.ic_empty_pink),
            contentDescription = "empty",
            modifier = Modifier.width(104.dp),
            contentScale = ContentScale.FillWidth
        )
        Spacer(modifier = Modifier.height(28.dp))
        AppText(
            text = emptyMessage,
            fontSize = 12.sp,
            lineHeight = 18.sp,
            color = Color.White,
            textAlign = TextAlign.Center,
            style = TextStyle(shadow = Shadow(Color(0xFFBF4280), blurRadius = 5.dp.value))
        )
    }
}

@Preview
@Composable
private fun EmptyPreview() {
    WeddingEmpty(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xD5B45656))
            .padding(vertical = 50.dp)
    )
}