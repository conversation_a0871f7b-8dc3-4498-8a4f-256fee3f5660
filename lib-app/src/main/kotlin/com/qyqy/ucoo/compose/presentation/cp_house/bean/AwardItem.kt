package com.qyqy.ucoo.compose.presentation.cp_house.bean


import android.os.Parcelable
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
 * 奖品
 */
@Parcelize
@Serializable
data class AwardItem(
    @SerialName("award_type")
    val awardType: Int = 0,// 1: 装扮, 2: 小屋币
    @SerialName("duration")
    val duration: Int = 0,// 奖励时间:天
    @SerialName("icon")
    val icon: String = "", // 奖励图标
    @SerialName("content")
    val name: String = "",// 奖励名称, 仅award_type=1是有值，其他情况为null
    @SerialName("position")
    val position: Int = 0,
    @SerialName("probability")
    val probability: Double = 0.0,// 中奖概率
    val count: Int = 0,
    @SerialName("create_timestamp")
    val timeStamp: Long = 0,
    val id: Int = 0
) : Parcelable {
    /**
     * 奖品记录标题
     */
    @IgnoredOnParcel
    val recordTitle: String = app.getString(
        R.string.format_reward_title,
        "${name}${
            if (awardType == AWARD_TYPE_DRESS_UP && duration > 0)
//                app.getString(
//                    R.string.format_day,
//                    duration.toString()
//                )
                ""
            else ""
        }", count
    )

    companion object {
        const val AWARD_TYPE_DRESS_UP = 1

        const val AWARD_TYPE_COIN = 2
    }
}


@Serializable
data class AwardResult(
    val balance: Int = 0,
    @SerialName("award_list")
    val awardList: List<AwardItem> = listOf(),
    @SerialName("award_position")
    val awardPosition: List<Int> = listOf()
)