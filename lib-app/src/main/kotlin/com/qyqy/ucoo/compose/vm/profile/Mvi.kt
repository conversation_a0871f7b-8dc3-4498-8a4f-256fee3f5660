package com.qyqy.ucoo.compose.vm.profile

import com.overseas.common.utils.TaskRunMode
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.data.ProfileTab
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.im.bean.CpTask
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.im.bean.ProfileExtra
import com.qyqy.ucoo.mine.MediaInfo
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlin.time.Duration

interface ProfileMvi {

    sealed interface ViewEvent : MviViewModel.MviEvent {

        /**
         * 获取用户信息
         */
        data class GetUserInfo(val userId: String, val mode: TaskRunMode?) : ViewEvent

        /**
         * 获取用户主页信息
         */
        data class GetUserProfileInfo(val userId: String) : ViewEvent

        data class GetCpZone(val userId: String) : ViewEvent

//        data class GetGiftWall(val userId: String) : ViewEvent

        data class ResetUserInfo(val user: AppUser) : ViewEvent

        /**
         * 切换关注关注
         */
        object ToggleFollowUser : ViewEvent

        /**
         * 切换拉黑关系
         */
        object ToggleBlackUser : ViewEvent

        // cp签到
        data class OnCpCheckInClicked(val cpTask: CpTask) : ViewEvent

        // 给cp发送一条提醒消息
        data class OnCpCheckInRemindClicked(val targetId: String) : ViewEvent

        object OnDissolveCpClicked : ViewEvent

        data class OnDoTaskEvent(val id: String) : ViewEvent

        data class OnRefreshEvent(val tab: ProfileTab) : ViewEvent
    }

    sealed interface ViewResult : MviViewModel.MviViewResult {

        object LoadingResult : ViewResult

        data class ErrorResult(val toastError: String? = null, val isLoading: Boolean? = null) : ViewResult

        interface UserViewResult : ViewResult {
            val user: AppUser
        }

        data class UserResult(
            override val user: AppUser,
        ) : UserViewResult

        interface ProfileViewResult : UserViewResult

        data class ResetProfileResult(
            override val user: AppUser,
        ) : ProfileViewResult

        data class UserProfileResult(
            override val user: AppUser,
            val userLabel: List<AttractiveFlag>,
            val extra: ProfileExtra,
            val momentImages: List<MediaInfo>,
        ) : ProfileViewResult

        data class CpZoneResult(
            val cpZone: CpZone,
        ) : ViewResult

//        data class GiftWallResult(
//            val giftWall: List<CategoryGiftWall>,
//        ) : ViewResult

        data class BlackUserResult(val blacked: Boolean) : ViewResult

        data class FollowUserResult(val followed: Boolean) : ViewResult

        object CpZoneTaskResult : ViewResult

        object DissolveCpResult : ViewResult

        data class RouterToResult(val link: String) : ViewResult

        data class RefreshEventResult(val tab: ProfileTab) : ViewResult
    }

    data class ViewState constructor(
        val isLoading: Boolean = false,
        val user: AppUser,
        val tabs: List<ProfileTab>,
        val extra: ProfileExtra = ProfileExtra(),
    ) : MviViewModel.MviViewState {

        val cpRelationship: Relationship.Cp = extra.cpRelationship
    }

    @Serializable
    data class UserSound(
        @SerialName("source_url") val url: String? = null,
        val duration: Int,
    ) {
        val requireUrl: String
            get() = url!!
    }

    interface ViewEffect : MviViewModel.MviSideEffect {

        data class Toast(val msg: String) : ViewEffect

        object GetCpZoneTask : ViewEffect

        data class RouterTo(val link: String) : ViewEffect

        data class Refresh(val tab: ProfileTab) : ViewEffect

    }

}

