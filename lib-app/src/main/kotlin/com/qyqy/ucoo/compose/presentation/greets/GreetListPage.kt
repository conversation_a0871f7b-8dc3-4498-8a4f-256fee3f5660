package com.qyqy.ucoo.compose.presentation.greets

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Button
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.voice.GreetTips
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.AppText

@Composable
fun GreetListPage(
    modifier: Modifier = Modifier,
    buttonText: String = "ok",
    onClick: () -> Unit = {},
    isEmpty: Boolean = true,
    content: @Composable () -> Unit = {},
) {
    Column(
        modifier = modifier
            .fillMaxSize(1f)
            .background(Color(0xFF1C1D1E))
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f)
        ) {

            if (isEmpty) {
                Image(
                    painter = painterResource(id = R.drawable.ic_greet_empty),
                    contentDescription = "empty",
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .padding(top = 120.dp, bottom = 40.dp)
                )
                GreetTips(modifier = Modifier.padding(horizontal = 16.dp))
            } else {
                content()
            }
        }
        Spacer(
            modifier = Modifier
                .fillMaxWidth(1f)
                .height(1.dp)
                .background(Color(0xFF1C1D1E))
        )
        Box(
            modifier = Modifier
                .fillMaxWidth(1f)
                .padding(16.dp)
        ) {
            Button(onClick = onClick, modifier = Modifier.fillMaxWidth(1f)) {
                AppText(text = buttonText, color = Color.White, fontSize = 16.sp)
            }
        }
    }
}

@Preview
@Composable
fun GreetListPagePreview() {
    AppTheme {
        GreetListPage()
    }
}