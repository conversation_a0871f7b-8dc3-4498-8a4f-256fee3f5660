package com.qyqy.ucoo.compose.presentation.room

import android.os.Bundle
import android.os.SystemClock
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.AnimatedContentTransitionScope
import androidx.compose.animation.animateContentSize
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.togetherWith
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyGridScope
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.activityViewModels
import com.overseas.common.ext.safeViewLifecycleScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseFragment
import com.qyqy.ucoo.base.startAct
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.data.ClickItem
import com.qyqy.ucoo.compose.data.CrossPkConfigResponse
import com.qyqy.ucoo.compose.data.Interactive
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.game.DiamondDestination
import com.qyqy.ucoo.compose.presentation.game.DiamondScreenNavigator
import com.qyqy.ucoo.compose.state.Empty
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.loadingUiState
import com.qyqy.ucoo.compose.state.toUIData
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.vm.room.cross.CrossRoomPkRepository
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.feat.mall.SilverMallActivity
import com.qyqy.ucoo.im.compat.IIMEnvironmentScope
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.im.room.ChatRoomViewModel
import com.qyqy.ucoo.sUserKV
import com.qyqy.ucoo.toastRes
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import kotlin.random.Random


class InteractiveExpressionFragment2 : BaseFragment() {

    private val chatViewModel by activityViewModels<ChatRoomViewModel>()

    private var incognitoModeUIState by mutableStateOf<UIState<IncognitoMode>?>(null)

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        return ComposeView(requireContext()).apply {
            layoutParams = FrameLayout.LayoutParams(FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.WRAP_CONTENT).also {
                it.gravity = Gravity.BOTTOM
            }
            // Dispose of the Composition when the view's LifecycleOwner
            // is destroyed
            setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)

            val list = buildList {
//                if (UIConfig.userConf.showSpeedRacing) {
//                    add(ClickItem(Interactive.WebGame(WebGameFragment.gameUrl, R.drawable.ic_speed_racer, getString(R.string.极速赛车))) {
//                          JsBridgeWebActivity.newIntent(requireContext(), Const.HttpUrl.speedRacing).startActivity()
//                    })
//                }

                val key = "new_version_feat_2.42.0_cross_room_pk_v2"

                add(ClickItem(Interactive.LocalItem(R.drawable.ic_room_cross_pk, R.string.跨房PK, false, if (sUserKV.getBoolean(key, false)) null else "New")) {
                    sUserKV.putBoolean(key, true)
                    safeViewLifecycleScope.launch {
                        CrossRoomPkRepository().fetchCrossPkConfig().onSuccess {
                            activity?.onBackPressedDispatcher?.onBackPressed()
                            RoomPkCreateNavigator.navigate(requireContext(), chatViewModel.room, it)
                        }.toastError()
                    }
                })

                if (UIConfig.userConf.showHiddenManEntrance) {
                    add(ClickItem(Interactive.LocalItem(R.drawable.ic_room_play_incognito, R.string.房间隐身侠, false)) {
                        refreshIncognitoMode()
                    })
                }

                if (UIConfig.userConf.showSilverCoinShopEntrance) {
                    add(ClickItem(Interactive.LocalItem(R.drawable.ic_silver_shop, R.string.银币商城)) {
                        startAct<SilverMallActivity>()
                    })
                }
                if (UIConfig.userConf.showDiamondShopEntrance) {
                    add(ClickItem(Interactive.LocalItem(R.drawable.icon_diamond_store_192, R.string.diamond_store)) {
                        DiamondScreenNavigator.navigate(requireContext(), DiamondDestination.Mall)
                    })
                }
                add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_touzi, R.string.骰子)) {
                    sendMessage(1, Random.nextInt(1, 7).toString())
                })

                add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_caiquan, R.string.猜拳)) {
                    sendMessage(5, Random.nextInt(1, 4).toString())
                })

                add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_number_1, R.string.幸运数字)) {
                    sendMessage(2, Random.nextInt(10).toString())
                })

                add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_number_3, R.string.幸运数字)) {
                    sendMessage(3, "${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}")
                })

                add(ClickItem(Interactive.LocalItem(R.drawable.ic_play_number_5, R.string.幸运数字)) {
                    sendMessage(4, "${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}${Random.nextInt(10)}")
                })
            }

            setContent {
                val showIncognitoModeCard by remember {
                    derivedStateOf {
                        incognitoModeUIState != null
                    }
                }

                Box(modifier = Modifier.fillMaxWidth()) {
                    AnimatedContent(
                        targetState = showIncognitoModeCard,
                        transitionSpec = {
                            fadeIn(animationSpec = tween(220, delayMillis = 120)).plus(
                                slideIntoContainer(
                                    towards = AnimatedContentTransitionScope.SlideDirection.Up,
                                    animationSpec = tween(220, delayMillis = 120)
                                )
                            ).togetherWith(
                                fadeOut(animationSpec = tween(120)).plus(
                                    slideOutOfContainer(
                                        towards = AnimatedContentTransitionScope.SlideDirection.Down,
                                        animationSpec = tween(120)
                                    )
                                )
                            )
                        }
                    ) { show ->
                        val uiState = incognitoModeUIState
                        if (show && uiState != null) {
                            IncognitoModeCard(
                                uiState = uiState,
                                modifier = Modifier.align(Alignment.BottomCenter),
                                onRetry = {
                                    refreshIncognitoMode()
                                },
                                onBuy = {
                                    buyIncognitoMode(it)
                                }
                            ) {
                                chatViewModel.roomRepository.switchIncognitoMode(it.value).onSuccess { _ ->
                                    if (it.value) {
                                        toastRes(R.string.隐身侠状态开启成功)
                                    } else {
                                        toastRes(R.string.隐身侠状态已关闭)
                                    }
                                }.isSuccess
                            }
                        } else {
                            Page(list)
                        }
                    }
                }
            }
        }
    }


    private fun refreshIncognitoMode() {
        safeViewLifecycleScope.launch {
            incognitoModeUIState = loadingUiState()
            request()
        }
    }

    private fun buyIncognitoMode(id: Int) {
        safeViewLifecycleScope.launch {
            val old = incognitoModeUIState
            incognitoModeUIState = loadingUiState()
            val start = SystemClock.elapsedRealtime()
            chatViewModel.roomRepository.buyIncognitoMode(id).onSuccess {
                delay(300)
                request()
            }.toastError().onFailure {
                delay(1000.minus(SystemClock.elapsedRealtime().minus(start)))
                incognitoModeUIState = old
            }
        }
    }

    private suspend fun request() {
        chatViewModel.roomRepository.apply {
            fetchMyIncognitoModeInfo().onSuccess {
                if (it.isExpired) {
                    fetchIncognitoModeGoods().onSuccess { result ->
                        incognitoModeUIState = IncognitoMode.Buy(goods = result.goods, tips = result.tips).toUIData()
                    }.onFailure { e ->
                        incognitoModeUIState = UIState.Error(Empty(getString(R.string.加载失败点击重试), R.drawable.ic_empty_for_fans), e)
                    }
                } else {
                    incognitoModeUIState = IncognitoMode.Already(
                        on = it.on,
                        remainingTime = if (it.isFree) getString(R.string.永久有效) else getString(
                            R.string.剩余有效期,
                            it.validityPeriod
                        ),
                        tips = it.tips
                    ).toUIData()
                }
            }.onFailure { e ->
                incognitoModeUIState = UIState.Error(Empty(getString(R.string.加载失败点击重试), R.drawable.ic_empty_for_fans), e)
            }
        }
    }

    @Composable
    private fun Page(list: List<ClickItem<Interactive>>) {

        var gameUiState by remember {
            mutableStateOf<UIState<List<ClickItem<Interactive>>>>(UIState.Loading(R.string.加载中))
        }

        if (gameUiState.isLoading) {
            LaunchedEffect(key1 = Unit) {
                withContext(Dispatchers.IO) {
                    chatViewModel.roomRepository.fetchSudGameList(chatViewModel.roomId).mapCatching { game ->
                        buildList {
                            game.competitiveList?.takeIf {
                                it.isNotEmpty()
                            }?.also {
                                add(ClickItem(Interactive.Title(buildAnnotatedString {
                                    append(getString(R.string.竞技游戏))
                                    withStyle(SpanStyle(fontSize = 12.sp, color = Color(app.getColor(R.color.white_alpha_50)))) {
                                        append(getString(R.string.可获得钻石奖励))
                                    }
                                })))

                                it.forEach { info ->
                                    add(ClickItem(Interactive.SudGame(info)) {
                                        if (info.type == 1) {
                                            (activity as? ChatRoomActivity)?.startSudGame(info)
                                        } else if (info.link.isNotEmpty()) {
                                            activity?.apply {
                                                AppLinkManager.open(this, info.link, info.container)
                                            }
                                        }
                                    })
                                }

                                add(ClickItem(Interactive.Divider("0")))
                            }

                            game.interactiveList?.takeIf {
                                it.isNotEmpty()
                            }?.also {
                                add(ClickItem(Interactive.Title(buildAnnotatedString {
                                    append(getString(R.string.互动游戏))
                                })))

                                it.forEach { info ->
                                    add(ClickItem(Interactive.SudGame(info)) {
                                        if (info.type == 1) {
                                            (activity as? ChatRoomActivity)?.startSudGame(info)
                                        } else if (info.link.isNotEmpty()) {
                                            activity?.apply {
                                                AppLinkManager.open(this, info.link, info.container)
                                            }
                                        }
                                    })
                                }

                                add(ClickItem(Interactive.Divider("1")))
                            }
                        }
                    }
                }.onSuccess {
                    gameUiState = it.toUIData()
                }.onFailure {
                    gameUiState = UIState.Error(Empty(R.string.加载失败点击重试, R.drawable.ic_empty_for_fans), throwable = it)
                }
            }
        }

        LazyVerticalGrid(
            columns = GridCells.Fixed(4),
            modifier = Modifier
                .fillMaxWidth()
                .animateContentSize()
                .heightIn(max = 600.dp)
                .background(Color(0xFF222222), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
                .padding(bottom = 10.dp)
                .navigationBarsPadding(),
            contentPadding = PaddingValues(start = 24.dp, top = 24.dp, end = 24.dp, bottom = 10.dp),
            horizontalArrangement = Arrangement.spacedBy(23.6667.dp),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            interactiveSudGameItems(gameUiState) {
                gameUiState = UIState.Loading(R.string.加载中)
            }
            interactiveItems(list)
        }
    }

    private fun LazyGridScope.interactiveSudGameItems(uiState: UIState<List<ClickItem<Interactive>>>, onRetry: () -> Unit) {
        when (uiState) {
            is UIState.Loading -> {
                item(key = "loading", span = { GridItemSpan(4) }, contentType = -1) {
                    Row(
                        modifier = Modifier
                            .padding(bottom = 10.dp)
                            .fillMaxWidth(),
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.spacedBy(5.dp, Alignment.CenterHorizontally),
                    ) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = colorResource(id = R.color.white),
                            strokeWidth = 1.5.dp
                        )
                        Text(
                            text = uiState.text,
                            color = colorResource(id = R.color.white),
                            fontSize = 14.sp
                        )
                    }
                }
            }

            is UIState.Error -> {
                item(key = "error", span = { GridItemSpan(4) }, contentType = -2) {
                    Box(
                        modifier = Modifier
                            .padding(bottom = 10.dp)
                            .fillMaxWidth()
                            .clickable(onClick = onRetry),
                        contentAlignment = Alignment.Center
                    ) {
                        Text(
                            text = uiState.text.orEmpty(),
                            color = colorResource(id = R.color.red_300),
                            fontSize = 14.sp
                        )
                    }
                }
            }

            is UIState.Data -> {
                interactiveItems(uiState.value)
            }
        }
    }

    private fun LazyGridScope.interactiveItems(list: List<ClickItem<Interactive>>) {
        items(items = list, key = {
            it.item.id
        }, span = {
            GridItemSpan(if (it.item is Interactive.Divider || it.item is Interactive.Title) 4 else 1)
        }, contentType = {
            when (it.item) {
                is Interactive.Title -> 0
                is Interactive.Divider -> 1
                else -> 2
            }
        }) {
            when (val item = it.item) {
                is Interactive.Title -> {
                    AppText(
                        text = item.content,
                        modifier = Modifier.padding(bottom = 10.dp),
                        fontSize = 16.sp,
                        color = Color.White,
                    )
                }

                is Interactive.Divider -> {
                    HorizontalDivider(modifier = Modifier.padding(vertical = 10.dp), thickness = 1.dp, color = Color(0x1AFFFFFF))
                }

                else -> {
                    Item(it)
                }
            }
        }
    }

    @Composable
    private fun Item(clickItem: ClickItem<Interactive>, modifier: Modifier = Modifier) {
        val item = clickItem.item
        val icon = item.icon
        val name = item.name
        var showTips by rememberSaveable {
            mutableStateOf(item.tipsText != null)
        }
        Column(
            modifier = modifier.noEffectClickable {
                showTips = false
                if (item.dismissAfterClick) {
                    activity?.onBackPressedDispatcher?.onBackPressed()
                }
                clickItem.onClick(item)
            }, horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Box(
                modifier = Modifier
                    .size(64.dp, 64.dp)
            ) {
                ComposeImage(
                    model = icon, modifier = Modifier
                        .size(64.dp, 64.dp)
                        .clip(Shapes.small), contentScale = ContentScale.Crop
                )
                if (showTips) {
                    Text(
                        text = item.tipsText ?: "New",
                        modifier = Modifier
                            .offset(4.dp, (-4).dp)
                            .align(Alignment.TopEnd)
                            .background(Color(0xFFFF3F3F), RoundedCornerShape(4.dp))
                            .padding(horizontal = 4.dp, vertical = 2.dp),
                        color = Color.White,
                        fontSize = 10.sp
                    )
                }
            }
            AutoSizeText(
                text = name, modifier = Modifier.padding(top = 12.dp), fontSize = 12.sp, color = Color.White, maxLines = 1
            )
        }
    }

    private fun findSendMessageEnv(): IIMEnvironmentScope? {
        return parentFragment as? IIMEnvironmentScope ?: activity as? IIMEnvironmentScope
    }

    private fun sendMessage(type: Int, result: String) {
        findSendMessageEnv()?.apply {
            sendMessage(
                MessageBundle.Custom.create(
                    cmd = MsgEventCmd.INTERACTIVE_MSG,
                    data = buildJsonObject {
                        put("type", type)
                        put("result", result)
                    },
                    summary = getString(R.string._互动表情_)
                )
            )
        }
    }
}