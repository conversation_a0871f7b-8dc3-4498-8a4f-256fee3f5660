package com.qyqy.ucoo.compose.presentation.mine

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ColorMatrix
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.boundsInRoot
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntOffset
import androidx.compose.ui.unit.IntRect
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Popup
import androidx.compose.ui.window.PopupPositionProvider
import androidx.compose.ui.window.PopupProperties
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.compose.border
import com.qyqy.ucoo.compose.data.BottomRadiusItem
import com.qyqy.ucoo.compose.data.CategoryTitleItem
import com.qyqy.ucoo.compose.data.GiftItem
import com.qyqy.ucoo.compose.data.GiftWallItem
import com.qyqy.ucoo.compose.data.GiftWallItemDetail
import com.qyqy.ucoo.compose.data.GiftWallSummaryBean
import com.qyqy.ucoo.compose.data.NewCategoryGiftWall
import com.qyqy.ucoo.compose.data.SpaceItem
import com.qyqy.ucoo.compose.data.SpanItem
import com.qyqy.ucoo.compose.data.TopRadiusItem
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.bless.BlessingWordEditLayout
import com.qyqy.ucoo.compose.router.UCOOPreviewTheme
import com.qyqy.ucoo.compose.ui.AnimatedDialog
import com.qyqy.ucoo.compose.ui.AnyPopDialogProperties
import com.qyqy.ucoo.compose.ui.AppScrollableTabRow
import com.qyqy.ucoo.compose.ui.ComposeDialog
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.DirectionState
import com.qyqy.ucoo.compose.vm.GiftWallDetailViewModel
import com.qyqy.ucoo.compose.vm.GiftWallViewModelV2
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.im.bean.Gift
import com.qyqy.ucoo.im.bean.IGift
import com.qyqy.ucoo.im.bean.MyGift
import com.qyqy.ucoo.user.gift.GiftRepository
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.toast
import kotlinx.coroutines.flow.collectLatest
import kotlin.math.roundToInt
import kotlin.random.Random

//礼物墙背景色
val LocalGiftBackgroundColor = compositionLocalOf { R.color.color_gift_item_bg }


/**
 * 来源:
 *
 * @param userId 查看的是谁的礼物墙
 * @param profileType 这个参数好像没啥用了
 *  从[com.qyqy.ucoo.compose.presentation.mine.MinePage]来
 *  * 0. 个人中心点击礼物墙礼物进来,只能赠送
 *  从[com.qyqy.ucoo.compose.presentation.profile.UserProfilePage]来
 *  * 1. 从自己的的资料卡里点进来, 只能赠送
 *  * 2. 从其他人的资料卡里点进来, 只能赠送
 *  从[com.qyqy.ucoo.user.GiftWallDialogFragment]来
 *  * 3. 礼物面板点进来, 这种情况下都是求打赏(可选数量), ps: 礼物面板入口处显示的也是自己的礼物墙, 只能索要
 *
 *
 *
 *  以下不会生效
 *  [com.qyqy.ucoo.im.compat.chat.delegates.C2CCustomViewHolder.BegGift]消息来
 *  * 4. 从打赏的信息里点进来, 这种情况下按钮显示打赏礼物, 只能赠送
 *
 * @param onGiftClicked 点击赠送/求打赏, 根据返回值来判断是否由内部自行处理
 */
@Composable
fun GiftWallPagerV2(
    userId: Int,
    profileType: Int,
    refreshFlag: Int = 0,
    oppoUserId: Int = -1,
    modifier: Modifier = Modifier.fillMaxSize(),
    onGiftClicked: (bean: GiftWallItemDetail, count: Int) -> Boolean = { _, _ -> false },
    onClose: (() -> Unit)? = null
) {
    val viewModel = viewModel(GiftWallViewModelV2::class, factory = viewModelFactory {
        initializer {
            GiftWallViewModelV2(
                if (profileType == 3) 4 else 6,//如果是礼物面板点进来的, 就是4私聊, 否则全是6个人主页来的
                userId
            )
        }
    })
    val detailViewModel = viewModel(GiftWallDetailViewModel::class)

    val showGiftDetailDialog = remember {
        mutableStateOf(false)
    }
    var selectedGift by remember {
        mutableStateOf<GiftItem?>(null)
    }

    val sceneId = userId
    val sceneType = if (profileType == 3) 4 else 6

    LaunchedEffect(Unit) {
        detailViewModel.events.collect {
            when (it) {
                is GiftWallDetailViewModel.Event.BegGiftEvent -> {
                    if (it.giftId == selectedGift?.gift?.id) {
                        showGiftDetailDialog.value = false
                        selectedGift = null
                    }
                }

                else -> {

                }
            }
        }
    }

    LaunchedEffect(refreshFlag) {
        viewModel.refresh()
    }

    ReportExposureCompose(onExposureStart = {
        Analytics.quickReportEvent(
            TracePoints.GIFT_WALL_ENTRY_CLICK,
            "scene_type" to sceneType,
            "user_id" to userId
        )
    }) {
        Column(modifier = modifier) {
            if (onClose != null) {
                Box(
                    modifier = Modifier
                        .height(54.dp)
                        .fillMaxWidth()
                ) {
                    ComposeImage(
                        R.drawable.ic_gift_wall_dialog_title, modifier = Modifier
                            .align(Alignment.Center)
                            .size(90.dp, height = 25.dp)
                    )
                    ComposeImage(R.drawable.ic_arrow_left, modifier = Modifier
                        .padding(start = 16.dp)
                        .size(24.dp)
                        .align(Alignment.CenterStart)
                        .click {
                            onClose()
                        })
                }
            }

            val giftWallSummary by viewModel.giftWallSummary.collectAsStateWithLifecycle()

            GiftWallMainWidget(
                giftWallSummary,
                contentWidget = { tab ->
                    val state by viewModel.getState(tab).collectAsStateWithLifecycle()
                    when (state) {
                        is GiftWallViewModelV2.State.LoadSucceedState -> {
                            (state as? GiftWallViewModelV2.State.LoadSucceedState)?.let {
                                ReportExposureCompose(onExposureStart = {
                                    Analytics.quickReportEvent(
                                        TracePoints.GIFT_WALL_TAB_SHOW,
                                        "scene_type" to sceneType,
                                        "scene_id" to sceneId,
                                        "gift_wall_tab_type" to tab.t
                                    )
                                }) {
                                    GiftWallPage(profileType, it.list) {
                                        showGiftDetailDialog.value = true
                                        selectedGift = it
                                    }
                                }
                            }
                        }

                        GiftWallViewModelV2.State.LoadingState -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize(), contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator()
                            }
                        }

                        GiftWallViewModelV2.State.LoadFailed -> {
                            Box(
                                modifier = Modifier
                                    .fillMaxSize(), contentAlignment = Alignment.Center
                            ) {
                                Text(stringResource(R.string.loading_failed))
                            }
                        }

                        else -> {

                        }
                    }
                }
            )
        }
    }

    //region 礼物详情弹窗
    LaunchedEffect(showGiftDetailDialog.value) {
        if (selectedGift?.gift == null && showGiftDetailDialog.value) {
            showGiftDetailDialog.value = false
        }
    }

    if (showGiftDetailDialog.value) {
        selectedGift?.gift?.let { gift ->
            AnimatedDialog(
                onDismiss = { showGiftDetailDialog.value = false },
                properties = AnyPopDialogProperties(direction = DirectionState.CENTER)
            ) {
                GiftItemDetailDialogContent(
                    gift,
                    targetUserId = if (profileType == 3 && oppoUserId != -1) oppoUserId else userId,
                    purpose = if (profileType == 3) 2 else 1,
                    sceneType = sceneType,
                    sceneId = userId,
                    onClose = {
                        showGiftDetailDialog.value = false
                    },
                    onConfirm = onGiftClicked,
                )
            }
        }
    }
    //endregion
}


/**
 * 礼物详情弹窗
 * 在打开后需要调用接口获取该礼物的其他信息
 *
 * @param gift 礼物信息
 * @param targetUserId purpose = 2时需要的是对方id ,其他时候这个id都是个人主页的用户id
 * @param purpose 目的, 1是赠送 2是索要 3是施舍
 * @param sceneType 详情见[com.qyqy.ucoo.user.gift.GiftApi.getWallSummaryInfo] sceneType参数
 * @param sceneId 详情见[com.qyqy.ucoo.user.gift.GiftApi.getWallSummaryInfo] sceneId参数
 * @param begId 乞讨id, purpose = 3 就需要用到这个id了
 * @param onClose 关闭
 * @param onConfirm 赠送/索要
 */
@Composable
fun GiftItemDetailDialogContent(
    gift: IGift,
    targetUserId: Int,
    purpose: Int,
    sceneType: Int,
    sceneId: Int,
    begId: Int? = null,
    defaultCount: Int = 1,
    onClose: () -> Unit = {},
    onConfirm: (bean: GiftWallItemDetail, count: Int) -> Boolean = { _, _ -> false },
) {
    var giftDetail by remember(gift.id) {
        mutableStateOf<GiftWallItemDetail?>(null)
    }
    val viewModel = viewModel(GiftWallDetailViewModel::class)

    LaunchedEffect(Unit) {
        GiftRepository.flowSentGiftToUser.collectLatest {
            if (targetUserId == it.first) {
                viewModel.getGiftDetail(targetUserId, gift.id, purpose, sceneType, sceneId, begId) {
                    giftDetail = it
                }
            }
        }
    }

    val isLoading by viewModel.isLoadingState.collectAsStateWithLifecycle()

    val giftCount = remember {
        mutableStateOf(defaultCount)
    }

    LaunchedEffect(gift.id) {
        viewModel.getGiftDetail(targetUserId, gift.id, purpose, sceneType, sceneId, begId) {
            giftDetail = it

            if (purpose == 1 && it != null) {
                Analytics.quickReportEvent(
                    TracePoints.GIFT_WALL_GIFT_POPUP_FOR_GIVE,
                    "user_id" to targetUserId,
                    "gift_id" to gift.id,
                    "gift_name" to gift.name,
                    "price" to gift.price,
                    "price_type" to gift.priceType,
                    "star_cnt" to it.starCnt,
                    "gift_wall_gift_popup_for_give_type" to it.popupType
                )
            } else if (purpose == 2 && it != null) {
                Analytics.quickReportEvent(
                    TracePoints.GIFT_WALL_GIFT_POPUP_FOR_BEG,
                    "target_user_id" to targetUserId,
                    "gift_id" to gift.id,
                    "gift_name" to gift.name,
                    "price" to gift.price,
                    "price_type" to gift.priceType,
                    "star_cnt" to it.starCnt,
                )
            } else if (purpose == 3 && it != null) {
                Analytics.quickReportEvent(
                    TracePoints.BEG_GIFT_MESSAGE_OPEN,
                    "target_user_id" to targetUserId,
                    "gift_id" to gift.id,
                    "gift_name" to gift.name,
                    "price" to gift.price,
                    "price_type" to gift.priceType,
                    "count" to defaultCount,
                )
            }
        }
        giftCount.value = 1
    }

    val context = LocalContext.current

    Box(
        modifier = Modifier
            .width(311.dp)
            .aspectRatio(0.906f)
            .paint(
                painterResource(R.drawable.ic_gift_wall_dialog_bg),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(
                modifier = Modifier
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(
                                Color(0x964A34A0),
                                Color(0x47362C85),
                                Color(0x3b212469),
                            )
                        ),
                        RoundedCornerShape(20.dp)
                    )
                    .padding(horizontal = 16.dp)
                    .padding(vertical = 6.dp),
                horizontalAlignment = Alignment.CenterHorizontally

            ) {
                ComposeImage(giftDetail?.icon ?: gift.icon, modifier = Modifier.size(134.dp))
                Text(
                    buildAnnotatedString {
                        append(giftDetail?.name ?: gift.name)
                        giftDetail?.begCount?.let {
                            if (it != 0) {
                                append("x$it")
                            }
                        }
                    }, fontSize = 14.sp, lineHeight = 12.sp, color = Color(0xffe5c79a),
                    modifier = Modifier.padding(top = 6.dp)
                )
            }
            Row(verticalAlignment = Alignment.CenterVertically) {
                ComposeImage(
                    if ((giftDetail?.priceType ?: gift.priceType) == 1) R.drawable.ic_gold_coin else R.drawable.ic_silver_coin, modifier = Modifier
                        .padding(end = 4.dp)
                        .size(14.dp)
                )
                Text((giftDetail?.price ?: gift.price).toString(), color = Color.White, fontSize = 12.sp, lineHeight = 10.sp)
            }

            Box(modifier = Modifier.weight(1f), contentAlignment = Alignment.Center) {
                if (isLoading) {
                    CircularProgressIndicator()
                } else if (purpose == 1) {
                    giftDetail?.starCnt?.let {
                        Text(
                            stringResource(R.string.当前点亮总数) + " $it" + stringResource(R.string.个),
                            fontSize = 12.sp,
                            lineHeight = 14.sp,
                            color = Color.White,
                            fontWeight = FontWeight.Medium
                        )
                    }
                } else if (purpose == 2) {
                    CounterWidget(modifier = Modifier.fillMaxSize(), giftCount)
                }
            }

            if (isLoading) {
                Spacer(modifier = Modifier.height(40.dp))
            } else if (giftDetail != null) {
                //点击按钮
                Box(
                    modifier = Modifier
                        .padding(bottom = 14.dp)
                        .size(156.dp, 40.dp)
                        .paint(
                            painterResource(if (giftDetail?.canAction != false) R.drawable.ic_gift_wall_dialog_button else R.drawable.ic_gift_wall_dialog_button_dis),
                            contentScale = ContentScale.FillWidth
                        )
                        .click(noEffect = true) {
                            val bean = giftDetail
                            if (bean != null) {
                                //如果可以点击
                                if (bean.canAction) {
                                    //如果上层处理完毕了就不需要处理了
                                    if (onConfirm(bean, giftCount.value)) {
                                        return@click
                                    }

                                    when (purpose) {
                                        1 -> {
                                            when (bean.type) {
                                                8 -> {//祝福语礼物
                                                    bean.bless?.let {
                                                        object : ComposeDialog(context) {
                                                            @Composable
                                                            override fun Content() {
                                                                BlessingWordEditLayout(
                                                                    lastBlessWord = "",
                                                                    title = bean.bless.title,
                                                                    hint = bean.bless.hint,
                                                                    placeholder = bean.bless.placeholder,
                                                                ) { bless ->
                                                                    if (bless.isNotBlank()) {
                                                                        viewModel.sendWallGift(userId = targetUserId, bean, giftCount.value,sceneType,sceneId, bless)
                                                                        dismiss()
                                                                    } else {

                                                                    }
                                                                }
                                                            }
                                                        }.show()
                                                    }
                                                }

                                                else -> {
                                                    viewModel.sendWallGift(userId = targetUserId, bean, giftCount.value,sceneType,sceneId)
                                                }
                                            }
                                        }

                                        2 -> {
                                            viewModel.makeBegGiftRequest(userId = targetUserId, bean, giftCount.value)
                                        }

                                        3 -> {
                                            when (bean.type) {
                                                8 -> {//祝福语礼物
                                                    bean.bless?.let {
                                                        object : ComposeDialog(context) {
                                                            @Composable
                                                            override fun Content() {
                                                                BlessingWordEditLayout(
                                                                    lastBlessWord = "",
                                                                    title = bean.bless.title,
                                                                    hint = bean.bless.hint,
                                                                    placeholder = bean.bless.placeholder,
                                                                ) { bless ->
                                                                    if (bless.isNotBlank()) {
                                                                        viewModel.giveBegGiftRequest(begId, bless)
                                                                        dismiss()
                                                                        onClose()
                                                                    } else {

                                                                    }
                                                                }
                                                            }
                                                        }.show()
                                                    }
                                                }

                                                else -> {
                                                    viewModel.giveBegGiftRequest(begId)
                                                }
                                            }
                                        }

                                        else -> {
                                            LogUtils.w("GiftWallScreen", "未知的操作purpose")
                                        }
                                    }
                                } else {
                                    //不能点击的话就toast具体错误
                                    if (bean.cantActionHint.isNotBlank()) {
                                        toast(bean.cantActionHint)
                                    }
                                }
                            }
                        }, contentAlignment = Alignment.Center
                ) {
                    Text(buildAnnotatedString {
                        val detail = giftDetail
                        if (detail != null) {
                            append(detail.actionBtnTxt)
                        }
                    }, fontSize = 16.sp, lineHeight = 22.sp, color = Color.White, fontWeight = FontWeight.SemiBold)
                }
            }

            giftDetail?.hint?.let {
                if (it.isNotBlank()) {
                    Text(it, fontSize = 11.sp, lineHeight = 10.sp, color = Color(0xffDAC0FF), modifier = Modifier.padding(bottom = 12.dp))
                }
            }
        }
        ComposeImage(
            R.drawable.ic_close_page,
            modifier = Modifier
                .padding(12.dp)
                .size(24.dp)
                .align(Alignment.TopEnd)
                .click(noEffect = true) { onClose() }
        )
    }

}

//endregion

//region item 组件

@Composable
private fun GiftWallMainWidget(
    summary: GiftWallSummaryBean,
    contentWidget: @Composable (GiftWallSummaryBean.Tab) -> Unit = {}
) {
    TotalGiftStatisticWidget(summary.starCnt, summary.totalCnt)

    val pageState = rememberPagerState {
        summary.tabs.size
    }
    AppScrollableTabRow(summary.tabs, pageState, modifier = Modifier.padding(vertical = 12.dp), indicatorColor = Color.Transparent, tabHeight = 54.dp) { index, tab ->
        if (tab is GiftWallSummaryBean.Tab) {
            Box(
                modifier = Modifier
                    .size(72.dp, 54.dp)
                    .then(if (index == pageState.settledPage) Modifier.paint(painterResource(R.drawable.ic_gift_wall_category_selected), contentScale = ContentScale.FillBounds) else Modifier)

            ) {
                val isSelected = index == pageState.settledPage
//                if (isSelected) {
//                    ComposeImage(model = R.drawable.ic_gift_wall_category_selected, modifier = Modifier.size(72.dp, 54.dp))
//                }
                Text(
                    text = buildAnnotatedString {
                        if (isSelected) {
                            append(tab.name)
                            withStyle(SpanStyle(fontSize = 10.sp)) {
                                append("\n(")
                                append(tab.starCnt.toString())
                                append("/")
                                append(tab.totalCnt.toString())
                                append(")")
                            }
                        } else {
                            withStyle(SpanStyle(fontSize = 10.sp)) {
                                append(tab.name)
                            }
                            withStyle(SpanStyle(fontSize = 8.sp)) {
                                append("\n(")
                                append(tab.starCnt.toString())
                                append("/")
                                append(tab.totalCnt.toString())
                                append(")")
                            }
                        }
                    },
                    fontSize = 14.sp,
                    lineHeight = 14.sp,
                    textAlign = TextAlign.Center,
                    color = if (isSelected) Color(0xffFFE7CF) else Color(0xB2FFFFFF),
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }

    HorizontalPager(pageState, userScrollEnabled = false) {
        contentWidget(summary.tabs[it])
    }
}

/**
 * 计数组件
 * @param modifier 修饰符，用于自定义组件的外观和行为
 * @param countState 外部传入的计数state，用于管理计数值
 */
@Composable
private fun CounterWidget(modifier: Modifier = Modifier, countState: MutableState<Int>) {
    // 使用remember保存makeVisible状态，使其在重组时保持不变
    var makeVisible by remember {
        mutableStateOf(false)
    }
    // 使用remember保存buttonRef状态，用于记录按钮的位置
    val buttonRef = remember { mutableStateOf<Rect?>(null) }
    // 创建一个水平排列的Row组件
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.Center,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 显示减少计数的图标按钮
        ComposeImage(R.drawable.ic_gift_wall_count_drease, modifier = Modifier
            .size(32.dp)
            .noEffectClickable {
                // 当计数大于1时，减少计数
                if (countState.value > 1) {
                    countState.value -= 1
                }
            })
        // 创建一个包含计数文本和下拉箭头的Box组件
        Box(
            modifier = Modifier
                .padding(horizontal = 16.dp)
                .width(98.dp)
                .height(32.dp)
                .background(
                    brush = Brush.verticalGradient(
                        listOf(
                            Color(0xff38269a),
                            Color(0xff8141ff)
                        )
                    ),
                    shape = RoundedCornerShape(16.dp)
                )
                .border(
                    color = Color(0xffe9d1ff), width = 0.5.dp,
                    shape = RoundedCornerShape(16.dp)
                )
                .onGloballyPositioned { coordinates ->
                    // 记录按钮的位置
                    buttonRef.value = coordinates.boundsInRoot()
                }
                .click(noEffect = true) {
                    // 点击时显示下拉菜单
                    makeVisible = true
                },
        ) {
            // 显示当前计数值
            Text(
                "${countState.value}" + stringResource(R.string.个), modifier = Modifier
                    .align(Alignment.Center),
                fontSize = 12.sp, lineHeight = 10.sp,
                color = Color.White
            )
            // 显示下拉箭头图标
            ComposeImage(
                R.drawable.ic_gift_wall_option_arrow,
                modifier = Modifier
                    .padding(end = 20.dp)
                    .size(10.dp, 7.dp)
                    .align(Alignment.CenterEnd)
            )
        }
        // 显示增加计数的图标按钮
        ComposeImage(R.drawable.ic_gift_wall_count_add, modifier = Modifier
            .size(32.dp)
            .noEffectClickable {
                // 增加计数
                countState.value += 1
            })

    }

    // 当makeVisible为true时，显示下拉菜单
    if (makeVisible) {
        val list = listOf(1, 5, 10) // 下拉菜单中的选项列表
        Popup(
            popupPositionProvider = object : PopupPositionProvider {

                // 计算Popup的位置
                override fun calculatePosition(anchorBounds: IntRect, windowSize: IntSize, layoutDirection: LayoutDirection, popupContentSize: IntSize): IntOffset {
                    return IntOffset(
                        x = buttonRef.value?.left?.roundToInt() ?: 0,
                        y = (buttonRef.value?.bottom?.roundToInt() ?: 0) + 16
                    )
                }
            },
            properties = PopupProperties(),
            onDismissRequest = { makeVisible = false }) {
            // 创建下拉菜单的Column组件
            Column(
                modifier = Modifier
                    .background(
                        color = Color(0xe34632B3),
                        RoundedCornerShape(14.dp)
                    )
                    .clip(
                        RoundedCornerShape(14.dp)
                    )
            ) {
                // 遍历选项列表，创建每个选项的Text组件
                list.forEach {
                    Text(
                        it.toString(), fontSize = 14.sp, lineHeight = 35.sp,
                        modifier = Modifier
                            .width(98.dp)
                            .click {
                                // 点击选项时更新计数值并隐藏下拉菜单
                                countState.value = it
                                makeVisible = false
                            },
                        textAlign = TextAlign.Center,
                        color = Color.White
                    )
                }
            }
        }
    }
}

/**
 * 顶部组件, 已点亮n/n
 * @param lightCnt 点亮数
 * @param totalCnt 总数
 */
@Composable
private fun TotalGiftStatisticWidget(lightCnt: Int, totalCnt: Int) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(top = 12.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Image(
            painter = painterResource(id = R.drawable.line_right_star),
            contentDescription = null,
            modifier = Modifier.width(80.dp),
            contentScale = ContentScale.FillWidth,
        )
        Text(
            text = buildAnnotatedString {
                append(stringResource(R.string.已点亮))
                append(lightCnt.toString())
                append("/")
                withStyle(
                    SpanStyle(
                        color = Color(0x66FFE7CF)
                    )
                ) {
                    append(totalCnt.toString())
                }
            },
            modifier = Modifier.widthIn(min = 100.dp),
            color = Color(0xFFFFE7CF),
            fontSize = 14.sp,
            textAlign = TextAlign.Center
        )
        Image(
            painter = painterResource(id = R.drawable.line_left_star),
            contentDescription = null,
            modifier = Modifier.width(80.dp),
            contentScale = ContentScale.FillWidth,
        )
    }
}

@Composable
private fun GiftWallPage(profileType: Int, list: List<GiftWallItem>, onGiftClicked: (GiftItem) -> Unit = {}) {
    LazyVerticalGrid(
        columns = GridCells.Fixed(8),
        modifier = Modifier.fillMaxWidth(),
        contentPadding = PaddingValues(horizontal = 16.dp)
    ) {
        items(list, span = {
            GridItemSpan(it.span)
        }) {
            when (it) {
                is SpaceItem -> {
                    SpacerItem(it.height)
                }

                TopRadiusItem -> {
                    TopRadiusItem()
                }

                BottomRadiusItem -> {
                    BottomRadiusItem()
                }

                is SpanItem -> {
                    SpanItemWidget(if (it.isStart) 1 else 3)
                }

                is CategoryTitleItem -> {
                    if (it.title.isNotBlank()) {
                        CategoryTitleWidget(buildAnnotatedString {
                            append(it.title)
                            append("(")
                            append(it.lightCount.toString())
                            append("/")
                            append(it.totalCount.toString())
                            append(")")
                        })
                    }
                }

                is GiftItem -> {
                    GiftUIItem(it.gift, it.count, direction = it.direction, modifier = Modifier.click(noEffect = true, onClick = remember(it.gift.id) {
                        {
                            onGiftClicked(it)
                        }
                    }))
                }
            }
        }
        item {
            when (profileType) {
                0 -> SpacerItem(25)
                1 -> SpacerItem(25)
                else -> SpacerItem(111)
            }
        }
    }
}

@Composable
private fun GiftUIItem(gift: Gift, count: Int, modifier: Modifier = Modifier, direction: Int = 0) {
    val color = LocalGiftBackgroundColor.current
    Column(
        modifier = modifier
            .fillMaxWidth()
            .background(color = colorResource(id = color))
            .border(
                if (direction < 0) 0.dp else 0.8.dp,
                color = Color(0xff383331), mode = direction
            )
            .padding(horizontal = 3.dp, vertical = 5.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        val colorFilter = if (count > 0) {
            null
        } else {
            ColorFilter.colorMatrix(ColorMatrix().apply { setToSaturation(0f) })
        }

        val textColor = if (count > 0) {
            Color(0xFFE5C79A)
        } else {
            Color(0xFF5E5E5F)
        }

        ComposeImage(
            model = gift.icon,
            modifier = Modifier.size(56.dp),
            contentScale = ContentScale.Crop,
            colorFilter = colorFilter,
            contentDescription = null,
        )

        Text(
            text = gift.name,
            modifier = Modifier
                .padding(top = 5.dp)
                .height(18.dp),
            color = textColor,
            fontSize = 12.sp,
            maxLines = 1,
        )
        Text(
            text = "x${count}",
            modifier = Modifier
                .padding(top = 2.dp)
                .height(16.dp),
            color = textColor,
            fontSize = 10.sp,
            maxLines = 1,
        )
    }
}

@Composable
private fun TopRadiusItem() {
    val color = LocalGiftBackgroundColor.current
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(12.dp)
            .background(colorResource(id = color), RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
            .border(
                0.8.dp,
                Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xff383331),
                        Color(0xffebd9bc),
                        Color(0xff50483f)
                    )
                ), mode = 2, radius = 12.dp
            )
    )
}

@Composable
private fun BottomRadiusItem() {
    val color = LocalGiftBackgroundColor.current
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(12.dp)
            .background(colorResource(id = color), RoundedCornerShape(bottomStart = 12.dp, bottomEnd = 12.dp))
            .border(
                0.8.dp,
                Brush.horizontalGradient(
                    colors = listOf(
                        Color(0xff383331),
                        Color(0xffebd9bc),
                        Color(0xff50483f)
                    )
                ), mode = 4, radius = 12.dp
            )
    )
}

@Composable
private fun CategoryTitleWidget(title: AnnotatedString) {
    val color = LocalGiftBackgroundColor.current
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .border(
                0.8.dp, color = Color(0xff383331), mode = 5
            )
            .background(colorResource(id = color))
            .padding(vertical = 6.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.Center,
    ) {
        Spacer(
            modifier = Modifier
                .padding(end = 3.dp)
                .size(2.dp)
                .background(
                    color = Color(0xffe3bc78), shape = CircleShape
                )
        )
        Spacer(
            modifier = Modifier
                .padding(end = 4.dp)
                .size(4.dp)
                .background(
                    color = Color(0xffe3bc78), shape = CircleShape
                )
        )
        Text(
            text = title,
            color = Color(0xFFFFE7CF),
            fontSize = 12.sp,
            textAlign = TextAlign.Center,
            lineHeight = 14.sp,
            fontWeight = FontWeight.SemiBold
        )
        Spacer(
            modifier = Modifier
                .padding(start = 4.dp)
                .size(4.dp)
                .background(
                    color = Color(0xffe3bc78), shape = CircleShape
                )
        )
        Spacer(
            modifier = Modifier
                .padding(start = 3.dp)
                .size(2.dp)
                .background(
                    color = Color(0xffe3bc78), shape = CircleShape
                )
        )
    }
}

@Composable
private fun SpacerItem(height: Int) {
    Spacer(
        modifier = Modifier
            .fillMaxWidth()
            .height(height.dp)
    )
}

@Composable
private fun SpanItemWidget(borderDirection: Int = 0) {
    val color = LocalGiftBackgroundColor.current
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(color = colorResource(id = color))
            .border(
                0.8.dp, color = Color(0xff383331), mode = borderDirection
            )
            .padding(horizontal = 3.dp, vertical = 5.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(56.dp))
        Text(
            text = "礼物",
            modifier = Modifier
                .padding(top = 5.dp)
                .height(18.dp),
            color = Color.Transparent,
            fontSize = 12.sp,
            maxLines = 1,
        )
        Text(
            text = "x0",
            modifier = Modifier
                .padding(top = 2.dp)
                .height(16.dp),
            color = Color.Transparent,
            fontSize = 10.sp,
            maxLines = 1,
        )
    }
}
//endregion

@Preview(showBackground = true)
@Composable
fun PreviewGiftWallPage() {
    UCOOPreviewTheme {
        val giftBean = MyGift(
            gift = Gift(t = 0, id = 311, name = "测试礼物"),
            count = Random.nextInt(0, 2)
        )
        val list = mutableListOf<NewCategoryGiftWall>().apply {
            for (j in 0..5) {
                add(NewCategoryGiftWall("测试礼物${j}", buildList {
                    repeat(10) {
                        add(
                            NewCategoryGiftWall.SeriesItem(
                                "合集名称${it}", buildList {
                                    val listSize = Random.nextInt(3, 20)
                                    for (i in 0 until listSize) {
                                        add(GiftItem(giftBean.gift, Random.nextInt(0, 2)))
                                    }
                                }
                            ))
                    }
                }))
            }
        }.map { GiftWallViewModelV2.convertGiftWall(it) }
        GiftWallMainWidget(
            GiftWallSummaryBean(
                tabs = listOf(
                    GiftWallSummaryBean.Tab(t = 0),
                    GiftWallSummaryBean.Tab(t = 1),
                    GiftWallSummaryBean.Tab(t = 2),
                )
            )
        ) {
            GiftWallPage(1, list[it.t].items) {

            }
        }
    }
}

