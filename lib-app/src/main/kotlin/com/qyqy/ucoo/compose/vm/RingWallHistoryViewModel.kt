package com.qyqy.ucoo.compose.vm

import com.qyqy.ucoo.compose.data.RingWallHistory
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.user.UserApi
import kotlinx.serialization.json.decodeFromJsonElement

class RingWallHistoryViewModel() : LiStateViewModel<RingWallHistory>() {
    private var lastItem: RingWallHistory? = null
    private var ringId: Int = 0
    private var userId: Int = 0
    fun updateParams(userId:Int,ringId:Int){
        this.userId = userId
        this.ringId = ringId
        refresh()
    }

    private val api by lazy {
        createApi<UserApi>()
    }

    override fun onStartRequest(isRefresh: Boolean) {
        super.onStartRequest(isRefresh)
        lastItem = if (isRefresh) null else dataState.value.lastOrNull()
    }

    override suspend fun fetch(): List<RingWallHistory> {
        if(ringId == 0 && userId == 0){
            return listOf()
        }
        val reqId = lastItem?.id ?: -1
        return runApiCatching {
            api.getRingWallHistory(userId, ringId, reqId)
        }.getOrNull()?.getOrNull("details")?.let { sAppJson.decodeFromJsonElement<List<RingWallHistory>>(it) } ?: listOf()
    }

}