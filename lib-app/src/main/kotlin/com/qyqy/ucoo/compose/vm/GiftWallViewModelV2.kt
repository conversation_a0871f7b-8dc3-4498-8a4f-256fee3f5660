package com.qyqy.ucoo.compose.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.BottomRadiusItem
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.data.CategoryTitleItem
import com.qyqy.ucoo.compose.data.GiftItem
import com.qyqy.ucoo.compose.data.GiftWallItem
import com.qyqy.ucoo.compose.data.GiftWallSummaryBean
import com.qyqy.ucoo.compose.data.NewCategoryGiftWall
import com.qyqy.ucoo.compose.data.SpaceItem
import com.qyqy.ucoo.compose.data.SpanItem
import com.qyqy.ucoo.compose.data.TopRadiusItem
import com.qyqy.ucoo.core.AppEventManager.list
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.BlindboxGift
import com.qyqy.ucoo.im.bean.MyGift
import com.qyqy.ucoo.im.bean.SeriesGift
import com.qyqy.ucoo.user.gift.GiftApi
import com.qyqy.ucoo.user.gift.GiftRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.flatMapConcat
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.onCompletion
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray
import java.util.concurrent.ConcurrentHashMap

class GiftWallViewModelV2(val sceneType: Int, val sceneId: Int) : ViewModel() {
    private val userId
        get() = sceneId

    private val giftApi by lazy {
        createApi<GiftApi>()
    }

    init {
        viewModelScope.launch {
            GiftRepository.flowSentGiftToUser.collect {
                if (userId == it.first) {
                    updateGiftData(it.second)
                }
            }
        }
    }

    companion object {
        //对应一次性返回所有数据的接口
        fun getDataFromJsonObject(jsonObject: JsonObject): List<NewCategoryGiftWall> {
            val blindboxGiftList = jsonObject["blindbox_gifts"]?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<BlindboxGift>>(it)
            }.orEmpty()

            val normalBlindboxGiftList =
                jsonObject["normal_blindbox_gifts"]?.jsonArray?.let {
                    sAppJson.decodeFromJsonElement<List<BlindboxGift>>(it)
                }.orEmpty()
            val commonGiftList = jsonObject["gift_wall"]?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<MyGift>>(it)
            }.orEmpty()

            return buildList {
                add(NewCategoryGiftWall(app.getString(R.string.盲盒礼物), buildList {
                    addAll(blindboxGiftList.map {
                        NewCategoryGiftWall.SeriesItem(it.seriesName, it.gifts.map { GiftItem(it.gift, it.count) })
                    })
                    addAll(normalBlindboxGiftList.map {
                        NewCategoryGiftWall.SeriesItem(it.seriesName, it.gifts.map { GiftItem(it.gift, it.count) })
                    })
                }))

                add(NewCategoryGiftWall(app.getString(R.string.common_gift), buildList {
                    add(NewCategoryGiftWall.SeriesItem("", commonGiftList.map { GiftItem(it.gift, it.count) }))
                }))
            }
        }

        fun convertGiftWall(bean: NewCategoryGiftWall, columnInt: Int = 4): CategoryGiftWall {
            var clightCount = 0
            var ctotalCount = 0
            val list = buildList {
                bean.items.forEachIndexed { index, it ->
                    add(TopRadiusItem)
                    val listSize = it.items.size

                    val seriesLightCount = it.items.count { it.count > 0 }
                    clightCount += seriesLightCount
                    ctotalCount += listSize
                    add(CategoryTitleItem(it.seriesName, seriesLightCount, listSize, index))

                    val tailSize = listSize % columnInt

                    it.items.forEachIndexed { i, gift ->
                        val direction = if (i in Math.max(0, listSize - tailSize) until listSize) -1 else if (i % columnInt == 0) 1 else if (i % columnInt == (columnInt - 1)) 3 else -1
                        add(gift.copy(direction = direction, seriesId = index))
                    }

                    val lastLineCount = it.items.size.rem(columnInt)
                    if (lastLineCount != 0) {
                        val leftSpan = columnInt.minus(lastLineCount)
                        add(size.minus(lastLineCount), SpanItem(leftSpan, isStart = true))
                        add(SpanItem(leftSpan))
                    }
                    add(BottomRadiusItem)
                    add(SpaceItem(16))
                }
            }
            return CategoryGiftWall(bean.category, list, clightCount, ctotalCount)
        }

        private fun isHeadOrTail(idx: Int, size: Int, columnInt: Int) {

        }

        //对应带tab的数据接口
        fun convertGiftWall(bean: List<SeriesGift>, columnInt: Int = 4): List<GiftWallItem> {
            val list = buildList {
                bean.forEachIndexed { index, it ->
                    add(TopRadiusItem)
                    val listSize = it.gifts.size
                    var seriesId = index
                    if (it.seriesName.isNotBlank()) {
                        add(CategoryTitleItem(it.seriesName, it.starCount, it.totalCount, seriesId))
                    } else {
                        seriesId = -1
                    }

                    val tailSize = listSize % columnInt

                    it.gifts.forEachIndexed { i, gift ->
                        val direction = if (i in Math.max(0, listSize - tailSize) until listSize) -1 else if (i % columnInt == 0) 1 else if (i % columnInt == (columnInt - 1)) 3 else -1
                        add(GiftItem(gift.gift, gift.count, direction, seriesId))
                    }
                    val lastLineCount = it.gifts.size.rem(columnInt)
                    if (lastLineCount != 0) {
                        val leftSpan = columnInt.minus(lastLineCount)
                        add(size.minus(lastLineCount), SpanItem(leftSpan, isStart = true))
                        add(SpanItem(leftSpan))
                    }
                    add(BottomRadiusItem)
                    add(SpaceItem(16))
                }
            }
            return list
        }

        //使用新版本
        const val useVersion2 = true
    }

    fun refresh() {
        if (useVersion2) {
            loadWallSummary()
        } else {
            loadWallAllData()
        }
    }

    private val _giftWallSummary = MutableStateFlow(GiftWallSummaryBean())
    val giftWallSummary = _giftWallSummary.asStateFlow()

    //region 旧版本, 数据一次性全部加载
    private fun loadWallAllData() {
        viewModelScope.launch {
            runApiCatching {
                giftApi.getUserGiftWall(userId.toString())
            }.onSuccess {
                withContext(Dispatchers.IO) {
                    val walls = getDataFromJsonObject(it)
                    val result = walls.map { convertGiftWall(it) }
                    _giftWallSummary.emit(
                        GiftWallSummaryBean(
                            starCnt = result.sumOf { it.lightCount },
                            totalCnt = result.sumOf { it.totalCount },
                            tabs = result.mapIndexed { index, it ->
                                val b = GiftWallSummaryBean.Tab(it.category, it.lightCount, index, it.totalCount)
                                _summaryTabData.put(index, MutableStateFlow(State.LoadSucceedState(it.items)))
                                b
                            }
                        ))
                }
            }.toastError()
        }
    }
    //endregion

    //region 新版本, 数据分类别加载
    /**
     * 加载礼物墙总览分类
     *
     */
    private fun loadWallSummary() {
        viewModelScope.launch {
            runApiCatching {
                giftApi.getWallSummaryInfo(userId, sceneType, sceneId.toString())
            }.onSuccess {
                _giftWallSummary.emit(it)
                _summaryTabData.forEach {
                    it.value.value = State.InitialState
                }
            }.onFailure {

            }.toastError()
        }
    }

    private val _summaryTabData = ConcurrentHashMap<Int, MutableStateFlow<State>>()
    private val summaryTabData = ConcurrentHashMap<Int, StateFlow<State>>()

    fun getState(summaryTab: GiftWallSummaryBean.Tab): StateFlow<State> {
        synchronized(summaryTab) {
            val stateFlow = _summaryTabData.getOrPut(summaryTab.t) {
                MutableStateFlow(State.InitialState)
            }

            val finalResult = summaryTabData.getOrPut(summaryTab.t) {
                stateFlow.asStateFlow()
            }

            if (finalResult.value == State.InitialState) {
                stateFlow.value = (State.LoadingState)
                viewModelScope.launch {
                    runApiCatching {
                        giftApi.getWallTabInfo(sceneId, summaryTab.t, sceneType, sceneId.toString())
                    }.onSuccess {
                        val list = withContext(Dispatchers.IO) {
                            when (it.getIntOrNull("ui_t")) {
                                1 -> {//普通礼物,取gift
                                    it.getOrNull("gifts")?.jsonArray?.let {
                                        val list = sAppJson.decodeFromJsonElement<List<MyGift>>(it)
                                        convertGiftWall(listOf(SeriesGift(gifts = list)))
                                    } ?: emptyList()
                                }

                                2 -> {//盲盒礼物,取series
                                    it.getOrNull("series")?.jsonArray?.let {
                                        val list = sAppJson.decodeFromJsonElement<List<SeriesGift>>(it)
                                        convertGiftWall(list)
                                    } ?: emptyList()
                                }

                                else -> {
                                    emptyList()
                                }
                            }
                        }
                        stateFlow.emit(State.LoadSucceedState(list))
                    }.onFailure {
                        stateFlow.emit(State.LoadFailed)
                    }
                }
            }

            return finalResult
        }
    }
    //endregion

    private fun updateGiftData(sentGiftIds: List<Int>) {
        viewModelScope.launch {
            withContext(Dispatchers.Default) {
                //总数新增
                val sentGifts = sentGiftIds.groupBy { it }.map { it.key to it.value.size }.toMap()
                val sentUniqeIds = sentGiftIds.distinct().toMutableSet()//发送的礼物id, 去重

                //顶部统计数据
                var summaryBean = _giftWallSummary.value.copy()

                _summaryTabData.forEach {
                    val tabId = it.key //tab id
                    val state = it.value// MutableState
                    val stateValue = it.value.value //具体的state数据

                    //新的数据列表

                    if (stateValue is State.LoadSucceedState) {//如果是已加载的state才有数据
                        val operationList: MutableList<GiftWallItem> = stateValue.list.toMutableList()
                        var needEmit = false
                        while (sentUniqeIds.isNotEmpty()) {
                            val giftItem = operationList.find { it is GiftItem && it.gift.id in sentUniqeIds } as? GiftItem
                            //如果找到了这个礼物
                            if (giftItem != null) {
                                needEmit = true
                                //礼物pos
                                val itemPos = operationList.indexOf(giftItem)

                                val needReCount = giftItem.count == 0//如果之前是空的则需要对系列进行重新计算

                                //将赠送出去的数量加上
                                operationList.set(itemPos, giftItem.updateLightCount(sentGifts[giftItem.gift.id] ?: 0))

                                if (needReCount) {
                                    if (giftItem.seriesId != -1) {//说明有系列, 还要把系列里的数量加上
                                        (operationList.find { it is CategoryTitleItem && it.seriesId == giftItem.seriesId } as? CategoryTitleItem)?.let {
                                            val seriesPos = operationList.indexOf(it)
                                            operationList.set(seriesPos, it.updateLightCount(1))
                                        }
                                    }
                                    //最顶部的tab需要更新
                                    summaryBean = summaryBean.updateTabCount(tabId, 1)
                                }

                                sentUniqeIds.remove(giftItem.gift.id)
                            } else {
                                break
                            }
                        }
                        if (needEmit) {
                            withContext(Dispatchers.Main) {
                                state.emit(State.LoadSucceedState(operationList))
                            }
                        }
                    }
                }
                withContext(Dispatchers.Main) {
                    _giftWallSummary.emit(summaryBean)
                }
            }
        }
    }

    sealed interface State {
        data object InitialState : State
        data object LoadingState : State
        data object LoadFailed : State
        data class LoadSucceedState(val list: List<GiftWallItem>) : State
    }
}