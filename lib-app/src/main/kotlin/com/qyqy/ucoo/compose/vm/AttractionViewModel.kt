package com.qyqy.ucoo.compose.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.UserApi
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

class AttractionViewModel(private val gender: Int = sUser.gender) : ViewModel() {
    private val _attractiveTags = MutableStateFlow(listOf<AttractiveFlag>())
    val attractiveTags = _attractiveTags.asStateFlow()
    val userApi = createApi(UserApi::class.java)

    init {
        refreshList()
    }

    fun refreshList() {
        viewModelScope.launch {
            runApiCatching {
                userApi.getAttractiveTagListV2(gender)
            }.onSuccess {
                _attractiveTags.value = it.getOrNull("tags")?.jsonArray?.let {
                    sAppJson.decodeFromJsonElement<List<AttractiveFlag>>(it)
                } ?: listOf()
            }
        }
    }

    suspend fun updateTags(tags: List<AttractiveFlag>) = runApiCatching {
        userApi.updateAttractiveTagList(mapOf("tag_ids" to tags.joinToString(",") { it.id.toString() }))
    }.onSuccess {
        toastRes(R.string.modify_success)
        app.accountManager.updateSelfUser {
            attractiveFlags = tags
        }
    }.toastError()
}