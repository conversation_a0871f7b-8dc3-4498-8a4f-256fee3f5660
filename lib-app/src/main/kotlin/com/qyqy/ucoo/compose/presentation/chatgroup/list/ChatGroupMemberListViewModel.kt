package com.qyqy.ucoo.compose.presentation.chatgroup.list

import com.qyqy.ucoo.compose.presentation.chatgroup.ChatGroupApi
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupMember
import com.qyqy.ucoo.compose.state.StateViewModelWithIntPage
import com.qyqy.ucoo.http.ApiException
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.toast
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.intOrNull
import kotlinx.serialization.json.jsonArray
import kotlinx.serialization.json.jsonPrimitive

/**
 *  @time 2024/6/24
 *  <AUTHOR>
 *  @package com.qyqy.ucoo.compose.presentation.chatgroup.list
 *  mode:
 *  0 = 所有成员
 *  1 = 仅管理员
 *  2 = 仅普通成员
 */

/**
 * 群组成员列表Viewmodel, 此类会包含所有role类型的成员
 *
 * @property chatGroupId 群组id
 */
open class ChatGroupMemberListViewModel(var chatGroupId: Int = -1) : StateViewModelWithIntPage<ChatGroupMember>() {
    protected val api = createApi<ChatGroupApi>()
    private var lastId = -1

    override suspend fun loadData(pageNum: Int): Result<List<ChatGroupMember>> {
        val isFirstPage = pageNum == firstPage
        val id = if (isFirstPage) 0 else lastId
        if (pageNum != firstPage && lastId == 0) {
            return Result.success(emptyList())
        }

        val result = runApiCatching { api.getGroupMemberList(chatGroupId, id) }
        return if (result.isSuccess) {
            val jsonObject = result.getOrThrow()

            val admins = jsonObject["admins"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<ChatGroupMember>>(it) } ?: listOf()
            val owner = jsonObject["owner"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<ChatGroupMember>>(it) } ?: listOf()
            val members = jsonObject["members"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<ChatGroupMember>>(it) } ?: listOf()
            lastId = (members.lastOrNull())?.member_id ?: 0
            Result.success(
                buildList {
                    /**
                     * 很奇怪,只有第一页拿到的群主和管理员是有效的, 之后每一次请求接口都会返回一样的数据但是得忽略
                     * 因为这个接口返回的是一个三种数据的集合体(或者三种类型的数据需要合并在一个列表中?)
                     */
                    if (filterData(ChatGroup.Role.OWNER) && isFirstPage) {
                        addAll(owner)
                    }
                    if (filterData(ChatGroup.Role.ADMIN) && isFirstPage) {
                        addAll(admins)
                    }
                    if (filterData(ChatGroup.Role.MEMBER)) {
                        addAll(members)
                    }
                }
            )
        } else {
            Result.failure(result.exceptionOrNull() ?: ApiException(msg = "error"))
        }
    }

    protected open fun filterData(role: Int): Boolean {
        return true
    }

    fun removeUser(memberId: Int) {
        filterItems { it.member_id != memberId }
    }

    override val emptyMessage: String = "暂无更多成员"
}

/**
 * 群组成员列表Viewmodel, 此类仅包含管理员成员
 *
 * @property chatGroupId 群组id
 */
class ChatGroupAdminListViewModel(id: Int) : ChatGroupMemberListViewModel(id) {
    override suspend fun loadData(pageNum: Int): Result<List<ChatGroupMember>> {
        if (pageNum != firstPage) {
            return Result.success(emptyList())
        }

        val result = runApiCatching { api.getManagerList(chatGroupId) }
        return if (result.isSuccess) {
            val jsonObject = result.getOrThrow()
            val admins = jsonObject["admins"]?.jsonArray?.let { sAppJson.decodeFromJsonElement<List<ChatGroupMember>>(it) } ?: listOf()
            Result.success(admins)
        } else {
            Result.failure(result.exceptionOrNull() ?: ApiException(msg = "error"))
        }
    }

    override val emptyMessage: String = "暂无管理员"
}

/**
 * 群组成员列表Viewmodel, 此类仅包含普通成员
 *
 * @property chatGroupId 群组id
 */
class ChatGroupOnlyMemberListViewModel(chatGroupId: Int) : ChatGroupMemberListViewModel(chatGroupId) {
    override fun filterData(role: Int): Boolean {
        return role == ChatGroup.Role.MEMBER
    }

    override val emptyMessage: String = "暂无更多成员"
}

/**
 * 群组成员列表Viewmodel, 此类仅包含普通成员和管理员
 *
 * @property chatGroupId 群组id
 */
class ChatGroupExceptOwnerListViewModel(chatGroupId: Int) : ChatGroupMemberListViewModel(chatGroupId) {
    override fun filterData(role: Int): Boolean {
        return role == ChatGroup.Role.MEMBER || role == ChatGroup.Role.ADMIN
    }

    override val emptyMessage: String = "暂无更多成员"
}

