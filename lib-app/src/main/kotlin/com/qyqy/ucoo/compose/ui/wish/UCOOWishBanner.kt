

package com.qyqy.ucoo.compose.ui.wish

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.im.panel.gift.GiftPosition
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.IVoiceLiveAction
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.profile.wish.RoomUserWishListModel
import com.qyqy.cupid.ui.profile.wish.RoomWishViewModel
import com.qyqy.cupid.ui.profile.wish.WishPage
import com.qyqy.cupid.ui.profile.wish.rememberRoomWishViewModel
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.room.IRoomDialogAction
import com.qyqy.ucoo.sAppKV
import kotlinx.coroutines.delay

@Composable
fun UCOORoomWishFloatWidget(
    roomId: Int,
    dialogQueue: DialogQueue<*>,
    modifier: Modifier = Modifier,
) {
    val wishViewModel = rememberRoomWishViewModel(roomId = roomId)
    val items by remember {
        derivedStateOf {
            wishViewModel.userWishEntries.filter { it.list.isNullOrEmpty().not() }
        }
    }

    var showGuide by remember {
        mutableStateOf(sAppKV.getBoolean("show_room_wish_guide_for_ucoo", true))
    }

    var visible by rememberSaveable {
        mutableStateOf(true)
    }

    if(!visible){
        return
    }

    Box(modifier = modifier) {
        RoomWishFloatWidgetContent(items) {
            // 打开心愿单弹窗
            showGuide = false
            wishViewModel.navToPage(WishPage.Home)
            dialogQueue.push { dialog, onAction ->
                WishRoomHostPage(
                    wishViewModel = wishViewModel,
                    dialogQueue = dialogQueue,
                    onSendGift = { user, gift ->
                        dialog.dismiss()
                        (onAction as? IRoomDialogAction)?.showUserInfoPanel(user, gift.rawGiftId)
                    }
                ) {
                    dialog.dismiss()
                    (onAction as? IRoomDialogAction)?.showUserInfoPanel(it)
                }
            }
        }

        if (showGuide) {
            val transition = rememberInfiniteTransition(label = "active_anim")
            val fraction by transition.animateFloat(
                initialValue = 0f, targetValue = 30f, animationSpec = infiniteRepeatable(
                    tween(200, easing = LinearEasing), repeatMode = RepeatMode.Reverse
                ), label = "background"
            )

            LaunchOnceEffect {
                sAppKV.putBoolean("show_room_wish_guide_for_ucoo", false)
            }

            Box(
                modifier = Modifier
                    .matchParentSize()
                    .background(Color(0x66000000), Shapes.corner12)
            ) {
                ComposeImage(
                    model = R.drawable.cpd_winner_raise_handle,
                    modifier = Modifier
                        .padding(start = 32.dp, top = 28.dp)
                        .rotate(fraction)
                        .size(28.dp)
                )

                Image(
                    painter = painterResource(id = R.drawable.ic_room_wish_guide_text),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(start = 4.dp, top = 60.dp)
                        .size(60.dp, 20.dp)
                )
            }
        } else {
            Image(
                painter = painterResource(id = R.drawable.ic_cpd_close_small),
                contentDescription = "close",
                modifier = Modifier
                    .padding(top = 2.dp, end = 2.dp)
                    .align(Alignment.TopEnd)
                    .size(15.dp)
                    .click(onClick = {
                        visible = false
                    })
            )
        }
    }
}


@Composable
private fun RoomWishFloatWidgetContent(
    items: List<RoomUserWishListModel>,
    onClick: (model: RoomUserWishListModel?) -> Unit = { _ -> },
) {
    val isEmpty = items.isEmpty()
    val pagerState = rememberPagerState {
        items.size
    }
    if (pagerState.pageCount > 1) {
        LaunchedEffect(key1 = items.size, pagerState.settledPage) {
            delay(5000L)
            val nextPage = pagerState.settledPage.plus(1).rem(pagerState.pageCount)
            if (nextPage == 0) {
                pagerState.scrollToPage(0)
            } else {
                pagerState.animateScrollToPage(nextPage)
            }
        }
    }
    Column(
        modifier = Modifier
            .size(68.dp, 90.dp)
            .paint(
                painter = painterResource(id = R.drawable.ic_room_wish_background),
                contentScale = ContentScale.FillBounds
            )
            .click {
                onClick(items.getOrNull(pagerState.settledPage))
            },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (isEmpty) {
            Text(
                text = stringResource(id = R.string.暂无心愿),
                modifier = Modifier.padding(top = 45.dp, start = 3.dp, end = 3.dp),
                color = Color(0xFFF4EEFF),
                fontSize = 12.sp,
                fontWeight = FontWeight.SemiBold,
                textAlign = TextAlign.Center
            )
        } else {
            HorizontalPager(state = pagerState) { index ->
                val user = items[index].user!!
                val wishEntry = items[index].list!!.first()
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(top = 34.dp),
                    contentAlignment = Alignment.TopCenter
                ) {
                    Column(
                        modifier = Modifier
                            .padding(top = 4.dp)
                            .size(48.dp)
                            .background(
                                brush = Brush.verticalGradient(listOf(Color(0xFFFF9CE9), Color.White)),
                                shape = RoundedCornerShape(4.dp)
                            )
                            .border(0.5.dp, colorResource(id = R.color.white_alpha_50), RoundedCornerShape(4.dp))
                            .padding(top = 4.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
                        ComposeImage(model = wishEntry.gift.icon, modifier = Modifier.size(28.dp))
                        AutoSizeText(
                            "${wishEntry.process}/${wishEntry.count}",
                            color = Color(0xFFFF2AD0),
                            maxLines = 1,
                            fontSize = 10.sp,
                            lineHeight = 10.sp,
                            modifier = Modifier.padding(top = 2.dp)
                        )
                    }

                    CircleComposeImage(
                        model = user.avatarUrl,
                        modifier = Modifier
                            .padding(start = 6.dp)
                            .align(Alignment.TopStart)
                            .size(18.dp)
                    )
                }
            }
        }
    }
}

@Composable
@Preview
private fun RoomWishFloatWidgetContentPreview() {
    PreviewCupidTheme {
        RoomWishFloatWidgetContent(RoomWishViewModel.Preview.userWishEntries.filter { it.list.isNullOrEmpty().not() })
    }
}