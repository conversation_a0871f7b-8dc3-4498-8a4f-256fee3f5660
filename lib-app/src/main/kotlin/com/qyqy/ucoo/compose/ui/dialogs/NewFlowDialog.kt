package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.runWithTopActivity
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

fun showNewFlowRecommendDialog(user: User) {
    runWithTopActivity {
        showComposeDialog(this) { d ->
            NewFlowRecommendPanel(user = user, onTimeout = { d.dismiss() }) {
                d.dismiss()
                if (this is ChatRoomActivity) {
                    this.showChatFragment(user)
                } else {
                    startActivity(ChatActivity.createIntent(this, user))
                }
            }
        }
    }
}

//新用户流量推荐

@Composable
fun NewFlowRecommendPanel(user: User, onTimeout: OnClick = {}, onClick: OnClick = {}) {
    val scope = rememberCoroutineScope()
    var second by remember {
        mutableIntStateOf(60)
    }
    LaunchedEffect(key1 = user.id) {
        scope.launch {
            while (second > 0) {
                delay(1000)
                second -= 1
                if (second <= 0) {
                    onTimeout.invoke()
                }
            }
        }
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color(0xFF2B023B), Shapes.small),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Image(
            modifier = Modifier.fillMaxWidth(),
            painter = painterResource(id = R.drawable.header_new_flow),
            contentDescription = "header",
            contentScale = ContentScale.FillWidth
        )
        Spacer(modifier = Modifier.height(16.dp))
        CircleComposeImage(model = user.avatarUrl, Modifier.size(80.dp))
        Spacer(modifier = Modifier.height(16.dp))
        AppText(text = user.nickname, fontSize = 16.sp, color = Color.White)
        Spacer(modifier = Modifier.height(8.dp))
        Row(verticalAlignment = Alignment.CenterVertically) {
            AgeGender(age = user.age, isBoy = user.isBoy)
            if (user.regionLabel.isNotEmpty()) {
                Spacer(modifier = Modifier.width(8.dp))
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .background(Color(0x26FFFFFF), Shapes.chip)
                        .padding(4.dp)
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.ic_moment_location),
                        contentDescription = "loc",
                        modifier = Modifier.size(12.dp)
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    AppText(text = user.regionLabel, fontSize = 12.sp, color = Color.White.copy(0.8f))
                }
            }
        }
        Spacer(modifier = Modifier.height(20.dp))
        Box(
            modifier = Modifier
                .widthIn(min = 195.dp)
                .height(40.dp)
                .click(onClick = onClick)
                .background(Brush.horizontalGradient(listOf(Color(0xFF945EFF), Color(0xFFFF58F8))), Shapes.chip),
            contentAlignment = Alignment.Center
        ) {
            AppText(text = stringResource(id = R.string.去接待, second), fontSize = 16.sp, color = Color.White)
        }
        Spacer(modifier = Modifier.height(20.dp))
    }
}

@Preview
@Composable
private fun Preview() {
    Box(modifier = Modifier.width(280.dp)) {
        NewFlowRecommendPanel(user = userForPreview.also { it.regionLabel = "台湾 新竹" })
    }
}