package com.qyqy.ucoo.compose.ui

import android.graphics.drawable.Animatable
import android.graphics.drawable.Drawable
import android.os.Handler
import android.os.Looper
import android.view.View
import androidx.annotation.DrawableRes
import androidx.appcompat.widget.AppCompatImageView
import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.RememberObserver
import androidx.compose.runtime.State
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.runtime.snapshotFlow
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.DefaultAlpha
import androidx.compose.ui.graphics.asAndroidColorFilter
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.graphics.withSave
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.ScaleFactor
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.viewinterop.AndroidView
import androidx.vectordrawable.graphics.drawable.Animatable2Compat
import com.bumptech.glide.Glide
import com.bumptech.glide.integration.compose.GlideImage
import com.bumptech.glide.integration.compose.GlideSubcomposition
import com.bumptech.glide.integration.compose.Placeholder
import com.bumptech.glide.integration.compose.RequestBuilderTransform
import com.bumptech.glide.integration.compose.RequestState
import com.bumptech.glide.integration.compose.Transition
import com.bumptech.glide.integration.compose.placeholder
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.github.penfeizhou.animation.FrameAnimationDrawable
import com.github.penfeizhou.animation.loader.ResourceStreamLoader
import com.github.penfeizhou.animation.webp.WebPDrawable
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.composePlaceholderDrawable
import com.qyqy.ucoo.compose.isEditOnCompose
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlin.math.roundToInt

object ComposeContentScale {

    /**
     * 发现直接使用[ContentScale.Crop]裁剪，可能在某些情况下会出错，委托代理下就好了
     */
    val ProxyCrop = object : ContentScale {
        override fun computeScaleFactor(srcSize: Size, dstSize: Size): ScaleFactor {
            return ContentScale.Crop.computeScaleFactor(srcSize, dstSize)
        }
    }

}

private fun isLocalResource(model: Any?) = model is Int || model is Painter

@Composable
fun ComposeImage(
    model: Any?,
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ComposeContentScale.ProxyCrop,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    loading: Placeholder? = placeholder(composePlaceholderDrawable()),
    failure: Placeholder? = loading,
    preview: Painter? = null,
    transition: Transition.Factory? = null,
    contentDescription: String? = null,
    forceThird:Boolean = false,
    requestBuilderTransform: RequestBuilderTransform<Drawable> = { it }
) {
    if (isEditOnCompose || (!forceThird && isLocalResource(model))) {
        Image(
            painter = when (model) {
                is Int -> painterResource(model)
                is Painter -> model
                else -> preview ?: rememberDrawablePainter(composePlaceholderDrawable())
            },
            contentDescription = contentDescription,
            modifier = modifier,
            alignment = alignment,
            contentScale = contentScale,
            alpha = alpha,
            colorFilter = colorFilter,
        )
        return
    }
    GlideImage(
        model = model,
        contentDescription = contentDescription,
        modifier = modifier,
        alignment = alignment,
        contentScale = contentScale,
        alpha = alpha,
        colorFilter = colorFilter,
        loading = loading,
        failure = failure,
        transition = transition,
        requestBuilderTransform = requestBuilderTransform,
    )
}


@Composable
fun CircleComposeImage(
    model: Any?,
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ComposeContentScale.ProxyCrop,
    borderStroke: BorderStroke? = null,
    alpha: Float = DefaultAlpha,
    colorFilter: ColorFilter? = null,
    loading: Placeholder? = placeholder(R.drawable.ic_default_male_unselected),
    failure: Placeholder? = loading,
    preview: Painter? = painterResource(id = R.drawable.ic_default_male_selected),
    transition: Transition.Factory? = null,
    contentDescription: String? = null,
    requestBuilderTransform: RequestBuilderTransform<Drawable> = { it },
) {
    ComposeImage(
        model = model,
        modifier = modifier
            .run {
                if (borderStroke != null) {
                    border(borderStroke, CircleShape)
                } else {
                    this
                }
            }
            .clip(CircleShape),
        alignment = alignment,
        contentScale = contentScale,
        alpha = alpha,
        colorFilter = colorFilter,
        loading = loading,
        failure = failure,
        preview = preview,
        transition = transition,
        contentDescription = contentDescription,
        requestBuilderTransform = requestBuilderTransform,
    )
}

@Composable
fun AnimatedComposeImage(
    model: Any?,
    modifier: Modifier = Modifier,
    alignment: Alignment = Alignment.Center,
    contentScale: ContentScale = ComposeContentScale.ProxyCrop,
    preview: Painter? = null,
    isPlayingState: State<Boolean> = remember {
        mutableStateOf(true)
    },
    requestBuilderTransform: RequestBuilderTransform<Drawable> = { it },
) {
    Box(
        modifier = modifier,
        contentAlignment = alignment
    ) {
        if (LocalInspectionMode.current && preview != null) {
            Image(
                painter = preview,
                contentDescription = null,
                modifier = modifier,
                alignment = alignment,
                contentScale = contentScale,
            )
        } else {

            var animatable by remember {
                mutableStateOf<Animatable?>(null)
            }

            if (animatable != null) {
                LaunchedEffect(isPlayingState) {
                    snapshotFlow {
                        animatable?.let { it to isPlayingState.value }
                    }.onEach {
                        it ?: return@onEach
                        if (it.second) {
                            it.first.start()
                        } else {
                            it.first.stop()
                        }
                    }.launchIn(this)
                }
            }

            AndroidView(
                factory = {
                    AppCompatImageView(it)
                },
                modifier = Modifier.fillMaxSize(),
                update = {
                    requestBuilderTransform(Glide.with(it).load(model).run {
                        when (contentScale) {
                            ContentScale.Crop -> {
                                optionalCenterCrop()
                            }

                            ContentScale.Inside,
                            ContentScale.Fit,
                            -> {
                                optionalCenterInside()
                            }

                            else -> {
                                optionalFitCenter()
                            }
                        }
                    }.addListener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(e: GlideException?, model: Any?, target: Target<Drawable>, isFirstResource: Boolean): Boolean {
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>?,
                            dataSource: DataSource,
                            isFirstResource: Boolean,
                        ): Boolean {
                            it.setImageDrawable(resource)
                            animatable = resource as? Animatable
                            if (isPlayingState.value) {
                                animatable?.start()
                            } else {
                                animatable?.stop()
                            }
                            return true
                        }
                    })).into(it)
                }
            )
        }
    }
}



@Composable
fun FillComposeImage(
    model: Any?,
    modifier: Modifier = Modifier,
    fillHeight: Boolean = true,
) {
    var aspectRatio by rememberSaveable {
        mutableFloatStateOf(-1f)
    }

    val density = LocalDensity.current

    GlideSubcomposition(
        model = model,
        modifier = Modifier
            .animateContentSize()
            .then(modifier)
            .run {
                if (aspectRatio == -1f) {
                    with(density) {
                        if (fillHeight) {
                            width(1.toDp())
                        } else {
                            height(1.toDp())
                        }
                    }
                } else {
                    aspectRatio(aspectRatio)
                }
            }
    ) {
        if (state is RequestState.Success) {
            aspectRatio = painter.intrinsicSize.let {
                it.width.div(it.height)
            }
            Image(
                painter = painter,
                contentDescription = null,
                modifier = Modifier.fillMaxSize(),
                contentScale = if (fillHeight) ContentScale.FillHeight else ContentScale.FillWidth
            )
        } else if (state is RequestState.Failure) {
            aspectRatio = 0.01f
        }
    }
}


private val MAIN_HANDLER by lazy(LazyThreadSafetyMode.NONE) {
    Handler(Looper.getMainLooper())
}

/**
 * A [Painter] which draws an Android [Drawable] and supports [Animatable] drawables. Instances
 * should be remembered to be able to start and stop [Animatable] animations.
 *
 * Instances are usually retrieved from [rememberDrawablePainter].
 */
class AnimatedDrawablePainter<D>(
    val drawable: D,
    autoPlay: Boolean = false,
) : Painter(), RememberObserver where D : Drawable, D : Animatable {

    private var drawInvalidateTick by mutableIntStateOf(0)

    private var drawableIntrinsicSize by mutableStateOf(drawable.intrinsicSize)

    var isRunning = autoPlay
        private set

    private var animateEnable = false

    private val callback: Drawable.Callback by lazy {
        object : Drawable.Callback {
            override fun invalidateDrawable(d: Drawable) {
                // Update the tick so that we get re-drawn
                drawInvalidateTick++
                // Update our intrinsic size too
                drawableIntrinsicSize = drawable.intrinsicSize
            }

            override fun scheduleDrawable(d: Drawable, what: Runnable, time: Long) {
                MAIN_HANDLER.postAtTime(what, time)
            }

            override fun unscheduleDrawable(d: Drawable, what: Runnable) {
                MAIN_HANDLER.removeCallbacks(what)
            }
        }
    }

    init {
        if (drawable.intrinsicWidth >= 0 && drawable.intrinsicHeight >= 0) {
            // Update the drawable's bounds to match the intrinsic size
            drawable.setBounds(0, 0, drawable.intrinsicWidth, drawable.intrinsicHeight)
        }
    }

    fun start() {
        isRunning = true
        if (animateEnable) {
            drawable.start()
        }
    }

    fun stop(maybeReset: Boolean = false) {
        isRunning = false
        if (animateEnable) {
            if (maybeReset && drawable is FrameAnimationDrawable<*>) {
                // 没有直接reset到第一帧的方法，所以使用曲线救国
                drawable.start()
                drawable.registerAnimationCallback(object : Animatable2Compat.AnimationCallback() {
                    override fun onAnimationStart(d: Drawable) {
                        drawable.unregisterAnimationCallback(this)
                        drawable.pause()
                    }
                })
            } else {
                drawable.stop()
            }
        }
    }

    override fun onRemembered() {
        animateEnable = true
        drawable.callback = callback
        drawable.setVisible(true, true)
        if (isRunning) drawable.start()
    }

    override fun onAbandoned(): Unit = onForgotten()

    override fun onForgotten() {
        animateEnable = false
        drawable.stop()
        drawable.setVisible(false, false)
        drawable.callback = null
    }

    override fun applyAlpha(alpha: Float): Boolean {
        drawable.alpha = (alpha * 255).roundToInt().coerceIn(0, 255)
        return true
    }

    override fun applyColorFilter(colorFilter: ColorFilter?): Boolean {
        drawable.colorFilter = colorFilter?.asAndroidColorFilter()
        return true
    }

    override fun applyLayoutDirection(layoutDirection: LayoutDirection): Boolean {
        return drawable.setLayoutDirection(
            when (layoutDirection) {
                LayoutDirection.Ltr -> View.LAYOUT_DIRECTION_LTR
                LayoutDirection.Rtl -> View.LAYOUT_DIRECTION_RTL
            }
        )
    }

    override val intrinsicSize: Size get() = drawableIntrinsicSize

    override fun DrawScope.onDraw() {
        drawIntoCanvas { canvas ->
            // Reading this ensures that we invalidate when invalidateDrawable() is called
            drawInvalidateTick

            // Update the Drawable's bounds
            drawable.setBounds(0, 0, size.width.roundToInt(), size.height.roundToInt())

            canvas.withSave {
                drawable.draw(canvas.nativeCanvas)
            }
        }
    }
}

@Composable
fun <D> rememberAnimatedDrawablePainter(
    drawable: Drawable,
    isPlayingState: State<Boolean>,
    maybeReset: Boolean = false,
): AnimatedDrawablePainter<D> where D : Drawable, D : Animatable {
    val painter = remember(drawable) {
        AnimatedDrawablePainter(
            drawable = drawable.mutate() as D,
            autoPlay = isPlayingState.value
        )
    }
    LaunchedEffect(painter, isPlayingState) {
        snapshotFlow {
            isPlayingState.value
        }.onEach {
            if (it) {
                painter.start()
            } else {
                painter.stop(maybeReset)
            }
        }.launchIn(this)
    }
    return painter
}


@Composable
fun rememberAnimatedDrawablePainter(
    @DrawableRes resId: Int,
    isPlayingState: State<Boolean>,
    maybeReset: Boolean = false,
): AnimatedDrawablePainter<WebPDrawable> {
    val context = LocalContext.current
    val drawable = remember(context, resId) {
        WebPDrawable(ResourceStreamLoader(context, resId))
    }
    return rememberAnimatedDrawablePainter(drawable, isPlayingState, maybeReset)
}

private val Drawable.intrinsicSize: Size
    get() = when {
        // Only return a finite size if the drawable has an intrinsic size
        intrinsicWidth >= 0 && intrinsicHeight >= 0 -> {
            Size(width = intrinsicWidth.toFloat(), height = intrinsicHeight.toFloat())
        }

        else -> Size.Unspecified
    }