package com.qyqy.ucoo.compose.theme

import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color


val colorTheme = Color(0xFF945EFF)
val colorPageBackground = Color(0XFF1C1D1E)
val colorFemaleBackground = Color(0xFFFF7EC6)
val colorMaleBackground = Color(0xFF5AB0FF)
val colorCard = Color(0xFF2C2C2C)
val colorSecondBlack = Color(0xFF222222)
val colorBlackDivider = Color(0xFF333333)
val colorOrigin = Color(0xFFFF7D00)

val colorWhite30Alpha = Color(0x4DFFFFFF)
val colorWhite50Alpha = Color(0x80FFFFFF)
val colorWhite80Alpha = Color(0xCCFFFFFF)

val brushBlueToRed = Brush.horizontalGradient(listOf(Color(0xFFAC64F5), Color(0XFFFC56E1)))

val brushThemeButton = Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC)))

val colorHouseBlue = Color(0xFF2EABF2)

val colorDarkScreenBackground = Color(0xFF1C1D1E)

val colorRed = Color(0xFFFF385C)