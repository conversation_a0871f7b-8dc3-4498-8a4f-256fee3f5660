package com.qyqy.ucoo.compose.presentation.room

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.compose.data.PkAvailableRoomInfo
import com.qyqy.ucoo.compose.data.PkRoomBasicInfo
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.LoadResult
import com.qyqy.ucoo.compose.ui.VoiceWaves
import com.qyqy.ucoo.compose.ui.rememberPaginateState
import com.qyqy.ucoo.compose.vm.room.cross.CrossRoomPkRepository
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch


/**
 * 房间列表屏幕
 * 展示可供PK的房间列表，用户可以选择一个房间进行PK
 */
@Composable
fun RoomListScreen(
    onRoomSelect: (PkRoomBasicInfo) -> Unit = {},
    onBackPressed: () -> Unit = {}
) {
    // 搜索框文本状态
    var searchText by remember { mutableStateOf("") }

    // 模拟房间数据

    val rooms = if (isEditOnCompose) {
        remember {
            mutableListOf(*createMockList())
        }
    } else {
        remember {
            mutableStateListOf<PkAvailableRoomInfo>()
        }
    }

    var searchResult by remember {
        mutableStateOf<List<PkAvailableRoomInfo>?>(null)
    }

    val scope = rememberCoroutineScope()
    val paginateState = rememberPaginateState<Int>(initialNextKey = 0)
    val state: LazyListState = rememberLazyListState()
    val pkRepository = remember {
        CrossRoomPkRepository()
    }

    paginateState.LaunchAttach(state) {
        pkRepository.fetchCrossPkRoomList(it.key ?: 0).fold({ response ->
            if (response.rooms.isNullOrEmpty()) {
                LoadResult.Page(null)
            } else {
                rooms.addAll(response.rooms)
                LoadResult.Page(response.rooms.last().id)
            }
        }, { throwable ->
            LoadResult.Error(throwable, retryDuration = 2000)
        })
    }

    // 主界面
    Surface(
        modifier = Modifier.fillMaxSize(),
        color = Color(0xFF121212) // 深色背景
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .statusBarsPadding() // 状态栏占位
                .padding(horizontal = 16.dp)
        ) {
            // 顶部标题栏
            AppTitleBar(title = stringResource(R.string.select_pk_room), onBack = onBackPressed)

            var searchJob by remember {
                mutableStateOf<Job?>(null)
            }

            // 搜索框
            SearchBar(
                searchText = searchText,
                onSearchTextChange = {
                    searchText = it
                    if (searchText.isEmpty()) {
                        searchResult = null
                        searchJob?.cancel()
                        searchJob = null
                    }
                },
                modifier = Modifier.padding(top = 16.dp)
            ) {
                searchJob = scope.launch {
                    pkRepository.searchCrossPkRoom(it).onSuccess {
                        searchResult = it.rooms
                    }
                }
            }

            // 房间列表
            RoomList(
                state = state,
                rooms = searchResult ?: rooms,
                onRoomSelect = onRoomSelect,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .padding(top = 16.dp)
            )
        }
    }
}


/**
 * 搜索框组件
 */
@Composable
private fun SearchBar(
    searchText: String,
    onSearchTextChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    onSearch: (String) -> Unit = {}
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .height(44.dp)
            .clip(CircleShape)
            .background(Color(0xFF2E3031))
            .padding(horizontal = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            imageVector = Icons.Default.Search,
            contentDescription = stringResource(R.string.search),
            tint = Color.White.copy(alpha = 0.6f)
        )

        AppBasicTextField(
            value = searchText,
            onValueChange = onSearchTextChange,
            textStyle = TextStyle(color = Color.White, fontSize = 14.sp),
            hintValue = stringResource(R.string.please_enter_room_number_to_pk),
            hintStyle = TextStyle(color = Color.White.copy(alpha = 0.5f), fontSize = 14.sp),
            modifier = Modifier
                .weight(1f)
                .padding(start = 8.dp)
                .align(Alignment.CenterVertically),
            cursorBrush = SolidColor(Color.White),
            maxLines = 1,
            keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = {
                    if (searchText.isNotEmpty()) {
                        onSearch(searchText)
                    }
                }
            )
        )
    }
}

/**
 * 房间列表
 */
@Composable
private fun RoomList(
    state: LazyListState,
    rooms: List<PkAvailableRoomInfo>,
    onRoomSelect: (PkRoomBasicInfo) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        state = state,
        verticalArrangement = Arrangement.spacedBy(16.dp),
        contentPadding = PaddingValues(bottom = 30.dp)
    ) {
        items(rooms) { room ->
            RoomListItem(
                room = room,
                onSelect = {
                    onRoomSelect(
                        PkRoomBasicInfo(
                            room.id,
                            "",
                            room.title,
                            room.owner.avatarUrl,
                            room.memberCnt
                        )
                    )
                }
            )
        }
    }
}

/**
 * 房间列表项
 */
@Composable
private fun RoomListItem(
    room: PkAvailableRoomInfo,
    onSelect: () -> Unit
) {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(172.dp)
            .clip(RoundedCornerShape(16.dp))
            .border(
                width = 1.dp,
                color = Color(0xFFFF92D6).copy(alpha = 0.2f),
                shape = RoundedCornerShape(16.dp)
            )
    ) {
        // 房间内容
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            verticalArrangement = Arrangement.Center
        ) {
            // 顶部信息栏
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 房间名称
                Text(
                    text = room.title,
                    color = Color.White,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium
                )

                // 选择按钮
                AppButton(
                    text = stringResource(R.string.select_room),
                    color = Color.White,
                    fontSize = 12.sp,
                    contentPadding = PaddingValues(horizontal = 16.dp),
                    modifier = Modifier
                        .height(height = 30.dp)
                        .widthIn(min = 80.dp),
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color(0xFFAF64F5),
                            Color(0xFF8B56FC)
                        )
                    ),
                    shape = CircleShape
                ) {
                    onSelect()
                }
            }

            // 成员头像列表
            MemberAvatarList(
                members = room.userList ?: emptyList(),
                modifier = Modifier.padding(top = 16.dp)
            )

            // 底部信息栏
            Row(
                modifier = Modifier
                    .padding(top = 12.dp)
                    .fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 心动模式标签
                if (!isEditOnCompose) {
                    if (room.roomModeName.isNotEmpty()) {
                        HeartModeTag(room.roomModeName)
                    }
                } else {
                    HeartModeTag(stringResource(R.string.heart_mode))
                }

                Spacer(modifier = Modifier.weight(1f))

                // 成员数量信息
                Text(
                    text = stringResource(R.string.format_people_in_audio_chat, room.inuseMicCnt.coerceAtLeast(1)),
                    color = Color(0xFF945EFF),
                    fontSize = 12.sp
                )
            }
        }
    }
}

/**
 * 心动模式标签
 */
@Composable
private fun HeartModeTag(modeName: String) {
    Row(
        modifier = Modifier
            .clip(RoundedCornerShape(24.dp))
            .height(20.dp)
            .background(Color(0xFF2C2C2C))
            .padding(horizontal = 8.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(5.dp)
    ) {
        // 心动图标
        VoiceWaves(
            count = 3,
            modifier = Modifier
                .height(12.dp)
                .padding(horizontal = 2.dp),
            color = Color(0xFF00E1FF),
            itemWidth = 2.dp,
            spaceWidth = 1.5.dp,
        )

        // 标签文字
        Text(
            text = modeName,
            color = Color.White,
            fontSize = 12.sp,
            lineHeight = 12.sp
        )
    }
}

/**
 * 成员头像列表
 */
@Composable
private fun MemberAvatarList(
    members: List<AppUser>,
    modifier: Modifier = Modifier
) {
    LazyRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(10.dp)
    ) {
        items(members) {
            MemberAvatar(avatarUrl = it.avatarUrl)
        }

        if (members.size < 10) {
            items(10 - members.size) {
                MemberAvatar(avatarUrl = "")
            }
        }
    }
}


/**
 * 成员头像
 */
@Composable
private fun MemberAvatar(avatarUrl: String) {
    Box(
        modifier = Modifier
            .size(66.dp)
            .clip(CircleShape)
            .background(Color.White.copy(alpha = 0.15f)),
        contentAlignment = Alignment.Center
    ) {
        if (avatarUrl.isEmpty()) {
            Box(
                modifier = Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .border(1.dp, Color.White.copy(alpha = 0.4f), CircleShape),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_room_avatar_plus),
                    contentDescription = stringResource(R.string.empty_avatar),
                    modifier = Modifier.size(18.dp)
                )
            }
        } else {
            ComposeImage(
                model = avatarUrl,
                contentDescription = stringResource(R.string.member_avatar),
                contentScale = ContentScale.Crop,
                modifier = Modifier
                    .size(60.dp)
                    .clip(CircleShape)
                    .border(1.dp, Color.White.copy(alpha = 0.4f), CircleShape)
            )
        }
    }
}

@Preview
@Composable
private fun PreviewRoomListScreen() {
    RoomListScreen({}) {}
}

private fun createMockList() = arrayOf(
    PkAvailableRoomInfo(
        id = 1,
        title = "·遇见你-全麦哇塞",
        roomMode = 1,
        inuseMicCnt = 12,
        owner = userForPreview,
        userList = List(2) { userForPreview }
    ),
    PkAvailableRoomInfo(
        id = 1,
        title = "·遇见你-全麦哇塞",
        roomMode = 1,
        inuseMicCnt = 12,
        owner = userForPreview,
        userList = List(8) { userForPreview }
    ),
    PkAvailableRoomInfo(
        id = 1,
        title = "·遇见你-全麦哇塞",
        roomMode = 1,
        inuseMicCnt = 12,
        owner = userForPreview,
        userList = List(8) { userForPreview }
    ),
    PkAvailableRoomInfo(
        id = 1,
        title = "·遇见你-全麦哇塞",
        roomMode = 1,
        inuseMicCnt = 12,
        owner = userForPreview,
        userList = List(8) { userForPreview }
    ),
    PkAvailableRoomInfo(
        id = 1,
        title = "·遇见你-全麦哇塞",
        roomMode = 1,
        inuseMicCnt = 12,
        owner = userForPreview,
        userList = List(8) { userForPreview }
    ),
)