package com.qyqy.ucoo.compose.presentation.wedding

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.GridItemSpan
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.grid.rememberLazyGridState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.alpha
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.theme.navigationPadding
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.getShadowColor
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.MI_SANS
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.sUserFlow
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

/**
```
{
"ring": {
"id": 1,
"icon": "xxx",
"name": "时光印记",
"desc": "以时光为契，镌刻不朽之爱",
"price": "",
"effect_file": "xxx"
},
"balance": 1000,
"privileges": [
{
"title": "xxxx",
"desc": "xxxx",
"icon": "xxxxxx",
"own": true
}
],
"title": "婚礼权益9/9",
"desc": "成功购买戒指后，戒指将自动存入您的背包。从背包选择戒指作为礼物赠予人，即可点亮戒指。每次点亮戒指均可触发解锁婚礼约资格。请注意，戒指的购买与赠送均无次数限制。"
}
```
 */
@Serializable
data class RingBenefits(
    @SerialName("balance")
    val balance: Int = 0, // 1000
    @SerialName("desc")
    val desc: String = "", // 成功购买戒指后，戒指将自动存入您的背包。从背包选择戒指作为礼物赠予人，即可点亮戒指。每次点亮戒指均可触发解锁婚礼约资格。请注意，戒指的购买与赠送均无次数限制。
    @SerialName("privileges")
    val privileges: List<Privilege> = emptyList(),
    @SerialName("gift")
    val ringGift: RingGift = RingGift(),
    @SerialName("title")
    val title: String = "" // 婚礼权益9/9
)

@Serializable
data class Privilege(
    @SerialName("desc")
    val desc: String = "", // xxxx
    @SerialName("icon")
    val icon: String = "", // xxxxxx
    @SerialName("own")
    val own: Boolean = false, // true
    @SerialName("title")
    val title: String = "" // xxxx
)

@Serializable
data class RingGift(
    @SerialName("desc")
    val desc: String = "", // 以时光为契，镌刻不朽之爱
    @SerialName("effect_file")
    val effectFile: String = "", // xxx
    @SerialName("icon")
    val icon: String = "", // xxx
    @SerialName("id")
    val id: Int = 0, // 1
    @SerialName("name")
    val name: String = "", // 时光印记
    @SerialName("price")
    val price: String = ""
)


@Composable
fun WeddingBenefitsContent(
    benefits: RingBenefits,
    onClose: () -> Unit = {},
    onBuy: (Int) -> Unit = {}
) {

    var height by rememberSaveable {
        mutableFloatStateOf(Float.NaN)
    }

    val density = LocalDensity.current

    Box(
        modifier = Modifier
            .run {
                if (height.isNaN()) {
                    padding(top = 100.dp).fillMaxSize()
                } else {
                    fillMaxWidth().height(with(density) { height.toDp() })
                }
            }
            .onSizeChanged {
                height = it.height.toFloat()
            }
            .clip(RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp))
    ) {

        val state = rememberLazyGridState()

        Column(modifier = Modifier.fillMaxSize()) {

            val imageBitmap = ImageBitmap.imageResource(id = R.drawable.bg_wedding_benefits_top)

            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                state = state,
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .drawWithCache {
                        val scale = size.width / imageBitmap.width
                        val dstHeight = imageBitmap.height * scale
                        val offsetY = dstHeight - 1f

                        val gradient = if (offsetY < size.height) {
                            Brush.verticalGradient(
                                0f to Color(0xFF2D0C74),
                                offsetY.div(size.height) to Color(0xFF2D0C74),
                                1f to Color(0xFF4D36A4),
                                startY = 0f,
                                endY = size.height
                            )
                        } else {
                            null
                        }
                        onDrawBehind {
                            if (gradient != null) {
                                drawRect(gradient, topLeft = Offset(0f, 0f))
                            }
                        }
                    }
            ) {
                item(span = { GridItemSpan(3) }) {

                    // 主要内容
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .drawWithCache {
                                // 相当于ContentScale.FillWidth
                                val scale = size.width / imageBitmap.width
                                val dstWidth = size.width
                                val dstHeight = imageBitmap.height * scale

                                onDrawBehind {
                                    drawImage(
                                        image = imageBitmap,
                                        dstSize = IntSize(dstWidth.toInt(), dstHeight.toInt())
                                    )
                                }
                            }
                            .padding(horizontal = 24.dp),
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {

                        // 戒指展示区域（占位）
                        ComposeImage(
                            model = benefits.ringGift.effectFile,
                            modifier = Modifier
                                .padding(top = 54.dp)
                                .size(166.dp),
                            contentScale = ContentScale.Fit,
                            loading = null
                        )

                        Spacer(modifier = Modifier.height(2.dp))

                        // 金币数量
                        Text(
                            text = stringResource(R.string.金币_模板, benefits.ringGift.price),
                            style = TextStyle(
                                fontSize = 16.sp,
                                fontFamily = FontFamily.MI_SANS,
                                brush = Brush.horizontalGradient(
                                    listOf(Color(0xFFFEFFE4), Color(0xFFFFEEA3))
                                ),
                                lineHeight = 24.sp
                            ),
                        )

                        Spacer(modifier = Modifier.height(9.dp))

                        // 戒指描述
                        Text(
                            text = stringResource(R.string.戒指描述_模板, benefits.ringGift.desc),
                            color = Color(0xFFD1C1FF),
                            fontSize = 14.sp,
                            textAlign = TextAlign.Center,
                            modifier = Modifier
                                .background(
                                    Brush.horizontalGradient(
                                        0f to Color(0x003A207A),
                                        0.5f to Color(0x993515AB),
                                        1f to Color(0x0039207B),
                                    )
                                )
                                .padding(vertical = 6.dp, horizontal = 12.dp)
                        )

                        Spacer(modifier = Modifier.height(67.dp))

                        // 婚礼权益标签
                        Row(
                            modifier = Modifier.height(32.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(3.dp)
                        ) {
                            Image(
                                painter = painterResource(R.drawable.ic_wedding_benefits_left_wing),
                                contentDescription = null
                            )

                            Text(
                                text = benefits.title,
                                modifier = Modifier
                                    .padding(bottom = 2.dp)
                                    .align(Alignment.Bottom),
                                style = TextStyle(
                                    color = Color.White,
                                    fontSize = 18.sp,
                                    fontFamily = FontFamily.MI_SANS,
                                    shadow = Shadow(
                                        color = Color(color = 0x99FFEF7B),
                                        offset = Offset(0f, 0f),
                                        blurRadius = 6f
                                    )
                                )
                            )

                            Image(
                                painter = painterResource(R.drawable.ic_wedding_benefits_right_wing),
                                contentDescription = null
                            )
                        }

                        Spacer(modifier = Modifier.height(16.dp))

                    }
                }

                // 权益网格
                items(benefits.privileges) {
                    BenefitItem(it.icon, it.title, it.desc, it.own)
                }

                item(span = { GridItemSpan(3) }) {
                    // 底部提示文本
                    Text(
                        text = benefits.desc,
                        fontSize = 11.sp,
                        color = Color(0xFFBBA5FF),
                        textAlign = TextAlign.Center,
                        modifier = Modifier.padding(
                            top = 50.dp,
                            bottom = 30.dp,
                            start = 20.dp,
                            end = 20.dp,
                        )
                    )
                }
            }

            // 底部购买bar
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(Color(0xFF5F3CC5), Color(0xFF301975))
                        )
                    )
                    .drawBehind {
                        val shadowColor = Color(0x1FFFFFFF)
                        drawRoundRect(
                            color = shadowColor,
                            size = size.copy(height = 2.dp.toPx()),
                        )
                    }
                    .padding(horizontal = 32.dp, vertical = 6.dp)
                    .navigationPadding(16.dp), verticalAlignment = Alignment.CenterVertically
            ) {
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = stringResource(R.string.我的余额),
                        fontSize = 12.sp,
                        color = Color.White
                    )
                    Spacer(modifier = Modifier.height(6.dp))
                    Row(verticalAlignment = Alignment.CenterVertically) {
                        Image(
                            painter = painterResource(R.drawable.ic_coin_msg),
                            contentDescription = null,
                            modifier = Modifier.size(18.dp),
                        )
                        Spacer(modifier = Modifier.width(4.dp))

                        val user by sUserFlow.collectAsStateWithLifecycle()

                        val balance by remember {
                            val initVale = user.balance
                            derivedStateOf {
                                if (user.balance != initVale) {
                                    user.balance
                                } else {
                                    benefits.balance
                                }
                            }
                        }

                        Text(
                            text = balance.toString(),
                            fontSize = 16.sp,
                            color = Color.White,
                            fontFamily = FontFamily.D_DIN
                        )
                    }
                }
                Box(
                    modifier = Modifier
                        .size(124.dp, 58.dp)
                        .paint(painterResource(R.drawable.btn_wedding_benefits_buy))
                        .noEffectClickable(onClick = {
                            onBuy(benefits.ringGift.id)
                        }),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = stringResource(R.string.购买),
                        style = TextStyle(
                            fontFamily = FontFamily.MI_SANS,
                            color = Color.White,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Bold,
                            shadow = Shadow(
                                color = Color(0xFF7951E2).getShadowColor(),
                                offset = Offset(3f, 3f), blurRadius = 2f
                            ),
                        ),
                    )
                }
            }
        }

        val alpha by remember(density) {
            val h = with(density) {
                54.dp.toPx()
            }
            derivedStateOf {
                state.firstVisibleItemScrollOffset.div(h)
            }
        }
        // 标题
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(54.dp)
                .background(
                    brush = Brush.verticalGradient(
                        0f to Color(0xFF8463E3),
                        0.4f to Color(0xFF6B4DC3),
                        1f to Color(0x0050359E),
                    ),
                    alpha = alpha,
                ),
        ) {
            Image(
                painter = painterResource(id = R.drawable.ic_navigation_back),
                contentDescription = "back",
                contentScale = ContentScale.Inside,
                modifier = Modifier
                    .fillMaxHeight()
                    .aspectRatio(1f)
                    .clip(CircleShape)
                    .clickable(onClick = onClose)
            )
            Text(
                text = benefits.ringGift.name,
                color = Color.White,
                fontSize = 18.sp,
                modifier = Modifier.align(Alignment.Center)
            )
        }
    }
}

@Composable
fun BuyRingResultContent(content: String, onClose: () -> Unit = {}) {
    Column(
        modifier = Modifier
            .padding(horizontal = 20.dp)
            .fillMaxWidth()
            .background(
                brush = Brush.verticalGradient(listOf(Color(0xFF202247), Color(0xFF101024))),
                shape = RoundedCornerShape(16.dp)
            )
            .padding(horizontal = 16.dp, vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(R.string.购买戒指提醒),
            fontSize = 17.sp,
            color = Color(0xFFFFE37E),
            fontWeight = FontWeight.Medium
        )
        Spacer(modifier = Modifier.height(4.dp))
        Text(
            text = content,
            fontSize = 15.sp,
            color = Color(0x80FFFFFF),
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.height(16.dp))
        AppButton(
            text = stringResource(R.string.i_known),
            background = Color(0xFF945EFF),
            fontSize = 14.sp,
            color = Color.White,
            contentPadding = PaddingValues(horizontal = 24.dp, vertical = 6.dp)
        ) {
            onClose()
        }
    }
}

@Composable
private fun BenefitItem(
    icon: String,
    title: String,
    desc: String,
    enable: Boolean,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .padding(8.dp)
            .alpha(if (enable) 1f else 0.3f),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 图标占位
        ComposeImage(
            model = icon,
            modifier = Modifier.size(67.dp, 78.dp),
            contentScale = ContentScale.Fit
        )

        Spacer(modifier = Modifier.height(8.dp))

        // 权益标题
        Text(
            text = title,
            color = Color.White,
            fontSize = 14.sp,
            textAlign = TextAlign.Center
        )

        // 权益描述
        Text(
            text = desc,
            color = Color(0xFFBBA5FF),
            fontSize = 12.sp,
            textAlign = TextAlign.Center
        )
    }
}

@Preview(widthDp = 375, heightDp = 812)
@Composable
private fun PreviewRingDetailScreen() {
    WeddingBenefitsContent(
        RingBenefits(
            balance = 1000,
            title = "婚礼权益9/9",
            desc = "成功购买戒指后，戒指将自动存入您的背包。从背包选择戒指作为礼物赠予人，即可点亮戒指。每次点亮戒指均可触发解锁婚礼约资格。请注意，戒指的购买与赠送均无次数限制。",
            privileges = listOf(
                Privilege(
                    icon = "",
                    own = true,
                    title = "戒指标识",
                    desc = "官宣CP专属展示",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "赠送点亮通知",
                    desc = "平台全服点亮通知",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "麦位专属表情",
                    desc = "专属表情影显身份",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "装扮五件套*7天",
                    desc = "庄严装扮霸气出场",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "专属礼物",
                    desc = "婚礼专属排面十足",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "婚礼布置",
                    desc = "宾客席位稀缺联联锁",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "婚礼场景",
                    desc = "专属现场彰显尊贵",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "专属背包礼物",
                    desc = "专属背包与众不同",
                ),
                Privilege(
                    icon = "",
                    own = true,
                    title = "礼物霸屏",
                    desc = "璀璨霸屏震撼全场",
                ),
            ),
            ringGift = RingGift(
                name = "时光印记",
                desc = "以时光为契，镌刻不朽之爱",
                price = "99999"
            )
        )
    )
}

@Preview
@Composable
private fun PreviewBuyRingResultContent() {
    Box(modifier = Modifier.padding(horizontal = 30.dp, vertical = 60.dp)) {
        BuyRingResultContent("已成功购买xx戒指，快去背包点亮戒指解锁婚礼预约资格～")
    }
}