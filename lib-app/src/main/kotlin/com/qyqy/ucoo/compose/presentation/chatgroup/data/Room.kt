package com.qyqy.ucoo.compose.presentation.chatgroup.data

import androidx.room.ColumnInfo
import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Entity
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.PrimaryKey
import androidx.room.Query

@Entity(tableName = "chat_group_infos")
data class ChatGroupRoomInfo(
    @PrimaryKey val id: Int,
    @ColumnInfo(name = "public_id") val publicId: String = "",
    @ColumnInfo(name = "rc_group_id") val rcGroupId: String = "",
    @ColumnInfo(name = "name") val name: String = "",
    @ColumnInfo(name = "avatar_url") val avatarUrl: String = "",
    @ColumnInfo(name = "intro") val intro: String = "",
    @ColumnInfo(name = "role") val role: Int = 0,
    @ColumnInfo(name = "i_enable_dont_disturb") val enableDontDisturb: Boolean = false,
    @ColumnInfo(name = "relation_with_me") val relationWithMe: Int = 0,
) {

    fun toBrief() = ChatGroupBrief(
        id = id,
        avatarUrl = avatarUrl,
        iEnableDontDisturb = enableDontDisturb,
        intro = intro,
        name = name,
        publicId = publicId,
        rcGroupId = rcGroupId,
        role = role,
        relationWithMe = relationWithMe,
    )
}

fun ChatGroupBrief.toChatGroupRoomInfo() = ChatGroupRoomInfo(
    id = id,
    avatarUrl = avatarUrl,
    enableDontDisturb = iEnableDontDisturb,
    intro = intro,
    name = name,
    publicId = publicId,
    rcGroupId = rcGroupId,
    role = role,
    relationWithMe = relationWithMe,
)


@Dao
interface ChatGroupInfoDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(info: ChatGroupRoomInfo)

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertAll(ids: List<ChatGroupRoomInfo>)

    @Delete
    suspend fun delete(info: ChatGroupRoomInfo)

    @Delete
    suspend fun deleteAll(ids: List<ChatGroupRoomInfo>)

    @Query("DELETE FROM chat_group_infos")
    suspend fun deleteAll()

    @Query("SELECT * FROM chat_group_infos WHERE id = (:id)")
    suspend fun getChatGroupInfoById(id: Int): ChatGroupRoomInfo?
}