package com.qyqy.ucoo.compose.vm

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.GiftWallItemDetail
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.user.gift.GiftRepository
import com.qyqy.ucoo.utils.logW
import com.qyqy.ucoo.utils.toast
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.decodeFromJsonElement

class GiftWallDetailViewModel : ViewModel() {

    private val _isLoadingState = MutableStateFlow(false)
    val isLoadingState = _isLoadingState.asStateFlow()

    private val repo by lazy {
        GiftRepository()
    }

    private val _events = MutableSharedFlow<Event>()
    val events = _events.asSharedFlow()

    @Synchronized
    fun getGiftDetail(userId: Int, giftId: Int, purpose: Int, sceneType: Int, sceneId: Int, begId: Int? = null, callback: (GiftWallItemDetail?) -> Unit) {
        if (_isLoadingState.value) {
            return
        }
        _isLoadingState.value = true
        viewModelScope.launch {
            repo.getGiftDetail(userId, giftId, purpose, sceneType, sceneId.toString(), begId)
                .onSuccess {
                    callback(it)
//                    _giftDetailState.value = it
                    _isLoadingState.value = false
                }.onFailure {
                    callback(null)
//                    _giftDetailState.value = it
                    _isLoadingState.value = false
                }
        }
    }

    /**
     * 将点击之后的请求接口进行集中处理
     * 这样也可以保证接口后续的处理逻辑都保持在同一块代码中,免得找来找去
     *
     */

    //1 赠送
    fun sendWallGift(userId: Int, giftDetail: GiftWallItemDetail, count: Int,sence_type: Int, scene_id: Int, greetings: String = "") {
        viewModelScope.launch {
            repo.sendGiftOnWall(userId.toString(), giftDetail.id, count, giftDetail.packetCnt > 0, sence_type, scene_id, greetings = greetings)
                .onSuccess {
                    toast(it.getStringOrNull("toast_msg"))
                    Analytics.quickReportEvent(
                        TracePoints.GIFT_WALL_GIFT_GIVE_SUCCESS,
                        "user_id" to userId,
                        "gift_id" to giftDetail.id,
                        "gift_name" to giftDetail.name,
                        "price" to giftDetail.price,
                        "price_type" to giftDetail.priceType,
                        "star_cnt" to giftDetail.starCnt,
                        "from_packet" to (giftDetail.packetCnt > 0),
                        "gift_wall_gift_popup_for_give_type" to giftDetail.popupType,
                    )
                    delay(500)
                    it.getOrNull("given_gift_ids")?.let {
                        val sentGiftIds = sAppJson.decodeFromJsonElement<List<Int>>(it)
//                                _events.emit(Event.GiveGiftEvent(userId, sentGiftIds))
                        GiftRepository.flowSentGiftToUser.emit(userId to sentGiftIds)
                    }

                }.toastError()
        }
    }

    /**
     * 2. 创建一个礼物求打赏信息
     *
     * @param giftId 打赏的礼物id
     * @param count 打赏的礼物数量
     */
    fun makeBegGiftRequest(userId: Int, giftDetail: GiftWallItemDetail, count: Int) {
        viewModelScope.launch {
            repo.requestRewardGift(userId.toString(), giftDetail.id, count).onSuccess {
                toast(it.getStringOrNull("toast"))
                _events.emit(Event.BegGiftEvent(giftDetail.id, count))
            }.toastError()
        }
    }

    /**
     * 3 施舍
     *
     * @param begId 乞讨id
     */
    fun giveBegGiftRequest(begId: Int?, greetings: String? = null) {
        if (begId == null) {
            logW("无法施舍礼物,因为begId = null")
            return
        }
        viewModelScope.launch {
            repo.requestCharityGIft(begId, greetings).onSuccess {
                toast(it.getStringOrNull("toast_msg"))
                _events.emit(Event.CharitGiftEvent(begId))
            }.toastError()
        }
    }

    sealed interface Event {
        data class GiveGiftEvent(val userId: Int, val sentGift: List<Int>) : Event
        data class BegGiftEvent(val giftId: Int, val count: Int, val timestamp: Long = System.currentTimeMillis()) : Event
        data class CharitGiftEvent(val begId: Int, val timestamp: Long = System.currentTimeMillis()) : Event
    }
}