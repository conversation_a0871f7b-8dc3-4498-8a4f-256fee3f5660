package com.qyqy.ucoo.compose.presentation.cp_house.bean


import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Parcelize
@Serializable
data class LotterySettings(
    @SerialName("balance")
    val balance: Int = 0,
    @SerialName("conf")
    val conf: List<Conf> = listOf(),
    val rule: String = "",
    val tips: String = ""
) : Parcelable {
    @Parcelize
    @Serializable
    data class Conf(
        @SerialName("coin_cost")
        val coinCost: Int = 0,
        @SerialName("coin_type")
        val coinType: Int = 0,
        @SerialName("count")
        val count: Int = 0,
        @SerialName("position")
        val position: Int = 0
    ) : Parcelable {
        companion object {
            const val COIN_TYPE_UCOO = 1
            const val COIN_TYPE_HOUSE = 2
        }
    }
}