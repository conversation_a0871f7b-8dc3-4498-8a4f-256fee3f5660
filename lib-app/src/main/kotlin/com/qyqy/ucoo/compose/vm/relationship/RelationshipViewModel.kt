package com.qyqy.ucoo.compose.vm.relationship

import androidx.compose.runtime.Composable
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.ucoo.compose.data.CpGraph
import com.qyqy.ucoo.compose.data.FamilyGraph
import com.qyqy.ucoo.compose.data.ProfileTab
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.data.RelationshipGraph
import com.qyqy.ucoo.compose.domain.usecase.relationship.BuyFamilySeatsUseCase
import com.qyqy.ucoo.compose.domain.usecase.relationship.GetFamilyRelationUseCase
import com.qyqy.ucoo.compose.state.MviState
import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi.ViewResult
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi.ViewState
import com.qyqy.ucoo.user.RelationshipRepository
import com.qyqy.ucoo.utils.findIsInstance
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.emitAll
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach

class RelationshipViewModel(
    initialState: ViewState,
    flow: Flow<CpGraph>,
    private val getFamilyRelationUseCase: GetFamilyRelationUseCase,
    private val buyFamilySeatsUseCase: BuyFamilySeatsUseCase,
) : MviViewModel<ViewEvent, ViewResult, ViewState, ViewEffect>(initialState) {

    companion object {

        @Composable
        fun rememberViewModel(
            tab: ProfileTab.Relationship,
            flow: Flow<MviState<ProfileMvi.ViewResult.ProfileViewResult, ProfileMvi.ViewState>>,
        ): RelationshipViewModel {
            return viewModel(initializer = {
                val relationshipRepository = RelationshipRepository()
                RelationshipViewModel(
                    ViewState(tab.userId, RelationshipGraph(tab.data, FamilyGraph())),
                    flow.filter { mvi ->
                        mvi.result != null
                    }.mapLatest { mvi ->
                        mvi.state.tabs.findIsInstance<ProfileTab.Relationship>()?.data ?: CpGraph(
                            Relationship.Cp.None(mvi.state.user), mvi.state.user.id, tab.data.envType
                        )
                    }.distinctUntilChanged(),
                    GetFamilyRelationUseCase(relationshipRepository),
                    BuyFamilySeatsUseCase(relationshipRepository),
                )
            })
        }
    }

    init {
        flow.onEach {
            val curUserId = states.value.userId
            if (curUserId != it.userId) {
                processEvent(ViewEvent.ResetFamilyRelationInfo(it))
            }
            processEvent(ViewEvent.GetFamilyRelationInfo(it.userId, it))
        }.launchIn(viewModelScope)
    }

    override fun Flow<ViewEvent>.toResults(): Flow<ViewResult> {
        return merge(
            filterIsInstance<ViewEvent.ResetFamilyRelationInfo>().toResetInfoResult(),
            filterIsInstance<ViewEvent.GetFamilyRelationInfo>().toFamilyRelationInfoResult(),
            filterIsInstance<ViewEvent.BuySeats>().toBuySeatsResult(),
        )
    }

    override fun Flow<ViewResult>.filterStateResult(): Flow<ViewResult> {
        return filter {
            when (it) {
                is ViewResult.CpGraphResult,
                is ViewResult.ResetGraphResult,
                is ViewResult.FamilyConfigInfoResult,
                is ViewResult.FamilyLabelListResult,
                is ViewResult.FamilyTagListResult,
                is ViewResult.BuySeatsResult,
                -> true

                else -> false
            }
        }
    }

    override suspend fun ViewResult.reduce(state: ViewState): ViewState {
        return when (this) {
            is ViewResult.CpGraphResult -> {
                state.copy(
                    userId = cpGraph.userId,
                    relationshipGraph = state.relationshipGraph.copy(
                        cpRelationship = cpGraph.cpRelationship
                    )
                )
            }

            is ViewResult.ResetGraphResult -> {
                state.copy(
                    userId = cpGraph.userId,
                    relationshipGraph = RelationshipGraph(cpGraph, FamilyGraph())
                )
            }

            is ViewResult.FamilyConfigInfoResult -> {
                state.copy(
                    relationshipGraph = state.relationshipGraph.copy(
                        emptyCount = emptyCount,
                        totalCount = totalCount,
                        maxCount = maxCount,
                        pricePerLabel = pricePerLabel,
                        ruleLink = ruleLink,
                        placeholderLabelVisible = false,
                    )
                )
            }

            is ViewResult.FamilyLabelListResult -> {
                state.copy(
                    relationshipGraph = state.relationshipGraph.copy(labelList = labelList)
                )
            }

            is ViewResult.FamilyTagListResult -> {
                state.copy(
                    relationshipGraph = state.relationshipGraph.copy(labelTagList = labelTagList)
                )
            }

            is ViewResult.BuySeatsResult -> {
                state.copy(
                    relationshipGraph = state.relationshipGraph.copy(
                        emptyCount = state.relationshipGraph.emptyCount.plus(buyCount),
                        totalCount = totalCount,
                    )
                )
            }

            else -> state
        }
    }

    override fun Flow<ViewResult>.toEffects(): Flow<ViewEffect> {
        return merge(
            filterIsInstance<ViewResult.ErrorResult>().filter {
                it.toastError.isNullOrEmpty().not()
            }.mapLatest { result ->
                ViewEffect.Toast(result.toastError!!)
            },
        )
    }

    private fun Flow<ViewEvent.ResetFamilyRelationInfo>.toResetInfoResult(): Flow<ViewResult> {
        return mapLatest {
            ViewResult.ResetGraphResult(it.cpGraph)
        }
    }

    private fun Flow<ViewEvent.GetFamilyRelationInfo>.toFamilyRelationInfoResult(): Flow<ViewResult> {
        return flatMapLatest {
            flow {
                if (it.cpGraph != null) {
                    emit(ViewResult.CpGraphResult(it.cpGraph))
                }
                emitAll(getFamilyRelationUseCase(it.userId).getOrThrow())
            }
        }.catch {
            emit(ViewResult.ErrorResult(null))
        }
    }

    private fun Flow<ViewEvent.BuySeats>.toBuySeatsResult(): Flow<ViewResult> {
        return flatMapLatest {
            buyFamilySeatsUseCase(it.count).getOrThrow()
        }.catch {
            emit(ViewResult.ErrorResult(null))
        }
    }
}