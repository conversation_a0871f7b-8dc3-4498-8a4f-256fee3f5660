package com.qyqy.ucoo.compose.state

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.utils.ComposeState
import kotlinx.coroutines.launch


interface LiState {
    val isRefreshing: Boolean
    val isLoadingNow: Boolean
    val isLoaded: Boolean
    val hasMore: Boolean
    val allowLoad: Boolean
}

class LiStateInfo : LiState {

    private val _isRefreshing: MutableState<Boolean> = mutableStateOf(false)

    private val _isLoaded: MutableState<Boolean> = mutableStateOf(false)

    private val _hasMore: MutableState<Boolean> = mutableStateOf(true)

    private val _isLoading: MutableState<Boolean> = mutableStateOf(false)

    override val isRefreshing: Boolean
        get() = _isRefreshing.value

    override val isLoadingNow: <PERSON>olean
        get() = _isLoading.value

    override val isLoaded: <PERSON>olean
        get() = _isLoaded.value

    override val hasMore: Boolean
        get() = _hasMore.value

    override val allowLoad: Boolean
        get() = isLoaded && (isRefreshing.not() && isLoadingNow.not() && hasMore)

    val isProgressing: Boolean
        get() = _isLoading.value || _isRefreshing.value

    fun setRefresh(refresh: Boolean) {
        _isRefreshing.value = refresh
    }

    fun setLoading(bool: Boolean) {
        _isLoading.value = bool
    }

    fun setHasMore(hasMore: Boolean) {
        _hasMore.value = hasMore
    }

    fun setLoaded(loaded: Boolean) {
        _isLoaded.value = loaded
    }

    fun onStartRefresh() {
        _isRefreshing.value = true
        _hasMore.value = true
    }

    fun onStartLoading() {
        _isLoading.value = true
    }

    fun onLoadComplete(hasMore: Boolean) {
        _hasMore.value = hasMore
        _isRefreshing.value = false
        _isLoading.value = false
        _isLoaded.value = true
    }

    fun onLoadError() {
        _isRefreshing.value = false
        _isLoading.value = false
    }
}

interface ListDataFetcher<T> {
    val dataState: ComposeState<List<T>>
    fun refresh()
    fun loadMore()
    suspend fun fetch(): List<T>
}

abstract class LiStateViewModel<T>() : ViewModel(), ListDataFetcher<T>,
    LiState {
    protected val _dataState = mutableStateOf(emptyList<T>())
    override val dataState: ComposeState<List<T>> = _dataState

    private data class FetchStateInfo(
        val isRefreshing: Boolean = false,
        val isLoading: Boolean = false,
        val isLoaded: Boolean = false,
        val hasMore: Boolean = true
    ) {
        val isFetching: Boolean
            get() = isLoading || isRefreshing
    }

    private val fetchState = mutableStateOf(FetchStateInfo())

    private val stateInfo: FetchStateInfo
        get() = fetchState.value

    override val isRefreshing: Boolean
        get() = stateInfo.isRefreshing
    override val isLoadingNow: Boolean
        get() = stateInfo.isLoading
    override val isLoaded: Boolean
        get() = stateInfo.isLoaded
    override val hasMore: Boolean
        get() = stateInfo.hasMore

    override val allowLoad: Boolean
        get() = stateInfo.run {
            (!isLoading && !isRefreshing && hasMore)
        }

    override fun refresh() {
        performRequest(true)
    }

    override fun loadMore() {
        performRequest(false)
    }

    protected open fun onStartRequest(isRefresh: Boolean) {

    }

    private fun performRequest(isRefresh: Boolean) {
        viewModelScope.launch {
            if (stateInfo.isFetching) {
                return@launch
            }
            if (isRefresh) {
                fetchState.value = fetchState.value.copy(isRefreshing = true, hasMore = true)
            } else {
                fetchState.value = fetchState.value.copy(isLoading = true)
            }
            onStartRequest(isRefresh)
            try {
                val list = fetch()
                if (isRefresh) {
                    _dataState.value = list
                } else {
                    _dataState.value += list
                }
                fetchState.value =
                    fetchState.value.copy(isRefreshing = false, isLoading = false, hasMore = list.isNotEmpty(), isLoaded = true)
            } catch (e: Exception) {
                e.printStackTrace()
                fetchState.value =
                    fetchState.value.copy(isRefreshing = false, isLoading = false)
            }

        }
    }
}