package com.qyqy.ucoo.compose.presentation.greets

import android.net.Uri
import kotlinx.serialization.json.JsonNull.content

interface GreetItem {
    val key: String
    val content: String
    val contentType: Int
    val id: Int
    val duration:Int
    var isNew:Boolean
    companion object Type {
        const val TYPE_TEXT = 1
        const val TYPE_IMAGE = 3
        const val TYPE_VOICE = 2
    }
}

val GreetItem.deleteParams: Map<String, String>
    get() = mapOf("content_type" to contentType.toString(), "content_id" to key)

val GreetItem.fileName: String
    get() = kotlin.runCatching { Uri.parse(content).lastPathSegment }.getOrNull().orEmpty()

val GreetItem.durationText:String
    get() = "${duration}″"

val GreetItem.ratioLength: Float
    get() = (1f * duration / 20f).coerceAtMost(1f).coerceAtLeast(0.25f)
