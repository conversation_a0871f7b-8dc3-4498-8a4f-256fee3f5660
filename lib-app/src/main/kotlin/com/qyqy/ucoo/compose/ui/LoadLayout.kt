package com.qyqy.ucoo.compose.ui

import com.qyqy.ucoo.LocalUserPartition
import androidx.compose.animation.core.InfiniteRepeatableSpec
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithContent
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.Dialog
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import kotlin.math.cos
import kotlin.math.sin

val LocalContentLoading = compositionLocalOf<MutableState<Boolean>> { error("LoadingLayout 作用域错误") }

suspend fun <T> MutableState<Boolean>.runWithLoading(block: suspend () -> T): T {
    value = true
    return try {
        block()
    } finally {
        value = false
    }
}

@Composable
fun LoadingLayout(
    modifier: Modifier = Modifier,
    content: @Composable BoxScope.() -> Unit,
) {
    val loadingState = remember {
        mutableStateOf(false)
    }

    CompositionLocalProvider(LocalContentLoading provides loadingState) {
        Box(
            modifier = modifier,
        ) {
            content()
            if (loadingState.value) {
                Dialog(onDismissRequest = {
                    loadingState.value = false
                }) {
                    IconLoading()
                }
            }
        }
    }

}

@Composable
fun IconLoading(
    modifier: Modifier = Modifier,
    iconRes: Int = if (LocalUserPartition.current.isCupid) R.drawable.ic_cpd_launcher else R.mipmap.ic_launcher,
    colorCircle: Color = if (LocalUserPartition.current.isCupid) Color(0xFFFFABC7) else Color.DarkGray,
) {
    val transition = rememberInfiniteTransition(label = "icon_loading")
    val value by transition.animateFloat(
        initialValue = 0f,
        targetValue = 1f,
        animationSpec = InfiniteRepeatableSpec(animation = tween(durationMillis = 1800, easing = LinearEasing)),
        label = "icon_loading"
    )

    val size = 48.dp
    val colorBall = if (LocalUserPartition.current.isCupid) Color(0xFFFF5584) else MaterialTheme.colorScheme.primary
    Box(
        modifier = modifier
            .background(if (LocalUserPartition.current.isCupid) Color.White else MaterialTheme.colorScheme.onBackground, Shapes.medium)
            .padding(12.dp)
            .drawWithContent {
                drawContent()
                drawCircle(colorCircle, style = Stroke(width = 5f))
                val degree = 2 * Math.PI * value - Math.PI / 2f
                val radius = size.toPx() / 2f
                val centerPoint =
                    Offset((center.x + radius * cos(degree)).toFloat(), ((center.y + radius * sin(degree)).toFloat()))
                drawCircle(colorBall, radius = 4.dp.toPx(), center = centerPoint)
            },
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = iconRes), contentDescription = "icon", modifier = Modifier
                .requiredSize(size)
                .clip(CircleShape)
        )
    }
}

@Preview
@Composable
fun IconLoadingPreviewer() {
    PreviewCupidTheme {
        IconLoading(modifier = Modifier.background(MaterialTheme.colorScheme.onBackground, Shapes.medium))
    }
}