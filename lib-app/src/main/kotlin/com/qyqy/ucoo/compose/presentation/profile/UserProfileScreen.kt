package com.qyqy.ucoo.compose.presentation.profile

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.annotation.DrawableRes
import androidx.appcompat.app.AppCompatActivity
import androidx.compose.animation.AnimatedContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredWidthIn
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.overseas.common.utils.TaskRunMode
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.data.CpGraph
import com.qyqy.ucoo.compose.data.CpZoneWrapper
import com.qyqy.ucoo.compose.data.ProfileTab
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.data.UserInfoItem
import com.qyqy.ucoo.compose.isEditOnCompose
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.obtainIntent
import com.qyqy.ucoo.compose.presentation.MainNavigator
import com.qyqy.ucoo.compose.presentation.ScreenNavigator
import com.qyqy.ucoo.compose.presentation.bless.BlessingWordEditLayout
import com.qyqy.ucoo.compose.presentation.bless.showBlessWordEditDialog
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.presentation.mine.CpZonePageRouter
import com.qyqy.ucoo.compose.presentation.mine.GiftWallPagerV2
import com.qyqy.ucoo.compose.presentation.mine.PreviewCpZonePage
import com.qyqy.ucoo.compose.presentation.mine.PreviewGiftWallPage
import com.qyqy.ucoo.compose.presentation.mine.PreviewRelationShipGraphPage
import com.qyqy.ucoo.compose.presentation.mine.RelationShipGraphRouter
import com.qyqy.ucoo.compose.state.MviState
import com.qyqy.ucoo.compose.ui.AppScrollableTabRow
import com.qyqy.ucoo.compose.ui.AppTab
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.ComposeDialog
import com.qyqy.ucoo.compose.ui.fixCurrentPage
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewResult
import com.qyqy.ucoo.compose.vm.profile.ProfileMvi.ViewState
import com.qyqy.ucoo.compose.vm.profile.UserProfileViewModel
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi
import com.qyqy.ucoo.compose.vm.relationship.RelationshipViewModel
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.im.bean.CpTask
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.room.CpViewModel
import com.qyqy.ucoo.runWithTopActivity
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.setting.ReportActivity
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.user.gift.ChatGiftPanelDialogFragment
import com.qyqy.ucoo.user.gift.ChatGiftPanelDialogFragment.Companion.makeShow
import com.qyqy.ucoo.user.gift.ChatGiftViewModel
import com.qyqy.ucoo.utils.ShushuUtils
import com.qyqy.ucoo.utils.statistic.ReportKey
import com.qyqy.ucoo.utils.statistic.StatisticReporter
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicator
import eu.bambooapps.material3.pullrefresh.PullRefreshIndicatorDefaults
import eu.bambooapps.material3.pullrefresh.pullRefresh
import eu.bambooapps.material3.pullrefresh.rememberPullRefreshState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.emptyFlow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

private const val KEY_USER = "key_user"

object UserProfileNavigator : ScreenNavigator {

    @Composable
    override fun ContentScreen(activity: BaseActivity, bundle: Bundle) {
        val user = bundle.getParcelable<AppUser>(KEY_USER) ?: sUser
        val initialUser = if (user.isSelf) {
            sUser
        } else {
            UserManager.getMemoryUserCacheById(user.id) ?: user
        }
        UserProfilePageRouter(initialUser)
    }

    fun obtainIntent(context: Context, user: User): Intent = obtainIntent(context) {
        putExtra(KEY_USER, user)
    }

    fun navigate(context: Context, user: User) {
        context.startActivity(obtainIntent(context) {
            putExtra(KEY_USER, user)
        })
    }
}

@Composable
fun UserProfilePageRouter(user: User) {
    val context = LocalContext.current
    val viewModel = viewModel<UserProfileViewModel>(
        factory = UserProfileViewModel.providerFactory(if (user.isSelf) 1 else 2, user as AppUser)
    )

    val profileType = viewModel.profileType
    val uiState = viewModel.states.collectAsStateWithLifecycle().value
    val lifecycleOwner = LocalLifecycleOwner.current
    val currentUserId by rememberUpdatedState(uiState.user.id)

    //region 2.42.0 添加送礼面板

    val giftViewModel = viewModel(modelClass = ChatGiftViewModel::class, factory = viewModelFactory {
        initializer {
            ChatGiftViewModel(user.id, ConversationType.C2C)
        }
    }, viewModelStoreOwner = context.asComponentActivity ?: LocalViewModelStoreOwner.current!!)

    //endregion

    LaunchedEffect(Unit) {
        if (user.id == sUser.id) {
            ShushuUtils.userSet(
                "follower_count" to sUser.followCount,
                "followee_count" to sUser.fansCount,
                "friend_count" to sUser.friendCnt,
            )
        }
    }
    LaunchedEffect(Unit) {
        viewModel.processEvent(ViewEvent.GetUserInfo(currentUserId, TaskRunMode.Local))
        viewModel.processEvent(ViewEvent.GetUserProfileInfo(currentUserId))
        viewModel.effects.onEach { effect ->
            when (effect) {
                is ViewEffect.Toast -> {
                    toast(effect.msg)
                }

                is ViewEffect.RouterTo -> {
                    AppLinkManager.open(context, effect.link)
                }
            }
        }.launchIn(this)
    }

    DisposableEffect(lifecycleOwner) {
        var first = true
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                // 刷新自己的用户信息
                if (!first) {
                    viewModel.processEvent(ViewEvent.GetUserInfo(currentUserId, TaskRunMode.Remote))
                }
                first = false
            }
        }
        lifecycleOwner.lifecycle.addObserver(observer)
        onDispose {
            lifecycleOwner.lifecycle.removeObserver(observer)
        }
    }
    UserProfilePage(profileType = profileType, uiState = uiState, eventFlow = viewModel.eventFlow, onRefresh = { tab ->
        viewModel.processEvent(ViewEvent.GetUserProfileInfo(viewModel.userId))
        tab ?: return@UserProfilePage
        when (tab) {
            is ProfileTab.Cp -> viewModel.processEvent(ViewEvent.GetCpZone(tab.userId))
//            is ProfileTab.GiftWall -> viewModel.processEvent(ViewEvent.GetGiftWall(tab.userId))
            else -> viewModel.processEvent(ViewEvent.OnRefreshEvent(tab))
        }
    }, onToggleBlackUser = {
        viewModel.processEvent(ViewEvent.ToggleBlackUser)
    }, onToggleFollowUser = {
        StatisticReporter.onClick(ReportKey.other_profile_btn_follow)
        viewModel.processEvent(ViewEvent.ToggleFollowUser)
    }, onReportUser = {
        context.startActivity(ReportActivity.newIntent(context, uiState.user.id))
    }, onStartChatUser = {
        context.startActivity(ChatActivity.createIntent(context, uiState.user))
    })
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun UserProfilePage(
    profileType: Int,
    uiState: ViewState,
    eventFlow: Flow<MviState<ViewResult.ProfileViewResult, ViewState>> = emptyFlow(),
    onRefresh: (ProfileTab?) -> Unit = {},
    onToggleFollowUser: () -> Unit = {},
    onToggleBlackUser: () -> Unit = {},
    onReportUser: () -> Unit = {},
    onStartChatUser: () -> Unit = {},
) {
    val pagerState = rememberPagerState {
        uiState.tabs.size
    }

    val state = rememberPullRefreshState(refreshing = uiState.isLoading, onRefresh = {
        onRefresh(uiState.tabs.getOrNull(pagerState.fixCurrentPage))
    })

    val backgroundColor = Color(0xFF070D14)
    BoxWithConstraints(
        modifier = Modifier
            .fillMaxSize()
            .pullRefresh(state)
    ) {
        val scrollState = rememberScrollState()
        var titleBarHeight by remember { mutableFloatStateOf(Float.NaN) }
        var topBoxHeight by remember { mutableFloatStateOf(Float.NaN) }
        var tabRowHeight by remember { mutableFloatStateOf(Float.NaN) }

        val scrollFraction by remember {
            derivedStateOf {
                if (topBoxHeight.isNaN() || titleBarHeight.isNaN()) {
                    return@derivedStateOf 0f
                }
                if (topBoxHeight <= 0) {
                    0f
                } else {
                    scrollState.value.div(topBoxHeight.minus(titleBarHeight)).coerceIn(0f, 1f)
                }
            }
        }

        val nestedScrollConnection = remember {
            object : NestedScrollConnection {
                override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                    if (available.y < 0 && scrollState.canScrollForward) {
                        val deltaY = -available.y
                        val consumedY = -scrollState.dispatchRawDelta(deltaY)
                        return available.copy(y = consumedY)
                    }
                    return Offset.Zero
                }
            }
        }

        val offsetY = 10.dp

        val minHeight by with(LocalDensity.current) {
            remember {
                derivedStateOf {
                    if (titleBarHeight.isNaN() || tabRowHeight.isNaN()) {
                        return@derivedStateOf <EMAIL>(offsetY)
                    }
                    <EMAIL>(titleBarHeight.toDp()).minus(tabRowHeight.toDp())
                }
            }
        }

        val editMode = isPreviewOnCompose

        Column(
            modifier = Modifier
                .fillMaxSize()
                .nestedScroll(nestedScrollConnection)
                .verticalScroll(scrollState)
        ) {
            ProfileTopPage(state = uiState, modifier = Modifier.onSizeChanged {
                topBoxHeight = it.height.toFloat()
            }) {
                onRefresh(null)
            }

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .offset(y = -offsetY)
                    .background(backgroundColor, RoundedCornerShape(topStart = 12.dp, topEnd = 12.dp)),
            ) {

                CompositionLocalProvider(LocalRelationShipEntranceFrom provides profileType) {
                    ProfileUserCard(uiState)
                }

                val context = LocalContext.current

                AppScrollableTabRow(
                    tabs = remember(context, uiState.tabs) {
                        uiState.tabs.map {
                            AppTab(
                                when (it) {
                                    is ProfileTab.Cp -> context.getString(R.string.mine_cp)
                                    is ProfileTab.Relationship -> context.getString(R.string.关系图谱)
                                    is ProfileTab.UserInfo -> if (profileType == 1) context.getString(R.string.个人资料)
                                    else if (uiState.user.isBoy) context.getString(R.string.关于他)
                                    else context.getString(R.string.关于她)

                                    is ProfileTab.GiftWall -> context.getString(R.string.gift_wall)
                                    is ProfileTab.RingInfo -> context.getString(R.string.戒指墙)
                                }
                            )
                        }
                    },
                    modifier = Modifier
                        .padding(bottom = 5.dp)
                        .fillMaxWidth()
                        .height(56.dp)
                        .onSizeChanged {
                            tabRowHeight = it.height.toFloat()
                        },
                    pagerState = pagerState,
                    indicatorColor = Color.White
                ) { index, tab ->
                    val selected = index == pagerState.fixCurrentPage
                    Text(
                        text = tab.name,
                        color = if (selected) Color.White else colorResource(R.color.white_alpha_50),
                        fontSize = 14.sp,
                        fontWeight = if (selected) FontWeight.Medium else FontWeight.Normal,
                        maxLines = 1,
                    )
                }

                HorizontalPager(
                    state = pagerState,
                    verticalAlignment = Alignment.Top,
                    modifier = Modifier.height(minHeight),
                    beyondViewportPageCount = 0,
                ) { index ->
                    when (val tabPage = uiState.tabs[index]) {
                        is ProfileTab.Cp -> {
                            if (editMode) {
                                PreviewCpZonePage()
                            } else {
                                val viewModel = viewModel<UserProfileViewModel>()
                                val cpViewModel = viewModel<CpViewModel>()
                                LaunchedEffect(key1 = tabPage.userId) {
                                    if (tabPage.data.cpZone == null) {
                                        viewModel.processEvent(ViewEvent.GetCpZone(tabPage.userId))
                                    }
                                }
                                CpZonePageRouter(profileType, tabPage.userId, tabPage.data, viewModel, cpViewModel)
                            }
                        }

                        is ProfileTab.Relationship -> {
                            if (editMode) {
                                PreviewRelationShipGraphPage()
                            } else {
                                val profileViewModel = viewModel<UserProfileViewModel>()
                                val viewModel = RelationshipViewModel.rememberViewModel(tabPage, eventFlow)

                                LaunchedEffect(key1 = tabPage.userId) {
                                    profileViewModel.effects.filterIsInstance<ViewEffect.Refresh>().onEach {
                                        if (it.tab is ProfileTab.Relationship) {
                                            viewModel.processEvent(RelationshipMvi.ViewEvent.GetFamilyRelationInfo(it.tab.userId))
                                        }
                                    }.launchIn(this)
                                }

                                RelationShipGraphRouter(uiState.user, viewModel)
                            }
                        }

                        is ProfileTab.UserInfo -> {
                            UserInfoPageV2(uiState.user, tabPage.data)
                        }

                        is ProfileTab.GiftWall -> {
                            if (editMode) {
                                PreviewGiftWallPage()
                            } else {
                                //使用标志符来主导礼物墙内的刷新, 包括 礼物墙 和 礼物墙弹窗
                                val refreshGiftWallFlag = remember {
                                    mutableIntStateOf(0)
                                }
                                val profileViewModel = viewModel<UserProfileViewModel>()
                                LaunchedEffect(key1 = tabPage.userId) {
                                    profileViewModel.effects.filterIsInstance<ViewEffect.Refresh>()
                                        .onEach {
                                            if (it.tab is ProfileTab.GiftWall) {
                                                refreshGiftWallFlag.value = (refreshGiftWallFlag.value % 10) + 1
                                            }
                                        }.launchIn(this)
                                }
                                GiftWallPagerV2(
                                    userId = uiState.user.userId,
                                    profileType = profileType,
                                    refreshFlag = refreshGiftWallFlag.value,
                                    onGiftClicked = { gift, count ->
                                        /**
                                         * 2.42.0 新增
                                         * profileType == 2时, 意味着从别人的个人主页进来
                                         * 如果以上成立且这个礼物是个盲盒礼物, 就唤起礼物面板并返回true
                                         */
                                        if (profileType == 2) {
                                            when (gift.type) {
                                                4 -> {
                                                    if(gift.isBlindboxGift){
                                                        //可以赠送
                                                        //tips: 盲盒id要取负
                                                        val blindBoxId = -gift.blindboxId
                                                        //done 2.42.0 盲盒礼物要唤起礼物面板
                                                        runWithTopActivity {
                                                            (this as? AppCompatActivity)?.let {
                                                                ChatGiftPanelDialogFragment.newInstance(
                                                                    uiState.user.id, blindBoxId, giftCount = count, from = "user_panel_profile"
                                                                ).makeShow(it.supportFragmentManager, "user_panel_profile")
                                                            }
                                                        }
                                                        return@GiftWallPagerV2 true
                                                    }
                                                }
                                                else -> {}
                                            }
                                        }
                                        return@GiftWallPagerV2 false
                                    })
                            }
                        }

                        is ProfileTab.RingInfo -> {
                            val refreshRingWallFlag = remember {
                                mutableStateOf(0)
                            }
                            val profileViewModel = viewModel<UserProfileViewModel>()
                            LaunchedEffect(key1 = tabPage.userId) {
                                profileViewModel.effects.filterIsInstance<ViewEffect.Refresh>()
                                    .onEach {
                                        if (it.tab is ProfileTab.RingInfo) {
                                            refreshRingWallFlag.value = (refreshRingWallFlag.value % 10) + 1
                                        }
                                    }.launchIn(this)
                            }
                            RingWallWidget(userId = uiState.user.userId, profileType = 1, refreshRingWallFlag.value)
                        }
                    }
                }
            }
        }

        TopTitleBar(
            uiState.user, backgroundColor, scrollFraction, Modifier.onSizeChanged {
                titleBarHeight = it.height.toFloat()
            }, onToggleBlackUser = onToggleBlackUser, onReportUser = onReportUser, onToggleFollowUser = onToggleFollowUser
        )

        PullRefreshIndicator(
            refreshing = uiState.isLoading,
            state = state,
            modifier = Modifier.align(Alignment.TopCenter),
            colors = PullRefreshIndicatorDefaults.colors(
                containerColor = colorResource(id = R.color.black_500),
                contentColor = colorResource(id = R.color.color_primary_pink),
            ),
            scale = true
        )

        if (profileType == 2) {
            Row(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .fillMaxWidth()
                    .background(
                        Brush.verticalGradient(
                            listOf(Color(0x00070D14), Color(0x99070D14), Color(0xFF070D14))
                        )
                    )
                    .padding(top = 38.dp, bottom = 54.dp, start = 16.dp, end = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {

                val hideCpButton by if (isEditOnCompose) {
                    remember {
                        mutableStateOf(false)
                    }
                } else {
                    val configState = UIConfig.configFlow.collectAsStateWithLifecycle()
                    remember(uiState.user.gender) {
                        derivedStateOf {
                            sUserFlow.value.gender == uiState.user.gender ||
                                    sUserFlow.value.cp?.id == uiState.user.id ||
                                    configState.value.getOrNull()?.hideCpAbility != false
                        }
                    }
                }

                if (!hideCpButton) {
                    val scope = rememberCoroutineScope()
                    val context = LocalContext.current
                    ActionButton(
                        modifier = Modifier
                            .height(48.dp)
                            .weight(1f),
                        brush = Brush.linearGradient(listOf(Color(0xFFFD9BFF), Color(0xFFFF42BF))),
                        resId = R.drawable.ic_cp_user,
                        text = stringResource(id = R.string.match_cp)
                    ) {
                        scope.launch {
                            UserManager.userRepository.getHaveCpInfoNotCheck(uiState.user.id).onSuccess { ret ->
                                MainNavigator.start(context, MainNavigator.BECOME_CP) {
                                    it.putExtra(Const.KEY_DATA, ret)
                                }
                            }.toastError()
                        }
                    }
                }

                ActionButton(
                    modifier = Modifier
                        .requiredWidthIn(max = 215.dp)
                        .height(48.dp)
                        .weight(1f),
                    brush = Brush.linearGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))),
                    resId = R.drawable.ic_chat_white,
                    text = stringResource(id = R.string.chat)
                ) {
                    onStartChatUser()
                }
            }
        }
    }
}

@Composable
private fun ActionButton(modifier: Modifier, brush: Brush, @DrawableRes resId: Int, text: String, onClick: () -> Unit) {
    Row(
        modifier = modifier
            .clip(CircleShape)
            .background(brush)
            .clickable(onClick = onClick)
            .padding(horizontal = 6.dp),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(4.dp, Alignment.CenterHorizontally)
    ) {
        Image(
            painter = painterResource(id = resId),
            contentDescription = null,
            modifier = Modifier.size(24.dp, 24.dp)
        )
        AnimatedContent(text, label = "") {
            AutoSizeText(text = it, color = Color.White, fontSize = 14.sp, maxLines = 2, alignment = Alignment.Center)
        }
    }
}


@Preview
@Composable
fun PreviewUserProfilePage() {
    val user: AppUser = userForPreview
    UserProfilePage(
        2,
        ViewState(
            false, user, listOf(
                ProfileTab.UserInfo(userId = "0", data = userForPreview.run {
                    buildList {
                        add(UserInfoItem.Moment(emptyList()))
                        add(UserInfoItem.AttractiveLabel(attractiveFlags))
                        add(UserInfoItem.Signature(shortIntro))
                        add(UserInfoItem.Tribe(tribe))
                    }.sortedDescending()
                }),
                ProfileTab.Cp(
                    userId = "0", data = CpZoneWrapper(
                        cpZone = CpZone(
                            60, 100, 233, userForPreview, userForPreview, CpTask()
                        ), cpRelationship = Relationship.Cp.previewSimple1()
                    )
                ),
                ProfileTab.Relationship(userId = "0", data = CpGraph(Relationship.Cp.previewSimple1(), "0", 0)),
                ProfileTab.GiftWall(userId = "0", data = emptyList()),
            )
        ),
    )
}

