package com.qyqy.ucoo.compose.data

import com.qyqy.ucoo.compose.ui.photo.IImageSource
import com.qyqy.ucoo.compose.ui.photo.IPhotoPreviewModel
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.mine.imageAspectRatio
import com.qyqy.ucoo.mine.isVideo
import com.qyqy.ucoo.mine.url
import kotlinx.parcelize.Parcelize

@Parcelize
data class MomentPreviewItem(
    val id: Int,
    val indexOfMoment: Int,
    val mediaInfo: MediaInfo,
) : IPhotoPreviewModel {

    override val key: String
        get() = mediaInfo.id
    override val aspectRatio: Float
        get() = mediaInfo.imageAspectRatio
    override val transitionSource: IImageSource
        get() = IImageSource.Url(mediaInfo.url)
    override val previewSource: IImageSource
        get() = transitionSource
    override val originSource: IImageSource
        get() = previewSource
    override val isVideo: Boolean
        get() = mediaInfo.isVideo

}
