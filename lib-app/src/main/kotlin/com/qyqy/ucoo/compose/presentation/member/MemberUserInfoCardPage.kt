package com.qyqy.ucoo.compose.presentation.member

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage


@Composable
fun ActivatedMemberCard(
    user: User,
    modifier: Modifier = Modifier,
    activeDate: String = "有效期至2023-09-09",
) {
    Row(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(343f / 100)
            .paint(painterResource(id = R.drawable.bg_vip_yes), contentScale = ContentScale.FillBounds)
            .padding(start = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        CircleComposeImage(
            model = user.avatarUrl,
            modifier = Modifier
                .fillMaxHeight(0.6f)
                .aspectRatio(1f)
                .heightIn(max = 60.dp),
        )

        Column(modifier = Modifier.padding(start = 12.dp)) {
            AppText(
                text = user.nickname,
                color = MemberCenter.Color.Text1,
                fontSize = 16.sp,
                fontWeight = FontWeight.ExtraBold,
            )

            Spacer(modifier = Modifier.height(12.dp))

            AppText(
                text = activeDate,
                color = MemberCenter.Color.Text2,
                fontSize = 12.sp,
                fontWeight = FontWeight.Normal,
            )
        }
    }
}

@Preview
@Composable
fun ActivatedMemberCardPreview() {
    ActivatedMemberCard(userForPreview)
}

@Composable
fun NotActiveMemberCard(
    memberCount: Int,
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxWidth()
            .aspectRatio(1029f / 336)
            .paint(painterResource(R.drawable.header_charge_member), contentScale = ContentScale.FillBounds)
            .padding(start = 24.dp)
    ) {
        Spacer(modifier = Modifier.fillMaxHeight(5f / 18))

        Text(
            text = stringResource(id = R.string.加入UCOO会员大家庭_享受更多社交乐趣),
            color = MemberCenter.Color.Text1,
            fontSize = 16.sp,
            fontWeight = FontWeight.ExtraBold,
        )

        Spacer(modifier = Modifier.fillMaxHeight(0.25f))

        Text(
            text = stringResource(id = R.string.format_active_member_count, memberCount),
            color = MemberCenter.Color.Text1,
            fontSize = 12.sp,
            fontWeight = FontWeight.Light,
        )
    }
}

@Preview(widthDp = 375)
@Composable
fun NotActiveVipCardPreview() {
    NotActiveMemberCard(100)
}

