package com.qyqy.ucoo.compose.presentation.ff

import android.graphics.drawable.Drawable
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.defaultMinSize
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.compose.composePlaceholderDrawable
import com.qyqy.ucoo.compose.theme.colorTheme
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.im.bean.FamilyRelation
import com.qyqy.ucoo.im.bean.colorSmallFont
import com.qyqy.ucoo.im.bean.colorSmallFontBg

/**
 * 邀请加入亲友团
 */
@Composable
fun InviteToFamilyUserItem(
    user: UserInfo,
    placeholderDrawable: Drawable = composePlaceholderDrawable(),
    onInvite: () -> Unit = {}
) {
    UserItem(user = user, placeholderDrawable) {
        Button(
            onClick = onInvite,
            contentPadding = PaddingValues(15.dp, 12.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorTheme),
        ) {
            Text(
                text = stringResource(id = R.string.send_invite),
                color = Color.White,
                fontSize = 12.sp,
            )
        }
    }
}


/**
 * 解除亲友团
 */
@Composable
fun RemoveFriendItem(
    user: FamilyRelation,
    placeholderDrawable: Drawable = composePlaceholderDrawable(),
    onRemove: () -> Unit = {}
) {
    UserItem(user = user.user, placeholderDrawable = placeholderDrawable, afterNickName = {
        Spacer(modifier = Modifier.width(4.dp))
        AppText(
            text = user.label.name,
            color = user.label.colorSmallFont,
            textAlign = TextAlign.Center,
            fontSize = 11.sp,
            lineHeight = 18.sp,
            modifier = Modifier
                .defaultMinSize(34.dp, 18.dp)
                .background(user.label.colorSmallFontBg, shape = RoundedCornerShape(4.dp))
        )
    }) {
        Button(
            onClick = onRemove,
            contentPadding = PaddingValues(15.dp, 12.dp),
            colors = ButtonDefaults.buttonColors(containerColor = colorTheme),
        ) {
            AppText(
                text = stringResource(id = R.string.remote_relation),
                color = Color.White,
                fontSize = 12.sp,
            )
        }
    }
}

//https://picsum.photos/id/106/400/300
private val userInfoPreview = UserInfo().apply {
    nickname = userForPreview.nickname
    age = userForPreview.age
    gender = userForPreview.gender
}


@Preview(showBackground = true, backgroundColor = 0xFF000000)
@Composable
fun InviteToFamilyUserItemPreviewer() {
    InviteToFamilyUserItem(user = userInfoPreview)
}

object PreviewSource {
    const val randomImageUrl = "https://picsum.photos/id/106/400/300"
}