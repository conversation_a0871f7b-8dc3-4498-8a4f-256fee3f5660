package com.qyqy.ucoo.compose.ui.newusers

import android.Manifest
import android.os.Parcelable
import androidx.activity.compose.BackHandler
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.overseas.common.utils.dpF
import com.qyqy.cupid.widgets.click
import com.qyqy.cupid.widgets.rememberPermissionLauncher
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.compose.LaunchOnceEffect
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.im.bean.RoomTokenRequest
import com.qyqy.ucoo.utils.VoiceRoomHelper
import com.qyqy.ucoo.utils.blur.BlurTransformation
import jp.wasabeef.glide.transformations.RoundedCornersTransformation
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable


@Parcelize
@Serializable
data class GuideInfo(
    val desc: String = "", // 她是官方匹配的专属...
    @SerialName("guide_audioroom_id")
    val guideAudioroomId: Int = 0, // 13
    @SerialName("guide_private_room_id")
    val guidePrivateRoomId: Int = 0, // 12
    @SerialName("guide_user_extra")
    val guideUserExtra: GuideUserExtra = GuideUserExtra(),
    val title: String = "", // 欢迎来到UCOO
    @SerialName("guide_user")
    val user: AppUser,
) : Parcelable

@Parcelize
@Serializable
data class GuideUserExtra(
    @SerialName("attractive_tags")
    val attractiveTags: List<AttractiveFlag> = listOf(),
    @SerialName("constellation_label")
    val constellationLabel: String = "", // 金牛座
) : Parcelable


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun NewcomerReceptionGuideScreen(info: GuideInfo, firstEnter: Boolean) {
    BackHandler(firstEnter) {

    }

    val context = LocalContext.current
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(painterResource(id = R.drawable.bg_new_user_onboarding), contentScale = ContentScale.Crop),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (firstEnter) {
            Spacer(modifier = Modifier.height(62.dp))
        } else{
            Image(
                painter = painterResource(id = R.drawable.ic_close_page),
                contentDescription = "close",
                modifier = Modifier
                    .statusBarsPadding()
                    .padding(end = 10.dp)
                    .padding(8.dp)
                    .click(onClick = {
                        context.asActivity?.finish()
                    })
                    .size(24.dp)
                    .align(Alignment.End)
            )
        }

        Image(
            painter = painterResource(id = R.drawable.ic_welcome_ucoo_new_user),
            contentDescription = null,
            modifier = Modifier
                .height(32.dp),
            contentScale = ContentScale.FillHeight
        )

        Box(
            modifier = Modifier
                .padding(24.dp)
                .fillMaxWidth()
                .aspectRatio(327f / 458f)
                .border(
                    width = 4.dp,
                    brush = Brush.verticalGradient(
                        colors = listOf(
                            Color(0xFF88DBFF),
                            Color(0xFFFFF493),
                            Color(0xFFAF64F5)
                        )
                    ),
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(4.dp)
        ) {
            ComposeImage(
                model = info.user.avatarUrl,
                modifier = Modifier.fillMaxSize()
            )

            Column(
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(start = 16.dp, bottom = 16.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = info.user.nickname,
                        color = Color.White,
                        fontSize = 18.sp
                    )

                    if (info.user.hasCertified) {
                        Row(
                            modifier = Modifier
                                .padding(start = 4.dp)
                                .background(Color(0xFF6D3E23), CircleShape)
                                .padding(start = 4.dp, top = 3.dp, bottom = 3.dp, end = 5.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_user_real_name),
                                contentDescription = null,
                                modifier = Modifier.size(10.dp)
                            )

                            Text(
                                text = stringResource(id = R.string.已认证),
                                color = Color(0xFFFFD7C0),
                                fontSize = 8.sp,
                                lineHeight = 8.sp
                            )
                        }
                    }
                }

                val userDesc = remember(info, context) {
                    buildString {
                        append(info.user.age)
                        append(context.getString(R.string.岁))

                        if (info.user.height > 0) {
                            append("丨")
                            append(info.user.height)
                            append("cm")
                        }

                        if (info.guideUserExtra.constellationLabel.isNotEmpty()) {
                            append("丨")
                            append(info.guideUserExtra.constellationLabel)
                        }
                    }
                }

                Text(
                    text = userDesc,
                    color = Color.White,
                    fontSize = 14.sp
                )

                FlowRow(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(6.dp)
                ) {
                    info.guideUserExtra.attractiveTags.forEach {
                        Row(
                            modifier = Modifier
                                .background(Color(0x33FFFFFF), RoundedCornerShape(4.dp))
                                .padding(6.dp),
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(1.5.dp)
                        ) {
                            ComposeImage(
                                model = it.url,
                                modifier = Modifier.size(12.dp)
                            )

                            Text(
                                text = it.label,
                                color = Color.White,
                                fontSize = 12.sp
                            )
                        }
                    }
                }
            }
        }

        val scope = rememberCoroutineScope()

        val permissionLauncher = rememberPermissionLauncher(false, {  // 拒绝了权限
            if (firstEnter) {
                scope.launch {
                    VoiceRoomHelper.joinVoiceChatRoom(info.guideAudioroomId, RoomTokenRequest(false), true)
                    context.asActivity?.finish()
                }
            } else {
                context.asActivity?.finish()
            }
        }) {  // 同意了权限
            scope.launch {
                VoiceRoomHelper.joinVoiceChatRoom(info.guidePrivateRoomId, RoomTokenRequest(true), true)
                context.asActivity?.finish()
            }
        }

        var downCounter by remember {
            mutableIntStateOf(0)
        }

        var launched by remember {
            mutableStateOf(false)
        }

        LaunchOnceEffect(Unit) {
            downCounter = 10
            while (downCounter > 0) {
                delay(1000)
                downCounter--
            }
            if (!launched) {
                if (firstEnter) {
                    launched = true
                    permissionLauncher.launch(arrayOf(Manifest.permission.RECORD_AUDIO))
                } else {
                    context.asActivity?.finish()
                }
            }
        }

        Text(
            text = info.desc,
            modifier = Modifier.padding(top = 12.dp, start = 24.dp, end = 24.dp),
            fontSize = 14.sp,
            color = Color.White.copy(0.5f),
            textAlign = TextAlign.Center
        )

        Box(
            modifier = Modifier
                .padding(top = 16.dp)
                .size(255.dp, 48.dp)
                .clip(CircleShape)
                .background(Brush.horizontalGradient(listOf(Color(0xFFAF64F5), Color(0xFF8B56FC))), CircleShape)
                .click(enabled = downCounter > 0) {
                    launched = true
                    permissionLauncher.launch(arrayOf(Manifest.permission.RECORD_AUDIO))
                },
            contentAlignment = Alignment.Center,
        ) {
            Row(verticalAlignment = Alignment.Bottom) {
                Text(
                    text = stringResource(id = R.string.接受邀请),
                    color = Color.White,
                    fontSize = 16.sp,
                    lineHeight = 16.sp
                )
                Text(
                    text = "(${downCounter}s)",
                    modifier = Modifier.padding(start = 1.dp, bottom = 2.dp),
                    color = Color.White,
                    fontSize = 12.sp,
                    lineHeight = 12.sp
                )
            }
        }
    }
}

@Preview
@Composable
fun PreviewNewcomerReceptionGuideScreen() {
    NewcomerReceptionGuideScreen(GuideInfo(user = userForPreview, desc = "她正在找人语音连麦聊天..."), false)
}

@Composable
fun NewcomerReceptionPhotoItem(
    avatarUrl: String,
    title: String?,
    desc: String?,
    url: String?,
    blur: Boolean,
    flagResId: Int,
    onClickAvatar: () -> Unit,
    onClickImage: () -> Unit,
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(10.dp)
            .padding(end = 25.dp)
    ) {
        CircleComposeImage(
            model = avatarUrl,
            modifier = Modifier
                .padding(end = 10.dp)
                .size(30.dp)
                .click(onClick = onClickAvatar)
        )

        Column(
            modifier = Modifier
                .background(Color(0x0DFFFFFF), Shapes.extraSmall)
                .padding(12.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = title ?: stringResource(id = R.string.对方发送了一张个人私密照片),
                color = Color(0xFFFFFFFF),
                fontSize = 14.sp,
                lineHeight = 14.sp
            )

            ComposeImage(
                model = url ?: painterResource(id = R.drawable.ic_blur_pic_example),
                modifier = Modifier
                    .size(160.dp)
                    .click(enabled = url != null && !blur, noEffect = true, onClick = onClickImage),
            ) {
                if (blur) {
                    it.transform(BlurTransformation(16, 4), CenterCrop(), RoundedCornersTransformation(4.dpF.toInt(), 4.dpF.toInt()))
                } else {
                    it.transform(CenterCrop(), RoundedCornersTransformation(4.dpF.toInt(), 4.dpF.toInt()))
                }
            }

            Row(verticalAlignment = Alignment.CenterVertically) {
                Image(
                    painter = painterResource(id = flagResId),
                    contentDescription = null,
                    modifier = Modifier
                        .padding(end = 8.dp)
                        .size(32.dp)
                )

                Text(
                    text = desc ?: stringResource(id = R.string.与对方互发消息3个回合),
                    color = Color(0xFFFF82C3),
                    fontSize = 12.sp,
                    lineHeight = 16.sp
                )
            }
        }
    }
}