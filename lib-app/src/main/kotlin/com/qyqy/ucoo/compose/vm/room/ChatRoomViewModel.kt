package com.qyqy.ucoo.compose.vm.room

import com.qyqy.ucoo.compose.presentation.room.MessageItem
import com.qyqy.ucoo.im.compat.MessageBundle

sealed interface UIAction {

    data class OnClickImage(val item: MessageItem.UserImageContent) : UIAction

    data class OnSendText(val content: String) : UIAction

    data class OnSendMessage(val content: MessageBundle) : UIAction

    object OnGameCenter : UIAction

    object OnOpenGift : UIAction

    data class JoinAudioRoom(val roomId: Int,val userId:String):UIAction

}