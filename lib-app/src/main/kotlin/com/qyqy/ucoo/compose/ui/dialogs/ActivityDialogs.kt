package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.widthIn
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.utils.OnClick

@Composable
fun LotteryDialogContent(
    title: String,
    icon: String,
    name: String,
    days: String,
    modifier: Modifier = Modifier,
    onClick: OnClick = {}
) {
    Column(
        modifier = modifier
            .background(Color(0xFFE26EFF), Shapes.corner12)
            .border(1.dp, Color(0xFFFFE789))
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = title,
            fontSize = 15.sp,
            color = Color.White,
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.height(12.dp))
        ComposeImage(
            model = icon,
            modifier = Modifier
                .size(72.dp)
                .clip(Shapes.small),
            contentScale = ContentScale.Crop
        )
        Spacer(modifier = Modifier.height(12.dp))
        val day = stringResource(id = R.string.format_day_count_toge)
        Text(text = "$name\n$days", textAlign = TextAlign.Center, color = Color.White)
        Spacer(modifier = Modifier.height(12.dp))
        AppButton(
            text = stringResource(id = R.string.我知道了),
            color = Color(0xFF8E4611),
            fontSize = 16.sp,
            modifier = Modifier
                .widthIn(min = 114.dp)
                .background(Color(0xFFFFE789), Shapes.chip),
            onClick = onClick
        )
    }
}

@Preview
@Composable
private fun LotteryPreview() {
    LotteryDialogContent(
        title = "赠人祝福，手有余香！恭喜您获得以下奖励，可在我的装扮中查看：",
        icon = "",
        name = "装扮名称",
        days = "xx天"
    )
}