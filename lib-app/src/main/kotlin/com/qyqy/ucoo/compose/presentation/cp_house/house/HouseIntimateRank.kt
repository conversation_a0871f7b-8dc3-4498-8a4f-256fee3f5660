package com.qyqy.ucoo.compose.presentation.cp_house.house

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.produceState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.pages.CPHouseNavigator
import com.qyqy.ucoo.compose.presentation.cp_house.CPHouseApi
import com.qyqy.ucoo.compose.presentation.cp_house.bean.HRank
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.viewModelOfActivity
import com.qyqy.ucoo.compose.vm.cp_house.CPHouseViewModel
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.OnClick
import com.qyqy.ucoo.utils.TaskHelper
import kotlinx.coroutines.delay
import java.util.Locale

@Composable
fun HouseIntimateRankScreen() {
    val data by produceRankData()
    val myRoom = data.myRoom
    val mySelf by sUserFlow.collectAsStateWithLifecycle()
    val text = LocalHouseText.current
    val backOwner = LocalOnBackPressedDispatcherOwner.current
    val vm = viewModelOfActivity<CPHouseViewModel>()
    val context = LocalContext.current
    Content(list = data.rankingData, loading = data.loading, onClickRule = {
        AppLinkManager.open(context, data.rule)
    }) {
        if (!data.loading) {
            if (myRoom != null) {
                val target = if (mySelf.id == myRoom.femaleUser.id) {
                    myRoom.maleUser
                } else {
                    myRoom.femaleUser
                }
                BottomBar(
                    avatarLeft = myRoom.maleUser.avatarUrl,
                    avatarRight = myRoom.femaleUser.avatarUrl,
                    title = stringResource(id = text.formatNamePair, target.nickname),
                    desc = myRoom.tips,
                    intimateStr = formatValue(value = myRoom.intimacyScore),
                    buttonText = stringResource(id = text.upNow),
                    buttonVisible = false,
                    rankString = if (myRoom.ranking > 100 || myRoom.ranking < 1) stringResource(id = text.noRank) else myRoom.ranking.toString(),
                    onClick = {
                        backOwner?.onBackPressedDispatcher?.onBackPressed()
                        vm?.sendAction(CPHouseViewModel.Action.ShowTask, 500L)
                    }
                )
            } else {
                BottomBar(
                    avatarLeft = mySelf.avatarUrl,
                    avatarRight = "",
                    rankString = stringResource(id = text.noRank),
                    noRank = true,
                    title = stringResource(id = text.emptyTitle),
                    desc = stringResource(id = text.emptyDesc),
                    intimateStr = "",
                    buttonText = stringResource(id = text.emptyButton),
                    onClick = {
                        TaskHelper.routerChatWithCpInvite()
                    }
                )
            }
        }
    }
}

@Composable
private fun formatValue(value: Int): String {
    val text = LocalHouseText.current
    return stringResource(
        id = text.formatInteRank,
        if (value > 99999) "${value / 10000}w" else value.toString()
    )
}

@Composable
private fun Content(
    list: List<IHouseIntimateRankItem>,
    loading: Boolean = false,
    onClickRule: OnClick = {},
    bottomBar: @Composable () -> Unit = {}
) {
    val text = LocalHouseText.current
    val context = LocalContext.current
    val bo = LocalOnBackPressedDispatcherOwner.current
    Scaffold(
        containerColor = Color.Transparent,
        modifier = Modifier
            .fillMaxSize()
            .background(Brush.verticalGradient(listOf(Color(0xFFC174FF), Color(0xFFD3CFF9))))
            .statusBarsPadding(),
        bottomBar = bottomBar,
        topBar = {
            AppTitleBar(title = stringResource(id = R.string.亲密周榜), onBack = {
                bo?.onBackPressedDispatcher?.onBackPressed()
            }) {
                if (!loading) {
                    Text(
                        text = stringResource(id = text.rankRule),
                        color = Color.White,
                        modifier = Modifier
                            .clickWithShape(onClick = onClickRule)
                            .align(Alignment.CenterEnd)
                            .padding(16.dp, 8.dp)
                    )
                }
            }
        }) { pd ->
        Column(modifier = Modifier.padding(pd)) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                if (loading) {
                    CircularProgressIndicator(
                        modifier = Modifier
                            .requiredSize(20.dp)
                            .align(Alignment.Center),
                        strokeWidth = 2.dp,
                        color = Color.White
                    )
                } else {
                    if (list.isEmpty()) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally,
                            modifier = Modifier.align(Alignment.Center)
                        ) {
                            Image(
                                painter = painterResource(id = R.drawable.ic_rank_empty),
                                contentDescription = "empty",
                                modifier = Modifier.size(120.dp)
                            )
                            Spacer(modifier = Modifier.height(12.dp))
                            AppText(
                                text = stringResource(id = text.houseRankEmpty),
                                color = Color.White.copy(alpha = 0.5f),
                                fontSize = 12.sp
                            )
                        }
                    } else {
                        //可优化当item可见再播放特效
                        LazyColumn(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(horizontal = 16.dp)
                        ) {
                            itemsIndexed(list) { index, item ->
                                IntimateRankItem(item = item, rank = index + 1, onGoHouse = {
                                    CPHouseNavigator.navigate(context, item.id)
                                })
                                Spacer(modifier = Modifier.height(if (index < 3) 4.dp else 8.dp))
                            }
                        }
                    }

                }
            }
        }
    }
}


interface IHouseIntimateRankItem {
    val userLeft: User
    val userRight: User
    val effectPath: String
    val value: Int
    val isHidden: Boolean
    val id: Int
}

data class RankItem(
    override val userLeft: User,
    override val userRight: User,
    override val value: Int = 0,
    override val effectPath: String = "",
    override val isHidden: Boolean = false,
    override val id: Int = 0
) : IHouseIntimateRankItem

@Composable
private fun IntimateRankItem(
    item: IHouseIntimateRankItem,
    rank: Int = 1,
    onGoHouse: OnClick = {}
) {
    val text = LocalHouseText.current
    val ratio = if (rank < 4) 343 / 152f else 343 / 126f
    val bg = when (rank) {
        1 -> R.drawable.bg_intimate_rank1
        2 -> R.drawable.bg_intimate_rank2
        3 -> R.drawable.bg_intimate_rank3
        else -> R.drawable.bg_intimate_rank_other
    }
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(ratio)
            .paint(painterResource(id = bg), contentScale = ContentScale.Crop)
            .padding(top = if (rank < 4) 26.dp else 0.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Box(modifier = Modifier.width(36.dp), contentAlignment = Alignment.Center) {
            if (rank > 3) {
                AppText(
                    text = rank.toString(),
                    color = Color(0xFF5A220A),
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
            }
        }
        val modU = Modifier
            .weight(1f)
            .padding(horizontal = 4.dp)
        val modA = Modifier
            .size(64.dp)
            .clip(CircleShape)
            .border(1.5.dp, Color.White, CircleShape)
        Column(modifier = modU, horizontalAlignment = Alignment.CenterHorizontally) {
            if (item.isHidden) {
                Image(
                    painter = painterResource(id = R.drawable.ic_secr),
                    contentDescription = "",
                    modifier = modA
                )
            } else {
                ComposeImage(model = item.userLeft.avatarUrl, modA.click(onClick = onGoHouse))
            }
            Spacer(modifier = Modifier.height(8.dp))
            AppText(
                text = if (item.isHidden) stringResource(id = text.nameHidden) else item.userLeft.nickname,
                color = Color(0xFF5A220A),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
        Column {
            Box(modifier = Modifier.size(80.dp)) {
                ComposeImage(model = item.effectPath)
            }

            AppText(
                text = formatValue(value = item.value),
                color = Color(0xFF6624A7),
                fontSize = 12.sp,
                lineHeight = 20.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .widthIn(min = 84.dp)
                    .background(
                        Brush.horizontalGradient(
                            listOf(
                                Color(0xFFF3E6FF),
                                Color(0xFFDEBDFF)
                            )
                        ), Shapes.chip
                    )
                    .border(0.5.dp, Color(0xFFC792FD), Shapes.chip)
            )
        }
        Column(modifier = modU, horizontalAlignment = Alignment.CenterHorizontally) {
            if (item.isHidden) {
                Image(
                    painter = painterResource(id = R.drawable.ic_secr),
                    contentDescription = "",
                    modifier = modA
                )
            } else {
                ComposeImage(model = item.userRight.avatarUrl, modA.click(onClick = onGoHouse))
            }
            Spacer(modifier = Modifier.height(8.dp))
            AppText(
                text = if (item.isHidden) stringResource(id = text.nameHidden) else item.userRight.nickname,
                color = Color(0xFF5A220A),
                maxLines = 1,
                overflow = TextOverflow.Ellipsis
            )
        }
        Box(modifier = Modifier.width(36.dp))
    }
}

@Composable
private fun BottomBar(
    avatarLeft: String,
    avatarRight: String,
    title: String,
    desc: String,
    intimateStr: String,
    buttonText: String,
    buttonVisible: Boolean = true,
    rankString: String = "",
    noRank: Boolean = false,
    onClick: OnClick = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Brush.horizontalGradient(listOf(Color(0xFFC073FF), Color(0xFF8579FF))))
            .navigationBarsPadding()
    ) {
        HorizontalDivider(
            modifier = Modifier.fillMaxWidth(),
            thickness = 2.dp,
            color = Color(0xFFEAE3FF)
        )
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            AppText(
                text = rankString,
                modifier = Modifier.width(38.dp),
                textAlign = TextAlign.Center,
                fontWeight = FontWeight.Bold,
                color = Color.White,
                fontSize = if (noRank) 12.sp else 14.sp
            )
            Box(modifier = Modifier.size(68.dp, 40.dp)) {
                ComposeImage(
                    model = avatarLeft,
                    modifier = Modifier
                        .size(40.dp)
                        .clip(CircleShape)
                        .border(1.dp, Color.White, CircleShape)
                )
                if (avatarRight.isEmpty()) {
                    Image(
                        painter = painterResource(id = R.drawable.empty_avatar),
                        modifier = Modifier
                            .size(40.dp)
                            .align(Alignment.CenterEnd),
                        contentDescription = "empty_avatar"
                    )
                } else {
                    ComposeImage(
                        model = avatarRight,
                        modifier = Modifier
                            .size(40.dp)
                            .clip(CircleShape)
                            .border(1.dp, Color.White, CircleShape)
                            .align(Alignment.CenterEnd)
                    )
                }
            }
            Column(
                modifier = Modifier
                    .weight(1f)
                    .padding(start = 8.dp)
            ) {
                AppText(text = title, color = Color.White)
                Spacer(modifier = Modifier.height(6.dp))
                AppText(text = desc, color = Color.White, fontSize = 12.sp)
            }

            Column(
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (intimateStr.isNotEmpty()) {
                    AppText(text = intimateStr, fontSize = 12.sp, color = Color.White)
                    Spacer(modifier = Modifier.height(6.dp))
                }
                if (buttonVisible) {
                    AppText(
                        text = buttonText,
                        modifier = Modifier
                            .widthIn(min = 80.dp)
                            .clickWithShape(Shapes.chip, onClick = onClick)
                            .background(Color(0xFFFFE55D), Shapes.chip)
                            .padding(horizontal = 2.dp),
                        lineHeight = 28.sp,
                        fontSize = 14.sp,
                        textAlign = TextAlign.Center,
                        color = Color(0xFF5A220A),
                        fontWeight = FontWeight.Medium
                    )
                }
            }

            Spacer(modifier = Modifier.width(16.dp))
        }
    }
}

@Preview(device = "spec:id=reference_phone,shape=Normal,width=411,height=891,unit=dp,dpi=420")
@Composable
private fun Preview() {
    val list = buildList {
        repeat(5) {
            add(RankItem(userForPreview.copy(nickname = "用户昵称昵称用户昵称昵称用户昵称昵称"), userForPreview.copy(nickname = "用户昵称昵称用户昵称昵称"), 999, ""))
        }
    }
    Column {
        Content(list = list) {
            BottomBar(
                avatarLeft = "",
                avatarRight = "",
                title = "我和小可爱",
                desc = "两线第二名99亲密度两线第二名99亲密度两线第二名99亲密度",
                intimateStr = "亲密度999w",
                buttonText = "立即上榜"
            )
        }
        Content(list = emptyList()) {
            BottomBar(
                avatarLeft = "",
                avatarRight = "",
                title = "我和小可爱",
                desc = "两线第二名99亲密度",
                intimateStr = "亲密度999w",
                buttonText = "立即上榜"
            )
        }
    }
}


@Composable
private fun produceRankData(): ComposeState<HRank> {
    val vm = viewModelOfActivity<CPHouseViewModel>()
    val cpHouseApi = createApi<CPHouseApi>()

    return produceState(
        initialValue = vm?.rankState?.value ?: HRank(
            loading = true,
            myRoom = HRank.Room(INVALID_USER, INVALID_USER, 0, 0, ""),
            rankingData = emptyList()
        )
    ) {
        while (true) {
            runApiCatching { cpHouseApi.getRankList() }
                .onSuccess {
                    vm?.cacheRank(it)
                    this.value = it.copy(loading = false)
                }.toastError()
            delay(5000L)
        }
    }
}