package com.qyqy.ucoo.compose.theme

import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import com.qyqy.ucoo.compose.ui.LoadingLayout


@Composable
fun AppTheme(content: @Composable () -> Unit) {
    val colorScheme =
        MaterialTheme.colorScheme.copy(
            primary = colorTheme,
            onPrimary = Color.White,
            surface = Color(0xFF222222),
            onSurface = Color.White.copy(0.5f),
            background = colorPageBackground,
            onBackground = Color(0xFF222222),
        )
    MaterialTheme(colorScheme = colorScheme) {
        LoadingLayout {
            content()
        }
    }
}

@Composable
fun ButtonDefaults.themeButtonColors() =
    buttonColors(disabledContainerColor = Color(0xFF5B4091), disabledContentColor = Color(0xFF919191))