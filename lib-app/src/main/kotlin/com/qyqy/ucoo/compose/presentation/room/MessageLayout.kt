package com.qyqy.ucoo.compose.presentation.room

import android.content.Context
import androidx.compose.animation.core.Animatable
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyItemScope
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.ClickableText
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.platform.LocalLayoutDirection
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.TextUnit
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.viewinterop.NoOpUpdate
import androidx.core.graphics.toColorInt
import androidx.core.util.SizeFCompat
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.im.messages.MessageReminder
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.R
import com.qyqy.ucoo.UserPartition
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.hasRelationship
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.nameColor
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.compose.data.GameInvite
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.page.ChatGroupDestination
import com.qyqy.ucoo.compose.presentation.chatgroup.page.GroupCreatedCard
import com.qyqy.ucoo.compose.presentation.chatgroup.page.LocalMsgEventHandler
import com.qyqy.ucoo.compose.presentation.chatgroup.page.MsgEvents
import com.qyqy.ucoo.compose.presentation.config.LocalChatUIProvider
import com.qyqy.ucoo.compose.presentation.profile.UserProfileNavigator
import com.qyqy.ucoo.compose.presentation.voice.VoiceIcon
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AgeGender
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.EvaAnimState
import com.qyqy.ucoo.compose.ui.photo.LocalPhotoPreviewState
import com.qyqy.ucoo.compose.ui.photo.previewKeyModifier
import com.qyqy.ucoo.compose.ui.toAnnotatedString
import com.qyqy.ucoo.compose.vm.room.UIAction
import com.qyqy.ucoo.config.LevelConfig
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.glide.SourceDensity
import com.qyqy.ucoo.glide.ninepatch.BubbleTheme
import com.qyqy.ucoo.glide.ninepatch.NinePathLoader
import com.qyqy.ucoo.glide.ninepatch.rememberNinePatchPainter
import com.qyqy.ucoo.glide.rememberPainterByUrl
import com.qyqy.ucoo.im.bean.AudioRoom
import com.qyqy.ucoo.im.bean.GiftWrapper
import com.qyqy.ucoo.im.bean.RoomSettings
import com.qyqy.ucoo.im.compat.ImageElem
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.compat.UCEmojiMessage
import com.qyqy.ucoo.im.compat.UCGiftMessage
import com.qyqy.ucoo.im.compat.UCImageMessage
import com.qyqy.ucoo.im.compat.UCInstanceMessage
import com.qyqy.ucoo.im.compat.UCNewMsgTagMessage
import com.qyqy.ucoo.im.compat.UCTextMessage
import com.qyqy.ucoo.im.compat.UCTimestampMessage
import com.qyqy.ucoo.im.compat.UCVoiceMessage
import com.qyqy.ucoo.im.compat.chat.FakerMessageEntry
import com.qyqy.ucoo.im.compat.chat.MessageEntry
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.im.compat.chat.utils.appendPrefixText
import com.qyqy.ucoo.im.compat.currentTranslateText
import com.qyqy.ucoo.im.compat.isRevoked
import com.qyqy.ucoo.im.message.LuckBalls
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.widget.LuckyNumberView
import com.qyqy.ucoo.widget.orElse
import com.yy.yyeva.EvaAnimConfig
import com.yy.yyeva.inter.IEvaAnimListener
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.util.ScaleType
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.channels.Channel
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.joinAll
import kotlinx.coroutines.launch
import kotlin.math.abs
import kotlin.random.Random

val LocalBubbleTheme = staticCompositionLocalOf {
    BubbleTheme()
}

@Deprecated("尽量别使用")
@Composable
fun ChatBubbleText(entry: UIMessageEntry<*>, contentTextSpan: TextSpan) {
    val skin = entry.user.bubble
    val bubbleTheme = LocalBubbleTheme.current
    val left = !entry.message.isSelf
    val ninePatchPainter = rememberNinePatchPainter(skin, left, bubbleTheme, if (entry.message.isSelf) 1 else 0).value
    if (skin != null && ninePatchPainter.isSkin) {
        Box {
            MessageTextFlow(
                messageTextSpan = contentTextSpan, modifier = Modifier
                    .align(Alignment.CenterStart)
                    .paint(
                        painter = rememberDrawablePainter(drawable = ninePatchPainter.drawable), contentScale = ContentScale.FillBounds
                    )
                    .padding(ninePatchPainter.padding), color = Color(skin.fontColor.toColorInt()), lineHeight = 21.sp
            )

            val options = remember {
                RequestOptions.bitmapTransform(SourceDensity(NinePathLoader.PIC_DENSITY_DPI)).override(Target.SIZE_ORIGINAL)
            }

            val images = if (left) skin.startImages else skin.startImages

            images?.getOrNull(0)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter, contentDescription = null, modifier = Modifier.align(Alignment.TopStart), contentScale = ContentScale.Fit
                )
            }

            images?.getOrNull(1)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter, contentDescription = null, modifier = Modifier.align(Alignment.TopEnd), contentScale = ContentScale.Fit
                )
            }

            images?.getOrNull(2)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter, contentDescription = null, modifier = Modifier.align(Alignment.BottomStart), contentScale = ContentScale.Fit
                )
            }

            images?.getOrNull(3)?.takeIf {
                it.isNotEmpty()
            }?.let {
                rememberPainterByUrl(it, options).value
            }?.also { painter ->
                Image(
                    painter = painter, contentDescription = null, modifier = Modifier.align(Alignment.BottomEnd), contentScale = ContentScale.Fit
                )
            }
        }
    } else {
        Box(
            modifier = Modifier
                .paint(rememberDrawablePainter(drawable = ninePatchPainter.drawable))
                .padding(ninePatchPainter.padding),
        ) {
            MessageTextFlow(messageTextSpan = contentTextSpan)
        }
    }
}

@Composable
fun ChatTranslateText(contentTextSpan: TextSpan) {
    val bubbleTheme = LocalBubbleTheme.current
//    "#FF1D2129"
//    Color(0xFF1D2129)
    Box(
        modifier = Modifier
            .background(bubbleTheme.translateColor, RoundedCornerShape(bubbleTheme.radius))
            .padding(bubbleTheme.paddingValues),
    ) {
        MessageTextFlow(messageTextSpan = contentTextSpan)
    }
}



typealias CGUserImageContent = MessageItem.UserImageContent

sealed class MessageItem(
    val key: Any,
    val contentType: Any,
) : IComposeWidget<LazyItemScope> {

    abstract class Base<M : UCInstanceMessage>(
        val entry: UIMessageEntry<M>,
        contentType: Any,
    ) : MessageItem(key = entry.message.id, contentType = contentType) {

        protected val user = entry.user

        val message: M
            get() = entry.message
    }

    abstract class EventMessage(
        entry: UIMessageEntry<UCCustomMessage>,
        contentType: Any = entry.message.cmd,
    ) : Base<UCCustomMessage>(entry = entry, contentType = contentType) {

        val cmd
            get() = message.cmd
    }

    /**
     * 群聊系统事件
     */
    class GroupSystemEvent(
        entry: UIMessageEntry<UCCustomMessage>,
        private val text: AnnotatedString,
        val sender: AppUser,
        val target: AppUser,
    ) : EventMessage(entry) {

        @Composable
        override fun LazyItemScope.Content() {
            val handler = LocalMsgEventHandler.current
            Box(contentAlignment = Alignment.Center, modifier = Modifier.fillMaxWidth()) {
                ClickableText(
                    text = text,
                    modifier = Modifier
                        .background(Color(0x0DFFFFFF), RoundedCornerShape(4.dp))
                        .padding(8.dp),
                    style = TextStyle(Color(0xCCFFFFFF))
                ) {
                    text.getStringAnnotations("user", it, it).firstOrNull()?.also { ann ->
                        val uid = ann.item
                        val user = if (uid == sender.id) sender else target
                        handler?.handle(MsgEvents.SeeUser(user))
                    }
                }
            }
        }
    }

    class GroupChatTip(entry: UIMessageEntry<UCCustomMessage>) : EventMessage(entry) {
        @Composable
        override fun LazyItemScope.Content() {
            val nav = LocalUCOONavController.current
            GroupCreatedCard(onEditProfile = {
                nav.navigate(ChatGroupDestination.ChatGroupSettingsDestination.route)
            }, onSetAdmin = {
                nav.navigate(ChatGroupDestination.ChatGroupSettingsDestination.route)
            })
        }
    }

    class TimeTag(private val entry: FakerMessageEntry<UCTimestampMessage>) : MessageItem(entry.message.id, 0) {

        @Composable
        override fun LazyItemScope.Content() {
            val p = LocalChatUIProvider.current
            val color = if (p?.isFamilyConversation == true) MaterialTheme.typography.labelMedium.color
            else colorResource(id = R.color.white_alpha_80)
            val context = LocalContext.current
            val locale = LocalConfiguration.current.currentLocale
            val timeTag = remember(context, locale) {
                entry.message.getMessageTimeFormatText(context, locale, UserPartition.Cupid)
            }
            Text(
                text = timeTag,
                modifier = Modifier.fillMaxWidth(),
                color = color,
                fontSize = 13.sp,
                textAlign = TextAlign.Center
            )
        }
    }

    class NewMsgTag(private val entry: FakerMessageEntry<UCNewMsgTagMessage>) : MessageItem(entry.message.id, -100) {

        @Composable
        override fun LazyItemScope.Content() {
            AppText(
                text = stringResource(id = R.string.new_message),
                color = colorWhite50Alpha,
                fontSize = 12.sp,
                textAlign = TextAlign.Center,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(4.dp)
            )
        }
    }

    /**
     * 带皮肤用户富文本消息框
     */
    class UserTextContent(
        entry: UIMessageEntry<UCTextMessage>,
        val userTextSpan: TextSpan? = null,
        private val transTextSpan: TextSpan? = null,
        val contentTextSpan: TextSpan? = null,
    ) : Base<UCTextMessage>(entry = entry, contentType = 1) {

        @Composable
        override fun LazyItemScope.Content() {
            val up = LocalChatUIProvider.current
            UserMessageContent(entry, userTextSpan) {
                if (contentTextSpan != null) {
                    if (up?.isFamilyConversation == true && user.bubble == null) {
                        val shape = remember(user.isSelf) {
                            if (user.isSelf) RoundedCornerShape(
                                topStart = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp
                            ) else RoundedCornerShape(
                                topEnd = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp
                            )
                        }
                        Box(
                            modifier = Modifier
                                .background(Color.White, shape)
                                .padding(horizontal = 16.dp, 9.dp)
                        ) {
                            MessageTextFlow(messageTextSpan = contentTextSpan, color = Color(0xFF1D2129))
                        }
                    } else {
                        ChatBubbleText(entry = entry, contentTextSpan = contentTextSpan)
                        if (transTextSpan != null) {
                            ChatTranslateText(contentTextSpan = transTextSpan)
                        }
                    }
                }
            }
        }
    }

    /**
     * 语音消息
     */
    class UserVoiceContent(
        entry: UIMessageEntry<UCVoiceMessage>,
        private val userTextSpan: TextSpan? = null,
    ) : Base<UCVoiceMessage>(entry = entry, contentType = 2) {

        @Composable
        override fun LazyItemScope.Content() {
            val bubbleTheme = LocalBubbleTheme.current
            val msgAudioPlayer = LocalMsgAudioPlayer.current
            val isDownloading by remember {
                derivedStateOf {
                    msgAudioPlayer.isDownloading(key.toString())
                }
            }
            UserMessageContent(entry, userTextSpan) {
                val duration = message.duration.coerceAtMost(60)

                val pro = LocalChatUIProvider.current
                val isFamily = pro?.isFamilyConversation == true
                val color = if (isFamily) Color(0xFF1D2129) else Color.White
                val shape = remember(user.isSelf) {
                    if (user.isSelf) RoundedCornerShape(
                        topStart = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp
                    ) else RoundedCornerShape(
                        topEnd = 12.dp, bottomStart = 12.dp, bottomEnd = 12.dp
                    )
                }
                val modifier = if (isFamily) {
                    Modifier
                        .clip(shape)
                        .clickable {
                            msgAudioPlayer.startAudio(message.id, message)
                        }
                        .background(
                            Color.White, shape
                        )
                        .padding(horizontal = 16.dp, 9.dp)
                } else {
                    Modifier
                        .height(40.dp)
                        .clip(Shapes.small)
                        .background(bubbleTheme.color(user))
                        .padding(horizontal = 12.dp)
                        .clickable {
                            msgAudioPlayer.startAudio(message.id, message)
                        }
                }
                val alignRight = isFamily && message.isSelf
                Row(
                    modifier = modifier,
                    horizontalArrangement = if (alignRight) Arrangement.End else Arrangement.Start,
                    verticalAlignment = Alignment.CenterVertically,
                ) {

                    if (alignRight) {
                        Box(contentAlignment = Alignment.CenterStart) {
                            Spacer(modifier = Modifier.width(24.dp.plus(duration.times(1.2f).dp)))
                            if (isDownloading) {
                                CircularProgressIndicator(
                                    modifier = Modifier
                                        .align(Alignment.CenterEnd)
                                        .size(12.dp), color = Color.White, strokeWidth = 1.dp
                                )
                            }
                        }
                        Text(text = "\"$duration", fontSize = 14.sp, color = color)
                        Spacer(modifier = Modifier.width(4.dp))
                        VoiceIcon(
                            anim = msgAudioPlayer.isPlaying(key.toString()), color = color, isRight = true
                        )
                    } else {
                        VoiceIcon(
                            anim = msgAudioPlayer.isPlaying(key.toString()), color = color
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(text = "\"$duration", fontSize = 14.sp, color = color)
                        Box(contentAlignment = Alignment.CenterStart) {
                            Spacer(modifier = Modifier.width(24.dp.plus(duration.times(1.2f).dp)))
                            if (isDownloading) {
                                CircularProgressIndicator(
                                    modifier = Modifier
                                        .align(Alignment.CenterEnd)
                                        .size(12.dp), color = Color.White, strokeWidth = 1.dp
                                )
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 图片消息
     */
    class UserImageContent(
        density: Density,
        entry: UIMessageEntry<UCImageMessage>,
        private val effect: Channel<UIAction>?,
        private val userTextSpan: TextSpan? = null,
    ) : Base<UCImageMessage>(entry = entry, contentType = 3) {

        private val widthDp: Dp

        private val heightDp: Dp

        private val imageElem: ImageElem = message.previewElem

        init {
            if (imageElem.width > 0 && imageElem.height > 0) {
                val size = UIMessageUtils.resizeImage(originalSize = with(density) {
                    SizeFCompat(imageElem.width.toDp().value, imageElem.height.toDp().value)
                })
                widthDp = size.width.dp
                heightDp = size.height.dp
            } else {
                widthDp = Dp.Unspecified
                heightDp = Dp.Unspecified
            }
        }

        @Composable
        override fun LazyItemScope.Content() {
            val coroutineScope = rememberCoroutineScope()
            val previewState = LocalPhotoPreviewState.current
            val mh = LocalMsgEventHandler.current
            UserMessageContent(entry, userTextSpan) {
                ComposeImage(
                    model = imageElem.localUri ?: imageElem.url,
                    modifier = Modifier
                        .size(widthDp, heightDp)
                        .sizeIn(maxWidth = UIMessageUtils.MaxWidthDp.dp, maxHeight = UIMessageUtils.MaxHeightDp.dp)
                        .previewKeyModifier(previewState, message.id)
                        .clip(Shapes.corner12)
                        .clickable(enabled = effect != null) {
                            mh?.handle(MsgEvents.OnPreview(this@UserImageContent)) ?: run {
                                coroutineScope.launch {
                                    effect?.send(UIAction.OnClickImage(this@UserImageContent))
                                }
                            }
                        },
                    contentScale = ContentScale.Fit
                )
            }
        }
    }

    class UserNotifyContent(
        entry: UIMessageEntry<UCInstanceMessage>,
        private val messageTextSpan: TextSpan,
    ) : Base<UCInstanceMessage>(entry = entry, contentType = 4) {

        @Composable
        override fun LazyItemScope.Content() {
            Row(
                modifier = Modifier
                    .padding(end = 20.dp)
                    .background(Color(0x33000000), RoundedCornerShape(8.dp))
                    .padding(start = 8.dp, top = 8.dp, end = 10.dp, bottom = 8.dp)
            ) {
                CircleComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier.size(32.dp),
                )
                MessageTextFlow(
                    messageTextSpan = messageTextSpan, modifier = Modifier
                        .padding(start = 4.dp)
                        .heightIn(min = 32.dp)
                )
            }
        }
    }

    class UserInteractiveContent1(
        entry: UIMessageEntry<UCEmojiMessage>,
        private val userTextSpan: TextSpan? = null,
    ) : Base<UCEmojiMessage>(entry = entry, contentType = 5) {

        private val resList = if (message.type == UCEmojiMessage.TYPE_DICE) {
            intArrayOf(
                R.drawable.dice_action_0,
                R.drawable.dice_action_1,
                R.drawable.dice_action_2,
                R.drawable.dice_action_3,
            )
        } else {
            intArrayOf(
                R.drawable.effect_scissor,
                R.drawable.effect_stone,
                R.drawable.effect_cloth,
            )
        }

        private val resultResId: Int

        init {
            val result = message.value
            resultResId = if (message.type == UCEmojiMessage.TYPE_DICE) {
                when (result.toIntOrNull() ?: 1) {
                    2 -> R.drawable.dice_2
                    3 -> R.drawable.dice_3
                    4 -> R.drawable.dice_4
                    5 -> R.drawable.dice_5
                    6 -> R.drawable.dice_6
                    else -> R.drawable.dice_1
                }
            } else {
                when (result.toIntOrNull() ?: 1) {
                    2 -> R.drawable.effect_stone
                    3 -> R.drawable.effect_cloth
                    else -> R.drawable.effect_scissor
                }
            }
        }

        private var count = -1

        private val isListened = abs(message.timestamp.minus(SNTPManager.now())) >= 300_000 //  超过5分钟

        @Composable
        override fun LazyItemScope.Content() {
            var interactiveId by remember {
                mutableIntStateOf(resultResId)
            }

            val bubbleTheme = LocalBubbleTheme.current

            if (!message.isPlayed && !isListened) { // 直接渲染结果
                LaunchedEffect(key1 = Unit) {
                    message.isPlayed = true
                    if (message.type == UCEmojiMessage.TYPE_DICE) {
                        while (isActive && count++ <= 14) {
                            interactiveId = resList[count.rem(resList.size)]
                            delay(120)
                        }
                        interactiveId = resultResId
                    } else {
                        var exclude = 3
                        while (isActive && count++ < 9) {
                            var index = Random.nextInt(3)
                            if (exclude == index) {
                                index = index.plus(1).rem(3)
                            }
                            interactiveId = resList[index]
                            exclude = index
                            delay(450)
                        }
                        interactiveId = resultResId
                    }
                    if (!message.isPlayed) {
                        message.isPlayed = true
                    }
                }
            }

            UserMessageContent(entry, userTextSpan) {
                Box(
                    modifier = Modifier
                        .clip(Shapes.small)
                        .background(bubbleTheme.color(user))
                        .padding(horizontal = 12.dp, vertical = 6.dp)
                ) {
                    Image(painter = painterResource(id = interactiveId), contentDescription = null, modifier = Modifier.size(40.dp))
                }
            }
        }
    }

    class UserInteractiveContent2(
        entry: UIMessageEntry<UCEmojiMessage>,
        private val userTextSpan: TextSpan? = null,
    ) : Base<UCEmojiMessage>(entry = entry, contentType = 6) {

        private val asset = EvaAnimState.Source.Asset(
            when (message.type) {
                UCEmojiMessage.TYPE_NUMBER_3 -> "effect_lucky_number_3.mp4"
                UCEmojiMessage.TYPE_NUMBER_5 -> "effect_lucky_number_5.mp4"
                else -> "effect_lucky_number_1.mp4"
            }
        )

        private val isListened = abs(message.timestamp.minus(SNTPManager.now())) >= 300_000 //  超过5分钟

        /**
         * 0 待播放
         * 1 正在播放
         * 2 转场
         * 3 完成
         */
        private var state by mutableIntStateOf(if (message.isPlayed || isListened) 3 else 0)

        @Composable
        override fun LazyItemScope.Content() {
            val density = LocalDensity.current
            val bubbleTheme = LocalBubbleTheme.current
            UserMessageContent(entry, userTextSpan) {
                Box(
                    modifier = Modifier
                        .size(80.dp, 40.dp)
                        .clip(Shapes.small)
                        .background(bubbleTheme.color(user))
                        .padding(horizontal = 12.dp)
                ) {

                    val alphaAnimation1 = remember { Animatable(1f) }
                    val scaleAnimation1 = remember { Animatable(1f) }

                    if (state != 3) {
                        AndroidView(factory = {
                            EvaAnimViewV3(it).apply {
                                setLastFrame(true)
                                setScaleType(ScaleType.CENTER_CROP)
                                setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                            }
                        }, modifier = Modifier
                            .fillMaxSize()
                            .graphicsLayer {
                                if (state != 2) {
                                    this.scaleX = 1f
                                    this.scaleY = 1f
                                    this.alpha = 1f
                                } else {
                                    this.scaleX = scaleAnimation1.value
                                    this.scaleY = scaleAnimation1.value
                                    this.alpha = alphaAnimation1.value
                                }
                            }, onReset = {
                            if (state != 0) {
                                it.setAnimListener(null)
                                if (it.isRunning()) {
                                    it.stopPlay()
                                }
                                state = 3
                                if (!message.isPlayed) {
                                    message.isPlayed = true
                                }
                            }
                        }) {
                            if (state == 0) {
                                state = 1
                                if (it.isRunning()) {
                                    it.stopPlay()
                                }
                                val assetManager = it.context.assets
                                it.setAnimListener(object : IEvaAnimListener {
                                    override fun onFailed(errorType: Int, errorMsg: String?) {
                                        if (state == 1) {
                                            state = 2
                                            if (!message.isPlayed) {
                                                message.isPlayed = true
                                            }
                                        }
                                    }

                                    override fun onVideoComplete(lastFrame: Boolean) {
                                        if (state == 1) {
                                            state = 2
                                            if (!message.isPlayed) {
                                                message.isPlayed = true
                                            }
                                        }
                                    }

                                    override fun onVideoRender(frameIndex: Int, config: EvaAnimConfig?) {
                                        if (state == 1 && frameIndex >= 45) {
                                            state = 2
                                            if (!message.isPlayed) {
                                                message.isPlayed = true
                                            }
                                        }
                                    }

                                    override fun onVideoDestroy() = Unit
                                    override fun onVideoRestart() = Unit
                                    override fun onVideoStart(isRestart: Boolean) = Unit

                                })
                                it.startPlay(assetManager, asset.assetPath)
                            }
                        }
                    }

                    val alphaAnimation2 = remember { Animatable(0f) }
                    val scaleAnimation2 = remember { Animatable(0.2f) }

                    if (state != 0 && state != 1) {
                        AndroidView(
                            factory = {
                                LuckyNumberView(it).also { v ->
                                    with(density) {
                                        v.setAttr(2.dp.toPx(), 15.dp.toPx())
                                    }
                                }
                            }, modifier = Modifier
                                .fillMaxSize()
                                .graphicsLayer {
                                    if (state == 3) {
                                        this.scaleX = 1f
                                        this.scaleY = 1f
                                        this.alpha = 1f
                                    } else {
                                        this.scaleX = scaleAnimation2.value
                                        this.scaleY = scaleAnimation2.value
                                        this.alpha = alphaAnimation2.value
                                    }
                                }, onReset = NoOpUpdate
                        ) {
                            it.setNumber(message.value)
                        }
                    }

                    if (state == 2) {
                        LaunchedEffect(key1 = Unit) {
                            listOf(launch {
                                alphaAnimation1.animateTo(0f)
                            }, launch {
                                scaleAnimation1.animateTo(0.2f)
                            }).joinAll()

                            listOf(launch {
                                alphaAnimation2.animateTo(1f)
                            }, launch {
                                scaleAnimation2.animateTo(1f)
                            }).joinAll()

                            state = 3
                        }
                    }
                }
            }
        }
    }

    class UserShareContent(
        private val room: AudioRoom?,
        entry: UIMessageEntry<UCCustomMessage>,
        private val effect: Channel<UIAction>?,
        private val userTextSpan: TextSpan? = null,
    ) : EventMessage(entry = entry, contentType = 7) {
        @Composable
        override fun LazyItemScope.Content() {
            val coroutineScope = rememberCoroutineScope()
            UserMessageContent(entry, userTextSpan) {
                Row(
                    modifier = Modifier
                        .width(236.dp)
                        .background(
                            brush = Brush.linearGradient(
                                listOf(Color(0xff604082), Color(0xff43254D))
                            ), shape = RoundedCornerShape(15.dp)
                        )
                        .padding(
                            top = 20.dp, bottom = 20.dp, start = 15.dp, end = 10.dp
                        )
                ) {
                    ComposeImage(
                        model = room?.owner?.avatarUrl ?: "", modifier = Modifier
                            .size(64.dp)
                            .clip(CircleShape)
                    )
                    Spacer(
                        modifier = Modifier.width(10.dp)
                    )
                    Column(
                        verticalArrangement = Arrangement.SpaceBetween, modifier = Modifier
                            .weight(1f)
                            .heightIn(64.dp, 81.dp)
                    ) {
                        Text(
                            text = message.getJsonString("hint", ""),
                            style = TextStyle(fontSize = 14.sp, color = Color.White, fontWeight = FontWeight.Bold)
                        )
                        Text(
                            stringResource(id = R.string.format_room_name, room?.title ?: ""),
                            style = TextStyle(fontSize = 12.sp, color = Color(0xffD080FF))
                        )
                        Text(stringResource(id = R.string.点击进入),
                            style = TextStyle(fontSize = 12.sp, color = Color(0xffD080FF), textAlign = TextAlign.Center),
                            softWrap = true,
                            maxLines = 2,
                            modifier = Modifier
                                .background(
                                    color = Color(0x33000000), shape = CircleShape
                                )
                                .widthIn(max = 150.dp)
                                .padding(horizontal = 10.dp, vertical = 4.dp)
                                .click {
                                    coroutineScope.launch {
                                        effect?.send(
                                            UIAction.JoinAudioRoom((room?.id ?: 0), room?.owner?.userId.toString())
                                        )
                                    }
                                })
                    }
                }
            }
        }
    }

    class UserInviteCardContent(
        entry: UIMessageEntry<UCCustomMessage>,
        private val effect: Channel<UIAction>?,
        private val userTextSpan: TextSpan? = null,
    ) : EventMessage(entry = entry, contentType = 8) {
        @Composable
        override fun LazyItemScope.Content() {
            val coroutineScope = rememberCoroutineScope()
            var image = ""
            var title = ""
            var roomId = 0
            var userId = ""
            if (cmd == MsgEventCmd.GAME_MATCH_INVITE) {
                val gameInvite = message.parseDataJson<GameInvite>()
                roomId = gameInvite?.roomInfo?.id ?: 0
                userId = gameInvite?.user?.id.orEmpty()
                image = gameInvite?.gameInfo?.icon.orEmpty()
                title = stringResource(id = R.string.大家一起来玩游戏, gameInvite?.gameInfo?.name.orEmpty())
            }
            UserMessageContent(entry, userTextSpan) {
                InviteCardContent(image = image, title = title, modifier = Modifier.noEffectClickable {
                    coroutineScope.launch {
                        effect?.send(UIAction.JoinAudioRoom(roomId, userId))
                    }
                })
            }
        }
    }

    /**
     * 不带皮肤用户富文本消息框， 如礼物消息
     */
    class SystemSendUserContent(
        entry: UIMessageEntry<UCInstanceMessage>,
        private val userTextSpan: TextSpan? = null,
        private val contentTextSpan: TextSpan? = null,
    ) : Base<UCInstanceMessage>(entry = entry, contentType = 101) {

        @Composable
        override fun LazyItemScope.Content() {
            Row(
                modifier = Modifier
                    .padding(end = 20.dp)
                    .background(Color(0x33000000), RoundedCornerShape(8.dp))
                    .padding(start = 8.dp, top = 8.dp, end = 10.dp, bottom = 8.dp)
            ) {
                CircleComposeImage(
                    model = user.avatarUrl,
                    modifier = Modifier.size(32.dp),
                )
                Column(
                    modifier = Modifier.padding(start = 4.dp), verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    if (userTextSpan != null) {
                        MessageTextFlow(
                            messageTextSpan = userTextSpan, modifier = Modifier.heightIn(min = 32.dp)
                        )
                    }
                    if (contentTextSpan != null) {
                        MessageTextFlow(messageTextSpan = contentTextSpan)
                    }
                }
            }
        }
    }

    class SystemMemberChange(
        entry: UIMessageEntry<UCCustomMessage>,
        private val messageTextSpan: TextSpan,
    ) : EventMessage(entry = entry, contentType = 102) {

        @Composable
        override fun LazyItemScope.Content() {
            val rtl = LocalLayoutDirection.current == LayoutDirection.Rtl
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 36.dp)
                    .clip(RoundedCornerShape(18.dp, 2.dp, 2.dp, 18.dp))
                    .background(
                        brush = Brush.horizontalGradient(
                            if (rtl) {
                                listOf(Color(0x00903E7B), Color(0xFFFF5EC8))
                            } else {
                                listOf(Color(0xFF903E7B), Color(0x00FF5EC8))
                            }
                        )
                    )
                    .padding(horizontal = 8.dp, vertical = 2.dp), contentAlignment = Alignment.CenterStart
            ) {
                MessageTextFlow(messageTextSpan = messageTextSpan)
            }
        }
    }

    class SystemNotifyContent(
        entry: UIMessageEntry<UCInstanceMessage>,
        private val messageTextSpan: TextSpan,
    ) : Base<UCInstanceMessage>(entry = entry, contentType = 103) {

        @Composable
        override fun LazyItemScope.Content() {
            Box(
                modifier = Modifier
                    .padding(end = 20.dp)
                    .background(Color(0x33000000), RoundedCornerShape(8.dp))
                    .padding(horizontal = 10.dp, vertical = 6.dp),
                contentAlignment = Alignment.Center,
            ) {
                MessageTextFlow(
                    messageTextSpan = messageTextSpan, color = colorResource(id = R.color.white_alpha_50), fontSize = 12.sp
                )
            }
        }
    }


    companion object {

        fun valueOfUiMessage(
            context: Context,
            density: Density,
            entry: MessageEntry<UCInstanceMessage>,
            effect: Channel<UIAction>? = null,
        ): MessageItem {
            val message = entry.message
            if (entry is FakerMessageEntry<*>) {
                if (message is UCTimestampMessage) {
                    return TimeTag(entry as FakerMessageEntry<UCTimestampMessage>)
                }
                if (message is UCNewMsgTagMessage) {
                    return NewMsgTag(entry as FakerMessageEntry<UCNewMsgTagMessage>)
                }
            }
            entry as UIMessageEntry<UCInstanceMessage>
            if (message.isRevoked) {
                return UserNotifyContent(
                    entry = entry,
                    buildTextSpan {
                        appendUserName(entry.user)
                        append(" ")
                        append(context.getString(R.string.rc_recalled_message, ""))
                    },
                )
            }
            val messageItem = when (message) {
                is UCTextMessage -> {
                    val trans = message.currentTranslateText.orEmpty()
                    val text = message.text
                    UserTextContent(
                        entry = entry as UIMessageEntry<UCTextMessage>,
                        userTextSpan = buildUserTextSpan(density, entry.user),
                        transTextSpan = if (trans.isNotEmpty()) {
                            buildTextSpan {
                                append(trans.appendPrefixText(context.getString(R.string.format_trans)))
                            }
                        } else {
                            null
                        },
                        contentTextSpan = buildTextSpan {
                            append(text)
                        },
                    )
                }

                is UCVoiceMessage -> {
                    UserVoiceContent(
                        entry = entry as UIMessageEntry<UCVoiceMessage>,
                        userTextSpan = buildUserTextSpan(density, entry.user),
                    )
                }

                is UCImageMessage -> {
                    UserImageContent(
                        density = density,
                        entry = entry as UIMessageEntry<UCImageMessage>,
                        effect = effect,
                        userTextSpan = buildUserTextSpan(density, entry.user),
                    )
                }

                is UCGiftMessage -> {
                    entry as UIMessageEntry<UCGiftMessage>
                    SystemSendUserContent(
                        entry = entry,
                        userTextSpan = buildUserTextSpan(density = density, user = entry.user),
                        contentTextSpan = buildTextSpan {
                            if (message.cmd == MsgEventCmd.GIVE_GIFT) {
                                message.gift.also { giftWrapper ->
                                    val receivers = giftWrapper.receivers
                                    val hasRelationship = try {
                                        receivers.isNotEmpty() && receivers[entry.requireMsgIndex].hasRelationship
                                    } catch (e: Exception) {
                                        false
                                    }
                                    if (hasRelationship) {
                                        buildRelationshipSpan(context, density, it, entry.requireMsgIndex, giftWrapper)
                                    } else {
                                        buildCommonGiftSpan(context, density, it, entry.requireMsgIndex, giftWrapper)
                                    }
                                }
                            } else {
                                LuckBalls.getGiftSpan(message.gift, GiftWrapper.room, null, false)?.toAnnotatedString()?.also {
                                    append(it)
                                }
                                it.appendInlineContent(inlineTextContent(
                                    key = "gift",
                                    density = density,
                                    width = 20,
                                    paddingValues = PaddingValues(start = 4.dp)
                                ) { modifier ->
                                    ComposeImage(model = message.gift.gift.icon, modifier = modifier)
                                })
                            }
                        })
                }

                is UCEmojiMessage -> {
                    entry as UIMessageEntry<UCEmojiMessage>
                    val type = message.type
                    val userTextSpan = buildUserTextSpan(density, entry.user)
                    when (type) {
                        UCEmojiMessage.TYPE_DICE -> UserInteractiveContent1(entry, userTextSpan = userTextSpan)
                        in UCEmojiMessage.TYPE_NUMBER_1..UCEmojiMessage.TYPE_NUMBER_5 -> UserInteractiveContent2(entry, userTextSpan = userTextSpan)
                        UCEmojiMessage.TYPE_GUESSING_FIST -> UserInteractiveContent1(entry, userTextSpan = userTextSpan)
                        else -> null
                    }
                }

                is UCCustomMessage -> {
                    entry as UIMessageEntry<UCCustomMessage>
                    when (message.cmd) {
                        MsgEventCmd.ROOM_SETTINGS -> {
                            val roomSettings = message.parseDataJson<RoomSettings>()
                            SystemNotifyContent(entry = entry, messageTextSpan = buildTextSpan {
                                roomSettings?.takeIf {
                                    it.name == RoomSettings.KEY_ROOM_MODE || it.name == RoomSettings.KEY_MIC_MODE
                                }?.apply {
                                    append(context.getString(R.string.房间已切换为))
                                    color(Color(0xFFFFA5A5)) {
                                        append(stringOfMode())
                                    }
                                } ?: if (AppUserPartition.isUCOO) {
                                    append(context.getString(R.string.暂不支持此消息类型_请升级版本))
                                } else {
                                    append(context.getString(R.string.cpd_暂不支持此消息类型_请升级版本))
                                }
                            })
                        }

                        MsgEventCmd.USER_LEVEL_CHANGE -> {
                            val sendUser = message.getJsonValue<AppUser>("user")
                            SystemNotifyContent(entry = entry, buildTextSpan {
                                append(context.getString(R.string.恭喜))
                                appendUserName(entry.user)
                                append(context.getString(R.string.user_leve_upgrade_to))
                                color(Color(0xFFFFA5A5)) {
                                    append(LevelConfig.getLevelText(sendUser?.level.orElse(0)))
                                }
                            })
                        }

                        MsgEventCmd.USER_ENTRANCE -> {
                            SystemMemberChange(entry = entry, buildUserEnterTextSpan(context, density, entry.user))
                        }

                        MsgEventCmd.USER_EXIT -> {
                            SystemMemberChange(entry = entry, buildUserTextSpan(density, entry.user) {
                                append(" ")
                                append(context.getString(R.string.离开了房间))
                            })
                        }

                        MsgEventCmd.INVITE_TO_ROOM -> {
                            val room = message.getJsonValue<AudioRoom>("audioroom")
                            UserShareContent(room = room, entry = entry, effect = effect, buildUserTextSpan(density, entry.user))
                        }

                        MsgEventCmd.GAME_MATCH_INVITE -> {
                            UserInviteCardContent(entry = entry, effect = effect, buildUserTextSpan(density, entry.user))
                        }

                        ChatGroup.Events.CHATGROUP_CREATED -> GroupChatTip(entry)
                        ChatGroup.Events.CHATGROUP_MEMBER_QUIT,
                        ChatGroup.Events.CHATGROUP_MEMBER_JOIN,
                        ChatGroup.Events.CHATGROUP_MEMBER_KICKED_OUT,
                        -> {
                            val sender = message.getJsonValue<AppUser>("user") ?: message.getJsonValue<AppUser>("admin_user") ?: INVALID_USER
                            val target = message.getJsonValue<AppUser>("kicked_user") ?: INVALID_USER
                            val text = when (message.cmd) {
                                ChatGroup.Events.CHATGROUP_MEMBER_JOIN -> {
                                    buildUserAnnotatedString(
                                        context.getString(R.string.format_chat_group_join, sender.nickname), listOf(sender.nameColorPair)
                                    )
                                }

                                ChatGroup.Events.CHATGROUP_MEMBER_KICKED_OUT -> {
                                    buildUserAnnotatedString(
                                        context.getString(R.string.format_chat_group_kick_out, target.nickname, sender.nickname),
                                        listOf(target.nameColorPair, sender.nameColorPair)
                                    )
                                }

                                ChatGroup.Events.CHATGROUP_MEMBER_QUIT -> {
                                    buildUserAnnotatedString(
                                        context.getString(R.string.format_chat_group_quit, sender.nickname), listOf(sender.nameColorPair)
                                    )
                                }

                                else -> buildAnnotatedString {
                                    if (AppUserPartition.isUCOO) {
                                        append(context.getString(R.string.暂不支持此消息类型_请升级版本))
                                    } else {
                                        append(context.getString(R.string.cpd_暂不支持此消息类型_请升级版本))
                                    }
                                }
                            }
                            GroupSystemEvent(entry, text, sender, target)
                        }

                        else -> {
                            null
                        }
                    }
                }

                else -> {
                    null
                }
            }

            return messageItem ?: kotlin.run {
                if (entry.user.isInvalid()) {
                    SystemNotifyContent(entry, buildTextSpan {
                        if (AppUserPartition.isUCOO) {
                            append(context.getString(R.string.暂不支持此消息类型_请升级版本))
                        } else {
                            append(context.getString(R.string.cpd_暂不支持此消息类型_请升级版本))
                        }
                    })
                } else {
                    SystemSendUserContent(entry, buildUserTextSpan(density, entry.user, false), buildTextSpan {
                        if (AppUserPartition.isUCOO) {
                            append(context.getString(R.string.暂不支持此消息类型_请升级版本))
                        } else {
                            append(context.getString(R.string.cpd_暂不支持此消息类型_请升级版本))
                        }
                    })
                }
            }
        }
    }
}


@Composable
fun UserMessageContent(entry: UIMessageEntry<UCInstanceMessage>, userTextSpan: TextSpan?, content: @Composable ColumnScope.() -> Unit) {
    val user = entry.user
    val uiProvider = LocalChatUIProvider.current
    uiProvider?.ItemLayout(entry, content) ?: run {
        val context = LocalContext.current
        Row(modifier = Modifier.padding(end = 20.dp)) {
            CircleComposeImage(
                model = user.avatarUrl,
                modifier = Modifier
                    .size(40.dp)
                    .clickable {
                        UserProfileNavigator.navigate(context, user)
                        Analytics.appReportEvent(DataPoint.clickBody("global_chat_avatar_click"))
                    },
            )
            Column(
                modifier = Modifier.padding(start = 4.dp), verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                if (userTextSpan != null) {
                    Column(
                        modifier = Modifier.heightIn(min = 40.dp), verticalArrangement = Arrangement.SpaceAround
                    ) {
                        MessageTextFlow(messageTextSpan = userTextSpan)
                        AgeGender(user.age, user.isBoy)
                    }

                    Spacer(modifier = Modifier.height(0.1.dp))
                }

                content()

                // 华语区
                MessageReminder(entry, Modifier)
            }
        }
    }

}

private fun buildUserTextSpan(
    density: Density,
    user: User,
    showExtraInfo: Boolean = false,
    builder: (AnnotatedString.Builder).(TextSpan.Builder) -> Unit = {},
): TextSpan {
    if (!showExtraInfo) {
        return buildTextSpan {
            if (user.countryFlag.isNotEmpty()) {
                appendUserName(user, ":")
                it.appendInlineContent(inlineTextContent(
                    key = "country", density = density, width = 20, height = 15, paddingValues = PaddingValues(start = 4.dp)
                ) { modifier ->
                    ComposeImage(
                        model = user.countryFlag, contentDescription = "country", modifier = modifier
                    )
                })
            } else {
                appendUserName(user)
            }
            builder(it)
        }
    }

    val paddingValues = PaddingValues(start = 4.dp, top = 2.dp, bottom = 2.dp)

    val list = buildList {
        add(inlineTextContent(key = "level", paddingValues = paddingValues) { modifier ->
            LevelBadge(level = user.level, modifier = modifier)
        })
        if (user.isVip) {
            add(inlineTextContent(
                key = "vip", density = density, width = 39, height = 20, paddingValues = paddingValues
            ) { modifier ->
                Image(
                    painter = painterResource(id = R.drawable.ic_vip_tag), contentDescription = "vip", modifier = modifier
                )
            })
        }

        user.publicCP?.also {
            add(inlineTextContent(
                key = "publish_cp", density = density, width = 64, height = 18, paddingValues = paddingValues
            ) { modifier ->
                PublishCp(
                    avatar = it.avatarUrl, bgUrl = it.cpExtraInfo?.levelInfo?.smallImgUrl, modifier = modifier
                )
            })
        }

        user.medalList.forEachIndexed { index, medal ->
            add(inlineTextContent(
                key = "medal_$index", density = density, width = medal.width, height = medal.height, paddingValues = paddingValues
            ) { modifier ->
                ComposeImage(
                    model = medal.icon, modifier = modifier
                )
            })
        }
    }

    return buildTextSpan {
        appendUserName(user)
        it.appendInlineContentList(list)
        builder(it)
    }
}


private fun buildUserEnterTextSpan(
    context: Context,
    density: Density,
    user: User,
): TextSpan {
    val paddingValues = PaddingValues(start = 4.dp, top = 2.dp, bottom = 2.dp)
    return buildTextSpan {
        append(context.getString(R.string.欢迎))
        it.appendInlineContent(inlineTextContent(
            key = "avatar", density = density, width = 28, height = 28, paddingValues = paddingValues
        ) { modifier ->
            CircleComposeImage(
                model = user.avatarUrl, contentDescription = "avatar", modifier = modifier
            )
        })
        append(" ")
        appendUserName(user)
        append(" ")
        append(context.getString(R.string.进入聊天室))
        if (user.countryFlag.isNotEmpty()) {
            append(context.getString(R.string.来自))
            it.appendInlineContent(inlineTextContent(
                key = "country", density = density, width = 20, height = 15, paddingValues = paddingValues
            ) { modifier ->
                ComposeImage(
                    model = user.countryFlag, contentDescription = "country", modifier = modifier
                )
            })
        }
    }
}

private fun AnnotatedString.Builder.buildRelationshipSpan(
    context: Context,
    density: Density,
    builder: TextSpan.Builder,
    receiverIndex: Int,
    giftWrapper: GiftWrapper,
) {
    val sender = giftWrapper.sender
    val stringRes = if (sender.isBoy) R.string.for_he else R.string.for_her
    val receiver = giftWrapper.receivers[receiverIndex]
    val textRel = "【${receiver.relationships?.tag?.name}】${receiver.nickname}"
    val giftName = giftWrapper.gift.name
    val color = Color(0xFFFFA5A5)
    val content = context.getString(stringRes, textRel, giftWrapper.count, giftName)
    append(content)
    content.indexOf(textRel).also {
        addStyle(SpanStyle(color), it, it.plus(textRel.length))
    }
    content.indexOf(giftName).also {
        addStyle(SpanStyle(color), it, it.plus(giftName.length))
    }
    builder.appendInlineContent(inlineTextContent(
        key = "gift", density = density, width = 20, paddingValues = PaddingValues(horizontal = 4.dp)
    ) { modifier ->
        ComposeImage(model = giftWrapper.gift.icon, modifier = modifier)
    })
    append(" ")
    append(receiver.relationships?.intimacy.orEmpty())
}


private fun AnnotatedString.Builder.buildCommonGiftSpan(
    context: Context,
    density: Density,
    builder: TextSpan.Builder,
    receiverIndex: Int,
    giftWrapper: GiftWrapper,
) {
    append(context.getString(R.string.赠送了))
    color(Color(0xFFFFA5A5)) {
        val receivers = giftWrapper.receivers
        if (receivers.isEmpty()) {
            append(context.getString(R.string.所有人_换行符))
        } else {
            append(receivers[receiverIndex].nickname)
        }
    }
    append(giftWrapper.count.toString())
    append(context.getString(R.string.个))
    val color = Color(context.getColor(R.color.color_tribe_male))
    if (giftWrapper.isBlinxBox) {
        color(color) {
            append(giftWrapper.blindboxName)
        }
        append(",")
        append(context.getString(R.string.unlock_limit_gift))
        color(color) {
            append(giftWrapper.gift.name)
        }
    } else {
        color(color) {
            append(giftWrapper.gift.name)
        }
        append(context.getString(R.string.礼物))
    }
    builder.appendInlineContent(inlineTextContent(
        key = "gift", density = density, width = 20, paddingValues = PaddingValues(start = 4.dp)
    ) { modifier ->
        ComposeImage(model = giftWrapper.gift.icon, modifier)
    })
    append(" ")
}


fun buildUserAnnotatedString(text: String, colorConfig: List<Pair<User, Color>>): AnnotatedString {
    return buildAnnotatedString {
        colorConfig.forEach {
            val nickname = it.first.nickname
            val start = text.indexOf(nickname)
            val end = start + nickname.length
            if (start != -1) {
                addStyle(SpanStyle(it.second), start, end)
                addStringAnnotation("user", it.first.id, start, end)
            }
        }
        append(text)
    }
}


//@Composable
//private fun UserTextFlow(
//    user: User,
//    showExtraInfo: Boolean,
//    modifier: Modifier = Modifier
//) {
//    MessageTextFlow(generateUserTextSpan(user, showExtraInfo), modifier)
//}

@Composable
fun MessageTextFlow(
    messageTextSpan: TextSpan,
    modifier: Modifier = Modifier,
    color: Color = Color.White,
    fontSize: TextUnit = 14.sp,
    textAlign: TextAlign? = null,
    lineHeight: TextUnit = TextUnit.Unspecified,
) {
    SpanText(
        textSpan = messageTextSpan, modifier = modifier, color = color, fontSize = fontSize, textAlign = textAlign, lineHeight = lineHeight
    )
}

private val AppUser.nameColorPair: Pair<User, Color>
    get() = this to Color(nameColor)


@Composable
private fun InviteCardContent(image: String, title: String, modifier: Modifier = Modifier) {
    Column(
        modifier = Modifier
            .width(236.dp)
            .clip(RoundedCornerShape(8.dp))
            .background(Color(0x26945EFF))
            .then(modifier)
            .padding(horizontal = 12.dp),
    ) {
        Spacer(modifier = Modifier.height(8.dp))

        Row(verticalAlignment = Alignment.CenterVertically) {
            Text(
                text = title,
                modifier = Modifier.weight(1f),
                fontSize = 14.sp,
                color = Color.White,
                maxLines = 2,
                minLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            CircleComposeImage(
                model = image, modifier = Modifier
                    .padding(start = 8.dp)
                    .size(48.dp)
            )
        }

        Spacer(modifier = Modifier.height(10.dp))

        HorizontalDivider(thickness = 0.5.dp, color = Color(0x14FFFFFF))

        Spacer(modifier = Modifier.height(10.dp))

        AppText(
            text = stringResource(id = R.string.点击进入),
            style = TextStyle(fontSize = 12.sp, color = Color(0xFF945EFF)),
        )

        Spacer(modifier = Modifier.height(10.dp))
    }
}

