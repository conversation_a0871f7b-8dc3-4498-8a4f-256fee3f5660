package com.qyqy.ucoo.compose.presentation.greets


import com.qyqy.ucoo.compose.ui.ITab
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

@Serializable
data class Greetings(
    val tabs: List<Tab> = emptyList()
) {
    @Serializable
    data class Tab(
        @SerialName("content_list")
        val contentList: List<Content> = emptyList(),
        @SerialName("content_type")
        val contentType: Int = 0,//# 1 - 文字，2-语音，3-图片
        val hint: List<String> = emptyList(),
        val title: String = ""
    ) : ITab {
        @Transient
        val tips: String = hint.joinToString(separator = "\n")

        @Serializable
        data class Content(
            override val content: String,
            @SerialName("content_type")
            override val contentType: Int,
            override val duration: Int,
            override val id: Int
        ) : GreetItem {
            @Transient
            override val key: String = id.toString()
            @Transient
            override var isNew: Boolean = false

        }

        override val name: String = title

    }
}
