package com.qyqy.ucoo.compose.presentation.cp_house.house

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.cupid.theme.PreviewCupidTheme
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorHouseBlue
import com.qyqy.ucoo.utils.OnClick

@Composable
fun HouseCoinPanel(
    coinCount: Int,
    descList: List<String>,
    modifier: Modifier = Modifier,
    onClose: OnClick = {}
) {
    Box(
        modifier = modifier
            .fillMaxWidth()
            .background(colorHouseBlue, Shapes.small)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp, 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            val text = LocalHouseText.current
            Text(text = stringResource(id = text.houseCoin), fontSize = 17.sp, color = Color.White)
            Spacer(modifier = Modifier.height(20.dp))
            Image(
                painter = painterResource(id = R.drawable.icon_house_coin),
                contentDescription = "",
                modifier = Modifier.size(64.dp)
            )
            Spacer(modifier = Modifier.height(16.dp))
            Text(
                text = stringResource(id = text.formatHouseCoin, coinCount),
                fontSize = 16.sp,
                color = Color.White,
                fontWeight = FontWeight.Medium
            )
            Spacer(modifier = Modifier.height(24.dp))
            Column {
                descList.forEach {
                    Text(text = it, fontSize = 12.sp, color = Color.White, lineHeight = 20.sp)
                }
            }
        }
        Image(
            painter = painterResource(id = R.drawable.ic_close_page),
            contentDescription = "close",
            modifier = Modifier

                .padding(8.dp)
                .click(onClick = onClose)
                .size(24.dp)
                .align(Alignment.TopEnd)
        )
    }
}

class HouseCoinDialog(
    private val coinCount: Int,
    private val descList: List<String>,
) : NormalDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        HouseCoinPanel(coinCount, descList, modifier = Modifier.width(270.dp)) {
            dialog.dismiss()
        }
    }
}

@Composable
fun CoinPopup(
    modifier: Modifier = Modifier,
    isAdmin:Boolean = false,
    onRuleClick: OnClick = {},
    onRecordClick: OnClick = {}
) {
    val text = LocalHouseText.current
    Column(modifier = modifier) {
//        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.End) {
//            Box(modifier = Modifier
//                .background(Color.White, shape)
//                .size(12.dp, 6.5.dp))
//            Spacer(modifier = Modifier.width(10.dp))
//        }
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .drawBehind {
                    drawPath(Path().apply {
                        val cx = size.width - 16.dp.toPx()
                        moveTo(cx, 0f)
                        lineTo(cx + 6.dp.toPx(), 6.5.dp.toPx())
                        lineTo(cx - 6.dp.toPx(), 6.5.dp.toPx())
                        close()
                    }, Color.White)
                }
                .padding(top = 6.5.dp)
                .background(Color.White, Shapes.small)
                .graphicsLayer {
                    shadowElevation = 0f
                }
                .padding(horizontal = 12.dp)

        ) {
            val mod = Modifier
                .fillMaxWidth()
                .height(44.dp)
            Box(
                modifier = mod.noEffectClickable(onClick = onRuleClick),
                contentAlignment = Alignment.Center
            ) {
                Text(text = stringResource(id = text.ruleDesc), color = Color(0xFF1D2129))
            }
            if(isAdmin){
                HorizontalDivider(thickness = 0.5.dp, color = Color(0xFFF0F0F0))
                Box(
                    modifier = mod.noEffectClickable(onClick = onRecordClick),
                    contentAlignment = Alignment.Center
                ) {
                    Text(text = stringResource(id = text.coinRecord), color = Color(0xFF1D2129))
                }
            }
        }
    }
}

@Preview
@Composable
private fun HouseCoinPreview() {
    PreviewCupidTheme {
        Column {
            HouseCoinPanel(
                coinCount = 999,
                descList = listOf(
                    "1. 小屋币为CP双方共同拥有使用",
                    "2. 提升小屋和车辆等级可以获得更高收益"
                ),
                modifier = Modifier.width(270.dp)
            )
            Box(
                modifier = Modifier
                    .background(Color.Black)
                    .padding(20.dp)
            ) {
                CoinPopup(modifier = Modifier.width(108.dp))
            }
        }
    }
}