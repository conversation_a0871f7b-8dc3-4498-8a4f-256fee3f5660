package com.qyqy.ucoo.compose.ui.video.exo

import android.content.Context
import androidx.annotation.OptIn
import androidx.media3.common.util.UnstableApi
import site.xzwzz.video.kernel.factory.PlayerFactory

@OptIn(UnstableApi::class)
class ExoPlayerFactory : PlayerFactory<ExoMediaPlayer> {
    override fun createPlayer(context: Context): ExoMediaPlayer {
		return ExoMediaPlayer(context)
	}

	companion object {
		@JvmStatic
		fun create(): ExoPlayerFactory {
			return ExoPlayerFactory()
		}
	}
}