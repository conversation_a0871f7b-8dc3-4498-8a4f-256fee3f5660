package com.qyqy.ucoo.compose.domain.usecase.relationship

import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.domain.SuspendUseCase
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi.ViewResult
import com.qyqy.ucoo.user.RelationshipRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow

class BuyFamilySeatsUseCase constructor(
    private val relationshipRepository: RelationshipRepository = RelationshipRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : SuspendUseCase<Int, Flow<ViewResult>>(ioDispatcher) {
    override suspend fun execute(parameters: Int): Flow<ViewResult> {
        val result = relationshipRepository.buyRelationshipSeat(parameters)
        return flow {
            if (result != null) {
                emit(ViewResult.ErrorResult(app.getString(R.string.亲友团席位购买成功)))
                emit(ViewResult.BuySeatsResult(parameters, result))
            } else {
                emit(ViewResult.ErrorResult(null))
            }
        }
    }
}