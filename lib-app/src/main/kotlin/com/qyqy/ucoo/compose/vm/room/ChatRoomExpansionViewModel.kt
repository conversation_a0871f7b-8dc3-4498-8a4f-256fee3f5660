package com.qyqy.ucoo.compose.vm.room

import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.compose.vm.room.ChatRoomExpansionMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.room.ChatRoomExpansionMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.room.ChatRoomExpansionMvi.ViewResult
import com.qyqy.ucoo.compose.vm.room.ChatRoomExpansionMvi.ViewState
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.merge

class ChatRoomExpansionViewModel(
    private val roomId: Int,
) : MviViewModel<ViewEvent, ViewResult, ViewState, ViewEffect>(ViewState()) {

    override fun Flow<ViewEvent>.toResults(): Flow<ViewResult> {
        return merge(
            filterIsInstance<ViewEvent.ChangeVideoEffect>().mapLatest {
                ViewResult.VideoEffectResult(it.path)
            }
        )
    }

    override suspend fun ViewResult.reduce(state: ViewState): ViewState {
        return state
    }

    override fun Flow<ViewResult>.toEffects(): Flow<ViewEffect> {
        return merge(
            filterIsInstance<ViewResult.VideoEffectResult>().mapLatest { result ->
                ViewEffect.VideoEffect(result.path)
            },
        )
    }

}