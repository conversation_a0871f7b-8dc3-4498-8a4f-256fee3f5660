package com.qyqy.ucoo.compose.presentation.login

import android.app.Activity
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.SideEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.saveable.rememberSaveable
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawWithCache
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.imageResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.ext.calculateLength
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asActivity
import com.qyqy.ucoo.base.show
import com.qyqy.ucoo.compose.clearFocusOnKeyboardDismiss
import com.qyqy.ucoo.compose.data.RegisterInfo
import com.qyqy.ucoo.compose.data.SelectItem
import com.qyqy.ucoo.compose.ui.AppBasicTextField
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.Loading
import com.qyqy.ucoo.mine.UploadImageContract
import com.qyqy.ucoo.mine.UploadImageViewModel
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.ReginCity
import com.qyqy.ucoo.user.RegionListActivity
import com.qyqy.ucoo.utils.rememberRequestAlbumPermissionHelper
import com.qyqy.ucoo.utils.taskflow.taskShowWithStack
import com.qyqy.ucoo.utils.toOSSObject
import com.qyqy.ucoo.widget.dialog.DatePickerDialog
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

@Composable
fun AccountBaseInfoScreen(
    registerInfo: RegisterInfo = RegisterInfo(),
    onAutoFillNickname: suspend (Int) -> String = { "" },
    onConfirm: (String, Boolean, String) -> Unit = { _, _, _ -> },
    onBack: () -> Unit = {},
) {
    var hasFocus by remember { mutableStateOf(false) }
    val focusRequester = remember { FocusRequester() } //焦点
    val focusManager = LocalFocusManager.current
    val context = LocalContext.current
    val resources = context.resources

    var editedNickname by rememberSaveable { // 是否编辑过nickname
        mutableStateOf(false)
    }

    var canRandomNickname by rememberSaveable { // 是否可以随机nickname
        mutableStateOf(!editedNickname)
    }

    var nickname by rememberSaveable {
        mutableStateOf(registerInfo.name)
    }

    var isBoy by rememberSaveable {
        mutableStateOf(registerInfo.isBoy)
    }

    var birthday by rememberSaveable {
        mutableStateOf(registerInfo.birthday)
    }

    var selection by remember {
        mutableStateOf<TextRange?>(null)
    }

    LaunchedEffect(key1 = isBoy) {
        if (canRandomNickname) {
            val value = onAutoFillNickname(if (isBoy) 1 else 2)
            if (!editedNickname && value.isNotEmpty()) {  // 如果随机结果返回之前手动编辑过则忽略
                nickname = value
                selection = TextRange(value.length)
            }
        }
    }

    SideEffect {
        selection = null
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .drawWithCache {
                val imageBitmap = ImageBitmap.imageResource(resources, id = R.drawable.header_home)
                val dstSize = IntSize(
                    size.width.toInt(),
                    size.width
                        .div(1125)
                        .times(840)
                        .toInt()
                )
                onDrawBehind {
                    drawImage(image = imageBitmap, dstSize = dstSize)
                }
            }
            .systemBarsPadding()
    ) {
        AppTitleBar("", modifier = Modifier.padding(start = 5.dp), onBack = {
            if (hasFocus) {
                focusManager.clearFocus(true)
            }
            onBack()
        })

        Text(
            text = stringResource(id = R.string.填写资料让大家认识你),
            modifier = Modifier.padding(start = 24.dp, top = 32.dp),
            fontSize = 24.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
            lineHeight = 40.sp
        )

        Row(
            modifier = Modifier
                .padding(top = 24.dp, start = 28.dp, end = 28.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceAround
        ) {
            Column(
                modifier = Modifier.clickable {
                    isBoy = true
                },
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_gender_male),
                    contentDescription = null,
                    modifier = Modifier
                        .size(80.dp)
                        .background(Color(0xFF342D43), CircleShape)
                        .border(1.5.dp, if (isBoy) Color(0xFF5CADFF) else Color.Transparent, CircleShape)
                        .clip(CircleShape)
                        .padding(26.dp),
                    colorFilter = ColorFilter.tint(if (isBoy) Color(0xFF5CADFF) else Color(0xFF9694A0))
                )
                AppText(
                    text = stringResource(id = R.string.i_am_male),
                    modifier = Modifier.padding(top = 12.dp),
                    fontSize = 14.sp,
                    color = if (isBoy) colorResource(id = R.color.white) else colorResource(id = R.color.white_alpha_50)
                )
            }

            Column(
                modifier = Modifier.clickable {
                    isBoy = false
                },
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_gender_female),
                    contentDescription = null,
                    modifier = Modifier
                        .size(80.dp)
                        .background(Color(0xFF342D43), CircleShape)
                        .border(1.5.dp, if (!isBoy) Color(0xFFFF7EC6) else Color.Transparent, CircleShape)
                        .clip(CircleShape)
                        .padding(26.dp),
                    colorFilter = ColorFilter.tint(if (!isBoy) Color(0xFFFF7EC6) else Color(0xFF9694A0))
                )
                AppText(
                    text = stringResource(id = R.string.i_am_female),
                    modifier = Modifier.padding(top = 12.dp),
                    fontSize = 14.sp,
                    color = if (!isBoy) colorResource(id = R.color.white) else colorResource(id = R.color.white_alpha_50)
                )
            }
        }

        LoginInputCard(
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .padding(top = 24.dp)
                .fillMaxWidth()
                .height(56.dp)
                .focusRequester(focusRequester)
                .onFocusChanged { hasFocus = it.isFocused }
                .clearFocusOnKeyboardDismiss(),
            prefix = stringResource(id = R.string.nickname),
            content = nickname,
            hintValue = stringResource(id = R.string.please_input_your_nick_name),
            selection = selection,
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
            keyboardActions = KeyboardActions(
                onDone = {
                    if (nickname.isNotEmpty()) {
                        onConfirm(nickname, isBoy, birthday)
                    }
                }
            )
        ) {
            if (it.calculateLength() <= 20) {
                editedNickname = true
                canRandomNickname = false
                nickname = it
            }
        }

        LaunchedEffect(Unit) {
            delay(150)
            if (!hasFocus) {
                focusRequester.requestFocus()
            }
        }

        Row(
            modifier = Modifier
                .padding(horizontal = 24.dp)
                .padding(top = 24.dp)
                .fillMaxWidth()
                .height(56.dp)
                .clip(CircleShape)
                .clickable {
                    context.asActivity?.apply {
                        DatePickerDialog
                            .Builder(this)
                            .setDate(datePosition = birthday)
                            .setSelectDateListener { date ->
                                birthday = date
                            }
                            .taskShowWithStack()
                    }
                }
                .background(Color(0x0DFFFFFF))
                .padding(horizontal = 24.dp),
            horizontalArrangement = Arrangement.SpaceBetween,
            verticalAlignment = Alignment.CenterVertically
        ) {
            val date = remember(birthday) {
                birthday.split("-")
            }
            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AppText(text = date[0], fontSize = 16.sp, color = Color.White)
                AppText(text = stringResource(id = R.string.年), fontSize = 16.sp, color = Color.White.copy(0.35f))
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AppText(text = date[1], fontSize = 16.sp, color = Color.White)
                AppText(text = stringResource(id = R.string.月), fontSize = 16.sp, color = Color.White.copy(0.35f))
            }

            Row(
                horizontalArrangement = Arrangement.spacedBy(8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                AppText(text = date[2], fontSize = 16.sp, color = Color.White)
                AppText(text = stringResource(id = R.string.日), fontSize = 16.sp, color = Color.White.copy(0.35f))
            }
        }

        LoginButton(
            text = stringResource(id = R.string.next_step),
            modifier = Modifier
                .padding(top = 64.dp)
                .padding(horizontal = 48.dp)
                .fillMaxWidth(),
            enabled = nickname.isNotEmpty(),
            onClick = {
                onConfirm(nickname, isBoy, birthday)
            }
        )
    }
}

@Preview(
    "手机号验证登录页面",
    widthDp = 375,
    heightDp = 812,
)
@Composable
fun AccountBaseInfoPreview() {
    MaterialTheme {
        AccountBaseInfoScreen()
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AccountOtherInfoScreen(
    registerInfo: RegisterInfo = RegisterInfo(),
    onConfirm: (SelectItem, SelectItem, String) -> Unit = { _, _, _ -> },
    onBack: () -> Unit = {},
) {

    val context = LocalContext.current
    val resources = context.resources

    var selectedLocation by rememberSaveable {
        mutableStateOf(registerInfo.location)
    }

    var selectedLife by rememberSaveable {
        mutableStateOf(registerInfo.life)
    }

    var inviteCode by rememberSaveable {
        mutableStateOf(registerInfo.inviteCode)
    }

    val launcher = rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) {
        if (it.resultCode == Activity.RESULT_OK) {
            it.data?.getParcelableExtra<ReginCity>(RegionListActivity.REGION_KEY)?.also { region ->
                (selectedLocation.takeIf { item ->
                    item.id == "-1"
                } ?: registerInfo.locationList.find { item ->
                    item.id == "-1"
                })?.also { item ->
                    selectedLocation = item.copy(text = region.chineseName, value = region.abbreviation)
                }
            }
        }
    }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .drawWithCache {
                val imageBitmap = ImageBitmap.imageResource(resources, id = R.drawable.header_home)
                val dstSize = IntSize(
                    size.width.toInt(),
                    size.width
                        .div(1125)
                        .times(840)
                        .toInt()
                )
                onDrawBehind {
                    drawImage(image = imageBitmap, dstSize = dstSize)
                }
            }
            .verticalScroll(rememberScrollState())
            .systemBarsPadding()
    ) {
        AppTitleBar("", modifier = Modifier.padding(start = 5.dp), onBack = onBack)
        Text(
            text = buildAnnotatedString {
                append(stringResource(id = R.string.目前居住地区))
                withStyle(SpanStyle(fontSize = 14.sp)) {
                    append("：${selectedLocation.text}")
                }
            },
            modifier = Modifier.padding(start = 24.dp, top = 32.dp),
            fontSize = 24.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
        )

        FlowRow(
            modifier = Modifier
                .padding(top = 24.dp)
                .padding(horizontal = 24.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp),
        ) {
            for (selectItem in registerInfo.locationList) {
                SelectTextItem(
                    item = selectItem,
                    selected = selectedLocation.id == selectItem.id,
                    modifier = Modifier.clickable {
                        if (selectItem.id == "-1") {
                            launcher.launch(RegionListActivity.createIntent(context))
                        } else {
                            selectedLocation = selectItem
                        }
                    })
            }
        }

        Text(
            text = buildAnnotatedString {
                append(stringResource(id = R.string.在当地的生活状态))
                withStyle(SpanStyle(fontSize = 14.sp)) {
                    append("：${selectedLife.text}")
                }
            },
            modifier = Modifier.padding(start = 24.dp, top = 48.dp),
            fontSize = 24.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
        )

        FlowRow(
            modifier = Modifier
                .padding(top = 24.dp)
                .padding(horizontal = 30.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(15.dp),
            verticalArrangement = Arrangement.spacedBy(15.dp),
        ) {
            for (selectItem in registerInfo.lifeList) {
                SelectTextItem(
                    item = selectItem,
                    selected = selectedLife.id === selectItem.id,
                    modifier = Modifier.clickable {
                        selectedLife = selectItem
                    }
                )
            }
        }

        if (!registerInfo.isBoy) {

            var hasFocus by remember { mutableStateOf(false) }
            val focusRequester = remember { FocusRequester() } //焦点
            val focusManager = LocalFocusManager.current

            Text(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.desc_invite_code))
                    addStyle(SpanStyle(fontSize = 14.sp), 3, length)
                },
                modifier = Modifier.padding(start = 24.dp, top = 48.dp),
                fontSize = 24.sp,
                color = Color.White,
                fontWeight = FontWeight.Medium,
            )

            Box(
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .padding(top = 24.dp)
                    .fillMaxWidth()
                    .height(56.dp)
                    .clip(CircleShape)
                    .background(Color(0x0DFFFFFF))
                    .padding(horizontal = 24.dp)
                    .focusRequester(focusRequester)
                    .onFocusChanged { hasFocus = it.isFocused }
                    .clearFocusOnKeyboardDismiss(),
                contentAlignment = Alignment.CenterStart
            ) {
                AppBasicTextField(
                    value = inviteCode,
                    onValueChange = {
                        inviteCode = it
                    },
                    modifier = Modifier.fillMaxWidth(),
                    hintValue = stringResource(id = R.string.hint_input_invitecode),
                    singleLine = true,
                    textStyle = TextStyle(
                        color = Color.White,
                        textAlign = TextAlign.Start,
                        letterSpacing = 1.sp,
                        fontSize = 16.sp,
                    ),
                    hintStyle = TextStyle(
                        color = Color.White.copy(0.35f),
                        textAlign = TextAlign.Start,
                        letterSpacing = 1.sp,
                        fontSize = 16.sp,
                    ),
                    cursorBrush = SolidColor(Color.White),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (!selectedLocation.isNull && !selectedLife.isNull) {
                                if (inviteCode.isNotEmpty() && inviteCode.length != 6) {
                                    toastRes(R.string.invite_format_error)
                                    return@KeyboardActions
                                }
                                if (hasFocus) {
                                    focusManager.clearFocus(true)
                                }
                                onConfirm(selectedLocation, selectedLife, inviteCode)
                            }
                        }
                    )
                )
            }

            LaunchedEffect(Unit) {
                delay(150)
                if (!hasFocus) {
                    focusRequester.requestFocus()
                }
            }
        }

        val keyboardController = LocalSoftwareKeyboardController.current
        LoginButton(
            text = if (registerInfo.isBoy) stringResource(id = R.string.start_app) else stringResource(id = R.string.next_step),
            modifier = Modifier
                .padding(top = 64.dp, bottom = 25.dp)
                .padding(horizontal = 48.dp)
                .fillMaxWidth(),
            enabled = !selectedLocation.isNull && !selectedLife.isNull,
            onClick = {
                if (inviteCode.isNotEmpty() && inviteCode.length != 6) {
                    toastRes(R.string.invite_format_error)
                    return@LoginButton
                }
                keyboardController?.hide()
                onConfirm(selectedLocation, selectedLife, inviteCode)
            }
        )
    }
}

@Composable
private fun SelectTextItem(item: SelectItem, selected: Boolean, modifier: Modifier = Modifier) {
    AppText(
        text = item.text,
        modifier = Modifier
            .border(1.dp, if (selected) Color(0xFF945EFF) else Color.Transparent, CircleShape)
            .clip(CircleShape)
            .background(Color(0x0DFFFFFF))
            .then(modifier)
            .padding(horizontal = 20.dp, vertical = 12.dp),
        color = if (selected) Color.White else colorResource(id = R.color.white_alpha_50),
        fontSize = 16.sp
    )
}
//
//@Preview(
//    "手机号验证登录页面",
//    widthDp = 375,
//    heightDp = 812,
//)
//@Composable
//fun AccountExtraInfoPreview() {
//    MaterialTheme {
//        AccountOtherInfoScreen(
//            RegisterInfo(
//                locationList = listOf(
//                    SelectItem("1", "中国大陆"),
//                    SelectItem("2", "中国台湾"),
//                    SelectItem("3", "美国"),
//                    SelectItem("4", "中国香港"),
//                    SelectItem("5", "菲律宾"),
//                    SelectItem("-1", "选择其他"),
//                ),
//                lifeList = listOf(
//                    SelectItem("1", "出生地"),
//                    SelectItem("2", "移民"),
//                    SelectItem("3", "留学"),
//                    SelectItem("4", "工作"),
//                    SelectItem("5", "旅游"),
//                ),
//            )
//        )
//    }
//}

@Composable
fun UploadAccountAvatarScreen(
    registerInfo: RegisterInfo = RegisterInfo(isBoy = false),
    onConfirm: (String, String) -> Unit = { _, _ -> },
    onBack: () -> Unit = {},
) {

    val context = LocalContext.current

    var avatarUrl by rememberSaveable {
        mutableStateOf(registerInfo.avatar)
    }

    val uploadViewModel = viewModel<UploadImageViewModel>()

    val loading by uploadViewModel.uiState.map {
        it.loadingState.isLoading
    }.collectAsStateWithLifecycle(false)

    val uploadImageHelper = rememberRequestAlbumPermissionHelper(context, isCupid = false) {
        val result = it.list.first()
        uploadViewModel.sendEvent(UploadImageContract.Event.UploadImage(result.name, result.toOSSObject(), false))
    }

    LaunchedEffect(Unit) {

        launch {
            uploadViewModel.uiState.map {
                it.uploadUrl
            }.filter {
                !it.isNullOrEmpty()
            }.onEach {
                avatarUrl = it!!
            }.launchIn(this)
        }

        launch {
            uploadViewModel.effect.onEach {
                it.show()
            }.launchIn(this)
        }

    }

    UploadAccountAvatarPage(avatarUrl, registerInfo, onConfirm, onBack) {
        uploadImageHelper.start()
    }

    if (loading) {
        Loading {
            uploadViewModel.sendEvent(UploadImageContract.Event.CancelUploadImage)
        }
    }
}


@Composable
fun UploadAccountAvatarPage(
    avatarUrl: String = "",
    registerInfo: RegisterInfo = RegisterInfo(isBoy = false),
    onConfirm: (String, String) -> Unit = { _, _ -> },
    onBack: () -> Unit = {},
    onSelect: () -> Unit = {},
) {

    val context = LocalContext.current
    val resources = context.resources

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Color(0xFF1C1D1E))
            .drawWithCache {
                val imageBitmap = ImageBitmap.imageResource(resources, id = R.drawable.header_home)
                val dstSize = IntSize(
                    size.width.toInt(),
                    size.width
                        .div(1125)
                        .times(840)
                        .toInt()
                )
                onDrawBehind {
                    drawImage(image = imageBitmap, dstSize = dstSize)
                }
            }
            .systemBarsPadding(),
    ) {

        AppTitleBar("", modifier = Modifier.padding(start = 5.dp), onBack = onBack)

        Text(
            text = stringResource(id = R.string.upload_avatar),
            modifier = Modifier.padding(start = 24.dp, top = 32.dp),
            fontSize = 24.sp,
            color = Color.White,
            fontWeight = FontWeight.Medium,
        )

        Box(
            modifier = Modifier
                .align(Alignment.CenterHorizontally)
                .padding(top = 48.dp)
                .height(133.dp)

        ) {

            if (avatarUrl.isNotEmpty()) {
                CircleComposeImage(
                    model = avatarUrl,
                    modifier = Modifier
                        .size(120.dp)
                        .clickable(onClick = onSelect)
                )
            } else {
                Image(
                    painter = painterResource(id = R.drawable.ic_add_tribe_avator_cross),
                    contentDescription = null,
                    modifier = Modifier
                        .size(120.dp)
                        .background(Color(0xFF2B2A32), CircleShape)
                        .border(1.dp, Color(0xFF5E5E60), CircleShape)
                        .clip(CircleShape)
                        .clickable(onClick = onSelect),
                    contentScale = ContentScale.Inside
                )
            }

            Box(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .height(26.dp)
                    .clip(CircleShape)
                    .background(Color(0xFF945EFF))
                    .padding(horizontal = 8.dp),
                contentAlignment = Alignment.Center
            ) {
                AppText(
                    text = stringResource(id = R.string.upload_avatar),
                    fontSize = 14.sp,
                    color = Color.White,
                )
            }
        }

        Text(
            text = stringResource(id = R.string.tip_plathform),
            modifier = Modifier.padding(top = 16.dp, start = 60.dp, end = 60.dp),
            fontSize = 12.sp,
            color = Color.White,
            lineHeight = 16.sp,
            textAlign = TextAlign.Center
        )

        var inviteCode by rememberSaveable {
            mutableStateOf(registerInfo.inviteCode)
        }

        if (!registerInfo.isBoy) {
            var hasFocus by remember { mutableStateOf(false) }
            val focusRequester = remember { FocusRequester() } //焦点
            val focusManager = LocalFocusManager.current

            Text(
                text = buildAnnotatedString {
                    append(stringResource(id = R.string.desc_invite_code))
                    addStyle(SpanStyle(fontSize = 14.sp), 3, length)
                },
                modifier = Modifier.padding(start = 24.dp, top = 48.dp),
                fontSize = 24.sp,
                color = Color.White,
                fontWeight = FontWeight.Medium,
            )

            Box(
                modifier = Modifier
                    .padding(horizontal = 24.dp)
                    .padding(top = 24.dp)
                    .fillMaxWidth()
                    .height(56.dp)
                    .clip(CircleShape)
                    .background(Color(0x0DFFFFFF))
                    .padding(horizontal = 24.dp)
                    .focusRequester(focusRequester)
                    .onFocusChanged { hasFocus = it.isFocused }
                    .clearFocusOnKeyboardDismiss(),
                contentAlignment = Alignment.CenterStart
            ) {
                AppBasicTextField(
                    value = inviteCode,
                    onValueChange = {
                        inviteCode = it.filter { c ->
                            c.isDigit()
                        }
                    },
                    modifier = Modifier.fillMaxWidth(),
                    hintValue = stringResource(id = R.string.hint_input_invitecode),
                    singleLine = true,
                    textStyle = TextStyle(
                        color = Color.White,
                        textAlign = TextAlign.Start,
                        letterSpacing = 1.sp,
                        fontSize = 16.sp,
                    ),
                    hintStyle = TextStyle(
                        color = Color.White.copy(0.35f),
                        textAlign = TextAlign.Start,
                        letterSpacing = 1.sp,
                        fontSize = 16.sp,
                    ),
                    cursorBrush = SolidColor(Color.White),
                    keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number, imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            if (avatarUrl.isEmpty()) {
                                toastRes(R.string.请上传头像)
                                return@KeyboardActions
                            }
                            if (inviteCode.isNotEmpty() && inviteCode.length != 6) {
                                toastRes(R.string.invite_format_error)
                                return@KeyboardActions
                            }
                            if (hasFocus) {
                                focusManager.clearFocus(true)
                            }
                            onConfirm(avatarUrl, inviteCode)
                        }
                    )
                )
            }

//            LaunchedEffect(Unit) {
//                delay(150)
//                if (!hasFocus) {
//                    focusRequester.requestFocus()
//                }
//            }
        }

        Spacer(modifier = Modifier.fillMaxHeight(0.56f))

        LoginButton(
            text = stringResource(id = R.string.start_app),
            modifier = Modifier
                .padding(horizontal = 48.dp)
                .fillMaxWidth(),
            enabled = avatarUrl.isNotEmpty(),
            onClick = {
                onConfirm(avatarUrl, inviteCode)
            }
        )
    }
}

@Preview(
    "手机号验证登录页面",
    widthDp = 375,
    heightDp = 812,
)
@Composable
fun UploadAccountAvatarPagePreview() {
    UploadAccountAvatarPage()
}

