package com.qyqy.ucoo.compose.presentation.cp_house.house

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.annotation.DrawableRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyHorizontalGrid
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.ui.dialog.IDialog
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.NormalDialog
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.pages.CPHouseDestination
import com.qyqy.ucoo.compose.presentation.cp_house.bean.AwardItem
import com.qyqy.ucoo.compose.presentation.cp_house.bean.AwardResult
import com.qyqy.ucoo.compose.presentation.cp_house.bean.LotterySettings
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.LiStateViewModel
import com.qyqy.ucoo.compose.state.itemLoadMore
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AppTitleBar
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.viewModelOfActivity
import com.qyqy.ucoo.compose.vm.cp_house.CPHouseViewModel
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.sAppKV
import com.qyqy.ucoo.utils.ComposeState
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.encodeToString
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query
import java.text.SimpleDateFormat
import java.util.Locale

@Composable
fun HouseTreasureBoxScreen(
    vm: HouseLotteryViewModel = viewModel(),
    vmRecord: LotteryRecordViewModel = getLotteryViewModel()
) {
    val nav = LocalUCOONavController.current
    var position by remember {
        mutableIntStateOf(0)
    }
    val context = LocalContext.current
    val dq = rememberDialogQueue<IDialogAction>()
    dq.DialogContent()
    val text = LocalHouseText.current
    val scope = rememberCoroutineScope()
    val viewModelMain = viewModelOfActivity<CPHouseViewModel>()
    var requesting by remember { mutableStateOf(false) }
    fun startLottery(conf: LotterySettings.Conf) {
        if (requesting) return
        val interval = 100L
        requesting = true
        scope.launch {
//            delay(1000L)
            runApiCatching {
                vm.lotteryApi.startLottery(mapOf("position" to conf.position.toString()))
            }.onSuccess { result ->
                requesting = false
                position = 0
                repeat(40) {
                    val target = (position + 1) % 10
                    position = if (target == 0) 10 else target
                    delay(interval)
                }
                val l = result.awardList
                if (l.size == 1) {
                    val p = result.awardPosition.getOrNull(0) ?: 0
                    while (position != p && p > 0) {
                        val target = (position + 1) % 10
                        position = if (target == 0) 10 else target
                        delay(interval)
                    }
                } else {
                    position = 0
                }
                dq.push(
                    LotteryResultDialog(
                        l,
                        if (conf.coinType == LotterySettings.Conf.COIN_TYPE_UCOO) text.costU else text.costH
                    ) {
                        dq.pop()
                        startLottery(conf)
                    })
                val balance = result.balance
                // 1: 装扮, 2: 小屋币
                viewModelMain?.updateHouseInfo(balance, l.any { it.awardType == 1 })
                vm.updateBalance(balance)
                //刷新抽奖记录
                vmRecord.refresh()
            }.onFailure {
                requesting = false
                position = 0
            }.toastError()
        }
    }

    val prizeList by vm.awardList
    val buttonCell by vm.buttonCells
    val settings by vm.settings
    val loading by vm.loading
    LaunchedEffect(key1 = Unit) {
        vm.requestSettings()
    }
    MaterialTheme(
        colorScheme = MaterialTheme.colorScheme.copy(
            onPrimary = Color(0xFF1D2129),
            onSecondary = Color(0xFF86909C)
        )
    ) {
        Content(
            prizeList, buttonCell,
            loading = loading,
            position = position,
            myCoinCount = settings.balance,
            tips = settings.tips,
            record = {
                LotteryRecord(vmRecord)
            },
            onClickRule = {
                AppLinkManager.open(context, settings.rule)
            },
            onCheckMoreRecord = {
                nav.navigateTo(CPHouseDestination.LotteryRecord)
            },
            onGotoDressUpCenter = {
                nav.navigateTo(CPHouseDestination.HouseDressUpDest)
            },
            onStartLottery = {
                startLottery(it)
            })
    }

}

@Composable
private fun Content(
    lotteryCell: List<LotteryCell.AwardCell>,
    buttonCell: List<LotteryCell.ButtonCell>,
    tips: String = "",
    loading: Boolean = false,
    position: Int = -1,
    myCoinCount: Int = 0,
    record: @Composable () -> Unit = {},
    onClickRule: OnClick = {},
    onGotoDressUpCenter: OnClick = {},
    onCheckMoreRecord: OnClick = {},
    onStartLottery: (LotterySettings.Conf) -> Unit = {},
) {
    val text = LocalHouseText.current
    val backOwner = LocalOnBackPressedDispatcherOwner.current
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Brush.verticalGradient(listOf(Color(0xFFB588FF), Color(0xFF7C2DC6))))
            .systemBarsPadding()
    ) {
        AppTitleBar(
            title = stringResource(id = text.treasureBox),
            onBack = { backOwner?.onBackPressedDispatcher?.onBackPressed() }) {
            Text(
                text = stringResource(id = text.dressUpCenter),
                color = Color.White,
                modifier = Modifier
                    .clickWithShape(onClick = onGotoDressUpCenter)
                    .align(Alignment.CenterEnd)
                    .padding(16.dp, 8.dp)
            )
        }
        val cells = remember(lotteryCell, buttonCell) {
            buildList {
                try {
                    add(lotteryCell[0])
                    add(lotteryCell[1])
                    add(lotteryCell[2])
                    add(lotteryCell[9])
                    add(buttonCell[0])
                    add(lotteryCell[3])
                    add(lotteryCell[8])
                    add(buttonCell[1])
                    add(lotteryCell[4])
                    add(lotteryCell[7])
                    add(lotteryCell[6])
                    add(lotteryCell[5])
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
        }
        val focus = if (position in 1..10) {
            lotteryCell.getOrNull(position - 1)
        } else null
        Spacer(modifier = Modifier.height(8.dp))
        Box(modifier = Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(id = R.drawable.bg_house_lottery),
                "",
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.FillWidth,
                alignment = Alignment.TopCenter
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()

            ) {
                Spacer(modifier = Modifier.height(32.dp))
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(40.dp)
                        .padding(horizontal = 24.dp)
                        .background(Color(0xFFE9A5FF), Shapes.corner12)
                        .padding(4.dp)
                        .background(Color(0xFF2E2648), Shapes.corner12)
                        .padding(horizontal = 12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Image(
                        painter = painterResource(id = R.drawable.icon_house_coin),
                        contentDescription = "",
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    AppText(
                        text = stringResource(id = text.formatMyHouseCoin, myCoinCount),
                        color = Color.White,
                        fontSize = 14.sp
                    )
                    Spacer(modifier = Modifier.weight(1f))
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.click(onClick = onClickRule)
                    ) {
                        AppText(
                            text = stringResource(id = text.boxRule),
                            fontSize = 12.sp,
                            color = Color(0xFFE9A5FF)
                        )
                        Spacer(modifier = Modifier.width(2.dp))
                        Image(
                            painter = painterResource(id = R.drawable.ic_arrow_blue),
                            contentDescription = "",
                            modifier = Modifier.size(12.dp)
                        )
                    }
                }
                Spacer(modifier = Modifier.height(46.dp))
                LazyVerticalGrid(
                    columns = GridCells.Fixed(3),
                    modifier = Modifier
                        .align(Alignment.CenterHorizontally)
                        .width((97 * 3 + 4 * 2).dp)
                        .heightIn(min = (97 * 4 + 4 * 3).dp, max = 500.dp),
                    horizontalArrangement = Arrangement.spacedBy(4.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(cells) { cell ->
                        cell.Content(focusCell = focus) {
                            val conf = it as? LotterySettings.Conf
                            conf?.let {
                                onStartLottery.invoke(conf)
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.height(44.dp))
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    AppText(
                        text = stringResource(id = text.lotteryRecord),
                        modifier = Modifier.padding(horizontal = 16.dp),
                        fontSize = 16.sp,
                        color = Color.White
                    )
                    AppText(
                        text = stringResource(id = text.checkMore),
                        modifier = Modifier
                            .click(onClick = onCheckMoreRecord)
                            .padding(horizontal = 16.dp),
                        fontSize = 12.sp,
                        color = Color.White
                    )
                }
                Spacer(modifier = Modifier.height(6.dp))
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                ) {
                    record()
                }
                Spacer(modifier = Modifier.height(8.dp))
            }
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .heightIn(min = 32.dp),
                contentAlignment = Alignment.Center
            ) {
                AppText(
                    text = tips,
                    fontSize = 12.sp,
                    color = Color(0xFF5F1373),
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 32.dp),
                    textAlign = TextAlign.Center,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
fun LotteryRecord(
    vm: LotteryRecordViewModel,
    whiteTheme: Boolean = true,
    onEmpty: @Composable () -> Unit = {}
) {
    val sdf = remember {
        SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
    }
    LaunchedEffect(key1 = vm) {
        vm.refresh()
    }
    val recordList by vm.dataState
    Box(modifier = Modifier.fillMaxSize()) {
        if (recordList.isEmpty()) {
            onEmpty()
        } else {
            LazyColumn(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(horizontal = 16.dp)
                    .then(
                        if (whiteTheme)
                            Modifier
                                .background(
                                    Color.White,
                                    Shapes.small
                                )
                                .padding(horizontal = 12.dp)
                        else Modifier
                    )
            ) {
                itemsIndexed(recordList) { index, item ->
                    LotteryRecordItem(
                        title = item.recordTitle,
                        time = sdf.format(item.timeStamp * 1000),
                        icon = item.icon
                    )
                    if (index != recordList.lastIndex) {
                        HorizontalDivider(
                            color = if (whiteTheme) Color(0xFFF0F0F0) else Color.White.copy(
                                0.25f
                            ), thickness = 0.5.dp
                        )
                    }
                }
                itemLoadMore(vm.allowLoad && recordList.isNotEmpty(), vm.isLoadingNow, vm.hasMore) {
                    vm.loadMore()
                }
            }
        }
    }
}

@Composable
fun LotteryRecordScreen() {
    val text = LocalHouseText.current
    val bo = LocalOnBackPressedDispatcherOwner.current
    val vm = getLotteryViewModel()
    MaterialTheme(
        colorScheme = MaterialTheme.colorScheme.copy(
            onPrimary = Color.White,
            onSecondary = Color.White.copy(alpha = 0.5f)
        )
    ) {
        Scaffold(modifier = Modifier
            .fillMaxSize()
            .systemBarsPadding(), topBar = {
            AppTitleBar(title = stringResource(id = text.lotteryRecord), onBack = {
                bo?.onBackPressedDispatcher?.onBackPressed()
            })
        }) {
            Box(modifier = Modifier.padding(it)) {
                LotteryRecord(vm = vm, whiteTheme = false, onEmpty = {
                    EmptyView(modifier = Modifier.fillMaxSize(), text.nodata)
                })
            }
        }
    }
}

@Composable
private fun getLotteryViewModel(): LotteryRecordViewModel {
    val context = LocalContext.current
    return context.asComponentActivity?.let {
        viewModel<LotteryRecordViewModel>(viewModelStoreOwner = it)
    } ?: viewModel()
}

@Composable
private fun LotteryRecordItem(title: String, time: String, icon: String) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 8.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Column(modifier = Modifier.weight(1f)) {
            Text(text = title, color = MaterialTheme.colorScheme.onPrimary)
            Spacer(modifier = Modifier.height(6.dp))
            Text(text = time, fontSize = 12.sp, color = MaterialTheme.colorScheme.onSecondary)
        }
        ComposeImage(model = icon, modifier = Modifier.size(40.dp))
    }
}

@Composable
private fun LotteryButton(
    title: String,
    coinDesc: String,
    @DrawableRes bgRes: Int,
    onClick: OnClick = {}
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .height(47.dp)
            .click(onClick = onClick)
            .paint(painterResource(id = bgRes), contentScale = ContentScale.FillBounds)
            .padding(vertical = 3.dp),
        verticalArrangement = Arrangement.SpaceAround,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AppText(text = title, fontWeight = FontWeight.Bold, color = Color.White)
        AppText(text = coinDesc, fontSize = 12.sp, color = Color.White)
    }
}


sealed class LotteryCell {

    @Composable
    abstract fun Content(focusCell: LotteryCell?, onClick: (Any) -> Unit)

    data class AwardCell(
        val name: String,
        val icon: String,
        val duration: String? = "",
        val position: Int = 0
    ) :
        LotteryCell() {
        @Composable
        override fun Content(focusCell: LotteryCell?, onClick: (Any) -> Unit) {

            val text = LocalHouseText.current
            val isCurrent = this.position == (focusCell as? AwardCell)?.position
            val bgColorListNormal = remember {
                listOf(Color(0xFFF099FF), Color(0xFFFBCFFF))
            }
            val bgColorListFocus = remember {
                listOf(Color(0xFFBD3EFD), Color(0xFFF7A8FF))
            }

            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(if (isCurrent) bgColorListFocus else bgColorListNormal),
                            Shapes.medium
                        )
                        .border(
                            if (isCurrent) 4.dp else 2.dp,
                            if (isCurrent) Color.White else Color(0xFFFDDFFF),
                            Shapes.medium
                        ),
                    horizontalAlignment = Alignment.CenterHorizontally,
                    verticalArrangement = Arrangement.SpaceEvenly
                ) {
                    Box(modifier = Modifier.size(48.dp), contentAlignment = Alignment.Center) {
                        ComposeImage(model = icon, Modifier.fillMaxSize())
                        val d = duration
                        if (!d.isNullOrEmpty()) {
                            Box(
                                modifier = Modifier
                                    .background(
                                        Color(0xFFFDDFFF),
                                        Shapes.chip
                                    )
                                    .widthIn(min = 34.dp)
                                    .heightIn(min = 14.dp)
                                    .align(Alignment.BottomCenter),
                                contentAlignment = Alignment.Center
                            ) {
                                AppText(
                                    text = stringResource(id = text.formatDay, d),
                                    fontSize = 10.sp,
                                    color = Color(0xFF6A2F87)
                                )
                            }
                        }
                    }
                    AppText(
                        text = name,
                        color = Color(0xFF6A2F87),
                        fontSize = 12.sp,
                        maxLines = 1,
                        overflow = TextOverflow.Ellipsis
                    )
                }
            }
        }
    }

    data class ButtonCell(val confs: List<LotterySettings.Conf>) :
        LotteryCell() {
        @Composable
        override fun Content(
            focusCell: LotteryCell?,
            onClick: (Any) -> Unit
        ) {
            val text = LocalHouseText.current
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f),
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                confs.forEach { conf ->
                    LotteryButton(
                        title = stringResource(id = text.formatC, conf.count),
                        coinDesc = stringResource(
                            id = if (conf.coinType == LotterySettings.Conf.COIN_TYPE_UCOO) text.formatUCoin else text.formatHCoin,
                            conf.coinCost
                        ),
                        bgRes = when (conf.position) {
                            1 -> R.drawable.ic_pick1
                            2 -> R.drawable.ic_pick2
                            3 -> R.drawable.ic_pick3
                            else -> R.drawable.ic_pick4
                        },
                        onClick = {
                            onClick.invoke(conf)
                        }
                    )
                }
            }
        }
    }
}

@Preview
@Composable
private fun HouseTBPreview() {
    val b1 = LotteryCell.ButtonCell(
        listOf(
            LotterySettings.Conf(2000, 2, 1, 1),
            LotterySettings.Conf(18000, 2, 10, 2)
        )
    )
    val b2 = LotteryCell.ButtonCell(
        listOf(
            LotterySettings.Conf(200, 1, 1, 3),
            LotterySettings.Conf(1800, 1, 10, 4)
        )
    )
    Content(
        buildList {
            repeat(10) {
                add(LotteryCell.AwardCell("xx小屋币", "$it", duration = "15"))
            }
        },
        listOf(b1, b2),
        tips = "男生使用U币抽奖，女生最多获得60%积分奖励",
    )
}

class LotteryResultDialog(
    private val prizeList: List<AwardItem>,
    val buttonText: Int,
    val onClick: OnClick
) : NormalDialog<IDialogAction>() {
    @Composable
    override fun Content(dialog: IDialog, onAction: IDialogAction?) {
        LotteryResult(
            prizeList = prizeList,
            buttonText = stringResource(id = buttonText),
            onClick = onClick
        )
    }
}

@Composable
private fun LotteryResult(prizeList: List<AwardItem>, buttonText: String, onClick: OnClick = {}) {
    val text = LocalHouseText.current

    Column(
        modifier = Modifier
            .background(Color(0xFFF4EDFF), Shapes.small)
            .fillMaxWidth()
            .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = text.prizeResult),
            fontSize = 16.sp,
            color = Color(0xFF6A2F87),
            fontWeight = FontWeight.Bold
        )
        Spacer(modifier = Modifier.height(20.dp))
        if (prizeList.size < 4) {
            Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.Center) {
                prizeList.forEachIndexed { index, prizeItem ->
                    PrizeResultItem(
                        name = prizeItem.name,
                        icon = prizeItem.icon,
                        count = prizeItem.count,
                        duration = prizeItem.duration
                    )
                    if (index < prizeList.lastIndex) {
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                }
            }
        } else {
            if (prizeList.size in 4..6) {
                LazyVerticalGrid(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp)
                        .heightIn(max = 258.dp),
                    columns = GridCells.Fixed(3),
                    horizontalArrangement = Arrangement.SpaceAround,
                    verticalArrangement = Arrangement.spacedBy(10.dp)
                ) {
                    items(prizeList) { prize ->
                        PrizeResultItem(
                            name = prize.name,
                            icon = prize.icon,
                            count = prize.count,
                            duration = prize.duration
                        )
                    }
                }
            } else {
                LazyHorizontalGrid(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(start = 16.dp)
                        .heightIn(max = 258.dp),
                    rows = GridCells.Fixed(2),
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(prizeList) { prize ->
                        PrizeResultItem(
                            name = prize.name,
                            icon = prize.icon,
                            count = prize.count,
                            duration = prize.duration
                        )
                    }
                }
            }
        }

        Spacer(modifier = Modifier.height(12.dp))
        Text(
            text = stringResource(id = text.descPrize),
            fontSize = 12.sp,
            color = Color(0xFF86909C)
        )
        Spacer(modifier = Modifier.height(12.dp))
        Box(
            modifier = Modifier
                .clickWithShape(Shapes.chip, onClick = onClick)
                .background(color = Color(0xFF9240D1), Shapes.chip)
                .widthIn(min = 192.dp)
                .heightIn(min = 40.dp)
                .padding(horizontal = 12.dp),
            contentAlignment = Alignment.Center
        ) {
            Text(text = buttonText, color = Color.White)
        }
    }
}

@Preview
@Composable
private fun ResultPreview() {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(Color.Black),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(modifier = Modifier.width(296.dp)) {
            LotteryResult(prizeList = buildList {
                repeat(3) {
                    add(AwardItem(duration = 2, name = "自行车"))
                }
            }, buttonText = "继续开")
        }
        Spacer(modifier = Modifier.height(20.dp))
        Box(modifier = Modifier.width(296.dp)) {
            LotteryResult(prizeList = buildList {
                repeat(10) {
                    add(AwardItem(duration = 2, name = "自行车"))
                }
            }, buttonText = "继续开")
        }

    }
}

@Composable
private fun PrizeResultItem(name: String, icon: String, count: Int, duration: Int) {
    val text = LocalHouseText.current
    val color = Color(0xFF6A2F87)
    Column(
        modifier = Modifier
            .width(80.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Box(
            modifier = Modifier
                .size(80.dp)
                .background(
                    Brush.verticalGradient(listOf(Color(0xFFF099FF), Color(0xFFFBCFFF))),
                    Shapes.corner12
                )
                .border(2.dp, Color(0xFFFDDFFF), Shapes.corner12)
        ) {
            ComposeImage(
                model = icon, modifier = Modifier
                    .fillMaxWidth()
                    .clip(Shapes.corner12)
            )
            Text(
                text = "x$count",
                color = color,
                fontSize = 12.sp,
                lineHeight = 20.sp,
                fontWeight = FontWeight.Medium,
                modifier = Modifier
                    .widthIn(min = 36.dp)
                    .align(Alignment.TopEnd)
                    .background(
                        Color(0xFFFDDFFF),
                        RoundedCornerShape(topEnd = 12.dp, bottomStart = 12.dp)
                    ),
                textAlign = TextAlign.Center
            )
        }
        Spacer(modifier = Modifier.height(6.dp))
        val style = TextStyle(color = color, fontSize = 12.sp, fontWeight = FontWeight.Medium)
        Text(text = name, style = style, maxLines = 1, overflow = TextOverflow.Ellipsis)
        Spacer(modifier = Modifier.height(4.dp))
        val d = if (duration > 0) {
            stringResource(id = text.formatDay, duration.toString())
        } else ""
        if (d.isNotEmpty()) {
            Text(text = "($d)", style = style)
        } else {
            Spacer(modifier = Modifier.height(14.dp))
        }
    }
}

interface LotteryApi {

    @GET("api/friendship/v1/cp_room/lottery/settings")
    suspend fun getSettings(): ApiResponse<LotterySettings>

    @GET("api/friendship/v1/cp_room/lottery/award_list")
    suspend fun getAwardList(): ApiResponse<List<AwardItem>>

    @POST("api/friendship/v1/cp_room/lottery/invoke")
    suspend fun startLottery(@Body map: Map<String, String>): ApiResponse<AwardResult>

    @GET("api/friendship/v1/cp_room/lottery/award/history")
    suspend fun getLotteryRecord(@Query("last_id") lastId: Int?): ApiResponse<List<AwardItem>>
}

class LotteryRecordViewModel : LiStateViewModel<AwardItem>() {
    private val lotteryApi = com.qyqy.ucoo.http.createApi<LotteryApi>()
    private var lastId: Int? = -1

    init {
        refresh()
    }

    override fun onStartRequest(isRefresh: Boolean) {
        super.onStartRequest(isRefresh)
        lastId = if (isRefresh) -1 else _dataState.value.lastOrNull()?.id ?: -1
    }

    override suspend fun fetch(): List<AwardItem> {
        return runApiCatching { lotteryApi.getLotteryRecord(lastId) }
            .getOrNull().orEmpty()
    }

}

class HouseLotteryViewModel : ViewModel() {

    private val KEY_HOUSE_LOTTERY_CONF = "key_house_lottery_conf"
    private val KEY_HOUSE_LOTTERY_AWARD = "key_house_lottery_award"

    val lotteryApi = com.qyqy.ucoo.http.createApi<LotteryApi>()

    private val _awardList: MutableState<List<LotteryCell.AwardCell>> = mutableStateOf(emptyList())
    val awardList: ComposeState<List<LotteryCell.AwardCell>> = _awardList

    private val _buttonCells: MutableState<List<LotteryCell.ButtonCell>> =
        mutableStateOf(emptyList())
    val buttonCells: ComposeState<List<LotteryCell.ButtonCell>> = _buttonCells

    private val _settings = mutableStateOf(LotterySettings())
    val settings: ComposeState<LotterySettings> = _settings

    private val _loading: MutableState<Boolean> = mutableStateOf(true)
    val loading: ComposeState<Boolean> = _loading


    init {
        viewModelScope.launch {
            sAppKV.getString(KEY_HOUSE_LOTTERY_CONF, "").takeIf { it.isNotEmpty() }?.also {
                try {
                    val settings = sAppJson.decodeFromString<LotterySettings>(it)
                    loadSettings(settings)
                } catch (e: Exception) {
                    e.printStackTrace()
                }
            }
            sAppKV.getString(KEY_HOUSE_LOTTERY_AWARD, "").takeIf { it.isNotEmpty() }?.also {
                val awardList = sAppJson.decodeFromString<List<AwardItem>>(it)
                loadAwardList(awardList)
            }
        }
    }

    fun updateBalance(balance: Int) = run {
        _settings.value = _settings.value.copy(balance = balance)
    }

    fun requestSettings() {
        viewModelScope.launch {
            val d1 = async {
                runApiCatching {
                    lotteryApi.getSettings()
                }.onSuccess { settings ->
                    loadSettings(settings)
                    sAppKV.putString(KEY_HOUSE_LOTTERY_CONF, sAppJson.encodeToString(settings))
                }.toastError()
            }
            val d2 = async {
                runApiCatching {
                    lotteryApi.getAwardList()
                }.onSuccess { awardList ->
                    loadAwardList(awardList)
                    sAppKV.putString(KEY_HOUSE_LOTTERY_AWARD, sAppJson.encodeToString(awardList))
                }.toastError()
            }
            awaitAll(d1, d2)
            _loading.value = false
        }
    }

    private fun loadAwardList(awardList: List<AwardItem>) {
        _awardList.value = awardList
            .map { award ->
                val duration =
                    if (award.duration > 0) award.duration.toString() else null
                LotteryCell.AwardCell(
                    award.name,
                    award.icon,
                    duration,
                    award.position
                )
            }
    }

    private fun loadSettings(settings: LotterySettings) {
        _settings.value = settings
        val cList = settings.conf
        _buttonCells.value = listOf(
            LotteryCell.ButtonCell(
                cList.take(2)
            ),
            LotteryCell.ButtonCell(
                cList.takeLast(2)
            )
        )
    }

}



