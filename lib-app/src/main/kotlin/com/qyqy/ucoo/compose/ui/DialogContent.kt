package com.qyqy.ucoo.compose.ui

import android.content.Context
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorTheme

class ThemeAlertDialog(
    context: Context,
    private val title: String? = null,
    private val content: String,
    private val buttonText: String,
    private val link: String? = null,
    private val cancelable: Boolean = true,
    private val onButtonClick: (ThemeAlertDialog) -> Unit = {
        link?.run {
            AppLinkManager.open(it.context, this)
        }
    },
) :
    ComposeDialog(context) {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setCancelable(cancelable)
    }

    @Composable
    override fun Content() {
        ThemeDialogContent(
            modifier = Modifier
                .background(colorTheme, RoundedCornerShape(50))
                .padding(horizontal = 16.dp), content = content, buttonText = buttonText, title = title
        ) {
            onButtonClick(this)
            dismiss()
        }
    }

}

@Composable
fun ThemeDialogContent(
    modifier: Modifier = Modifier,
    content: String,
    buttonText: String,
    title: String? = null,
    onButtonClick: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth(1f)
            .background(Color(0xFF222222), shape = Shapes.small)
            .padding(16.dp, 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        if (!title.isNullOrEmpty()) {
            Text(text = title, fontSize = 18.sp, color = Color.White)
            Spacer(modifier = Modifier.height(20.dp))
        }

        if (content.isNotEmpty()) {
            Text(text = content, fontSize = 15.sp, color = Color(0x80FFFFFF), textAlign = TextAlign.Center)
            Spacer(modifier = Modifier.height(20.dp))
        }

        val context = LocalContext.current
        AppButton(text = buttonText, fontSize = 16.sp, onClick = onButtonClick, modifier = modifier)
    }
}

@Preview
@Composable
fun ThemeDialogContentPreviewer() {
    AppTheme {
        ThemeDialogContent(
            modifier = Modifier
                .background(colorTheme, RoundedCornerShape(50))
                .padding(horizontal = 16.dp),
            title = "你好",
            content = "亲友团礼物只能送给自己的亲友团成员。对方还未加入你的亲友团，快邀请她加入吧！",
            buttonText = "邀请她加入我的亲友团"
        ) {}
    }
}