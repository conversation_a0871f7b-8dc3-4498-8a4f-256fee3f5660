package com.qyqy.ucoo.compose.vm.virtually

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.compose.state.MviViewModel
import com.qyqy.ucoo.compose.vm.virtually.VirtuallyMvi.ViewEffect
import com.qyqy.ucoo.compose.vm.virtually.VirtuallyMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.virtually.VirtuallyMvi.ViewResult
import com.qyqy.ucoo.compose.vm.virtually.VirtuallyMvi.ViewState
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.match.MatchUserRepository
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.toast
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.cancellable
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.mapLatest
import kotlinx.coroutines.flow.merge
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch

class VirtualMatchViewModel(
    savedStateHandle: SavedStateHandle
) : MviViewModel<ViewEvent, ViewResult, ViewState, ViewEffect>(ViewState(info = savedStateHandle[Const.KEY_DATA]!!)) {

    private val matchRepository = MatchUserRepository()

    private val callback = object : IMCompatListener {

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            when (message.cmd) {
                MsgEventCmd.VIRTUAL_GIRLFRIEND_MATCH_SUCCESS -> {
                    val matchId = message.getJsonInt("match_id") ?: return
                    val user = message.getJsonValue<AppUser>("chat_user") ?: return
                    processEvent(ViewEvent.SendMatchResult(matchId =matchId, user = user))
                }

                MsgEventCmd.VIRTUAL_GIRLFRIEND_MATCH_FAILED -> {
                    val matchId = message.getJsonInt("match_id") ?: return
                    processEvent(
                        ViewEvent.SendMatchResult(
                            matchId = matchId,
                            error = app.getString(R.string.当前没有合适的匹配对象)
                        )
                    )
                }
            }
        }
    }

    private var job: Job? = null

    init {
        IMCompatCore.addIMListener(callback)
        effects.onEach {
            if (it is ViewEffect.Toast) {
                toast(it.msg)
            }
        }.launchIn(viewModelScope)
    }

    override fun Flow<ViewEvent>.toResults(): Flow<ViewResult> {
        return merge(
            filterIsInstance<ViewEvent.SelectTag>().mapLatest {
                ViewResult.SelectResult(it.tagId)
            },
            filterIsInstance<ViewEvent.StartMatch>().flatMapLatest {
                flow {
                    if (cur.step != 0) {
                        emit(ViewResult.NoOp)
                    } else {
                        emit(ViewResult.PreMatchResult)
                        emit(ViewResult.LoadingResult(true))
                        matchRepository.startMatchByVirtual(cur.tagId).fold({
                            if (cur.step == 1) { // 接口调用成功后，要检查是否在预匹配阶段，是则继续匹配
                                startMatchTimeout() // 匹配超时检测
                                emit(ViewResult.MatchIdResult(it))
                            } else { // 不在预匹配阶段，则要立刻取消此次匹配请求
                                callCancelMatch(it)
                                emit(ViewResult.NoOp)
                            }
                        }) {
                            if (cur.step == 1) {
                                emit(ViewResult.CancelResult)
                                emit(ViewResult.ErrorResult(it.message))
                            }
                        }
                        emit(ViewResult.LoadingResult(false))
                    }
                }.cancellable()
            },
            filterIsInstance<ViewEvent.CancelMatch>().flatMapLatest {
                flowOf(if (it.finish) ViewResult.Finish else ViewResult.CancelResult)
            },
            filterIsInstance<ViewEvent.SendMatchResult>().flatMapLatest {
                if (cur.matchId == it.matchId) {
                    when {
                        cur.step >= 1 && it.user != null -> flowOf(ViewResult.MatchSuccess(it.user), ViewResult.Finish) // 结束
                        cur.step >= 1 && it.error != null -> flowOf(ViewResult.ErrorResult(it.error), ViewResult.Finish)// 结束
                        else -> flowOf(ViewResult.NoOp)
                    }
                } else {
                    flowOf(ViewResult.NoOp)
                }
            }
        )
    }

    override fun Flow<ViewResult>.filterStateResult(): Flow<ViewResult> {
        return filter {
            when (it) {
                is ViewResult.SelectResult,
                is ViewResult.LoadingResult,
                is ViewResult.PreMatchResult,
                is ViewResult.MatchIdResult,
                is ViewResult.CancelResult,
                -> true

                else -> false
            }
        }
    }

    override suspend fun ViewResult.reduce(state: ViewState): ViewState {
        return when (this) {

            is ViewResult.SelectResult -> {
                state.copy(tagId = id)
            }

            is ViewResult.LoadingResult -> {
                state.copy(showLoading = show)
            }

            is ViewResult.PreMatchResult -> {
                state.copy(step = 1)
            }

            is ViewResult.CancelResult -> {
                if (state.step != 0) { // 正在匹配中，要返回引导页
                    state.copy(showLoading = false, step = 0, matchId = -1)
                } else {
                    state.copy(showLoading = false, matchId = -1)
                }
            }

            is ViewResult.MatchIdResult -> {
                state.copy(step = 2, matchId = id)
            }

            else -> state
        }
    }

    override fun Flow<ViewResult>.toEffects(): Flow<ViewEffect> {
        return merge(
            filterIsInstance<ViewResult.ErrorResult>().mapLatest { result ->
                ViewEffect.Toast(result.toastError)
            },
            filterIsInstance<ViewResult.MatchSuccess>().mapLatest { result ->
                ViewEffect.MatchSuccess(result.user)
            },
            filterIsInstance<ViewResult.Finish>().mapLatest { _ ->
                ViewEffect.Finish
            }
        )
    }

    override fun onCleared() {
        super.onCleared()
        IMCompatCore.removeIMListener(callback)
        cancel()
    }

    fun select(id: Int) {
        if (cur.step == 0) {
            processEvent(ViewEvent.SelectTag(id))
        }
    }

    fun startMatch() {
        cancel()
        if (cur.step == 0) {
            processEvent(ViewEvent.StartMatch)
        }
    }

    fun close() {
        callCancelMatch()
        processEvent(ViewEvent.CancelMatch(true))
    }

    fun cancel() {
        callCancelMatch()
        processEvent(ViewEvent.CancelMatch(false))
    }

    private fun callCancelMatch(id: Int? = null) {
        job?.cancel()
        job = null
        val matchId = id ?: cur.matchId
        if (matchId != -1) {
            appCoroutineScope.launch {
                matchRepository.cancelMatchByVirtual(matchId)
            }
        }
    }

    private fun startMatchTimeout() {
        job?.cancel()
        job = viewModelScope.launch {
            delay(35_000)
            processEvent(ViewEvent.SendMatchResult(cur.matchId, error = app.getString(R.string.当前没有合适的匹配对象)))
            close()
        }
    }
}