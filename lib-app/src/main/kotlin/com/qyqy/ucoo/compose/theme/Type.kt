package com.qyqy.ucoo.compose.theme

import androidx.compose.ui.text.font.Font
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import com.qyqy.ucoo.R

val _D_DIN = FontFamily(
    Font(R.font.din_bold, FontWeight.Light),
    Font(R.font.din_bold, FontWeight.Normal),
    Font(R.font.din_bold, FontWeight.Bold),
)

@Deprecated(
    message = "use FontFamily.D_DIN",
    replaceWith = ReplaceWith("FontFamily.D_DIN", "androidx.compose.ui.text.font.FontFamily", "com.qyqy.ucoo.compose.theme.D_DIN")
)
val D_DIN
    get() = _D_DIN

val FontFamily.Companion.D_DIN
    get() = _D_DIN

private val _MI_SANS = FontFamily(
    Font(R.font.mi_sans, FontWeight.Light),
    <PERSON>ont(R.font.mi_sans, FontWeight.Normal),
    Font(R.font.mi_sans, FontWeight.Bold),
)

val FontFamily.Companion.MI_SANS
    get() = _MI_SANS