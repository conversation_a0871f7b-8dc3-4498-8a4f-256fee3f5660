package com.qyqy.ucoo.compose.domain.usecase.profile

import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.JsonObject


class ToggleBlackUseCase constructor(
    private val userRepository: UserRepository = UserRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Pair<String, Boolean>, JsonObject>(ioDispatcher) {
    override suspend fun execute(parameters: Pair<String, Boolean>): Result<JsonObject> {
        val (userId, black) = parameters
        return userRepository.updateBlackShip(userId, black)
    }

}