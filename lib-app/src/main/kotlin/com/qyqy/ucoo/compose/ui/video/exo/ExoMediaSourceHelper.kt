package com.qyqy.ucoo.compose.ui.video.exo

import android.app.Application
import android.content.Context
import android.net.Uri
import android.text.TextUtils
import androidx.media3.common.C
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.common.util.Util
import androidx.media3.database.StandaloneDatabaseProvider
import androidx.media3.datasource.DataSource
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.DefaultHttpDataSource
import androidx.media3.datasource.HttpDataSource
import androidx.media3.datasource.cache.Cache
import androidx.media3.datasource.cache.CacheDataSource
import androidx.media3.datasource.cache.LeastRecentlyUsedCacheEvictor
import androidx.media3.datasource.cache.SimpleCache
import androidx.media3.exoplayer.source.MediaSource
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import java.io.File
import java.util.Locale

@UnstableApi
class ExoMediaSourceHelper private constructor(context: Context) {
    private val mUserAgent: String
    private lateinit var mAppContext: Context
    private var mHttpDataSourceFactory: HttpDataSource.Factory? = null
    private var mCache: Cache? = null

    init {
        mAppContext = context as? Application ?: context.applicationContext
        mUserAgent = Util.getUserAgent(mAppContext, mAppContext.applicationInfo.name)
    }

    fun getMediaSource(uri: String): MediaSource {
        return getMediaSource(uri, null, false)
    }

    fun getMediaSource(uri: String, headers: Map<String, String>?): MediaSource {
        return getMediaSource(uri, headers, false)
    }

    fun getMediaSource(uri: String, isCache: Boolean): MediaSource {
        return getMediaSource(uri, null, isCache)
    }

    fun getMediaSource(uri: String, headers: Map<String, String>?, isCache: Boolean): MediaSource {
        val contentUri = Uri.parse(uri)
        val mediaItem = MediaItem.fromUri(contentUri)
//        if ("rtmp" == contentUri.scheme) {
//            val rtmpDataSourceFactory = RtmpDataSource.Factory()
//            return ProgressiveMediaSource.Factory(rtmpDataSourceFactory).createMediaSource(mediaItem)
//        }
        val contentType = inferContentType(uri)
        val factory: DataSource.Factory
        factory = if (isCache) {
            cacheDataSourceFactory
        } else {
            dataSourceFactory
        }
        if (mHttpDataSourceFactory != null && headers != null) {
            setHeaders(headers)
        }
        return when (contentType) {
//            C.TYPE_DASH -> DashMediaSource.Factory(factory).createMediaSource(mediaItem)
//            C.TYPE_SS -> SsMediaSource.Factory(factory).createMediaSource(mediaItem)
//            C.TYPE_HLS -> HlsMediaSource.Factory(factory).createMediaSource(mediaItem)
            C.TYPE_OTHER -> ProgressiveMediaSource.Factory(factory).createMediaSource(mediaItem)
            else -> ProgressiveMediaSource.Factory(factory).createMediaSource(mediaItem)
        }
    }

    private fun inferContentType(fn: String): Int {
        var fileName = fn
        fileName = fileName.lowercase(Locale.US)
        return if (fileName.contains(".mpd")) {
            C.TYPE_DASH
        } else if (fileName.contains(".m3u8")) {
            C.TYPE_HLS
        } else if (fileName.matches(".*\\.ism(l)?(/manifest(\\(.+\\))?)?".toRegex())) {
            C.TYPE_SS
        } else {
            C.TYPE_OTHER
        }
    }

    private val cacheDataSourceFactory: DataSource.Factory
        private get() {
            if (mCache == null) {
                mCache = newCache()
            }

            return CacheDataSource.Factory().apply {
                setCache(mCache)
                setCacheReadDataSourceFactory(dataSourceFactory)
                setFlags(CacheDataSource.FLAG_IGNORE_CACHE_ON_ERROR)
            }
        }

    private fun newCache(): Cache {
        return SimpleCache( // 存储目录
            File(mAppContext.externalCacheDir, "exo-video-cache"),  // 存储大小，默认512M，使用LRU算法实现
            LeastRecentlyUsedCacheEvictor((512 * 1024 * 1024).toLong()),
            StandaloneDatabaseProvider(mAppContext)
        )
    }

    private val dataSourceFactory: DataSource.Factory
        /*** 返回一个新的数据源工厂。*
         * @return 一个新的数据源工厂。
         */
        private get() = DefaultDataSource.Factory(mAppContext, httpDataSourceFactory)
    private val httpDataSourceFactory: DataSource.Factory
        /*** 返回一个新的 HttpDataSource 工厂。*
         * @return 一个新的 HttpDataSource 工厂。
         */
        private get() {
            if (mHttpDataSourceFactory == null) {
                synchronized(this) {
                    if (mHttpDataSourceFactory == null) {
                        mHttpDataSourceFactory = DefaultHttpDataSource.Factory().apply {
                            this.setAllowCrossProtocolRedirects(true)
                            this.setUserAgent(mUserAgent)
                        }
                    }
                }
            }
            return mHttpDataSourceFactory!!
        }

    private fun setHeaders(source: Map<String, String>) {
        val headers = source.toMutableMap()
        if (headers.isNotEmpty()) {
            for ((key, value) in headers) {
                //如果发现用户通过header提交了UA，则强行将HttpDataSourceFactory里面的userAgent字段替换成用户的
                if (TextUtils.equals(key, "User-Agent")) {
                    if (!TextUtils.isEmpty(value)) {
                        try {
                            val userAgentField = mHttpDataSourceFactory!!.javaClass.getDeclaredField("userAgent")
                            userAgentField.isAccessible = true
                            userAgentField[mHttpDataSourceFactory] = value
                        } catch (e: Exception) {
                            //忽略
                        }
                    }
                }
            }
            mHttpDataSourceFactory?.setDefaultRequestProperties(headers)
        }
    }

    fun setCache(cache: Cache?) {
        mCache = cache
    }

    companion object {
        private var sInstance: ExoMediaSourceHelper? = null
        fun getInstance(context: Context): ExoMediaSourceHelper {
            if (sInstance == null) {
                synchronized(ExoMediaSourceHelper::class.java) {
                    if (sInstance == null) {
                        sInstance = ExoMediaSourceHelper(context)
                    }
                }
            }
            return sInstance!!
        }
    }
}