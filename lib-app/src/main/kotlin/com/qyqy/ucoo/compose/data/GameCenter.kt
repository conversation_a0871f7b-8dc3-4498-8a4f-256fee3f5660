package com.qyqy.ucoo.compose.data

import android.os.Parcelable
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.compose.ui.text.AnnotatedString
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.app
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.im.room.game.GameInfo
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

abstract class Base(
    override val id: Any,
    override val icon: Any,
) : Interactive

sealed interface Interactive {

    val id: Any

    val name: String

    val icon: Any

    val tipsText: String?
        get() = null

    val dismissAfterClick: Boolean
        get() = true

    class Divider(id: String) : Base("Divider_$id", -1) {

        override val name: String = ""
    }

    class Title(val content: AnnotatedString) : Base("Title_${content.text}", -1) {

        override val name: String = content.text
    }

    class WebGame(
        val url: String,
        iconRes: Int,
        override val name: String,
    ) : Base(url, iconRes)


    class LocalItem(
        @DrawableRes val iconRes: Int,
        @StringRes val stringRes: Int,
        override val dismissAfterClick: Boolean = true,
        override val tipsText: String? = null,
    ) : Base(iconRes + stringRes, iconRes) {
        override val name: String
            get() = app.getString(stringRes)

    }

    class SudGame(
        private val gameInfo: GameInfo,
    ) : Base("${gameInfo.type}_${gameInfo.id}_${gameInfo.name}_${gameInfo.link}", gameInfo.icon) {
        override val name: String
            get() = gameInfo.name
    }

}


data class GameRouterInfo(
    val gameId: Int,
    val gameType: Int = 1,
    val matchId: Int = -1,
    val gameUrl: String = "",
) {

    fun toLink(): String {
        return when (gameType) {
            1 -> "${AppLinkManager.BASE_URL}${AppLinkManager.PATH_CHAT_ROOM}?roomId=$gameId"
            else -> gameUrl
        }
    }
}

@Parcelize
@Serializable
data class MatchGameInfo(
    @SerialName("match_id")
    val matchId: Int = -1,
    @SerialName("room_info")
    val roomInfo: RoomInfo? = null,
) : Parcelable {

    fun toGameRouterInfo() = roomInfo?.let {
        GameRouterInfo(gameId = it.id, matchId = matchId)
    } ?: GameRouterInfo(gameId = -1, gameType = 0, matchId = matchId) // 伪造一个无效数据
}

@Parcelize
@Serializable
data class GameInvite(
    @SerialName("match_id")
    private val match_id: String = "",
    @SerialName("inviter")
    val user: AppUser,
    val title: String, // "xxx邀请你一起玩游戏xxxx",
    @SerialName("room_info")
    val roomInfo: RoomInfo? = null,
    @SerialName("game_info")
    val gameInfo: GameInfo? = null,
    @SerialName("btn_text")
    val btnText: String = "", //"接受邀请"
) : Parcelable {

    constructor(
        matchId: Int = -1,
        user: AppUser,
        title: String, // "xxx邀请你一起玩游戏xxxx",
        roomInfo: RoomInfo? = null,
        gameInfo: GameInfo? = null,
        btnText: String = "", //"接受邀请"
    ) : this(matchId.toString(), user, title, roomInfo, gameInfo, btnText)

    @IgnoredOnParcel
    val matchId: Int = match_id.toIntOrNull() ?: -1
}

@Parcelize
@Serializable
data class RoomInfo(
    val id: Int,
    val title: String,
) : Parcelable

