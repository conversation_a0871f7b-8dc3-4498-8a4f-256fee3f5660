package com.qyqy.ucoo.compose.presentation.wedding

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.widthIn
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.produceState
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.ViewModel
import androidx.lifecycle.compose.LifecycleResumeEffect
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.viewModelScope
import androidx.lifecycle.viewmodel.compose.viewModel
import com.qyqy.cupid.ui.dialog.DialogQueue
import com.qyqy.cupid.ui.dialog.IDialogAction
import com.qyqy.cupid.ui.dialog.rememberDialogQueue
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.clickWithShape
import com.qyqy.ucoo.compose.getShadowColor
import com.qyqy.ucoo.compose.presentation.wedding.bean.BookingInfo
import com.qyqy.ucoo.compose.presentation.wedding.bean.BookingResult
import com.qyqy.ucoo.compose.presentation.wedding.bean.BookingTimeEntity
import com.qyqy.ucoo.compose.presentation.wedding.bean.LightUser
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingConf
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingRoom
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingRoomType
import com.qyqy.ucoo.compose.router.LocalUCOONavController
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.state.ComposeLoading
import com.qyqy.ucoo.compose.state.UIState
import com.qyqy.ucoo.compose.state.toUIState
import com.qyqy.ucoo.compose.theme.MI_SANS
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.IconLoading
import com.qyqy.ucoo.compose.ui.LocalContentLoading
import com.qyqy.ucoo.compose.ui.runWithLoading
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.EntityCallback
import com.qyqy.ucoo.utils.OnClick
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.launch

@Composable
fun WeddingMainScreen() {
    val vm = viewModel<WeddingMainViewModel>()
    val state by vm.stateFlow.collectAsStateWithLifecycle()
    val backOwner = LocalOnBackPressedDispatcherOwner.current
    val nav = LocalUCOONavController.current
    val scope = rememberCoroutineScope()
    val loadingState = LocalContentLoading.current
    val context = LocalContext.current
    LifecycleResumeEffect(key1 = vm) {
        vm.refresh()
        onPauseOrDispose {
        }
    }


    var bookingRoom by remember {
        mutableStateOf<WeddingRoom?>(null)
    }

    val dialogQueue: DialogQueue<IDialogAction> = rememberDialogQueue()
    dialogQueue.DialogContent()

    fun buyRing(id: Int) {
        Analytics.reportClickEvent("wedding_ring_buy_click", useShushu = true)
        scope.launch {
            loadingState.runWithLoading {
                val content = vm.buyWeddingRing(id)
                if (content.isNullOrEmpty()) {
                    return@runWithLoading
                }
                dialogQueue.pushCenterDialog(immediatelyShow = true) { dialog, _ ->
                    BuyRingResultContent(content) {
                        dialog.dismiss()
                    }
                }
            }
        }
    }

    fun openRightsPage(room: WeddingRoom) {
        scope.launch {
            loadingState.runWithLoading {
                val benefits = vm.getWeddingRingBenefits(room.roomType) ?: return@runWithLoading
                dialogQueue.push(immediatelyShow = true) { dialog, _ ->
                    WeddingBenefitsContent(
                        benefits = benefits,
                        onClose = {
                            dialog.dismiss()
                        },
                        onBuy = {
                            buyRing(it)
                        }
                    )
                }
            }
        }
    }

    WeddingBookingPanel(
        room = bookingRoom,
        dialogQueue = dialogQueue,
        timeRangeFetcher = vm::getBookingTimeRange,
        ringUserFetcher = vm::getBookingUser,
        bookingApi = vm::bookingWedding,
        onOpenRight = ::openRightsPage,
        onDismiss = {
            bookingRoom = null
        }
    )

    val from = LocalWeddingPageFrom.current
    ReportExposureCompose(onExposureStart = {
        Analytics.reportClickEvent("wedding_home_show", extra = "{\"from\":${from}}", useShushu = true)
    }, reExposureMode = false) {
        WeddingMainContent(
            cards = state?.rooms.orEmpty(), tips = state?.rule.orEmpty(),
            onBack = {
                backOwner?.onBackPressedDispatcher?.onBackPressed()
            },
            isLoading = state == null,
            onClickWeddingWall = {
                Analytics.reportClickEvent("wedding_history_click", useShushu = true)
                nav.navigateTo(WeddingNavigator.WeddingDest.WeddingWall)
            },
            onClickWeddingPreview = {
                Analytics.reportClickEvent("wedding_booking_click", useShushu = true)
                nav.navigateTo(WeddingNavigator.WeddingDest.WeddingPreview)
            },
            onQuestion = {
                AppLinkManager.open(context = context, state?.ruleUrl.orEmpty())
            },
            onSchedule = {
                Analytics.reportClickEvent("wedding_home_reservation_click", extra = "{\"wedding_room_type\":${it.roomType}}", useShushu = true)
                if (it.bookingCount > 0) {
                    bookingRoom = it
                } else {
                    openRightsPage(it)
                }
            }
        )
    }
}

fun Pair<String, TimeSlot?>.toDateText(): String = "${this.first} ${this.second?.timeRange}"

/**
 * [preBook] 婚礼预约前置弹窗，从婚礼殿堂预约有这个流程，系统消息预约则没有
 * [lightUser] 指定的婚礼对象，从系统消息里获得，若有则不可更换其他对象
 */
@Composable
fun WeddingBookingPanel(
    room: WeddingRoom?,
    dialogQueue: DialogQueue<IDialogAction>,
    timeRangeFetcher: suspend (Int) -> Result<List<BookingTimeEntity>>,
    ringUserFetcher: suspend (Int) -> Result<List<LightUser>>,
    bookingApi: suspend (roomId: Int, date: String, rangeType: Int, ringLightId: Int) -> Result<BookingResult>,
    preBook: Boolean = true,
    lightUser: LightUser? = null,
    onOpenRight: (WeddingRoom) -> Unit,
    onDismiss: () -> Unit
) {

    if (room != null) {
        val scope = rememberCoroutineScope()

        val theme = remember(room.roomType) {
            WeddingTheme.getByType(room.roomType)
        }

        var showPreBook by remember("pre_book") {
            mutableStateOf(preBook)
        }

        var showBooking by remember("booking") {
            mutableStateOf(!preBook)
        }

        if (showPreBook) {
            Dialog(onDismissRequest = onDismiss) {
                WeddingPreBooking(
                    weddingType = room.roomTypeDesc,
                    title = stringResource(R.string.专属婚礼礼遇),
                    message = stringResource(R.string.format_message_wedding, room.bookingCount),
                    theme = WeddingTheme.getByType(room.roomType),
                    leftButtonText = stringResource(R.string.继续购买戒指),
                    rightButtonText = stringResource(R.string.立即预约婚礼),
                    onLeftClick = {
                        onOpenRight(room)
                    },
                    onRightClick = {
                        showPreBook = false
                        showBooking = true
                    },
                    onClose = onDismiss,
                )
            }
        }

        if (showBooking) {
            var selectedUser by remember(room) {
                mutableStateOf<LightUser?>(lightUser)
            }

            var dateParams by remember {
                mutableStateOf<Pair<String, TimeSlot?>?>(null)
            }

            var dateOptions by remember {
                mutableStateOf<List<DateOption>?>(null)
            }

            var bookingTime by remember {
                mutableStateOf<String?>(null)
            }

            var allowBooking by remember {
                mutableStateOf(lightUser != null)
            }

            var lightUsers by remember {
                mutableStateOf<List<LightUser>>(emptyList())
            }

            val loadingState = LocalContentLoading.current
            if (lightUser == null) {
                LaunchedEffect(key1 = room.audioroom.id) {
                    scope.launch {
                        loadingState.runWithLoading {
                            ringUserFetcher(room.audioroom.id)
                                .onSuccess {
                                    allowBooking = it.isNotEmpty()
                                    if (it.size == 1) {
                                        selectedUser = it[0]
                                    }
                                    lightUsers = it
                                }.onFailure {
                                    showBooking = false
                                }
                                .toastError()
                        }
                    }
                }
            }

            if (allowBooking) {
                var showUserSelectPanel by remember {
                    mutableStateOf(false)
                }

                if (showUserSelectPanel) {
                    Dialog(onDismissRequest = { showUserSelectPanel = false }) {
                        GuestSelectionContent(
                            WeddingInfo(true, theme),
                            label = room.roomTypeDesc,
                            selectedUser = selectedUser, users = lightUsers,
                            onDismissRequest = {
                                showUserSelectPanel = false
                            }) {
                            showUserSelectPanel = false
                            selectedUser = it
                        }
                    }
                }

                var bookingResult by remember {
                    mutableStateOf<BookingResult?>(null)
                }

                if (bookingResult == null) {
                    var posting by remember {
                        mutableStateOf(false)
                    }
                    Dialog(onDismissRequest = onDismiss) {
                        WeddingBookingContent(
                            weddingType = room.roomTypeDesc, title = stringResource(id = R.string.婚礼预约),
                            roomId = room.audioroom.publicId,
                            info = WeddingInfo(true, theme),
                            leftUser = sUser, rightUser = selectedUser?.user, onDismissRequest = onDismiss,
                            buttonText = stringResource(R.string.提交预约),
                            bookingTime = bookingTime,
                            posting = posting,
                            onSubmit = {
                                scope.launch {
                                    val roomId = room.audioroom.id
                                    val date = dateParams?.first ?: return@launch
                                    val rangeType = dateParams?.second?.rangeType ?: return@launch
                                    val ringLightId = selectedUser?.ringLightId ?: return@launch
                                    posting = true
                                    bookingApi(roomId, date, rangeType, ringLightId)
                                        .onSuccess {
                                            bookingResult = it
                                        }.toastError()
                                    posting = false
                                }
                            },
                            canChange = lightUsers.size > 1 && lightUser == null,
                            submitEnable = selectedUser != null && dateParams != null,
                            onTimeSelect = {
                                scope.launch {
                                    val options = dateOptions ?: run {
                                        timeRangeFetcher.invoke(room.audioroom.id)
                                            .toastError()
                                            .getOrNull()
                                            ?.map { it.toDateOption() }?.also { opts ->
                                                dateOptions = opts
                                            }
                                    }
                                    if (options == null) return@launch
                                    dialogQueue.push(immediatelyShow = true) { dialog, onAction ->
                                        WeddingTimeSelectionContent(
                                            theme = theme, dateOptions = options, selectedTime = dateParams,
                                            onDismissRequest = { dialog.dismiss() }) { datePair ->
                                            dateParams = datePair
                                            bookingTime = datePair.toDateText()
                                            dialog.dismiss()
                                        }
                                    }
                                }
                            },
                            onSelectGuest = {
                                showUserSelectPanel = true
                            },
                        )
                    }
                }

                //预定结果dialog
                val bookingInfo = bookingResult
                if (bookingInfo != null) {
                    Dialog(onDismissRequest = onDismiss) {
                        WeddingBookingContent(
                            weddingType = room.roomTypeDesc, title = stringResource(R.string.婚礼已预定),
                            roomId = room.audioroom.publicId,
                            info = WeddingInfo(false, theme),
                            leftUser = bookingInfo.lightUser, rightUser = bookingInfo.targetUser,
                            onDismissRequest = onDismiss,
                            buttonText = stringResource(R.string.i_known),
                            bookingTime = bookingTime,
                            ringUrl = bookingInfo.ring.icon,
                            textDisplayUI = @Composable {
                                // Information Fields
                                InfoField(
                                    label = stringResource(R.string.wedding_room_id),
                                    value = bookingInfo.audioroom.publicId,
                                    theme = theme,
                                    modifier = Modifier.padding(bottom = 16.dp)
                                )
                                InfoField(
                                    label = stringResource(R.string.wedding_time),
                                    value = bookingInfo.displayDate,
                                    theme = theme,
                                    modifier = Modifier.padding(bottom = 24.dp),
                                    isBoldText = false,
                                    onClick = {},
                                )
                            },
                            submitEnable = true,
                            onSubmit = onDismiss
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun BookingNow(ringLightId: Int, onDismiss: () -> Unit) {
    val vm = viewModel<WeddingMainViewModel>()
    val initV: UIState<BookingInfo?> = remember {
        UIState.Loading("")
    }
    val dq = rememberDialogQueue<IDialogAction>(autoEnable = false)
    dq.DialogContent()
    val state by produceState(initialValue = initV) {
        value = vm.getBookingInfo(ringLightId).onFailure {
            toast(it.message)
            onDismiss()
        }.onSuccess {
            if (it == null) {
                toast("booking info is null")
                onDismiss()
            }
        }.toUIState()
    }
    when (state) {
        is UIState.Data -> {
            val data = state.data ?: return
            WeddingBookingPanel(
                room = data.room, dq, timeRangeFetcher = vm::getBookingTimeRange, ringUserFetcher = vm::getBookingUser,
                bookingApi = vm::bookingWedding, preBook = false, data.lightUser, {}, onDismiss
            )
        }

        is UIState.Error -> {}
        is UIState.Loading -> {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .aspectRatio(1f), contentAlignment = Alignment.Center
            ) {
                IconLoading()
            }
        }
    }
}

class WeddingMainViewModel : ViewModel() {

    val stateFlow = MutableStateFlow<WeddingConf?>(null)

    private val api = createApi<WeddingApi>()

    fun refresh() {
        viewModelScope.launch {
            runApiCatching { api.getConf() }
                .onSuccess {
                    stateFlow.value = it
                }
                .toastError()
        }
    }

    suspend fun getBookingTimeRange(roomId: Int): Result<List<BookingTimeEntity>> =
        runApiCatching {
            api.getTimeRange(roomId)
        }.map {
            it.parseValue<List<BookingTimeEntity>>("booking_timeranges").orEmpty()
        }.onFailure {
            refresh()
        }


    suspend fun getBookingUser(roomId: Int): Result<List<LightUser>> = runApiCatching {
        api.getBookingUsers(roomId)
    }.map {
        it.parseValue<List<LightUser>>("users").orEmpty()
    }.onFailure {
        //刷新，有可能被对方提前预定了
        refresh()
    }

    suspend fun bookingWedding(roomId: Int, date: String, rangeType: Int, ringLightId: Int) = runApiCatching {
        api.bookingWedding(mapOf("room_id" to roomId, "date" to date, "range_type" to rangeType, "ring_light_id" to ringLightId))
    }.onSuccess {
        //刷新婚礼殿堂
        refresh()
    }

    suspend fun getWeddingRingBenefits(weddingType: Int): RingBenefits? = runApiCatching {
        api.getWeddingRingBenefits(weddingType)
    }.toastError().getOrNull()

    suspend fun buyWeddingRing(ringGiftId: Int): String? {
        return runApiCatching {
            api.buyWeddingRing(mapOf("gift_id" to ringGiftId))
        }.onSuccess {
            app.accountManager.refreshSelfUserByRemote()
        }.toastError().getOrNull()?.getStringOrNull("toast")
    }

    suspend fun getBookingInfo(ringLightId: Int) = runApiCatching {
        api.getBookingInfo(ringLightId)
    }.map {
        it.parseValue<BookingInfo>("info")
    }
}

@Composable
private fun WeddingMainContent(
    cards: List<WeddingRoom>,
    isLoading: Boolean = false,
    tips: String = "",
    onBack: OnClick = {},
    onClickWeddingWall: OnClick = {},
    onClickWeddingPreview: OnClick = {},
    onQuestion: OnClick = {},
    onSchedule: EntityCallback<WeddingRoom> = {
    }
) {
    val scrollState = rememberScrollState()
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(Brush.verticalGradient(listOf(Color(0xFFFCE3ED), Color(0xFFFF85BA))))
//            .overScrollVertical()
            .verticalScroll(scrollState)
    ) {
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(1125 / 723f)
        ) {
            Image(
                painter = painterResource(id = R.drawable.header_wedding_page),
                contentDescription = "header",
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop
            )
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .statusBarsPadding()
                    .height(44.dp)
                    .padding(horizontal = 16.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_wedding_back),
                    contentDescription = "",
                    modifier = Modifier
                        .size(28.dp)
                        .clickWithShape(onClick = onBack)
                )
                Image(
                    painter = painterResource(id = R.drawable.ic_wedding_question),
                    contentDescription = "",
                    modifier = Modifier
                        .size(28.dp)
                        .clickWithShape(onClick = onQuestion)
                )
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
                .navigationBarsPadding()
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 5.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                val md = Modifier.size(150.dp, 48.dp)
                Image(
                    painter = painterResource(id = R.drawable.tab_wedding_item1),
                    contentDescription = "tab1",
                    modifier = md.click(onClick = onClickWeddingWall)
                )
                Image(
                    painter = painterResource(id = R.drawable.tab_wedding_item2),
                    contentDescription = "tab1",
                    modifier = md.click(onClick = onClickWeddingPreview)
                )
            }

            if (isLoading) {
                Box(
                    modifier = Modifier
                        .fillMaxWidth()
                        .aspectRatio(1f), contentAlignment = Alignment.Center
                ) {
                    ComposeLoading(color = Color.White)
                }
            } else {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 10.dp)
                ) {
                    cards.forEach { item ->
                        WeddingHomeCard(item = item, onClick = {
                            onSchedule.invoke(item)
                        })
                        Spacer(modifier = Modifier.height(6.dp))
                    }
                }

                AppText(
                    text = tips, color = Color.White, modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 20.dp, 14.dp),
                    fontSize = 11.sp,
                    lineHeight = 16.sp
                )
            }
        }
    }
}

@Composable
private fun WeddingHomeCard(item: WeddingRoom, onClick: OnClick = {}) {
    val theme = remember(item.roomType) {
        WeddingTheme.getByType(item.roomType)
    }
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(1071f / 576)
            .paint(painterResource(id = item.cardBgRes), contentScale = ContentScale.FillBounds),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = item.roomTypeDesc,
            modifier = Modifier.padding(top = 22.dp),
            style = TextStyle(
                fontSize = 14.sp,
                lineHeight = 14.sp,
                fontFamily = FontFamily.MI_SANS,
                fontWeight = FontWeight.Normal,
                color = Color.White,
                shadow = Shadow(
                    color = theme.primaryColor.getShadowColor(),
                    offset = Offset(3f, 3f),
                    blurRadius = 4f
                ),
            )
        )
        Spacer(modifier = Modifier.height(38.dp))
        Text(
            text = item.roomDesc,
            color = Color(item.colorTitleText),
            lineHeight = 30.sp,
            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .background(
                    Brush.horizontalGradient(
                        listOf(
                            Color(0x00FFBBEA),
                            Color(item.colorTitleBg),
                            Color(0x00FFBBEA)
                        )
                    )
                )
                .widthIn(min = 200.dp)
        )
        Spacer(modifier = Modifier.height(12.dp))
        val offset = 4.dp.value
        val style = remember {
            TextStyle(
                shadow = Shadow(
                    Color(0xFF999999), Offset(offset, offset), 2 * offset
                )
            )
        }
        Column(
            modifier = Modifier
                .size(124.dp, 58.dp)
                .clickWithShape(onClick = onClick)
                .paint(painterResource(id = item.btnRes), contentScale = ContentScale.FillBounds),
            verticalArrangement = Arrangement.Center,
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            AppText(
                text = stringResource(R.string.婚礼预约),
                fontWeight = FontWeight.Bold,
                fontSize = 14.sp,
                color = Color.White,
                style = style,
                fontFamily = FontFamily.MI_SANS
            )
            if (item.bookingHint.isNotEmpty()) {
                Text(
                    text = item.bookingHint, fontSize = 11.sp, color = Color.White, style = style
                )
            }
        }
        Spacer(modifier = Modifier.height(14.dp))
    }
}

@Preview(device = "id:pixel_8_pro")
@Composable
private fun Preview() {
    val list = listOf(
        WeddingRoom(
            roomDesc = "尊贵1314房间号举办", bookingHint = "可预约次数（1）次",
            roomTypeDesc = "时间印记",
            roomType = WeddingRoomType.TIME_PRINT_PURPLE
        ),
        WeddingRoom(
            roomDesc = "稀有888房间号举办", roomType = WeddingRoomType.LOVE_FOREVER_RED,
            roomTypeDesc = "真爱永恒",
        ),
        WeddingRoom(
            roomDesc = "专属520房间号举办", roomType = WeddingRoomType.STAR_PROMISE_BLUE,
            roomTypeDesc = "星辰之诺"
        )
    )
    val tips =
        "本玩法为用户自愿参与、自主选择的模拟体验场景。所有模拟内容仅为虚拟情境演绎，不具备任何法律效力或实际效力；场景设置及互动内容不构成对现实规则的指引或暗示，亦不代表平台的任何立场或观点；本产品所有功能及内容均基于娱乐属性开发，仅供用户休闲体验目的使用。"
    WeddingMainContent(cards = list, tips = tips)
}

@Preview
@Composable
private fun PreviewLoading() {
    val tips = ""
    WeddingMainContent(cards = emptyList(), tips = tips, isLoading = true)
}