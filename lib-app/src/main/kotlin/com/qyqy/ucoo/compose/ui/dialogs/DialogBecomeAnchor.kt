package com.qyqy.ucoo.compose.ui.dialogs

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.ui.AppText


@Composable
fun DialogBecomeAnchor(
    title: String = "恭喜成为平台主播",
    desc: String = "获得130元新人奖励",
    buttonText: String = "领取红包",
    onClick: () -> Unit = {},
) {
    Column(
        modifier = Modifier
            .width(320.dp)
            .aspectRatio(288f / 318)
            .paint(painterResource(id = R.drawable.bg_dialog_become_anchor), contentScale = ContentScale.Fit),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.fillMaxHeight(0.45f))
        AppText(text = title, color = Color.White, fontSize = 20.sp)
        Spacer(modifier = Modifier.height(10.dp))
        AppText(text = buildAnnotatedString {
            append(desc)
            "\\d+".toRegex().find(desc)?.also {
                addStyle(SpanStyle(color = Color(0xFFFFE6B5)), it.range.first, it.range.last.plus(1))
            }
        }, fontSize = 16.sp, color = Color.White)
        Spacer(modifier = Modifier.height(24.dp))
        Button(
            colors = ButtonDefaults.buttonColors(contentColor = Color(0xFFB32E00), containerColor = Color.Transparent),
            contentPadding = PaddingValues(horizontal = 48.dp, vertical = 13.dp),
            onClick = onClick, modifier = Modifier
                .background(
                    Brush.horizontalGradient(
                        listOf(Color(0xFFFFF2AC), Color(0xFFFFFDF0)),
                    ),
                    RoundedCornerShape(50)
                )
        ) {
            AppText(text = buttonText, fontSize = 16.sp, fontWeight = FontWeight.Medium)
        }

    }
}

@Preview
@Composable
fun DialogBecomeAnchorPreview(width: Int = 270) {
    AppTheme {
        DialogBecomeAnchor()
    }
}