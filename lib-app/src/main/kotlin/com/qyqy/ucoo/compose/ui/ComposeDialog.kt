package com.qyqy.ucoo.compose.ui

import android.content.Context
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.WindowManager
import androidx.activity.ComponentDialog
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.lzf.easyfloat.utils.DisplayUtils
import com.overseas.common.utils.dpF
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes

abstract class ComposeDialog(
    context: Context,
    private val windowAnimations: Int = R.style.Animation_AppCompat_Dialog_ScaleIn,
    private val dialogWidth: Int = 270.dpF.toInt(),
) :
    ComponentDialog(context) {

     var widthRatio: Float? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.let {
            it.decorView.setPadding(0, 0, 0, 0)
            it.setBackgroundDrawable(ColorDrawable(android.graphics.Color.TRANSPARENT))
            it.setWindowAnimations(windowAnimations)
        }
        val composeView = ComposeView(context, null, 0)
        setContentView(composeView)
        composeView.setContent {
            AppTheme {
                Content()
            }
        }
    }

    override fun onStart() {
        super.onStart()
        window?.also {
            val attr = it.attributes
            val ratio = widthRatio
            if (ratio != null) {
                attr.width = (DisplayUtils.getScreenWidth(context) * ratio.coerceIn(0.1f, 1f)).toInt()
            } else {
                attr.width = dialogWidth
            }
            it.attributes = attr
        }
    }

    @Composable
    abstract fun Content()
}

class SimpleComposeDialog(
    context: Context,
    private val dialogWidth: Int = 270.dpF.toInt(),
    private val contentView: @Composable (ComposeDialog) -> Unit
) : ComposeDialog(context, dialogWidth) {
    @Composable
    override fun Content() {
        contentView(this)
    }
}

class BottomComposeDialog(
    context: Context,
    private val contentView: @Composable (ComposeDialog) -> Unit
) : ComposeDialog(context, R.style.Animation_AppCompat_Dialog_SlideIn) {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        window?.let {
            it.setGravity(Gravity.BOTTOM)

            val params = it.attributes

            params.width = WindowManager.LayoutParams.MATCH_PARENT
            params.height = WindowManager.LayoutParams.WRAP_CONTENT
            params.flags = WindowManager.LayoutParams.FLAG_DIM_BEHIND
            params.dimAmount = 0.5f
            it.attributes = params
        }
    }

    @Composable
    override fun Content() {
        contentView(this)
    }

}

class ConfirmDialog(
    context: Context,
    private val title: String? = null,
    private val content: String,
    val onConfirm: () -> Unit = {},
) :
    ComposeDialog(context, windowAnimations = R.style.Animation_AppCompat_Dialog_ScaleIn, dialogWidth = 270.dpF.toInt()) {

    @Composable
    override fun Content() {
        ConfirmDialogWidget(title = title ?: "", content, onDismiss = { dismiss() }, onConfirm)
    }
}

@Composable
fun ConfirmDialogWidget(
    title: String,
    content: String,
    onDismiss: () -> Unit,
    onConfirm: () -> Unit
) {
    Column(
        modifier = Modifier
            .width(270.dp)
            .background(Color(0xFF222222), Shapes.small)
            .padding(vertical = 20.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (title.isNotEmpty()) {
            Text(
                modifier = Modifier
                    .padding(top = 12.dp)
                    .padding(horizontal = 16.dp),
                text = title,
                color = Color.White,
                fontSize = 16.sp
            )
        }
        Text(
            modifier = Modifier
                .padding(top = 12.dp)
                .padding(horizontal = 16.dp),
            text = content,
            color = colorResource(id = R.color.white_alpha_50)
        )
        Row(
            modifier = Modifier
                .padding(top = 30.dp)
                .fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            val sizeModifier = Modifier.size(113.dp, 36.dp)
            Button(
                modifier = sizeModifier,
                onClick = {
                    onDismiss()
                },
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF464646)),
            ) {
                Text(text = stringResource(id = R.string.取消), color = Color(0xFFA3A3A3))
            }
            Button(
                modifier = sizeModifier,
                onClick = {
                    onConfirm()
                    onDismiss()
                },
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF945EFF))
            ) {
                Text(text = stringResource(id = R.string.confirm), color = Color.White)
            }
        }
    }
}