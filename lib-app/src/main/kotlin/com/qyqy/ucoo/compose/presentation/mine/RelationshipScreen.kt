package com.qyqy.ucoo.compose.presentation.mine

import android.app.Activity
import android.content.Intent
import android.graphics.drawable.ColorDrawable
import android.net.Uri
import android.util.Log
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxWithConstraints
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentWidth
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.itemsIndexed
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.selection.selectableGroup
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.ButtonDefaults.elevatedButtonElevation
import androidx.compose.material3.FloatingActionButtonDefaults
import androidx.compose.material3.LocalTextStyle
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.CompositionLocalProvider
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.compositionLocalOf
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Rect
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Shadow
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.graphics.painter.Painter
import androidx.compose.ui.layout.boundsInParent
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.bumptech.glide.integration.compose.placeholder
import com.google.accompanist.placeholder.PlaceholderHighlight
import com.google.accompanist.placeholder.placeholder
import com.google.accompanist.placeholder.shimmer
import com.qyqy.cupid.widgets.click
import com.qyqy.ucoo.LocalTracePage
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TraceConst
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.app
import com.qyqy.ucoo.component.AppLinkManager
import com.qyqy.ucoo.compose.data.LabelItem
import com.qyqy.ucoo.compose.data.Relationship
import com.qyqy.ucoo.compose.data.RelationshipGraph
import com.qyqy.ucoo.compose.getApproximateColor
import com.qyqy.ucoo.compose.presentation.profile.CPHouseEntranceWidget
import com.qyqy.ucoo.compose.presentation.ff.FamilySettingActivity
import com.qyqy.ucoo.compose.presentation.ff.InviteFamilyActivity
import com.qyqy.ucoo.compose.presentation.profile.LocalRelationShipCPFrom
import com.qyqy.ucoo.compose.presentation.profile.LocalRelationShipEntranceFrom
import com.qyqy.ucoo.compose.presentation.wedding.WeddingNavigator
import com.qyqy.ucoo.compose.theme.D_DIN
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.ThemeElevatedButton
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.AutoSizeText
import com.qyqy.ucoo.compose.ui.CircleComposeImage
import com.qyqy.ucoo.compose.ui.CommonElevatedButton
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.compose.ui.VerticalGrid
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi
import com.qyqy.ucoo.compose.vm.relationship.RelationshipMvi.ViewEvent
import com.qyqy.ucoo.compose.vm.relationship.RelationshipViewModel
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.mine.UserProfileActivity
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.TaskHelper
import com.qyqy.ucoo.utils.isInstance
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import kotlinx.collections.immutable.persistentListOf
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach

@Composable
fun RelationShipGraphRouter(user: User, viewModel: RelationshipViewModel) {
    val pg = LocalTracePage.current
    LaunchedEffect(Unit) {
        viewModel.effects.onEach { effect ->
            when (effect) {
                is RelationshipMvi.ViewEffect.Toast -> {
                    toast(effect.msg)
                }
            }
        }.launchIn(this)
    }
    val uiState = viewModel.states.collectAsStateWithLifecycle().value.relationshipGraph
    val context = LocalContext.current
    val userId by rememberUpdatedState(user.id)
    val launcher =
        rememberLauncherForActivityResult(contract = ActivityResultContracts.StartActivityForResult()) {
            if (it.resultCode == Activity.RESULT_OK) {
                viewModel.processEvent(ViewEvent.GetFamilyRelationInfo(userId))
            }
        }
    RelationShipGraphPage(uiState, Modifier, {
        if (pg == TraceConst.MINE_PAGE) {
            Analytics.reportClickEvent(TracePoints.MY_PAGE_RELATIVE_FRIEND_CELL_CLICK_INVITE)
        }
        launcher.launch(InviteFamilyActivity.newIntent(context, it.id))
    }, {
        viewModel.processEvent(ViewEvent.BuySeats(it))
    }, onWhichCp = {
        if (pg == TraceConst.MINE_PAGE) {
            Analytics.reportClickEvent(TracePoints.MY_PAGE_MAKE_CP)
        }
        TaskHelper.routerChatWithCpInvite()
    },
        onGoUserProfile = {
            if (uiState.envType == 1 && it.isSelf) {
                return@RelationShipGraphPage
            }
            context.startActivity(UserProfileActivity.createIntent(context, it))
        }, {
            context.startActivity(JsBridgeWebActivity.newIntent(context, it))
        }) {
        launcher.launch(Intent(context, FamilySettingActivity::class.java))
    }
}

@Composable
fun RelationShipGraphPage(
    uiState: RelationshipGraph,
    modifier: Modifier = Modifier,
    onConfirmAddLabel: (LabelItem) -> Unit = {},
    onConfirmUnlockLabel: (Int) -> Unit = {},
    onWhichCp: () -> Unit = {},
    onGoUserProfile: (User) -> Unit = {},
    onGoRulePage: (String) -> Unit = {},
    onManagerFamilyLabel: () -> Unit = {},
) {
    val pg = LocalTracePage.current
    val showSelectLabelDialog = remember {
        mutableStateOf(false)
    }
    val showLockLabelDialog = remember {
        mutableStateOf(false)
    }
    BoxWithConstraints(
        Modifier
            .fillMaxSize()
            .padding(horizontal = 18.dp)
    ) {
        Column(
            modifier = modifier
                .fillMaxSize()
                .verticalScroll(rememberScrollState())
        ) {
            //查看自己的关系图谱，并且自己没有CP，则在亲友团tab现实CP关系
            if (uiState.envType < 2 && uiState.cpRelationship.isInstance<Relationship.Cp.None>()) {
                CpRelationShipCard(uiState.cpRelationship, onWhichCp, onGoUserProfile)
                Spacer(modifier = Modifier.height(16.dp))
            }
            UserRelationShipListCard(<EMAIL>, uiState, onGoRulePage) {
                if (it is Relationship.Label.None) {
                    showSelectLabelDialog.value = true
                } else if (it is Relationship.Label.Normal) {
                    onGoUserProfile(it.user)
                    if (pg == TraceConst.MINE_PAGE) {
                        Analytics.reportClickEvent(TracePoints.MY_PAGE_RELATIVE_FRIEND_CELL_CLICK_PROFILE)
                    }
                }
            }
            when (uiState.envType) {
                0 -> {
                    Spacer(modifier = Modifier.height(25.dp))
                }

                1 -> {
                    Spacer(modifier = Modifier.height(25.dp))
                }

                else -> {
                    Spacer(modifier = Modifier.height(111.dp))
                }
            }
        }

        if (!uiState.hasBottomAction) {
            return@BoxWithConstraints
        }

        Row(
            modifier = Modifier
                .padding(horizontal = 11.dp)
                .padding(bottom = if (uiState.envType == 0) 45.dp else 40.dp)
                .fillMaxWidth()
                .align(Alignment.BottomCenter),
            horizontalArrangement = if (uiState.hasUnlockMore && uiState.hasLabel) Arrangement.SpaceBetween else Arrangement.Center,
        ) {
            val elevation = elevatedButtonElevation(
                ThemeElevatedButton.Level2,
                ThemeElevatedButton.Level2,
                ThemeElevatedButton.Level2,
                ThemeElevatedButton.Level3,
                ThemeElevatedButton.Level0,
            )

            if (uiState.hasUnlockMore) {
                CommonElevatedButton(
                    onClick = { showLockLabelDialog.value = true },
                    modifier = Modifier.size(152.dp, 36.dp),
                    shape = FloatingActionButtonDefaults.extendedFabShape,
                    border = BorderStroke(1.dp, Color(0xA6FFFFFF)),
                    elevation = elevation,
                    gradient = Brush.verticalGradient(listOf(Color(0xFFEEE5FF), Color(0xFF945EFF))),
                    contentPadding = PaddingValues(5.dp)
                ) {
                    AutoSizeText(
                        text = stringResource(id = R.string.解锁更多席位),
                        color = Color(0xFF61439C),
                        fontSize = 14.sp,
                        maxLines = 2,
                        alignment = Alignment.Center
                    )
                }
            }

            if (uiState.hasLabel) {
                CommonElevatedButton(
                    onClick = {
                        onManagerFamilyLabel()
                    },
                    modifier = Modifier.size(152.dp, 36.dp),
                    shape = FloatingActionButtonDefaults.extendedFabShape,
                    border = BorderStroke(1.dp, Color(0xA6FFFFFF)),
                    elevation = elevation,
                    gradient = Brush.verticalGradient(listOf(Color(0xFFE9FFFF), Color(0xFF77FFFD))),
                    contentPadding = PaddingValues(5.dp)
                ) {
                    AutoSizeText(
                        text = stringResource(id = R.string.亲友关系设置),
                        color = Color(0xFF3E9797),
                        fontSize = 14.sp,
                        maxLines = 2,
                        alignment = Alignment.Center
                    )
                }
            }
        }

        SelectRelationshipLabelDialog(
            showSelectLabelDialog,
            uiState.labelTagList,
            onConfirmAddLabel
        )

        UnLockUserRelationshipLabelPositionDialog(
            showLockLabelDialog,
            uiState.pricePerLabel,
            onConfirmUnlockLabel
        )
    }
}

@Composable
@Preview
fun CardPreview() {
    CpRelationShipCard(
        cpRelationship = Relationship.Cp.previewSimple1(),
        onWhichCp = { },
        onGoUserProfile = {})
}

@Composable
@Preview
fun CardPreview2() {
    CpRelationShipCard(
        cpRelationship = Relationship.Cp.previewSimple2(),
        onWhichCp = { },
        onGoUserProfile = {})
}

@Composable
fun CpRelationShipCard(
    cpRelationship: Relationship.Cp,
    onWhichCp: () -> Unit,
    onGoUserProfile: (User) -> Unit
) {
    val ctx = LocalContext.current
    val scene_type = LocalRelationShipEntranceFrom.current
    Column {
        Surface(
            modifier = Modifier
                .fillMaxWidth()
                .aspectRatio(2.28667f),
            shape = Shapes.corner12,
        ) {
            //卡片背景
            ComposeImage(
                model = cpRelationship.cardBgUrl.orEmpty(), modifier = Modifier
                    .background(
                        brush = Brush.verticalGradient(
                            listOf(
                                cpRelationship.gradientTopColor,
                                cpRelationship.gradientBottomColor
                            )
                        ),
                    )
                    .height(36.dp)
                    .clip(Shapes.corner12),
                failure = placeholder(ColorDrawable(android.graphics.Color.TRANSPARENT))
            )
            val isNormal = cpRelationship is Relationship.Cp.Normal
            Column(verticalArrangement = Arrangement.Center) {
                if (isNormal) {
                    Spacer(modifier = Modifier.height(12.dp))
                }
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceEvenly,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    UserAvatarWithNameItem(
                        cpRelationship.avatar1,
                        cpRelationship.name1,
                        Modifier.clickable {
                            onGoUserProfile(cpRelationship.self)
                        })
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally
                    ) {
//                        Column(
//                            modifier = Modifier
//                                .align(Alignment.CenterHorizontally)
//                                .height(66.dp),
//                            horizontalAlignment = Alignment.CenterHorizontally,
//                            verticalArrangement = Arrangement.Center
//                        ) {
//
////                            LocalDensity.current.apply {
////                                Text(
////                                    text = stringResource(id = R.string.CP值).takeIf { cpValue.isNotEmpty() }
////                                        ?: "",
////                                    color = Color.White,
////                                    fontSize = 12.sp,
////                                    fontWeight = FontWeight.Bold,
////                                    style = TextStyle(
////                                        shadow = Shadow(
////                                            color = Color.White.getApproximateColor(),
////                                            offset = Offset(0.15.dp.toPx(), 0.3.dp.toPx()),
////                                            blurRadius = 0.2.dp.toPx()
////                                        )
////                                    )
////                                )
////                            }
//                        }
//                        if(cpRelationship.cpCardEffect==null){
//                            Spacer(modifier = Modifier.height(102.dp))
//                        }else{
                            val effect = cpRelationship.cpCardEffect
                            if(effect != null){
                                ComposeImage(model = effect.fileUrl ,modifier = Modifier
                                    .size(80.dp)
                                    .click {
                                        // 7 还是 8
//                                    WeddingNavigator.navigate(ctx,from = 8)
                                        if (effect.link.isNotBlank()) {
                                            //012 是个人主页 345是部落和语音房资料卡
                                            AppLinkManager.open(ctx,Uri.parse(effect.link).buildUpon().apply {
                                                appendQueryParameter("from","${if(scene_type >=3) 7 else 8}")
                                            }.build())
                                        }
                                    })
                            }else{
                                Spacer(modifier = Modifier.height(80.dp))
                            }

//                        }

                        if (isNormal) {
                            val cpValue = cpRelationship.cpValue.orEmpty()
                            //done 2.41.0 资料卡CP模块还缺一个图片
                            AutoSizeText(
                                text = buildAnnotatedString {
                                    if (cpValue.isNotEmpty()) {
                                        withStyle(
                                            SpanStyle(
                                                color = Color.White,
                                                fontSize = 12.sp,
                                                fontWeight = FontWeight.Bold,
                                            )
                                        ) {
                                            append(ctx.getString(R.string.CP值))
                                        }
                                    }
                                    append(" ")
                                    append(cpValue)
                                },
                                modifier = Modifier
                                    .padding(horizontal = 3.dp)
                                    .padding(top = 3.dp),
                                color = Color(0xFFFF035E),
                                fontSize = 15.sp,
                                fontFamily = D_DIN,
                                maxLines = 1,
                            )

                            AppText(
                                text = cpRelationship.cpDurationDay.orEmpty(),
                                color = Color.White,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium,
                                lineHeight = 20.sp,
                                modifier = Modifier
                                    .padding(top = 4.dp)
                                    .background(Color(0x59030303), Shapes.large)
                                    .padding(horizontal = 8.dp)
                            )
                        } else {
//                            Spacer(modifier = Modifier.height(66.dp))
                            Button(
                                modifier = Modifier.size(96.dp, 32.dp),
                                colors = ButtonDefaults.buttonColors(Color.Transparent),
                                contentPadding = PaddingValues(),
                                elevation = ButtonDefaults.buttonElevation(2.dp),
                                onClick = { onWhichCp() },
                            ) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxSize()
                                        .background(
                                            brush = Brush.verticalGradient(
                                                listOf(
                                                    Color(0xFFFFD1DF), Color(0xFFFF6694)
                                                )
                                            ),
                                            shape = Shapes.medium
                                        )
                                        .border(
                                            0.8.dp,
                                            Brush.verticalGradient(
                                                listOf(
                                                    Color(0xA6FFFFFF),
                                                    Color(0xD9DD2566)
                                                )
                                            ),
                                            Shapes.medium
                                        )
                                        .padding(PaddingValues(horizontal = 5.dp))
                                ) {
                                    AutoSizeText(
                                        modifier = Modifier.align(Alignment.Center),
                                        text = stringResource(R.string.去组CP),
                                        color = Color(0xFFFFFEFE),
                                        fontSize = 14.sp,
                                    )
                                }
                            }
                        }
                    }
                    UserAvatarWithNameItem(
                        cpRelationship.avatar2,
                        cpRelationship.name2,
                        Modifier.clickable {
                            if (cpRelationship.target != null) {
                                onGoUserProfile(cpRelationship.target!!)
                            }
                        })
                }
            }
        }

        CompositionLocalProvider(
            LocalRelationShipCPFrom provides cpRelationship
        ) {
            CPHouseEntranceWidget(modifier = Modifier.padding(top = 16.dp))
        }
    }
}

@Composable
fun UserAvatarWithNameItem(avatar: Any?, name: String?, modifier: Modifier = Modifier) {
    Column(
        modifier = Modifier.width(72.dp),
        horizontalAlignment = Alignment.CenterHorizontally,
    ) {
        if (avatar != null) {
            CircleComposeImage(
                model = avatar,
                modifier = Modifier
                    .border(1.dp, Color.White, CircleShape)
                    .size(72.dp)
                    .then(modifier),
            )
        } else {
            Box(
                modifier = Modifier
                    .size(72.dp)
                    .clip(CircleShape)
                    .background(
                        Brush.radialGradient(
                            listOf(
                                Color.White,
                                Color(0xFFFFEDF1),
                                Color(0xFFFFD3DB)
                            )
                        )
                    )
                    .padding(4.dp),
                contentAlignment = Alignment.Center
            ) {
                AutoSizeText(
                    text = stringResource(id = R.string.暂无CP),
                    color = Color(0xFFF07D94),
                    fontSize = 12.sp,
                    alignment = Alignment.Center,
                    maxLines = 2
                )
            }
        }

        if (name != null) {
            Text(
                text = name,
                modifier = Modifier
                    .padding(top = 2.dp)
                    .wrapContentWidth(Alignment.CenterHorizontally),
                color = Color.White,
                fontWeight = FontWeight.Medium,
                fontSize = 14.sp,
                overflow = TextOverflow.Ellipsis,
                maxLines = 1,
            )
        }
    }
}

@Composable
fun UserRelationShipListCard(
    rootHeight: Dp,
    uiState: RelationshipGraph,
    onGoRulePage: (String) -> Unit,
    onClickUserLabel: (Relationship.Label) -> Unit,
) {
    val labelShape = remember {
        GenericShape { size, _ ->
            val topRadius = size.height.times(0.4f)
            val bottomRadius = size.height.times(0.3f)
            arcTo(
                Rect(0f, 0f, topRadius.times(2), topRadius.times(2)),
                165f, 105f, false
            )
            lineTo(size.width.minus(topRadius), 0f)
            arcTo(
                Rect(size.width.minus(topRadius.times(2)), 0f, size.width, topRadius.times(2)),
                -90f, 105f, false
            )
            arcTo(
                Rect(
                    size.width.minus(bottomRadius.times(2)).minus(3),
                    size.height.minus(bottomRadius.times(2)),
                    size.width.minus(3),
                    size.height
                ),
                15f, 75f, false
            )
            arcTo(
                Rect(
                    3f,
                    size.height.minus(bottomRadius.times(2)),
                    3f.plus(bottomRadius.times(2)),
                    size.height
                ),
                90f, 75f, false
            )
        }
    }
    var familyCardRect by remember { mutableStateOf(Rect.Zero) }

    val minHeight by with(LocalDensity.current) {
        remember {
            derivedStateOf {
                if (uiState.hasBottomAction) {
                    if (uiState.envType == 0) {
                        rootHeight.minus(familyCardRect.top.toDp()).minus(25.dp)
                    } else {
                        0.dp
                    }
                } else {
                    0.dp
                }
            }
        }
    }
    Surface(
        modifier = Modifier
            .fillMaxWidth()
            .placeholder(
                visible = uiState.placeholderLabelVisible,
                color = Color(0xFFC19DF0),
                shape = Shapes.corner12,
                highlight = PlaceholderHighlight.shimmer(
                    highlightColor = Color(0xFFDDC7FE),
                )
            )
            .onGloballyPositioned {
                familyCardRect = it.boundsInParent()
            },
        shape = Shapes.corner12,
        border = BorderStroke(1.5.dp, Color.White)
    ) {
        Column(
            modifier = Modifier
                .heightIn(min = minHeight)
                .background(
                    brush = Brush.verticalGradient(listOf(Color(0xFFDFC9FF), Color(0xFFC09AF0))),
                )
                .padding(bottom = 8.dp)
        ) {
            Box(
                modifier = Modifier.fillMaxWidth(),
            ) {
                Image(
                    modifier = Modifier
                        .height(36.dp)
                        .align(Alignment.Center),
                    painter = painterResource(id = R.drawable.ic_family_relationship_top_tag),
                    contentDescription = null
                )

                if (uiState.ruleLink.isNotEmpty()) {
                    Image(
                        modifier = Modifier
                            .padding(end = 12.dp)
                            .height(36.dp)
                            .align(Alignment.CenterEnd)
                            .clickable {
                                onGoRulePage(uiState.ruleLink)
                            },
                        painter = painterResource(id = R.drawable.ic_family_relationship_rule),
                        contentDescription = null
                    )
                }
            }


            val modifier = Modifier.fillMaxWidth()

            VerticalGrid(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(12.dp),
                columns = 3,
                horizontalSpace = 12.dp,
                verticalSpace = 16.dp,
            ) {
                for (label in uiState.labelList) {
                    RelationShipItem(
                        Modifier
                            .fillMaxWidth()
                            .clickable {
                                onClickUserLabel(label)
                            },
                        labelShape,
                        label
                    )
                }

                repeat(uiState.placeholderCount) {
                    RelationShipItem(
                        Modifier.fillMaxWidth(),
                        labelShape,
                        Relationship.Label.Placeholder
                    )
                }

                if (uiState.envType < 2) {
                    repeat(uiState.emptyCount) {
                        RelationShipItem(
                            modifier.clickable {
                                onClickUserLabel(Relationship.Label.None)
                            },
                            labelShape,
                            Relationship.Label.None
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun RelationShipItem(
    modifier: Modifier,
    shape: Shape,
    label: Relationship.Label,
) {
    Box {
        Column(
            modifier = Modifier
                .aspectRatio(0.7424242f)
                .run {
                    if (label is Relationship.Label.Normal) {
                        background(
                            Brush.verticalGradient(
                                listOf(
                                    label.gradientTopColor,
                                    label.gradientBottomColor
                                )
                            ),
                            Shapes.small
                        )
                    } else {
                        background(Color(0xFFE3D0FC), Shapes.small)
                    }
                }
                .border(1.dp, Color(0x80FFFFFF), Shapes.small)
                .clip(Shapes.small)
                .then(modifier),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            Spacer(modifier = Modifier.fillMaxHeight(0.212121f))
            val imageModifier = Modifier
                .fillMaxWidth(0.4898f)
                .aspectRatio(1f)
            if (label.avatar is Painter) {
                Image(
                    modifier = imageModifier.clip(CircleShape),
                    painter = label.avatar as Painter,
                    contentDescription = null,
                )
            } else {
                val avatarUrl = label.avatar
                CircleComposeImage(
                    model = avatarUrl,
                    modifier = imageModifier,
                )
            }
            AppText(
                modifier = Modifier.padding(top = 10.dp, start = 6.dp, end = 6.dp),
                text = label.name,
                color = label.textColor,
                fontSize = 12.sp,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )

            if (label is Relationship.Label.Normal) {
                AutoSizeText(
                    modifier = Modifier.padding(top = 5.dp, start = 6.dp, end = 6.dp),
                    text = label.label.intimacyValue,
                    color = label.textColor,
                    fontSize = 12.sp,
                    alignment = Alignment.Center,
                    maxLines = 1,
                )
            }
        }

        if (label is Relationship.Label.Normal) {
            LocalDensity.current.apply {
                Surface(
                    modifier = Modifier
                        .align(Alignment.TopCenter)
                        .height(20.dp)
                        .offset(y = (-4).dp),
                    shape = shape,
                    border = BorderStroke(1.dp, Color.White),
                    color = label.labelColor,
                    contentColor = Color.White
                ) {
                    Box(
                        modifier = Modifier.padding(horizontal = 8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        AutoSizeText(
                            text = label.label.labelName,
                            fontSize = 12.sp,
                            style = LocalTextStyle.current.merge(
                                TextStyle(
                                    shadow = Shadow(
                                        color = Color.Green.getApproximateColor(),
                                        offset = Offset(0.15.dp.toPx(), 0.3.dp.toPx()),
                                        blurRadius = 0.2.dp.toPx()
                                    ),
                                )
                            ),
                        )
                    }
                }
            }
        }
    }
}

@Preview(widthDp = 375, heightDp = 812)
@Composable
fun PreviewRelationShipGraphPage() {
    RelationShipGraphPage(
        RelationshipGraph(
            cpRelationship = Relationship.Cp.previewSimple1(),
            envType = 1,
            totalCount = 10,
            labelList = persistentListOf(
                Relationship.Label.previewSimple(),
                Relationship.Label.previewSimple()
            ),
            placeholderCpVisible = false,
            placeholderLabelVisible = false,
        )
    )
}


@Composable
private fun SelectRelationshipLabelDialog(
    flag: MutableState<Boolean>,
    labelList: List<LabelItem>,
    onConfirm: (LabelItem) -> Unit = {},
) {
    if (!flag.value) {
        return
    }
    var selectedIndex by remember(labelList) {
        mutableIntStateOf(-1)
    }
    Dialog(onDismissRequest = { flag.value = false }) {
        Column(
            modifier = Modifier
                .width(290.dp)
                .background(Color(0xFF222222), Shapes.small)
                .padding(vertical = 20.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            AutoSizeText(
                modifier = Modifier
                    .height(24.dp)
                    .padding(horizontal = 10.dp),
                text = stringResource(id = R.string.请选择亲友关系标签),
                fontSize = 17.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                maxLines = 2,
                alignment = Alignment.Center
            )
            LazyVerticalGrid(
                columns = GridCells.Fixed(3),
                modifier = Modifier
                    .padding(top = 10.dp)
                    .sizeIn(
                        maxHeight = 280.dp
                    )
                    .selectableGroup(),
                contentPadding = PaddingValues(horizontal = 16.dp, vertical = 10.dp),
                horizontalArrangement = Arrangement.spacedBy(12.dp),
                verticalArrangement = Arrangement.spacedBy(12.dp),
            ) {
                itemsIndexed(labelList, key = { _, item ->
                    item
                }) { index, item ->
                    val selected = selectedIndex == index
                    Surface(
                        selected = selected,
                        onClick = {
                            selectedIndex = index
                        },
                        modifier = Modifier.height(36.dp),
                        shape = Shapes.extraSmall,
                        color = if (selected) Color(0xFF945EFF) else Color(0x33FFFFFF),
                        contentColor = if (selected) Color.White else Color(0x80FFFFFF),
                    ) {
                        Box(
                            modifier = Modifier
                                .fillMaxSize()
                                .padding(5.dp), contentAlignment = Alignment.Center
                        ) {
                            AutoSizeText(text = item.name, fontSize = 15.sp)
                        }
                    }
                }
            }
            Button(
                onClick = {
                    onConfirm(labelList[selectedIndex])
                    flag.value = false
                },
                modifier = Modifier
                    .padding(top = 22.dp)
                    .size(238.dp, 36.dp),
                enabled = selectedIndex > -1,
                colors = ButtonDefaults.buttonColors(
                    containerColor = Color(0xFF945EFF),
                    disabledContainerColor = Color(0x80945EFF)
                )
            ) {
                Text(
                    text = stringResource(id = R.string.下一步),
                    fontSize = 16.sp,
                    color = if (selectedIndex > -1) Color.White else Color(0x80FFFFFF)
                )
            }
        }
    }
}

@Preview(widthDp = 375)
@Composable
fun PreviewSelectRelationshipLabelDialog() {
    val flag = remember {
        mutableStateOf(true)
    }
    SelectRelationshipLabelDialog(
        flag, listOf(
            LabelItem(name = "你大爷"),
            LabelItem(name = "你二爷"),
            LabelItem(name = "你三爷"),
            LabelItem(name = "你四爷"),
            LabelItem(name = "你五爷"),
            LabelItem(name = "你六爷"),
            LabelItem(name = "你七爷"),
        )
    )
}

@Composable
fun UnLockUserRelationshipLabelPositionDialog(
    flag: MutableState<Boolean>,
    pricePerLabel: Int,
    onConfirm: (Int) -> Unit = {},
) {
    if (!flag.value) {
        return
    }
    var unlockCount by remember {
        mutableIntStateOf(1)
    }
    Dialog(onDismissRequest = { flag.value = false }) {
        Column(
            modifier = Modifier
                .width(290.dp)
                .background(Color(0xFF222222), Shapes.small)
                .padding(vertical = 20.dp, horizontal = 10.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            AutoSizeText(
                modifier = Modifier.height(24.dp),
                text = stringResource(id = R.string.解锁亲友团席位),
                fontSize = 17.sp,
                fontWeight = FontWeight.Medium,
                color = Color.White,
                alignment = Alignment.Center,
                maxLines = 2,
            )

            Row(
                modifier = Modifier.padding(vertical = 40.dp),
                horizontalArrangement = Arrangement.spacedBy(2.dp),
            ) {
                Image(
                    painter = painterResource(id = R.drawable.ic_filled_count_minus),
                    contentDescription = null,
                    modifier = Modifier
                        .size(28.dp, 28.dp)
                        .clickable {
                            if (unlockCount > 1) {
                                unlockCount--
                            }
                        },
                )
                Box(
                    modifier = Modifier
                        .size(60.dp, 28.dp)
                        .background(Color(0x1AFFFFFF), Shapes.extraSmall),
                    contentAlignment = Alignment.Center,
                ) {
                    Text(
                        text = unlockCount.toString(),
                        color = Color.White,
                        fontSize = 15.sp,
                        textAlign = TextAlign.Center,
                    )
                }

                Image(
                    painter = painterResource(id = R.drawable.ic_filled_count_add),
                    contentDescription = null,
                    modifier = Modifier
                        .size(28.dp, 28.dp)
                        .clickable {
                            unlockCount++
                        },
                )
            }

            AutoSizeText(
                text = stringResource(id = R.string.N个金币可解锁1个亲友团席位, pricePerLabel),
                fontSize = 14.sp,
                color = Color.White.copy(0.5f),
                alignment = Alignment.Center,
                maxLines = 2,
            )

            AutoSizeText(
                modifier = Modifier.padding(top = 16.dp, bottom = 20.dp),
                text = stringResource(
                    id = R.string.本次总花费N个金币,
                    pricePerLabel.times(unlockCount)
                ),
                fontSize = 14.sp,
                color = Color(0xFFFFD37E),
                alignment = Alignment.Center,
                maxLines = 2,
            )

            Button(
                onClick = {
                    onConfirm(unlockCount)
                    flag.value = false
                },
                modifier = Modifier.size(144.dp, 44.dp),
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF945EFF)),
                contentPadding = PaddingValues(5.dp)
            ) {
                AutoSizeText(
                    text = stringResource(R.string.确认解锁),
                    color = Color.White,
                    alignment = Alignment.Center,
                    maxLines = 2,
                    fontSize = 16.sp
                )
            }
        }
    }
}

@Preview(widthDp = 375)
@Composable
fun PreviewUnLockUserRelationshipLabelPositionDialog() {
    val flag = remember {
        mutableStateOf(true)
    }
    UnLockUserRelationshipLabelPositionDialog(flag, 5000)
}