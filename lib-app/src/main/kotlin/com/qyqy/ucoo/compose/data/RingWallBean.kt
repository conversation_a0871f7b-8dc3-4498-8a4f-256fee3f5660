package com.qyqy.ucoo.compose.data


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import com.qyqy.ucoo.account.AppUser
import java.io.Serial
import java.text.SimpleDateFormat
import java.util.Locale

/**
{
"ring": {    // 戒指信息
"id": 1,
"name": "リング1",
"icon": "https://media.ucoofun.com/opsite/prop/icon/%E9%BE%99%E5%B9%B4%E5%A4%B4%E5%83%8F%E6%A1%86400-276_iaAWhAB.webp",
"t": 20,
"days": 0,
"price": 0,
"order": 0,
"gain_type": 2,
"prop_desc": null,
"effect_file": "https://media.ucoofun.com/opsite/ring/effect/%E9%BE%99%E5%B9%B4%E5%A4%B4%E5%83%8F%E6%A1%86400-276.webp",
"extra": {}
},
"count": 0,    // 点亮数量
"last_light_user": null,    // 最后点亮人
"last_light_time": null    // 最后点亮时间
}
 */
@Keep
@Serializable
data class RingWallBean(
    @SerialName("count")
    val count: Int = 0,
    @SerialName("last_light_time")
    val lastLightTime: Long = 0L,
    @SerialName("last_light_user")
    val lastLightUser: AppUser? = null,
    @SerialName("ring")
    val ring: Ring = Ring()
) {
    @Keep
    @Serializable
    data class Ring(
        @SerialName("days")
        val days: Int = 0,
        @SerialName("effect_file")
        val effectFile: String = "",
//        @SerialName("extra")
//        val extra: Extra = Extra(),
        @SerialName("gain_type")
        val gainType: Int = 0,
        @SerialName("icon")
        val icon: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("name")
        val name: String = "",
        @SerialName("order")
        val order: Int = 0,
        @SerialName("price")
        val price: Int = 0,
//        @SerialName("prop_desc")
//        val propDesc: Any = Any(),
        @SerialName("t")
        val t: Int = 0
    )

    private var _formatTime: String? = null

    val formatTime: String
        get() = _formatTime ?: SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(lastLightTime.times(1000L))
            .also {
                _formatTime = it
            }
}


@Keep
@Serializable
data class RingWallHistory(
    @SerialName("id")
    val id: Int,
    @SerialName("from_user")
    val fromUser: AppUser,
    @SerialName("create_timestamp")
    val createTimeStamp: Long
){
    private var _formatTime: String? = null

    val formatTime: String
        get() = _formatTime ?: SimpleDateFormat("yyyy-MM-dd", Locale.getDefault()).format(createTimeStamp.times(1000L))
            .also {
                _formatTime = it
            }
}