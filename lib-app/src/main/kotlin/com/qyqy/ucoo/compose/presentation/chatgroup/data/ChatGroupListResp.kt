package com.qyqy.ucoo.compose.presentation.chatgroup.data


import com.qyqy.ucoo.account.AppUser
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ChatGroupListResp(
    val chatgroups: List<Chatgroup> = listOf(),
    @SerialName("my_chatgroup")
    val myChatgroup: MyChatgroup? = null
) {
    @Serializable
    data class Chatgroup(
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        val intro: String = "",
        @SerialName("member_cnt")
        val memberCnt: Int = 0,
        val name: String = "",
        @SerialName("public_id")
        val publicId: Int = 0,
        @SerialName("rc_group_id")
        val rcGroupId: String = "",
        @SerialName("relation_with_me")
        val relationWithMe: Int = 0,
        @SerialName("sample_members")
        val sampleMembers: List<AppUser> = listOf()
    ) {
        val sampleAvatars = sampleMembers.take(4).map { it.avatarUrl }
        val chatGroupId: Int = rcGroupId.replace("chatgroup", "").toIntOrNull() ?: 0
    }

    @Serializable
    data class MyChatgroup(
        val id: Int = 0,
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        val intro: String = "",
        val name: String = "",
        @SerialName("public_id")
        val publicId: Int = 0
    )
}