package com.qyqy.ucoo.compose.presentation.cp_house

import com.qyqy.ucoo.compose.data.CPHouseFullInfo
import com.qyqy.ucoo.compose.data.CPHouseIntimacyHistoryBean
import com.qyqy.ucoo.compose.data.CPMemoriesBean
import com.qyqy.ucoo.compose.presentation.cp_house.bean.HRank
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import kotlinx.serialization.json.JsonArray
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface CPHouseApi {
    /**
     * 获取亲密小屋基础信息
     *
     * @param room_id 小屋id
     *
     * 返回结果:
     * {
     *     "data": {
     *         "female_user": {
    ...
     *         },
     *         "male_user": {
    ...
     *         },
     *         "cp_extra_info": {
     *             "cp_value": 22980,
     *             "togather_days": "在一起23天",
     *             "level_info": null
     *         },
     *         "balance": 1000, // 小屋币
     *         "intimacy_score": 100, // 亲密度
     *         "intimacy_ranking": 100, // 亲密度排行, 如果不在榜单中则返回-1
     *         "car": {
     *             "name": "xxx",
     *             "icon": "xxxx",
     *             "effect_file": "xxx"，// 动效
     *             "profit": 100, // 每天收益
     *         },
     *         "house": {
     *             "name": "xxx",
     *             "icon": "xxxx",
     *             "effect_file": "xxx"
     *             "profit": 100, // 每天收益
     *         }，
     *         "background": "xxxxxx"
     *         "private_room_id": 100, // 私密小屋id
     *         "is_hidden": true // 亲密小屋是否隐藏
     *     }
     * }
     */
    @GET("api/friendship/v1/cp_room")
    suspend fun getCpHouseInfo(@Query("room_id") room_id: Int): ApiResponse<CPHouseFullInfo>

    /**
     * @param type // 装扮类型 17:小屋模型 18: cp小屋车辆
     * @param gainType //获取方式 3: 亲密度解锁，4: 宝箱抽取 5: 系统赠送。 可选字段，不传则获取所有的数据
     *
     * result:
     * {
     *     "data": [
     *         {
     *             "t": 1,    // 装扮种类
     *             "id": 100,
     *             "name": "xxx",
     *             "icon": "xxxx",
     *             "effect_file": "xxxx",
     *             "profit": 100, // 小屋币收益
     *             "gain_type": 1, // 获取方式 3: 亲密度解锁，4: 宝箱抽取 5: 系统赠送
     *             "intimacy_score_limit": 100, // 亲密度解锁门槛
     *             "obtained": true // 已获得
     *         }
     *     ]
     * }
     */
    @GET("api/friendship/v1/cp_room/prop")
    suspend fun getCpHouseHomeProp(@Query("t") type: Int): ApiResponse<List<CPHouseFullInfo.CPHouseFullPropItem>>

    /**
     * cp小屋装扮中心tab
     *
     * 结果:
     * {
     *     "data": {
     *         "tab_list": [
     *             {
     *                 "t": 1,
     *                 "name": 1,
     *                 "order": 1,
     *             },
     *             ...
     *         ]
     *     }
     * }
     */
    @GET("api/friendship/v1/cp_room/prop/tab")
    suspend fun getCpHousePropTab(): ApiResponse<JsonObject>

    /**
     * cp小屋已获得的装扮展示
     *
     * @param type 道具类型
     * @param last_id 最后一个item id
     * @return
     */
    @GET("api/friendship/v1/cp_room/prop/obtained")
    suspend fun getCpHouseMineProp(
        @Query("t") type: Int,
        @Query("last_id") last_id: Int
    ): ApiResponse<JsonArray>

    /**
     * 佩戴cp小屋装扮或其他道具
     *
     * @param { "t": 1, "prop_id": 11,"use": true }
     * t: 道具类型
     * prop_id: 道具id
     * use: 是否使用
     * {
     *     "data": {
     *         "msg": "xxxx"
     *     }
     * }
     */
    @POST("api/friendship/v1/cp_room/prop/use")
    suspend fun modifyCpHouseProp(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>

    /**
     * 获取亲密度记录
     *
     * @param last_id
     * @return
     */
    @GET("api/friendship/v1/cp_room/intimacy/change_history")
    suspend fun getCpIntimacyHistory(@Query("last_id") last_id: Int): ApiResponse<List<CPHouseIntimacyHistoryBean>>

    /**
     * 1. 获取与Ta的独家记忆
     *
     * {
     *     "data" : {
     *         "intimacy_score": 100,    // 当前亲密度
     *         "next_level_intimacy_score": 10000, // 下一个等级亲密度
     *         "female_user": {
     *              ...
     *         },
     *         "male_user": {
     *             ...
     *         },
     *         "history": [   // 按照date升序排列
     *             {
     *                 "date": "2021-01-01",
     *                 "content": "xxxxxx"
     *             },
     *             ...
     *         ],
     *         "togather_days": 10,
     *         "house": {
     *             "name": "xxx",
     *             "icon": "xxxx",
     *             "effect_file": "xxx"
     *             "profit": 100, // 每天收益
     *         }
     *     }
     * }
     */
    @GET("api/friendship/v1/cp_room/exclusive_memory")
    suspend fun getCpHouseMemories(): ApiResponse<CPMemoriesBean>

    /**
     * 隐藏亲密小屋
     *
     * @param { "hide": true/false }
     * @return
     */
    @POST("api/friendship/v1/cp_room/update")
    suspend fun setCpHouseVisible(@Body body: Map<String, @JvmSuppressWildcards Any>): ApiResponse<JsonObject>


    /**
     * 1. 获取小屋亲密度周榜数据
     */
    @GET("api/friendship/v1/cp_room/ranking")
    suspend fun getRankList(): ApiResponse<HRank>

    /**
     * 2.39.0 获取任务信息
     */
    @GET("api/friendship/v1/cp_room/task/info")
    suspend fun getHouseTaskInfo(): ApiResponse<JsonObject>

    @POST("api/friendship/v1/cp_room/task/check_in")
    suspend fun checkIn(): ApiResponse<JsonObject>

    @GET("api/friendship/v1/cp_room/task/to_finish")
    suspend fun finishTask(@Query("task_id") taskId:Int): ApiResponse<JsonObject>
}
