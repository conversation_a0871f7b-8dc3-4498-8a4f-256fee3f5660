package com.qyqy.ucoo.compose.presentation.greets

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.lifecycle.viewmodel.compose.viewModel
import com.overseas.common.utils.id2String
import com.qyqy.ucoo.R
import com.qyqy.ucoo.compose.presentation.redpackage.TextFieldWithHint
import com.qyqy.ucoo.compose.presentation.voice.GreetTips
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.theme.colorSecondBlack
import com.qyqy.ucoo.compose.theme.colorWhite50Alpha
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.compose.ui.ConfirmDialog
import com.qyqy.ucoo.toastRes

@Composable
fun TextGreetings(
    buttonText: String = "Hello",
    vm: GreetViewModel = viewModel(),
    colorSurface: Color = Color(0x26FFFFFF)
) {

    val loading by vm.refresh.collectAsState()
    if (loading) {
        Box(modifier = Modifier.fillMaxSize(1f), contentAlignment = Alignment.Center) {
            CircularProgressIndicator()
        }
        return
    }
    val textState = remember { mutableStateOf(TextFieldValue("")) }
    var dialogVisible by remember { mutableStateOf(false) }
    if (dialogVisible) {
        Dialog(onDismissRequest = {
            dialogVisible = false
        }) {
            EditDialog(
                modifier = Modifier
                    .width(270.dp)
                    .background(colorSecondBlack, Shapes.small)
                    .padding(16.dp, 20.dp),
                stringResource(id = R.string.input_greet_content),
                stringResource(id = R.string.input_greet_content_hint),
                stringResource(id = R.string.rc_cancel),
                stringResource(id = R.string.rc_confirm),
                textState,
                onCancel = {
                    dialogVisible = false
                },
                onConfirm = {
                    if (textState.value.text.isNotEmpty()) {
                        vm.addTextItem(textState.value.text)
                        dialogVisible = false
                    } else {
                        toastRes(R.string.input_greet_content_hint)
                    }
                }
            )
        }
    }
    val greetings = vm.greetingsFlow.collectAsState()
    val greetingInfo by remember {
        derivedStateOf { greetings.value.tabs.firstOrNull { it.contentType == GreetItem.TYPE_TEXT } ?: Greetings.Tab() }
    }
    val list = greetingInfo.contentList
    val context = LocalContext.current
    GreetListPage(buttonText = buttonText, onClick = {
        dialogVisible = true
    }, isEmpty = list.isEmpty()) {
        LazyColumn(modifier = Modifier.padding(16.dp), content = {
            items(list) {
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier.padding(vertical = 8.dp)
                ) {
                    Box(
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                            .background(colorSurface)
                            .clickable {
                                ConfirmDialog(context, content = id2String(R.string.confrim_to_delete)) {
                                    vm.deleteItem(it)
                                }.show()
                            }
                    ) {
                        Box(
                            modifier = Modifier
                                .size(12.dp, 1.5.dp)
                                .background(Color.White)
                                .align(Alignment.Center)
                        )
                    }
                    Spacer(modifier = Modifier.width(12.dp))
                    AppText(
                        text = it.content,
                        modifier = Modifier
                            .background(Color(0x4D6A6A6A), Shapes.small)
                            .padding(12.dp),
                        color = Color.White,
                        fontSize = 14.sp
                    )
                }
            }
            item {
                GreetTips(tips = greetingInfo.tips)
            }
        })
    }
}

@Preview
@Composable
fun TextGreetingsPreview() {
    TextGreetings()
}

@Composable
fun EditDialog(
    modifier: Modifier = Modifier,
    title: String,
    hint: String,
    leftButtonText: String,
    rightButtonText: String,
    textFieldValueState: MutableState<TextFieldValue> = remember {
        mutableStateOf(TextFieldValue())
    },
    onCancel: () -> Unit,
    onConfirm: () -> Unit
) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {

        // 标题
        Text(text = title, fontSize = 17.sp, color = Color.White)
        Spacer(modifier = Modifier.height(20.dp))
        // 输入框
        TextFieldWithHint(
            textFieldValueState = textFieldValueState,
            textFieldModifier = Modifier.fillMaxHeight(1f),
            maxLength = 50,
            hint = hint,
            hintColor = colorWhite50Alpha,
            textColor = Color.White,
            modifier = Modifier
                .background(Color(0x0DFFFFFF), Shapes.small)
                .padding(12.dp)
                .height(88.dp),
            contentAlignment = Alignment.TopStart
        )
        Spacer(modifier = Modifier.height(20.dp))
        // 按钮
        Row {
            // 取消按钮
            Button(
                onClick = onCancel,
                colors = ButtonDefaults.buttonColors(containerColor = Color(0xFF464646)),
                modifier = Modifier.weight(1f)
            ) {
                Text(text = leftButtonText, fontSize = 16.sp, color = Color(0xFFA3A3A3))
            }
            Spacer(modifier = Modifier.width(12.dp))
            // 确认按钮
            Button(onClick = onConfirm, modifier = Modifier.weight(1f)) {
                Text(text = rightButtonText, fontSize = 16.sp, color = Color.White)
            }
        }
    }
}

@Preview
@Composable
fun EditDialogPreview() {
    AppTheme {
        EditDialog(
            modifier = Modifier
                .width(270.dp)
                .background(colorSecondBlack, Shapes.small)
                .padding(16.dp, 20.dp),
            stringResource(id = R.string.input_greet_content),
            stringResource(id = R.string.input_greet_content_hint),
            stringResource(id = R.string.rc_cancel),
            stringResource(id = R.string.rc_confirm),
            onCancel = {},
            onConfirm = {}
        )
    }
}