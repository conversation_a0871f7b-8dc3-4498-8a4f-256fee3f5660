package com.qyqy.ucoo.compose.presentation.ff

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.requiredHeight
import androidx.compose.foundation.layout.requiredSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Tab
import androidx.compose.material3.TabRow
import androidx.compose.material3.TabRowDefaults.tabIndicatorOffset
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringArrayResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.lifecycle.viewmodel.initializer
import androidx.lifecycle.viewmodel.viewModelFactory
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.account.Medal
import com.qyqy.ucoo.account.RoomInfo
import com.qyqy.ucoo.account.WeddingRing
import com.qyqy.ucoo.compose.state.StateListView
import com.qyqy.ucoo.compose.theme.AppTheme
import com.qyqy.ucoo.compose.theme.colorPageBackground
import com.qyqy.ucoo.compose.ui.AppTitleBar
import kotlinx.coroutines.launch


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun InviteFamilyPage(
    tagId: Int = 0,
    onBack: () -> Unit,
    latestViewModel: LatestViewModel = viewModel(factory = viewModelFactory {
        initializer {
            LatestViewModel(tagId)
        }
    }),
    friendsViewModel: MyFriendsViewModel = viewModel(factory = viewModelFactory {
        initializer {
            MyFriendsViewModel(tagId)
        }
    }),
) {
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .background(colorPageBackground)
        ) {
            val scope = rememberCoroutineScope()
            AppTitleBar(title = stringResource(id = R.string.send_family_invite), onBack = onBack)

            val pagerState = rememberPagerState(0) {
                2
            }
            val titles = stringArrayResource(id = R.array.array_title_invite_family)
            TabRow(
                modifier = Modifier
                    .fillMaxWidth(0.6f)
                    .align(Alignment.CenterHorizontally),
                selectedTabIndex = pagerState.currentPage,
                containerColor = Color.Transparent,
                contentColor = Color.Transparent,
                indicator = { tabPositions ->
                    val tabPoi = tabPositions[pagerState.currentPage]
                    Box(
                        Modifier
                            .tabIndicatorOffset(tabPoi)
                            .requiredSize(10.dp, 3.dp)
                            .background(Color.White, RoundedCornerShape(1.5.dp)),
                    )
                },
                divider = {}
            ) {
                titles.forEachIndexed { index, title ->
                    Tab(
                        text = {
                            Text(text = title, color = Color.White)
                        },
                        modifier = Modifier.requiredHeight(36.dp),
                        selectedContentColor = Color.White,
                        unselectedContentColor = colorResource(id = R.color.white_alpha_50),
                        selected = pagerState.currentPage == index,
                        onClick = {
                            scope.launch {
                                pagerState.scrollToPage(index, 0f)
                            }
                        })
                }
            }
            HorizontalPager(state = pagerState) {
                when (it) {
                    0 -> InviteUserList(viewModel = latestViewModel)

                    1 -> InviteUserList(viewModel = friendsViewModel)
                    else -> Text(text = "我的好友")
                }
            }

        }
    }
}

val userForPreview: AppUser
    get() = AppUser(
        room = RoomInfo(),
        isFirstClassUser = true,
        firstClassUserNo = "No.001",
        cityName = "火星",
        hasCertified = true,
        onlineStatus = 0,
        ringInfo = WeddingRing(name = "戒指",icon = "")
    ).apply {
        this.id = "1"
        gender = 2
        nickname = "幼儿园班花"
        avatarUrl = "https://media.ucoofun.com/opsite/avatar/male/avatar_male_18.jpg"
        age = 20
        publicId = "10086"
        isVip = true
        cash = "10"
        isVip = true
        height = 180
        characteristicMedalList = listOf(
            Medal("https://media.ucoofun.com/opsite/prop/icon/20241128-ziv.png", 100, 40),
            Medal("https://media.ucoofun.com/opsite/prop/icon/20241128-ziv.png", 100, 40),
        )
        attractiveFlags = listOf(
            AttractiveFlag(1,1,"可御可甜","https://media.ucoofun.com/opsite/common/icon1_0uTh8o1.png"),
            AttractiveFlag(1,2,"人皮话多","https://media.ucoofun.com/opsite/common/icon1_0uTh8o1.png"),
            AttractiveFlag(1,3,"气质拽姐","https://media.ucoofun.com/opsite/common/icon1_0uTh8o1.png"),
        )
    }

@Composable
fun InviteUserList(viewModel: InviteViewModel) {
    StateListView(viewModel = viewModel, keyProvider = null) { item, _, coroutineScope, loadingState ->
        InviteToFamilyUserItem(user = item) {
            coroutineScope.launch {
                loadingState.value = true
                viewModel.inviteUser(item.userId)
                loadingState.value = false
            }
        }
    }
}

@Preview(showBackground = true, backgroundColor = 0xFF030303)
@Composable
fun InviteFamilyPagePreviewer() {
    InviteFamilyPage(onBack = {})
}