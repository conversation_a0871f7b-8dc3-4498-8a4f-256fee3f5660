package com.qyqy.ucoo.compose.domain.usecase.profile

import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.data.BottomRadiusItem
import com.qyqy.ucoo.compose.data.CategoryGiftWall
import com.qyqy.ucoo.compose.data.CategoryTitleItem
import com.qyqy.ucoo.compose.data.GiftItem
import com.qyqy.ucoo.compose.data.SpaceItem
import com.qyqy.ucoo.compose.data.SpanItem
import com.qyqy.ucoo.compose.data.TopRadiusItem
import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.BlindboxGift
import com.qyqy.ucoo.im.bean.MyGift
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonArray

class GetGiftWallUseCase constructor(
    private val userRepository: UserRepository = UserRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<String, List<CategoryGiftWall>>(ioDispatcher) {
    override suspend fun execute(parameters: String): Result<List<CategoryGiftWall>> {
        return userRepository.getUserGiftWall(parameters).mapCatching {
            getGiftList(it)
        }
    }

    private fun getGiftList(jsonObject: JsonObject): List<CategoryGiftWall> {
        val blindboxGiftList = jsonObject["blindbox_gifts"]?.jsonArray?.let {
            sAppJson.decodeFromJsonElement<List<BlindboxGift>>(it)
        }.orEmpty()

        val normalBlindboxGiftList =
            jsonObject["normal_blindbox_gifts"]?.jsonArray?.let {
                sAppJson.decodeFromJsonElement<List<BlindboxGift>>(it)
            }.orEmpty()
        val commonGiftList = jsonObject["gift_wall"]?.jsonArray?.let {
            sAppJson.decodeFromJsonElement<List<MyGift>>(it)
        }.orEmpty()

        return buildList {
            add(CategoryGiftWall(app.getString(R.string.盲盒礼物), buildList {
                blindboxGiftList.forEach {
                    add(TopRadiusItem)
                    add(CategoryTitleItem(it.seriesName))
                    it.gifts.forEach { gift ->
                        add(GiftItem(gift.gift, gift.count))
                    }
                    val lastLineCount = it.gifts.size.rem(4)
                    if (lastLineCount != 0) {
                        val leftSpan = 4.minus(lastLineCount)
                        add(size.minus(lastLineCount), SpanItem(leftSpan))
                        add(SpanItem(leftSpan))
                    }
                    add(BottomRadiusItem)
                    add(SpaceItem(16))
                }

                normalBlindboxGiftList.forEach {
                    add(TopRadiusItem)
                    add(CategoryTitleItem(it.seriesName))
                    it.gifts.forEach { gift ->
                        add(GiftItem(gift.gift, gift.count))
                    }
                    val lastLineCount = it.gifts.size.rem(4)
                    if (lastLineCount != 0) {
                        val leftSpan = 4.minus(lastLineCount)
                        add(size.minus(lastLineCount), SpanItem(leftSpan))
                        add(SpanItem(leftSpan))
                    }
                    add(BottomRadiusItem)
                    add(SpaceItem(16))
                }
            }))

            add(CategoryGiftWall(app.getString(R.string.common_gift), buildList {
                add(TopRadiusItem)
                add(CategoryTitleItem(app.getString(R.string.common_gift)))
                commonGiftList.forEach { gift ->
                    add(GiftItem(gift.gift, gift.count))
                }
                val lastLineCount = commonGiftList.size.rem(4)
                if (lastLineCount != 0) {
                    val leftSpan = 4.minus(lastLineCount)
                    add(size.minus(lastLineCount), SpanItem(leftSpan))
                    add(SpanItem(leftSpan))
                }
                add(BottomRadiusItem)
            }))
        }
    }

}