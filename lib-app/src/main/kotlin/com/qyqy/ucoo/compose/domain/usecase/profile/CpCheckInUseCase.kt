package com.qyqy.ucoo.compose.domain.usecase.profile

import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers

class CpCheckInUseCase constructor(
    private val userRepository: UserRepository = UserRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<Unit, Boolean?>(ioDispatcher) {
    override suspend fun execute(parameters: Unit): Result<Boolean?> {
        return userRepository.cpCheckIn()
    }

}