package com.qyqy.ucoo.compose.ui

import android.app.Activity
import android.content.Context
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import com.qyqy.ucoo.compose.theme.AppTheme

@Composable
fun UCOOScreen(
    title: String = "UCOO",
    onBack: (Context) -> Unit = {
        if (it is Activity) {
            it.finish()
        }
    },
    content: @Composable ColumnScope.() -> Unit = {},
) {
    val context = LocalContext.current
    AppTheme {
        Column(
            modifier = Modifier
                .fillMaxSize(1f)
                .systemBarsPadding()
        ) {
            AppTitleBar(
                title = title,
                onBack = {
                    onBack(context)
                }
            )
            content()
        }
    }
}