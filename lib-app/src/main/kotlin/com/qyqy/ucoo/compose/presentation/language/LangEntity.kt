package com.qyqy.ucoo.compose.presentation.language


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient
import java.util.Locale

@Serializable
data class LangEntity(
    val options: List<Option> = listOf(),
    @SerialName("system_language")
    val systemLanguage: String = ""
) {
    @Serializable
    data class Option(
        val code: String = "",
        val label: String = ""
    ) {
        @Transient
        val locale: Locale = when (code) {
            "ja" -> Locale.JAPANESE
            else -> Locale.forLanguageTag(code)
        }
    }
}