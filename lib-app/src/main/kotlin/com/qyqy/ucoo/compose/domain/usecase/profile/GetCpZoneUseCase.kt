package com.qyqy.ucoo.compose.domain.usecase.profile

import com.qyqy.ucoo.compose.domain.ResultSuspendUseCase
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.CpZone
import com.qyqy.ucoo.user.UserRepository
import kotlinx.coroutines.CoroutineDispatcher
import kotlinx.coroutines.Dispatchers
import kotlinx.serialization.json.decodeFromJsonElement

class GetCpZoneUseCase constructor(
    private val userRepository: UserRepository = UserRepository(),
    ioDispatcher: CoroutineDispatcher = Dispatchers.IO,
) : ResultSuspendUseCase<String, CpZone>(ioDispatcher) {
    override suspend fun execute(parameters: String): Result<CpZone> {
        return userRepository.getUserCpZone(parameters).mapCatching { json ->
            requireNotNull(json["cp_zone"]?.let {
                sAppJson.decodeFromJsonElement<CpZone?>(it)
            })
        }
    }

}