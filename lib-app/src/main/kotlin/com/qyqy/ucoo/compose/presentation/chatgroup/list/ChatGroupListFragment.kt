package com.qyqy.ucoo.compose.presentation.chatgroup.list

import com.qyqy.ucoo.base.BaseComposeFragment
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.statusBarsPadding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.clipToBounds
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.fragment.app.viewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.qyqy.ucoo.R
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.base.DataState
import com.qyqy.ucoo.compose.presentation.chatgroup.create.CreateGroupDialogFragment
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroupListResp
import com.qyqy.ucoo.compose.presentation.chatgroup.launchChatGroup
import com.qyqy.ucoo.compose.presentation.chatgroup.launchChatGroupDetail
import com.qyqy.ucoo.compose.state.EmptyView
import com.qyqy.ucoo.compose.state.PullRefreshBox
import com.qyqy.ucoo.compose.ui.AppText
import com.qyqy.ucoo.core.Analytics
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.flow.collectLatest

class ChatGroupListFragment : BaseComposeFragment() {
    private val viewModel by viewModels<ChatGroupListViewModel>()

    context(CoroutineScope) override fun onRefresh() {
        super.onRefresh()
        viewModel.refresh()
    }

    @OptIn(ExperimentalMaterial3Api::class)
    @Composable
    override fun ContentView() {
        val isRefreshing by viewModel.refreshFlow.collectAsStateWithLifecycle()
        val data by viewModel.dataFlow.collectAsStateWithLifecycle()
        LaunchedEffect(key1 = viewModel) {
            ChatGroupListViewModel.effect.collectLatest {
                viewModel.refresh()
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize()
        ) {
            Spacer(modifier = Modifier.height(48.dp))
            Spacer(modifier = Modifier.statusBarsPadding())
            PullRefreshBox(modifier = Modifier.fillMaxSize(), isRefreshing = isRefreshing, onRefresh = { viewModel.refresh() }) {
                if (data is DataState.Success) {
                    val resp = (data as DataState.Success<ChatGroupListResp>).data
                    val list = resp.chatgroups
                    // 内容
                    ChatGroupList(list = list, toDetail = {
                        launchChatGroupDetail(it.chatGroupId)
                    }, onOpenGroup = {
                        launchChatGroup(it.chatGroupId)
                    }) {
                        viewModel.joinGroup(it)
                    }
                    if (list.isEmpty()) {
                        EmptyView(modifier = Modifier.align(Alignment.Center))
                    }
                    val bottomModifier: (() -> Unit) -> Modifier = {
                        Modifier
                            .clip(RoundedCornerShape(50))
                            .clickable(onClick = it)
                            .background(
                                Brush.horizontalGradient(listOf(Color(0xFFCCB5FF), Color(0xFF8B56FC))),
                                RoundedCornerShape(50)
                            )
                            .padding(18.dp, 6.dp)
                    }
                    Box(
                        modifier = Modifier
                            .padding(bottom = 20.dp)
                            .align(Alignment.BottomCenter)
                    ) {
                        if (resp.myChatgroup == null) {
                            Row(
                                modifier = bottomModifier {
                                    CreateGroupDialogFragment().show(childFragmentManager, "create_group")
                                }, verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(
                                    painter = painterResource(R.drawable.icon_create_group),
                                    "",
                                    modifier = Modifier.size(32.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                AppText(text = stringResource(id = R.string.create_group), fontSize = 16.sp, color = Color.White)
                            }
                        } else {
                            Row(
                                modifier = bottomModifier {
                                    launchChatGroup(resp.myChatgroup.id)
                                }, verticalAlignment = Alignment.CenterVertically
                            ) {
                                Image(painter = painterResource(R.drawable.icon_my_group), "", modifier = Modifier.size(32.dp))
                                Spacer(modifier = Modifier.width(4.dp))
                                AppText(text = stringResource(id = R.string.mine_group), fontSize = 16.sp, color = Color.White)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun onResume() {
        super.onResume()
        Analytics.reportExposureEvent(TracePoints.SQUARE_GROUP)
    }

}