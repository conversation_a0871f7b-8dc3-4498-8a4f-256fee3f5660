package com.qyqy.ucoo.compose.presentation.profile

import android.content.Intent
import android.content.res.Configuration
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AttractiveFlag
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.data.UserInfoItem
import com.qyqy.ucoo.compose.keepLastNonNullState
import com.qyqy.ucoo.compose.noEffectClickable
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.MainNavigator
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.compose.theme.Shapes
import com.qyqy.ucoo.compose.ui.ComposeImage
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.mine.EditContentActivity
import com.qyqy.ucoo.mine.EditFlag
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.mine.url
import com.qyqy.ucoo.moment.publish.PublishMomentActivity
import com.qyqy.ucoo.tribe.bean.Tribe
import com.qyqy.ucoo.tribe.launchTribeActivity
import com.qyqy.ucoo.utils.OnClick


@Composable
fun UserInfoPageV2(user: User, items: List<UserInfoItem>) {
    val context = LocalContext.current
    Column(
        modifier = Modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        for (item in items) {
            when (item) {
                is UserInfoItem.Moment -> {
                    InfoCardScaffold(
                        title = "${stringResource(id = R.string.title_moment)}(${item.count})",
                        showEdit = user.isSelf && item.list.isNotEmpty(),
                        showMore = true,
                        clickEdit = {
                            context.startActivity(Intent(context, PublishMomentActivity::class.java))
                        },
                        clickMore = {
                            MainNavigator.start(context, MainNavigator.MOMENT_LIST) {
                                it.putExtra(Const.KEY_DATA, user)
                            }
                        }
                    ) {
                        if (item.list.isNotEmpty()) {
                            MomentItem(item.list) {
                                MainNavigator.start(context, MainNavigator.MOMENT_LIST) {
                                    it.putExtra(Const.KEY_DATA, user)
                                }
                            }
                        } else {
                            if (user.isSelf) {
                                AddItem(text = stringResource(id = R.string.publish_moment)) {
                                    context.startActivity(Intent(context, PublishMomentActivity::class.java))
                                }
                            } else {
                                EmptyItem(
                                    text = if (user.isBoy) {
                                        stringResource(id = R.string.他还没有发布过动态瞬间)
                                    } else {
                                        stringResource(id = R.string.她还没有发布过动态瞬间)
                                    }
                                )
                            }
                        }
                    }
                }

                is UserInfoItem.AttractiveLabel -> {
                    InfoCardScaffold(
                        title = stringResource(id = R.string.attraction),
                        showEdit = user.isSelf && item.list.isNotEmpty(),
                        clickEdit = {
                            MainNavigator.start(context, MainNavigator.ATTRACTIVE_SETTINGS)
                        }
                    ) {
                        if (item.list.isNotEmpty()) {
                            AttractiveLabelItem(item.list)
                        } else {
                            if (user.isSelf) {
                                AddItem(text = stringResource(id = R.string.add_attraction)) {
                                    MainNavigator.start(context, MainNavigator.ATTRACTIVE_SETTINGS)
                                }
                            } else {
                                EmptyItem(
                                    text = if (user.isBoy) {
                                        stringResource(id = R.string.他还没有设置过引力签)
                                    } else {
                                        stringResource(id = R.string.她还没有设置过引力签)
                                    }
                                )
                            }
                        }
                    }
                }

                is UserInfoItem.Signature -> {
                    InfoCardScaffold(
                        title = stringResource(id = R.string.个性签名),
                        showEdit = user.isSelf && item.shortIntro.isNotEmpty(),
                        clickEdit = {
                            context.startActivity(
                                EditContentActivity.newIntent(
                                    context,
                                    context.getString(R.string.modify_profile),
                                    EditFlag.Signature,
                                    item.shortIntro,
                                    context.getString(R.string.input_profile_to_youself),
                                    null,
                                    7,
                                    40
                                )
                            )
                        }
                    ) {
                        if (item.shortIntro.isNotEmpty()) {
                            TextItem(text = item.shortIntro)
                        } else {
                            if (user.isSelf) {
                                AddItem(text = stringResource(id = R.string.添加签名)) {
                                    context.startActivity(
                                        EditContentActivity.newIntent(
                                            context,
                                            context.getString(R.string.modify_profile),
                                            EditFlag.Signature,
                                            "",
                                            context.getString(R.string.input_profile_to_youself),
                                            null,
                                            7,
                                            40
                                        )
                                    )
                                }
                            } else {
                                EmptyItem(
                                    text = if (user.isBoy) {
                                        stringResource(id = R.string.他还没有设置个性签名)
                                    } else {
                                        stringResource(id = R.string.她还没有设置个性签名)
                                    }
                                )
                            }
                        }
                    }
                }

                is UserInfoItem.Tribe -> {
                    val tribe = item.tribe
                    val lastNonNullTribe = keepLastNonNullState(newState = tribe)
                    AnimatedVisibility(visible = tribe != null) {
                        InfoCardScaffold(
                            title = if (user.isSelf) {
                                stringResource(id = R.string.mine_tribe)
                            } else if (user.isBoy) {
                                stringResource(id = R.string.his_tribe)
                            } else {
                                stringResource(id = R.string.her_tribe)
                            },
                        ) {
                            if (lastNonNullTribe != null) {
                                TribeItem(lastNonNullTribe)
                            } else {
                                EmptyItem(
                                    text = if (user.isBoy) {
                                        stringResource(id = R.string.他还没有部落)
                                    } else {
                                        stringResource(id = R.string.她还没有部落)
                                    }
                                )
                            }
                        }
                    }
                }
            }
        }
    }
}


@Preview(showBackground = true, uiMode = Configuration.UI_MODE_NIGHT_YES)
@Composable
private fun PreviewUserInfoPageV2() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(800.dp)
            .background(Color(0xFF181029))
    ) {
        UserInfoPageV2(userForPreview, userForPreview.run {
            buildList {
                add(UserInfoItem.Moment(emptyList()))
                add(UserInfoItem.AttractiveLabel(attractiveFlags))
                add(UserInfoItem.Signature(shortIntro))
                add(UserInfoItem.Tribe(tribe))
            }.sortedDescending()
        })
    }
}

@Composable
private fun InfoCardScaffold(
    title: String,
    showEdit: Boolean = false,
    showMore: Boolean = false,
    minHeight: Dp = Dp.Unspecified,
    clickEdit: OnClick = {},
    clickMore: OnClick = {},
    content: @Composable ColumnScope.() -> Unit,
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .heightIn(min = minHeight)
            .background(Color(0xFF12151D))
            .padding(vertical = 16.dp)
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                color = Color.White,
                fontSize = 14.sp,
                lineHeight = 14.sp,
                fontWeight = FontWeight.Medium
            )

            if (showEdit) {
                IconButton(
                    onClick = clickEdit,
                    modifier = Modifier
                        .padding(start = 5.dp)
                        .size(18.dp),
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_edit_info),
                        contentDescription = null,
                        tint = colorResource(id = R.color.white_alpha_50)
                    )
                }
            }

            Spacer(modifier = Modifier.weight(1f))

            if (showMore) {
                Row(
                    modifier = Modifier.noEffectClickable(onClick = clickMore),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = stringResource(id = R.string.更多),
                        color = colorResource(id = R.color.white_alpha_30),
                        fontSize = 12.sp
                    )
                    Icon(
                        painter = painterResource(id = R.drawable.ic_arrow_end_v2),
                        contentDescription = null,
                        modifier = Modifier.size(12.dp),
                        tint = colorResource(id = R.color.white_alpha_30)
                    )
                }
            }
        }

        content()
    }
}

@Composable
private fun AddItem(text: String, onClick: OnClick) {
    Row(
        modifier = Modifier
            .padding(start = 16.dp, top = 20.dp)
            .height(32.dp)
            .background(Color(0xFF25282F), CircleShape)
            .noEffectClickable(onClick = onClick)
            .padding(horizontal = 12.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Icon(
            painter = painterResource(id = R.drawable.ic_add_photo),
            contentDescription = null,
            modifier = Modifier.size(12.dp),
            tint = Color.White
        )
        Text(
            text = text,
            color = Color.White,
            fontSize = 14.sp
        )
    }
}

@Composable
private fun EmptyItem(text: String) {
    TextItem(text, colorResource(id = R.color.white_alpha_50))
}

@Composable
private fun TextItem(text: String, color: Color = Color.White) {
    Text(
        text = text,
        modifier = Modifier.padding(start = 16.dp, top = 20.dp),
        color = color,
        fontSize = 14.sp
    )
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
private fun AttractiveLabelItem(list: List<AttractiveFlag>) {
    FlowRow(
        modifier = Modifier.padding(start = 16.dp, top = 14.dp, end = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(8.dp),
        verticalArrangement = Arrangement.spacedBy(12.dp),
    ) {
        list.forEach {
            Row(
                modifier = Modifier
                    .height(28.dp)
                    .background(Color(0xFF25282F), RoundedCornerShape(4.dp))
                    .padding(horizontal = 6.dp),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(2.dp)
            ) {
                if (it.url.isNullOrEmpty().not()) {
                    ComposeImage(model = it.url, modifier = Modifier.size(12.dp))
                }
                Text(
                    text = it.label,
                    color = Color.White,
                    fontSize = 12.sp
                )
            }
        }
    }
}

@Composable
private fun MomentItem(images: List<MediaInfo>, onClick: OnClick) {
    LazyRow(
        modifier = Modifier.fillMaxWidth(),
        contentPadding = PaddingValues(top = 16.dp, start = 16.dp, end = 16.dp),
        horizontalArrangement = Arrangement.spacedBy(4.dp)
    ) {
        items(images) {
            ComposeImage(
                model = it.url,
                modifier = Modifier
                    .size(80.dp)
                    .clip(Shapes.corner12)
                    .clickable(onClick = onClick)
            )
        }
    }
}

@Composable
private fun TribeItem(tribe: Tribe) {
    val context = LocalContext.current
    Row(
        modifier = Modifier
            .padding(start = 16.dp, top = 8.dp, end = 16.dp)
            .fillMaxWidth()
            .background(Color(0xFF25282F), Shapes.corner12)
            .clickable {
                (context as? BaseActivity)?.launchTribeActivity(tribe.id.toString())
            }
            .padding(8.dp),
        verticalAlignment = Alignment.CenterVertically,
    ) {
        ComposeImage(
            model = tribe.avatarUrl.orEmpty(),
            modifier = Modifier
                .size(64.dp)
                .clip(RoundedCornerShape(6.dp))
        )

        Spacer(modifier = Modifier.width(8.dp))

        Column(
            modifier = Modifier.weight(1f),
            verticalArrangement = Arrangement.spacedBy(10.dp)
        ) {
            Text(
                text = tribe.name.orEmpty(),
                color = Color.White,
                fontSize = 14.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 1,
                overflow = TextOverflow.Ellipsis,
            )

            Text(
                text = stringResource(R.string.几人, tribe.memberCnt.orDefault(0)),
                color = colorResource(id = R.color.white_alpha_50),
                fontSize = 12.sp
            )
        }

        Icon(
            painter = painterResource(id = R.drawable.ic_arrow_end_v2),
            contentDescription = null,
            modifier = Modifier.size(12.dp),
            tint = colorResource(id = R.color.white_alpha_30)
        )
    }
}