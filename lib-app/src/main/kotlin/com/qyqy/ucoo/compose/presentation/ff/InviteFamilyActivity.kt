package com.qyqy.ucoo.compose.presentation.ff

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.core.view.WindowCompat
import com.qyqy.ucoo.base.BaseActivity

class InviteFamilyActivity : BaseActivity() {

    companion object {
        const val RELATION_TAG_ID = "relation_tag_id"
        fun newIntent(context: Context, tagId: Int) = Intent(context, InviteFamilyActivity::class.java).apply {
            putExtra(RELATION_TAG_ID, tagId)
        }

    }

    override val immersiveMode: Boolean = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        WindowCompat.setDecorFitsSystemWindows(window, true)
        val tagId = intent.getIntExtra(RELATION_TAG_ID, 1)
        setContent { InviteFamilyPage(tagId, onBack = { onBackPressedDispatcher.onBackPressed() }) }
    }
}