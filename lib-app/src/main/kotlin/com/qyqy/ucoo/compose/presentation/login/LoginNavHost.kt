package com.qyqy.ucoo.compose.presentation.login

import com.qyqy.ucoo.LocalUserPartition
import androidx.compose.animation.slideInHorizontally
import androidx.compose.animation.slideOutHorizontally
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import com.qyqy.cupid.ui.login.CupidAccountBaseInfoScreen
import com.qyqy.cupid.ui.login.CupidStartLoginScreen
import com.qyqy.cupid.ui.login.CupidUploadAccountAvatarScreen
import com.qyqy.ucoo.ABTest
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.DataTrace
import com.qyqy.ucoo.ReportExposureCompose
import com.qyqy.ucoo.compose.autoCloseKeyboard
import com.qyqy.ucoo.compose.presentation.profile.AttractionTagWidget
import com.qyqy.ucoo.compose.router.IDestination
import com.qyqy.ucoo.compose.router.navigateTo
import com.qyqy.ucoo.compose.vm.login.LoginViewModel
import com.qyqy.ucoo.core.Analytics


sealed class LoginDestination : IDestination {

    /**
     * 登录起始页，可以选择不同登录方式
     */
    object Start : LoginDestination() {

        override val route = "login-start"
    }

    /**
     * 问题反馈
     */
    object Feedback : LoginDestination() {

        override val route = "login-feed-back"
    }

    /**
     * 手机号登录-输入页面
     */
    object PhoneInput : LoginDestination() {

        override val route = "login-phone-input"

        override val popUpRoute: String = Start.route
    }

    /**
     * 手机号登录-验证页面
     */
    object PhoneVerify : LoginDestination() {

        override val route = "login-phone-verify"
    }

    /**
     * 用户信息-基本信息
     */
    object UserInfoBase : LoginDestination() {

        override val route = "login-register-info-base"

        override val popUpRoute: String = Start.route
    }

    /**
     * 用户信息-其他信息
     */
    object UserInfoOther : LoginDestination() {

        override val route = "login-register-info-other"

    }

    /**
     * 手机号登录-上传头像
     */
    object UserInfoAvatar : LoginDestination() {

        override val route = "login-register-info-avatar"
    }

    /**
     * 引力签填写
     */
    object UserAttraction : LoginDestination() {

        override val route = "login-user-attraction"
    }
}

sealed class CupidLoginDestination : IDestination {

    /**
     * 登录起始页，可以选择不同登录方式
     */
    object Start : LoginDestination() {

        override val route = "cupid-login-start"
    }

//    /**
//     * 手机号登录-输入页面
//     */
//    object PhoneInput : LoginDestination() {
//
//        override val route = "cupid-login-phone-input"
//
//        override val popUpRoute: String = Start.route
//    }
//
//    /**
//     * 手机号登录-验证页面
//     */
//    object PhoneVerify : LoginDestination() {
//
//        override val route = "cupid-login-phone-verify"
//    }

    /**
     * 用户信息-基本信息
     */
    object UserInfoBase : LoginDestination() {

        override val route = "cupid-login-register-info-base"

        override val popUpRoute: String = Start.route
    }

    /**
     * 用户信息-其他信息
     */
    object UserInfoOther : LoginDestination() {

        override val route = "cupid-login-register-info-other"

    }

    /**
     * 手机号登录-上传头像
     */
    object UserInfoAvatar : LoginDestination() {

        override val route = "cupid-login-register-info-avatar"
    }
}

//object SingleAccount : RallyDestination {
//    // Added for simplicity, this icon will not in fact be used, as SingleAccount isn't
//    // part of the RallyTabRow selection
//    override val icon = Icons.Filled.Money
//    override val route = "single_account"
//    const val accountTypeArg = "account_type"
//    val routeWithArgs = "$route/{$accountTypeArg}"
//    val arguments = listOf(
//        navArgument(accountTypeArg) { type = NavType.StringType }
//    )
//    val deepLinks = listOf(
//        navDeepLink { uriPattern = "rally://$route/{$accountTypeArg}" }
//    )
//}

@Composable
fun LoginNavHost(
    loginActivity: LoginActivity,
    viewModel: LoginViewModel,
    navController: NavHostController,
    modifier: Modifier = Modifier,
) {
    val configs = viewModel.configFlow.collectAsStateWithLifecycle().value
    val isCupid = LocalUserPartition.current.isCupid

    val startDestination = if (isCupid) CupidLoginDestination.Start.route else LoginDestination.Start.route

    NavHost(
        navController = navController,
        startDestination = startDestination,
        modifier = modifier.autoCloseKeyboard(),
        enterTransition = {
            slideInHorizontally(initialOffsetX = { it })
        },
        exitTransition = {
            slideOutHorizontally(targetOffsetX = { -it })
        },
        popEnterTransition = {
            slideInHorizontally(initialOffsetX = { -it })
        },
        popExitTransition = {
            slideOutHorizontally(targetOffsetX = { it })
        },
    ) {
        composable(route = LoginDestination.Start.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.OVERVIEW, ABTest.Device.LoginUI))
            }) {
                StartLoginScreen(configs, onFeedback = {
                    navController.navigateTo(LoginDestination.Feedback)
                }) {
                    viewModel.startLogin(loginActivity, it)
                }
            }
        }

        composable(route = LoginDestination.Feedback.route) {
            FeedbackPage(configs.questionFeedback.orEmpty()) {
                navController.popBackStack()
            }
        }

        composable(route = LoginDestination.PhoneInput.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.PHONE_INPUT, ABTest.Device.LoginUI))
            }) {
                PhoneInputLoginScreen(
                    phoneInfo = viewModel.phoneInfoState,
                    onConfirm = viewModel::sendPhoneVerifyCode,
                    onBack = navController::popBackStack
                )
            }
        }
        composable(route = LoginDestination.PhoneVerify.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.PHONE_VERIFY, ABTest.Device.LoginUI))
            }) {
                PhoneVerifyLoginScreen(
                    phoneInfo = viewModel.phoneInfoState,
                    onConfirm = viewModel::checkPhoneVerifyCode,
                    onResend = viewModel::resendPhoneVerifyCode,
                    onStartTimer = viewModel::tryStartResendTimer,
                    onPopupToStart = {
                        navController.popBackStack(LoginDestination.Start.route, inclusive = false, saveState = false)
                    },
                    onBack = navController::popBackStack,
                )
            }
        }
        composable(route = LoginDestination.UserInfoBase.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.EDIT_NAME, ABTest.Device.LoginUI))
            }) {
                AccountBaseInfoScreen(
                    registerInfo = viewModel.registerInfoState,
                    onAutoFillNickname = viewModel::randomNickname,
                    onConfirm = viewModel::stagingAccountInfo,
                    onBack = navController::popBackStack
                )
            }
        }
//        composable(route = LoginDestination.UserInfoOther.route) {
//            ReportExposureCompose(onExposureStart = {
//                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.EDIT_STATUS, ABTest.Device.LoginUI))
//            }) {
//                AccountOtherInfoScreen(
//                    registerInfo = viewModel.registerInfoState,
//                    onConfirm = viewModel::saveAccountInfo,
//                    onBack = navController::popBackStack,
//                )
//            }
//        }
        composable(route = LoginDestination.UserInfoAvatar.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.EDIT_AVATAR, ABTest.Device.LoginUI))
            }) {
                UploadAccountAvatarScreen(
                    registerInfo = viewModel.registerInfoState,
                    onConfirm = viewModel::stagingAvatarInfo,
                    onBack = navController::popBackStack,
                )
            }
        }

        composable(route = LoginDestination.UserAttraction.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.EDIT_ATTRACTION, ABTest.Device.LoginUI))
            }) {
                AttractionTagWidget(
                    registerInfo = viewModel.registerInfoState,
                    onConfirm = viewModel::stagingAttractiveInfo,
                    onBack = navController::popBackStack,
                )
            }
        }

        composable(route = CupidLoginDestination.Start.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.OVERVIEW, ABTest.Device.LoginUI))
            }) {
                CupidStartLoginScreen(configs) {
                    viewModel.startLogin(loginActivity, it)
                }
            }
        }
//        composable(route = CupidLoginDestination.PhoneInput.route) {
//            ReportExposureCompose(onExposureStart = {
//                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.PHONE_INPUT, ABTest.Device.LoginUI))
//            }) {
//                CupidPhoneInputLoginScreen(
//                    phoneInfo = viewModel.phoneInfoState,
//                    onConfirm = viewModel::sendPhoneVerifyCode,
//                    onBack = navController::popBackStack
//                )
//            }
//        }
//        composable(route = CupidLoginDestination.PhoneVerify.route) {
//            ReportExposureCompose(onExposureStart = {
//                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.PHONE_VERIFY, ABTest.Device.LoginUI))
//            }) {
//                CupidPhoneVerifyLoginScreen(
//                    phoneInfo = viewModel.phoneInfoState,
//                    onConfirm = viewModel::checkPhoneVerifyCode,
//                    onResend = viewModel::resendPhoneVerifyCode,
//                    onStartTimer = viewModel::tryStartResendTimer,
//                    onPopupToStart = {
//                        navController.popBackStack(LoginDestination.Start.route, inclusive = false, saveState = false)
//                    },
//                    onBack = navController::popBackStack,
//                )
//            }
//        }
        composable(route = CupidLoginDestination.UserInfoBase.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.EDIT_NAME, ABTest.Device.LoginUI))
            }) {
                CupidAccountBaseInfoScreen(
                    registerInfo = viewModel.registerInfoState,
                    onAutoFillNickname = viewModel::randomNickname,
                    onConfirm = viewModel::stagingAccountInfo,
                    onBack = navController::popBackStack
                )
            }
        }
//        composable(route = CupidLoginDestination.UserInfoOther.route) {
//            ReportExposureCompose(onExposureStart = {
//                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.EDIT_STATUS, ABTest.Device.LoginUI))
//            }) {
//                CupidAccountOtherInfoScreen(
//                    registerInfo = viewModel.registerInfoState,
//                    onConfirm = viewModel::saveAccountInfo,
//                    onBack = navController::popBackStack,
//                )
//            }
//        }
        composable(route = CupidLoginDestination.UserInfoAvatar.route) {
            ReportExposureCompose(onExposureStart = {
                Analytics.appReportEvent(DataPoint.exposureBody(DataTrace.Exposure.Login.EDIT_AVATAR, ABTest.Device.LoginUI))
            }) {
                CupidUploadAccountAvatarScreen(
                    registerInfo = viewModel.registerInfoState,
                    onConfirm = viewModel::registerUser,
                    onBack = navController::popBackStack,
                )
            }
        }
    }
}
