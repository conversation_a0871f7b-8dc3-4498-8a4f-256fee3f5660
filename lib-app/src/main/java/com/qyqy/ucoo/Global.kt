package com.qyqy.ucoo

import android.app.Activity
import android.content.Intent
import android.os.Build
import android.os.Process
import android.provider.Settings
import android.widget.Toast
import androidx.activity.OnBackPressedCallback
import androidx.activity.addCallback
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.adjust.sdk.Adjust
import com.google.firebase.analytics.ktx.analytics
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.ishumei.smantifraud.SmAntiFraud
import com.overseas.common.ext.BaseKVData
import com.overseas.common.ext.KVData
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.account.AccountToken
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isHighQuality
import com.qyqy.ucoo.compose.presentation.ff.userForPreview
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.login.LoginEvent
import com.qyqy.ucoo.login.LoginReason
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.ToastUtils
import com.qyqy.ucoo.utils.VoiceRoomHelper
import com.qyqy.ucoo.utils.taskflow.ActiveActivityTaskScheduler
import io.fastkv.FastKV
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString
import kotlin.coroutines.EmptyCoroutineContext

private val filesDir: String by lazy(LazyThreadSafetyMode.NONE) {
    app.filesDir.absolutePath
}

private val fastKVDir: String by lazy(LazyThreadSafetyMode.NONE) {
    "${filesDir}/fastkv"
}

/**
 * token不为null既是有效, 既是登录
 */
val sAccountTokenFlow: StateFlow<AccountToken?>
    get() = app.accountManager.tokenFlow

val sAccountToken: AccountToken?
    get() = sAccountTokenFlow.value

val sIsLoginFlow: StateFlow<Boolean>
    get() = app.accountManager.isLoginFlow

//val sRefreshToken: String?
//    get() = sAccountToken?.refreshToken
//
//val sRcToken: String?
//    get() = sAccountToken?.rcToken

val sUserFlow: StateFlow<User>
    get() = app.accountManager.userFlow

val sUser: User
    get() = if (isPreviewOnCompose) {
        userForPreview
    } else {
        sUserFlow.value
    }

val self: User
    get() = sUser

val selfIsVip: Boolean
    get() = sUser.isVip

val User.isNormalUser: Boolean
    get() = isHighQuality.not() && isVideoHqu.not()

fun isMyCp(userId: String): Boolean {
    return sUser.cp?.id == userId
}

fun isBestMatchUser(userId: String): Boolean {
    return sUser.bestMatchUser?.id == userId
}

fun isMyCp(user: User): Boolean {
    return sUser.cp?.id == user.id
}

fun User.isSelf() = id == sUser.id

// release buildType
// 编译为Release版本
val isRelease: Boolean by lazy {
    BuildConfig.BUILD_TYPE.equals("release", true)
}

// 是否是线上环境
val isProd: Boolean
    get() = isRelease || UCOOEnvironment.currentEnv() == UCOOEnvironmentInstance.RELEASE

val isPlayChannel: Boolean by lazy(LazyThreadSafetyMode.NONE) {
    BuildConfig.FLAVOR_CHANNEL == "play"
}

val sLoginEventFlow: SharedFlow<LoginEvent>
    get() = app.accountManager.loginEventFlow

val sIsSignIn: Boolean
    get() = app.accountManager.isLogin

val sAppKV: FastKV by lazy {
    FastKV.Builder(fastKVDir, "Global").build()
}

@Volatile
private var _sUserKV: Pair<String, FastKV>? = null

val sUserKV: FastKV
    get() {
        val name = sUser.id
        return _sUserKV?.takeIf {
            it.first == name
        }?.second ?: FastKV.Builder(fastKVDir, name).build().also {
            _sUserKV = name to it
        }
    }

val appCoroutineScope = CoroutineScope(
    SupervisorJob() + Dispatchers.Main + if (isProd && isRelease) {
    CoroutineExceptionHandler { _, throwable ->
        Firebase.crashlytics.recordException(throwable)
    }
} else {
    EmptyCoroutineContext
})

val invalidCoroutineScope = CoroutineScope(
    SupervisorJob() + Dispatchers.Main + if (isProd && isRelease) {
    CoroutineExceptionHandler { _, throwable ->
        Firebase.crashlytics.recordException(throwable)
    }
} else {
    EmptyCoroutineContext
}).apply {
    cancel()
}


inline fun <reified T> FastKV.getCacheEntity(key: String = "fk_${T::class.java.simpleName.lowercase()}"): T? {
    return getString(key)?.let { sAppJson.decodeFromString<T>(it) }
}

data class FastEntitySaver<T : Any>(@Serializable val entity: T) {
    val defaultKey = "fk_${entity::class.java.simpleName.lowercase()}"
}

inline fun <reified T : Any> cacheEntityWithFastKV(store: FastKV, key: String, value: T) {
    store.putString(key, sAppJson.encodeToString<T>(value))
}

context(FastKV)
inline fun <reified T : Any> FastEntitySaver<T>.save(key: String = this.defaultKey) {
    cacheEntityWithFastKV(this@FastKV, key, this.entity)
}

val loginCoroutineScope: CoroutineScope
    get() = app.accountManager.obtainLoginCoroutineScope()

val appActivityTaskScheduler by lazy {
    ActiveActivityTaskScheduler("ActiveActivityTaskScheduler")
}

val InValidCoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate + EmptyCoroutineContext).also {
    it.cancel()
}

fun registerGlobalLoginEvent() {
    appCoroutineScope.launch {
        sLoginEventFlow.collect {
            if (it is LoginEvent.Logout) {
                VoiceRoomHelper.leaveCurrentRoom()
                when (it.reason) {
                    LoginReason.Expired -> {
                        if (AppUserPartition.isUCOO) {
                            toast(app.getString(R.string.please_log_in_again))
                        } else {
                            toast(app.getString(R.string.cpd_please_log_in_again))
                        }
                    }

                    LoginReason.Kick -> {
                        if (AppUserPartition.isUCOO) {
                            toast(app.getString(R.string.account_yi_jing_deng_lu))
                        } else {
                            toast(app.getString(R.string.cpd_account_yi_jing_deng_lu))
                        }
                    }

                    else -> {}
                }
            }
        }
    }
}

fun obtainKV(name: String): FastKV {
    return FastKV.Builder(fastKVDir, name).build()
}

fun obtainKVData(name: String): KVData {
    return BaseKVData(fastKVDir, name)
}

fun obtainKVData(name: String, provide: (String, String) -> KVData): KVData {
    return provide(fastKVDir, name)
}

fun Throwable.toast() {
    toast(message)
}

fun toast(msg: String?, duration: Int = Toast.LENGTH_SHORT) {
    try {
        if (duration == Toast.LENGTH_SHORT) {
            ToastUtils.showShort(msg)
        } else {
            ToastUtils.showLong(msg)
        }
    } catch (e: Exception) {
        e.printStackTrace()
    }
}

fun toastRes(@StringRes resId: Int, vararg formatArgs: Any, duration: Int = Toast.LENGTH_SHORT) {
    try {
        if (duration == Toast.LENGTH_SHORT) {
            ToastUtils.showShort(resId, *formatArgs)
        } else {
            ToastUtils.showLong(resId, *formatArgs)
        }
    } catch (e: Exception) {
        e.printStackTrace()
        toast(app.getString(resId, *formatArgs), duration)
    }
}

fun anyStartActivity(block: Activity.() -> Intent) {
    ActivityLifecycle.startTopActivity?.also {
        it.startActivity(block.invoke(it))
    }
}


object AppInfo {

    private const val KEY_ANDROID_ID = "key_android_id"

    const val OS_PLATFORM = "android"

    val deviceId: String by lazy {
        (sAppKV.getString(KEY_ANDROID_ID).takeIf {
            it.isNotBlank()
        } ?: try {
            Settings.Secure.getString(app.contentResolver, Settings.Secure.ANDROID_ID)
        } catch (e: Exception) {
            ""
        }.orEmpty().also {
            sAppKV.putString(KEY_ANDROID_ID, it)
        })
    }

    val deviceName: String by lazy {
        "${Build.BRAND};${Build.MODEL};${Build.DEVICE}"
    }

    val deviceOs: String by lazy {
        "android ${Build.VERSION.RELEASE}"
    }

    val apiHost: String
        get() = BuildConfig.FAST_API_HOST

    private var _adid: String = ""

    fun bindAdjustID(adid: String) {
        if (adid.isNotEmpty()) {
            Firebase.analytics.setUserProperty("adid", adid)
            _adid = adid
        }
    }

    val adid: String
        get() {
            val ret = if (_adid.isNotEmpty()) {
                _adid
            } else {
                synchronized(this) {
                    _adid.ifEmpty {
                        Adjust.getAdid().orEmpty().also {
                            bindAdjustID(it)
                        }
                    }
                }
            }
            return ret
        }


    var smBoxId: String? = null
        private set

    fun initSmAntiFraud() {
        val option: SmAntiFraud.SmOption = SmAntiFraud.SmOption()
        option.setOrganization(BuildConfig.SM_ORGANIZATION)
        option.setAppId(BuildConfig.SM_APP_ID)
        option.setPublicKey(BuildConfig.SM_PUBLISH_KEY)
        option.setArea(SmAntiFraud.AREA_XJP)
        // 此方法必须在 create 方法之前调用，否则可能会出现不触发回调问题
        SmAntiFraud.registerServerIdCallback(object : SmAntiFraud.IServerSmidCallback {
            override fun onSuccess(boxId: String) {
                // 服务器下发成功或缓存中有可用 boxId
                // 如果缓存中存在 boxId，此方法会触发 2 次，第 2 次会更新缓存中的 boxId
                LogUtils.i("SmAntiFraud", "onSuccess $boxId")
                smBoxId = boxId
            }

            override fun onError(errCode: Int) {
                appCoroutineScope.launch(Dispatchers.IO) {
                    smBoxId = SmAntiFraud.getDeviceId()
                }
                // -1：无网络，常见原因：设备无网络
                // -2：网络异常，网络连接异常（conn.getResponseCode() 抛出异常）或者 http 状态非 200，常见原因：代理或私有化服务器配置错误
                // -3：业务异常，下发业务状态码非 1100，服务器未返回 deviceId，常见原因：参数配置错误、qps 超限、服务器异常
                LogUtils.w("SmAntiFraud", "onError $errCode")
            }
        })
        // 初始化
        val isOk = SmAntiFraud.create(app, option)

        LogUtils.i("SmAntiFraud", "sm init $isOk")
    }
}

fun FragmentActivity.backHome(
    owner: LifecycleOwner? = this,
    onFailBlock: OnBackPressedCallback.() -> Unit = {
        onBackPressedDispatcher.onBackPressed()
    },
) {
    onBackPressedDispatcher.addCallback(owner) {
        isEnabled = false
        try {
            if (onBackPressedDispatcher.hasEnabledCallbacks()) {
                onBackPressedDispatcher.onBackPressed()
            } else {
                try {
                    val homeIntent = Intent(Intent.ACTION_MAIN)
                    homeIntent.addCategory(Intent.CATEGORY_HOME)
                    homeIntent.flags = Intent.FLAG_ACTIVITY_NEW_TASK
                    startActivity(homeIntent)
                } catch (e: Exception) {
                    onFailBlock()
                }
            }
        } finally {
            isEnabled = true
        }
    }
}

fun Fragment.backHome(
    owner: LifecycleOwner? = this,
    onFailBlock: OnBackPressedCallback.() -> Unit = {
        activity?.onBackPressedDispatcher?.onBackPressed()
    },
) {
    activity?.backHome(owner, onFailBlock)
}

fun restartApp() {
    app.apply {
        packageManager.getLaunchIntentForPackage(packageName)?.component?.let {
            Intent.makeRestartActivityTask(it)
        }?.also {
            startActivity(it)
        }
        Process.killProcess(Process.myPid())
    }
}

//private const val H5_USER_INCOME_PROD = "https://api.ucoofun.com/wallet/user/income"
//
//private const val H5_USER_INCOME_DEV = "https://api.test.ucoofun.com/wallet/user/income"

//val h5UserIncomeUrl: String
//    get() = if (isProd) H5_USER_INCOME_PROD else H5_USER_INCOME_DEV
val h5UserIncomeUrl: String
    get() = "${BuildConfig.API_HOST}/wallet/user/income"