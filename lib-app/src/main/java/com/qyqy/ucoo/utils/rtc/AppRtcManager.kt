package com.qyqy.ucoo.utils.rtc

import android.util.ArrayMap
import android.util.Log
import android.view.View
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.overseas.common.utils.isPreviewOnCompose
import com.overseas.common.utils.postToMainThread
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.bean.RtcToken
import com.qyqy.ucoo.im.room.game.AudioPCMData
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.utils.LogUtils
import com.tencent.trtc.TRTCCloud
import kotlinx.coroutines.CancellationException
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.jsonPrimitive
import retrofit2.http.Body
import retrofit2.http.POST
import java.lang.ref.WeakReference
import kotlin.coroutines.EmptyCoroutineContext


private const val TAG = "app_rtc_manager"

private var lastRtc: WeakReference<AppRtcManager>? = null

interface RtcApi {

    @POST("api/privatechat/v1/videochat/token")
    suspend fun getRtcToken(@Body body: Map<String, String>): ApiResponse<JsonObject>

}

fun interface IRtcTokenProvider {
    suspend fun getRtcToken(tag: String): RtcToken?
}

object VideoRtcTokenProviderImpl : IRtcTokenProvider {

    private val api = createApi<RtcApi>()
    override suspend fun getRtcToken(tag: String): RtcToken? {
        return runApiCatching {
            api.getRtcToken(mapOf("conversation_id" to tag))
        }.getOrNull()?.let {
            RtcToken(
                it["room_id"]?.jsonPrimitive?.content.orEmpty(),
                it["rtc_token"]?.jsonPrimitive?.content.orEmpty()
            )
        }
    }
}

class AppRtcManager constructor(
    private val tag: String,
    private val rtcProvider: IRtcTokenProvider,
    private val disableOptimization: Boolean = true,
    private val factory: IRtcEngineFactory,
    private val autoPCMCapture: Boolean = true,
    private val reportVad: Boolean = false,
) : IRtcEventHandle {

    constructor() : this("", IRtcTokenProvider { null }, false, IRtcEngineFactory { error("") }, false) {
        isDestroy = true
    }

    init {
        if (!isPreviewOnCompose) {
            lastRtc?.get()?.levelChannel()
            lastRtc?.clear()
            lastRtc = WeakReference(this)
        }
    }

    @Volatile
    private var isDestroy = false

    private val state: State = State()

    private val rtcCoroutineScope = CoroutineScope(
        SupervisorJob() + Dispatchers.Main.immediate + if (isProd && isRelease) {
            CoroutineExceptionHandler { _, throwable ->
                Firebase.crashlytics.recordException(throwable)
            }
        } else {
            EmptyCoroutineContext
        })

    private val micList = ArrayMap<String, Mic>()

    private val unSubscribeAudioSet = mutableSetOf<String>()

    private var micUserListFromServer: Set<String>? = null

    private var remoteStreamWhiteList: Set<String>? = null

    private var listener: IRtcUpdateListener? = null

    private var channelName: String = ""

    private val rtcEngine: IRtcEngine by lazy(LazyThreadSafetyMode.NONE) {
        try {
            factory.create(this)
        } catch (e: Exception) {
            isDestroy = true
            listener = null
            micUserListFromServer = null
            remoteStreamWhiteList = null
            destroyRtcEngine()
            rtcCoroutineScope.cancel()
            error("rtc engin is null $e")
        }
    }

    var enableGameAsr = false

    fun joinChannel(token: RtcToken?, upMic: Boolean = false, mute: Boolean = false, speaker: Boolean = true) {
        if (isDestroy) {
            return
        }
        LogUtils.i(TAG, "调用加入频道: $state")
        rtcCoroutineScope.launch {
            state.apply {
                joinState = TState(false)
                upMicState = TState(upMic)
                muteState = TState(mute)
                speakerState = TState(speaker)
            }
            rtcJoinChannel(token)
        }
    }

    fun levelChannel() {
        if (isDestroy) {
            return
        }
        LogUtils.i(TAG, "调用离开频道")
        isDestroy = true
        rtcCoroutineScope.launch {
            rtcEngine.stopPCMCapture()
            rtcEngine.levelChannel()
            listener = null
            micUserListFromServer = null
            remoteStreamWhiteList = null
            destroyRtcEngine()
            rtcCoroutineScope.cancel()
        }
        appCoroutineScope.launch {
            AppAsrManager.stop()
        }
    }

    fun upMic(rtcToken: RtcToken?) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            val oldRole = state.upMicState.value
            if (oldRole) {
                return@launch
            }
            LogUtils.i(TAG, "调用上麦，oldRole: $oldRole")
            state.upMicState = TState(true, Status.InProgress)
            state.muteState = TState(false, Status.InProgress)
            val token = requestRtcToken(rtcToken)
            rtcEngine.upMic(token.channelName, token.rtcToken)
        }
    }

    fun downMic(rtcToken: RtcToken?) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            val oldRole = state.upMicState.value
            if (oldRole.not()) {
                return@launch
            }
            LogUtils.i(TAG, "调用下麦，oldRole: $oldRole")
            state.upMicState = TState(false, Status.InProgress)
            if (rtcToken == null) {
                rtcEngine.downMic("", null)
                requestRefreshRtcToken()
            } else {
                val token = requestRtcToken(rtcToken)
                rtcEngine.downMic(token.channelName, token.rtcToken)
            }
        }
    }

    fun setEnableMuteAudio(muted: Boolean) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "调用麦克风状态：${muted}")
            val oldEnable = rtcEngine.isLocalAudioMuted()
            state.muteState = TState(muted, Status.InProgress)
            rtcEngine.muteLocalAudioStream(muted).let { ret ->
                if (ret) {
                    state.muteState = TState(muted, Status.Done)
                } else {
                    LogUtils.e(TAG, "麦克风切换失败, expect: $muted  actual: $oldEnable")
                    state.muteState = TState(oldEnable, Status.Done)
                }
                if (state.muteState.value) {
                    stopPCMCapture()
                } else if (autoPCMCapture) {
                    startPCMCapture()
                }
                val uid = sUser.id
                micList[uid]?.also {
                    if (it.mute != state.muteState.value) {
                        micList[uid] = it.copy(mute = state.muteState.value)
                        dispatchMicStatus()
                    }
                }
            }
        }
    }

    private fun startPCMCapture() {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            rtcEngine.startPCMCapture()
            AppAsrManager.start()
        }
    }

    private fun stopPCMCapture() {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            rtcEngine.stopPCMCapture()
            AppAsrManager.stop()
        }
    }

    fun startSubscribingStream() {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            rtcEngine.startSubscribingStream()
        }
    }

    fun stopSubscribingStream() {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            rtcEngine.stopSubscribingStream()
        }
    }


    fun startPlayingStream(uid: String, view: View?) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "开始拉流：uid: $uid, view: $view")
            rtcEngine.startPlayingStream(uid, view)
        }
    }

    fun stopPlayingStream(uid: String) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "停止拉流：uid: $uid")
            rtcEngine.stopPlayingStream(uid)
        }
    }

    fun startVideoPreview(view: View) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "开始视频预览")
            rtcEngine.startVideoPreview(view)
        }
    }

    fun stopVideoPreview() {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "停止视频预览")
            rtcEngine.stopVideoPreview()
        }
    }

    fun mutePublishStreamVideo(muted: Boolean) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "停止视频推流")
            rtcEngine.mutePublishStreamVideo(muted)
        }
    }

    fun enableCamera(enabled: Boolean) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "相机开关切换：$enabled")
            rtcEngine.enableCamera(enabled)
        }
    }

    fun useFrontCamera(front: Boolean) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "前后相机切换：$front")
            rtcEngine.useFrontCamera(front)
        }
    }

    fun setEnableSpeakerphone(enabled: Boolean) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "调用切换扬声器状态：${enabled}")
            state.speakerState = TState(enabled, Status.InProgress)
            if (!state.joinState.value || state.joinState.status != Status.Done) {
                return@launch
            }
            LogUtils.i(TAG, "切换扬声器状态：${enabled}")
            rtcEngine.setEnableSpeakerphone(enabled).let { ret ->
                val enable = rtcEngine.isSpeakerphoneEnabled()
                if (!ret || enable != state.speakerState.value) {
                    LogUtils.e(TAG, "扬声器切换失败：$ret, expect: ${state.speakerState.value}  actual: $enable")
                }
                state.speakerState = TState(enable, Status.Done)
            }
        }
    }

    fun getMicById(userId: String): Mic? {
        if (isDestroy) {
            return null
        }
        return micList.get(userId)
    }

    fun setRtcUpdateListener(listener: IRtcUpdateListener?) {
        if (isDestroy) {
            return
        }
        this.listener = listener
    }

    fun updateRemoteAudioStream(userListFromServer: Set<String>) {
        if (isDestroy) {
            return
        }
        if (disableOptimization) {
            return
        }
        rtcCoroutineScope.launch {
            micUserListFromServer = userListFromServer
            if (!state.joinState.value) { // 不在频道内
                return@launch
            }
            for (id in userListFromServer) {
                if (id != sUser.id && unSubscribeAudioSet.contains(id)) { // 在未订阅音频流列表
                    unSubscribeAudioSet.remove(id)
                    rtcEngine.muteRemoteAudioStream(id, false)
                    LogUtils.i(TAG, "uid: $id, 订阅音频")
                }
            }

            micList.forEach { (id, _) ->
                if (id != sUser.id && !unSubscribeAudioSet.contains(id) && !userListFromServer.contains(id) && remoteStreamWhiteList?.contains(id) != true) { // 取消订阅非主播的音频流
                    unSubscribeAudioSet.add(id)
                    rtcEngine.muteRemoteAudioStream(id.toString(), true)
                    LogUtils.i(TAG, "uid: $id, 取消订阅音频")
                }
            }
        }
    }

    fun updateRemoteStreamWhiteList(pushStreamWhiteList: Set<String>) {
        if (isDestroy) {
            return
        }
        if (disableOptimization) {
            return
        }
        rtcCoroutineScope.launch {
            remoteStreamWhiteList = pushStreamWhiteList
            if (!state.joinState.value) { // 不在频道内
                return@launch
            }
            for (id in pushStreamWhiteList) {
                if (id != sUser.id && unSubscribeAudioSet.contains(id)) { // 在未订阅音频流列表
                    unSubscribeAudioSet.remove(id)
                    rtcEngine.muteRemoteAudioStream(id, false)
                    LogUtils.i(TAG, "uid: $id, 订阅音频")
                }
            }

            micList.forEach { (id, _) ->
                if (id != sUser.id && !unSubscribeAudioSet.contains(id) && !pushStreamWhiteList.contains(id) && micUserListFromServer?.contains(id) != true) { // 取消订阅非主播的音频流
                    unSubscribeAudioSet.add(id)
                    rtcEngine.muteRemoteAudioStream(id.toString(), true)
                    LogUtils.i(TAG, "uid: $id, 取消订阅音频")
                }
            }
        }
    }

    override fun onJoinChannelSuccess(channel: String, uid: String) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "加入频道成功: $uid")
            state.apply {
                if (!joinState.value) {
                    LogUtils.w(TAG, "rtc状态不正确, 加入频道状态不一致1, expect: true  actual: false")
                }
                if (Status.InProgress != joinState.status) {
                    LogUtils.w(TAG, "rtc状态不正确, 加入频道状态不一致2, expect: ${Status.InProgress}  actual: ${joinState.status}")
                }
                joinState = TState(true, Status.Done)
                muteState = muteState.copy(status = Status.Done)
                if (rtcEngine.isSpeakerphoneEnabled() != speakerState.value) {
                    LogUtils.i(TAG, "切换扬声器状态：${speakerState.value}")
                    val ret = rtcEngine.setEnableSpeakerphone(speakerState.value)
                    val enable = rtcEngine.isSpeakerphoneEnabled()
                    if (!ret || enable != speakerState.value) {
                        LogUtils.e(TAG, "扬声器切换失败：$ret, expect: ${speakerState.value}  actual: $enable")
                    }
                    speakerState = TState(enable, Status.Done)
                }
            }
        }
    }

    override fun onJoinChannelFailed() {
        // token 或 uid 不正确
        rtcCoroutineScope.launch {
            state.joinState = TState(false)
            delay(3000)
            LogUtils.i(TAG, "重新尝试加入频道: $state")
            rtcJoinChannel(null)
        }
    }

    override fun onUserJoined(uid: String, streamId: String) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "uid: $uid, streamId:$streamId 加入频道")
            micList.put(uid, Mic(0, false))
            if (disableOptimization) {
                return@launch
            }
            if (micUserListFromServer?.contains(uid) != true && remoteStreamWhiteList?.contains(uid) != true) { // 不在主播列表, 取消订阅音频流
                if (!unSubscribeAudioSet.contains(uid)) {
                    unSubscribeAudioSet.add(uid)
                    rtcEngine.muteRemoteAudioStream(streamId, true)
                    LogUtils.i(TAG, "uid: $uid, 取消订阅音频")
                }
            } else {
                if (unSubscribeAudioSet.contains(uid)) {
                    unSubscribeAudioSet.remove(uid)
                    rtcEngine.muteRemoteAudioStream(streamId, false)
                    LogUtils.i(TAG, "uid: $uid, 订阅音频")
                }
            }
        }
    }

    override fun onUserOffline(uid: String, streamId: String, reason: Int) {
        if (isDestroy) {
            return
        }
        rtcCoroutineScope.launch {
            LogUtils.i(TAG, "uid: $uid, 离开频道")
            micList.remove(uid)
            if (disableOptimization) {
                return@launch
            }
            if (unSubscribeAudioSet.contains(uid)) {
                unSubscribeAudioSet.remove(uid)
            }
        }
    }

    override fun onStreamQualityUpdate(
        uid: String,
        streamId: String,
        videoRecvFPS: Double,
        videoRenderFPS: Double,
        audioRecvFPS: Double,
        audioRenderFPS: Double,
        level: Int,
    ) {
        if (isDestroy) {
            return
        }
        listener?.onStreamQualityUpdate(uid, streamId, videoRecvFPS, videoRenderFPS, audioRecvFPS, audioRenderFPS, level)
    }

    override fun onRemoteAudioStateChanged(uid: String, streamId: String, muted: Boolean) {
        if (isDestroy) {
            return
        }
        val userId = if (uid == "0") sUser.id else uid
        rtcCoroutineScope.launch {
            micList[userId]?.also {
                if (it.mute != muted) {
                    LogUtils.i(TAG, "uid: $userId, ${if (muted) "关麦" else "开麦"}")
                    micList[userId] = it.copy(mute = muted)
                    dispatchMicStatus()
                }
            }
        }
    }

    override fun onRemoteVideoStateChanged(uid: String, streamId: String, muted: Boolean) {
        if (isDestroy) {
            return
        }
        LogUtils.i(TAG, "uid: $uid, 切换相机状态：${!muted}")
        listener?.onVideoCameraStateUpdate(uid, streamId, muted)
    }

    private var dispatchJob: Job? = null

    override fun onLocalAudioVolumeIndication(speakerLevel: Float) {
        if (isDestroy) {
            return
        }
        if (state.muteState.value || !state.upMicState.value) { // 已静音
            return
        }
        dispatchJob?.cancel()
        dispatchJob = rtcCoroutineScope.launch {
            if (!state.muteState.value && state.upMicState.value) { // 已静音
                val userId = sUser.id
                micList[userId]?.also {
                    micList[userId] = it.copy(volume = speakerLevel.toInt())
                }
                delay(30)
            }
            dispatchMicStatus()
        }
    }

    private val reportedSet by lazy {
        mutableSetOf<String>()
    }

    override fun onRemoteAudioVolumeIndication(speakers: Map<String, Float>) {
        if (isDestroy) {
            return
        }
        if (micList.isNotEmpty()) {
            dispatchJob?.cancel()
            dispatchJob = rtcCoroutineScope.launch {
                micList.forEach { (key, value) ->
                    if (key != sUser.id) {
                        micList[key] = value.copy(volume = speakers[key.toString()]?.toInt() ?: 0)
                    }
                }
                delay(30)
                dispatchMicStatus()
            }
        }

        if (reportVad) {
            speakers.forEach { (uid, volume) ->
                if (volume > 5 && !reportedSet.contains(uid)) {
                    reportedSet.add(uid)
                    Analytics.appReportEvent(DataPoint.eventBody("hear_sound_in_audioroom", extra = "{\"audioroom_id\": ${0}, \"speaker_id\": uid}"))
                }
            }
        }
    }

    override fun onPlayerRenderStreamFirstFrame(uid: String, streamId: String, video: Boolean) {
        if (isDestroy) {
            return
        }
        LogUtils.i(TAG, "uid: $streamId, 拉流渲染第一帧，video: $video")
        listener?.onStreamFirstFrameUpdate(uid, streamId, video)
    }

    override fun onPlayerRenderCameraFirstFrame(uid: String, streamId: String) {
        if (isDestroy) {
            return
        }
        LogUtils.i(TAG, "uid: $streamId, 远程相机渲染第一帧")
        listener?.onCameraFirstFrameUpdate(uid, streamId, false)
    }

    override fun onPublisherCapturedVideoFirstFrame(value: Int) {
        if (isDestroy) {
            return
        }
        LogUtils.i(TAG, "本地相机渲染第一帧：$value")
        listener?.onCameraFirstFrameUpdate("", "", true)
    }

    override fun onClientRoleChanged(oldRole: Int, newRole: Int) {
        if (isDestroy) {
            return
        }
        val upMic = newRole == 1
        rtcCoroutineScope.launch {
            state.upMicState.apply {
                if (value != upMic) {
                    LogUtils.w(TAG, "rtc状态不正确, 麦位状态不一致1, expect: $value  actual: $upMic")
                }
                if (Status.InProgress != status) {
                    LogUtils.w(TAG, "rtc状态不正确, 麦位状态不一致2, expect: ${Status.InProgress}  actual: $status")
                }
                LogUtils.i(TAG, "onClientRoleChanged: 切换角色成功，newRole: $newRole, upMic: $upMic")
                state.upMicState = this.copy(upMic, Status.Done)
            }
            if (upMic) {
                Log.d("AppAsrManager", "上麦")
                micList.put(sUser.id, Mic(0, state.muteState.value))
                if (autoPCMCapture) {
                    startPCMCapture()
                }
            } else {
                Log.d("AppAsrManager", "下麦")
                micList.remove(sUser.id)
                stopPCMCapture()
            }
            if (upMic && state.muteState.value != rtcEngine.isLocalAudioMuted()) {
                setEnableMuteAudio(state.muteState.value)
            }
        }
    }

    override fun onClientRoleChangeFailed(reason: Int, currentRole: Int) {
        if (isDestroy) {
            return
        }
        LogUtils.e(TAG, "onClientRoleChangeFailed, reason: $reason, currentRole: $currentRole")
        val upMic = currentRole == 1
        rtcCoroutineScope.launch {
            state.upMicState.apply {
                if (Status.InProgress != status) {
                    LogUtils.w(TAG, "rtc状态不正确, 麦位状态不一致3, expect: ${Status.InProgress}  actual: $status")
                }
                state.upMicState = this.copy(upMic, Status.Done)
            }
        }
    }

    override fun onTokenPrivilegeWillExpire() {
        if (isDestroy) {
            return
        }
        LogUtils.i(TAG, "token过期, 自动刷新")
        rtcCoroutineScope.launch {
            requestRefreshRtcToken()
        }
    }

    override fun onCapturedPCMData(audioPCMData: AudioPCMData) {
        if (isDestroy || !enableGameAsr) {
            return
        }
        listener?.onCapturedPCMData(audioPCMData)
    }

    private fun dispatchMicStatus() {
        if (isDestroy) {
            return
        }
        listener?.onAudioVolumeUpdate()
    }

    private suspend fun rtcJoinChannel(rtcToken: RtcToken?) {
        if (state.joinState.value) {
            LogUtils.w(TAG, "rtc状态不正确，应该还未加入频道")
        }
        val token = rtcToken ?: getRtcToken()
        with(state) {
            joinState = TState(true, Status.InProgress)
            upMicState = upMicState.copy(status = Status.InProgress)
            muteState = muteState.copy(status = Status.InProgress)
            speakerState = speakerState.copy(status = Status.InProgress)
            channelName = token.channelName
            LogUtils.i(TAG, "开始加入频道: ${token.channelName}, 当前状态: $state, token: $token")
            val ret = rtcEngine.joinChannel(
                token.rtcToken,
                token.channelName,
                upMicState.value,
                muteState.value,
                speakerState.value,
                token.useOldStreamIdByVideo
            )
            LogUtils.i(TAG, "加入频道结果: $ret")
        }
    }

    private suspend fun requestRtcToken(rtcToken: RtcToken? = null): RtcToken {
        if (!state.joinState.value) {
            LogUtils.w(TAG, "rtc状态不正确，应该已经在频道内")
        }
        return rtcToken ?: getRtcToken()
    }

    private suspend fun requestRefreshRtcToken() {
        if (!state.joinState.value) {
            LogUtils.w(TAG, "rtc状态不正确，应该已经在频道内")
        }
        val newToken = requestRtcToken()
        val ret = rtcEngine.renewToken(newToken.channelName, newToken.rtcToken)
        LogUtils.i(TAG, "token刷新结果: $ret")
    }

    private suspend fun getRtcToken(): RtcToken {
        LogUtils.i(TAG, "正在获取token，当前状态: $state")
        return rtcProvider.getRtcToken(tag).let { token ->
            if (token == null) {
                throw CancellationException("获取token失败")
            }
            token
        }
    }

    private data class State(
        var joinState: TState<Boolean> = TState(false),
        var upMicState: TState<Boolean> = TState(false),
        var muteState: TState<Boolean> = TState(false),
        var speakerState: TState<Boolean> = TState(false),
    )

    private data class TState<T>(val value: T, val status: Status = Status.Done)

    private sealed class Status {
        object Done : Status()
        object InProgress : Status()
    }
}

private fun destroyRtcEngine() {
    postToMainThread {
        try {
            TRTCCloud.destroySharedInstance()
        } catch (e: Exception) {
        }
    }
}