package com.qyqy.ucoo.utils

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.view.Gravity
import android.view.View
import android.widget.ImageView
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.lzf.easyfloat.EasyFloat
import com.lzf.easyfloat.anim.DefaultAnimator
import com.lzf.easyfloat.enums.ShowPattern
import com.lzf.easyfloat.enums.SidePattern
import com.lzf.easyfloat.permission.PermissionUtils
import com.lzf.easyfloat.utils.DisplayUtils
import com.overseas.common.ext.click
import com.overseas.common.utils.AppPermissionResultLauncher
import com.overseas.common.utils.PermissionRequest
import com.overseas.common.utils.PermissionUtil
import com.overseas.common.utils.dp
import com.qyqy.cupid.event.IMEvent
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.R
import com.qyqy.ucoo.VoiceLiveService
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.asComponentActivity
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.data.CrossRoomPkMicMutedEvent
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.AppEventManager
import com.qyqy.ucoo.glide.createPlaceholderDrawable
import com.qyqy.ucoo.im.bean.LoveInfo
import com.qyqy.ucoo.im.bean.MIC_MODE_REQ
import com.qyqy.ucoo.im.bean.PkInfo
import com.qyqy.ucoo.im.bean.PrivateRoomStatus
import com.qyqy.ucoo.im.bean.ROOM_MODE_GAME
import com.qyqy.ucoo.im.bean.ROOM_MODE_LOVE
import com.qyqy.ucoo.im.bean.ROOM_MODE_PK
import com.qyqy.ucoo.im.bean.ROOM_MODE_PRIVATE
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.im.bean.RoomSettings
import com.qyqy.ucoo.im.bean.RoomTokenRequest
import com.qyqy.ucoo.im.bean.RtcToken
import com.qyqy.ucoo.im.bean.Seat
import com.qyqy.ucoo.im.bean.isPrivate
import com.qyqy.ucoo.im.bean.isText
import com.qyqy.ucoo.im.compat.ConversationType
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.im.compat.MessageBundle
import com.qyqy.ucoo.im.compat.UCCustomMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.im.room.RoomRepository
import com.qyqy.ucoo.im.room.game.SudGameInfo
import com.qyqy.ucoo.loginCoroutineScope
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.user.voice.VoiceCallNotice
import com.qyqy.ucoo.utils.rtc.AppAsrManager
import com.qyqy.ucoo.utils.rtc.AppRtcManager
import com.qyqy.ucoo.utils.rtc.engine.TencentRtcFactory
import kotlinx.coroutines.Job
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.put
import java.lang.ref.WeakReference


object VoiceRoomHelper {

    private const val VOICE_ROOM_TAG = "voice_room"

    private val roomRepository by lazy(LazyThreadSafetyMode.NONE) {
        RoomRepository()
    }

    private var currentRoomFlow = MutableStateFlow<Room?>(null)

    internal val currentRoom: Room?
        get() = currentRoomFlow.value

    var appRtcManager: AppRtcManager? = null
        private set

    private var activityFlag = -2

    private var collapseState = false

    private var job: Job? = null

    private var delayRtc: Job? = null

    private val callback = object : IMCompatListener {

        override fun onRecvNewCustomMessage(message: UCCustomMessage, offline: Boolean) {
            val room = currentRoom ?: return
            if (collapseState && message.targetId == room.rongCloudId && !message.isC2CMsg) {
                handleCollapseRoomMessage(room, message)
            }
        }
    }

    var hasCollapsedRecord: Boolean = false
        private set

    val flow = currentRoomFlow.asStateFlow()

    init {
        IMCompatCore.addIMListener(callback)
    }

    fun attachRoom(activity: ChatRoomActivity, room: Room, rtcToken: RtcToken?): Pair<Boolean, AppRtcManager> {
        activityFlag = System.identityHashCode(activity)
        val lastRoom = currentRoom
        if (lastRoom == null && !room.isText) {
            VoiceLiveService.start(app)
        }
        val isSameRoom = lastRoom != null && lastRoom.isSameRoom(room)
        AppEventManager.enterRoom(room, isSameRoom.not())
        val reportVad = activity.intent?.getBooleanExtra("report_vad", false) == true
        return isSameRoom to joinRoom(isSameRoom, room, rtcToken, reportVad)
    }

    fun leaveRoom(room: Room, flag: Int) {
        if (flag != -1 && activityFlag != flag) {
            return
        }
        val lastRoom = currentRoom
        if (lastRoom != null && lastRoom.rongCloudId == room.rongCloudId && !collapseState) {
            dismissFloatView()
            innerLeaveRoom(lastRoom)
            currentRoomFlow.update {
                null
            }
            appRtcManager = null
            VoiceLiveService.stop(app)
            AppEventManager.leaveRoom(lastRoom)
        }
        activityFlag = -2
    }

    fun collapseRoom(activity: Activity, room: Room): Boolean {
        val lastRoom = currentRoom
        if (lastRoom != null && lastRoom.rongCloudId == room.rongCloudId) {
            currentRoomFlow.update {
                room
            }
            hasCollapsedRecord = true
            showFloatView(activity, room)
            return true
        }
        return false
    }

    fun dismissFloatView() {
        EasyFloat.dismiss(VOICE_ROOM_TAG)
    }

    fun leaveCurrentRoom() {
        hasCollapsedRecord = false
        collapseState = false
        dismissFloatView()
        currentRoom?.also {
            leaveRoom(it, -1)
        }
    }

    private fun joinRoom(isSameRoom: Boolean, room: Room, rtcToken: RtcToken?, reportVad: Boolean): AppRtcManager {
        if (isSameRoom) {
            // 进入了同一个房间，不需要做任何处理
            currentRoomFlow.update {
                room
            }
            collapseState = false
            return appRtcManager!!
        }
        currentRoom?.also {
            innerLeaveRoom(it)
        }
        collapseState = false
        currentRoomFlow.update {
            room
        }
        hasCollapsedRecord = false
        isAsrActive = false

        joinImRoom(room)

        if (room.isText) {
            return AppRtcManager()
        }

        val isInMic = room.isInMic
        val jsonConfig = rtcToken?.rtcConfig
        return AppRtcManager(
            room.roomId.toString(), { roomRepository.getRtcToken(room).getOrNull() }, room.isPrivate, TencentRtcFactory(jsonConfig = jsonConfig),
            reportVad = reportVad
        ).apply {
            updateRemoteAudioStream(buildSet {
                room.seats.forEach {
                    if (it.hasUser) {
                        add(it.user.id)
                    }
                }
            })
            appRtcManager = this
            joinChannel(rtcToken, isInMic)
        }
    }

    private fun joinImRoom(room: Room) {
        job = loginCoroutineScope.launch {
            try {
                val ret = IMCompatCore.joinConversation(room.rongCloudId, ConversationType.CHATROOM, true)
                if (ret) {
                    if (!room.isText) {
                        startAsrMessage()
                    }
                } else {
                    toastRes(R.string.连接聊天室失败_请重新加入)
                }
            } finally {
                if (currentRoom == null) {
                    withContext(NonCancellable) {
                        IMCompatCore.quitConversation(room.rongCloudId, ConversationType.CHATROOM, true)
                    }
                }
            }
        }
    }

    private fun innerLeaveRoom(room: Room) {
        if (room.isPrivate && room.privateRoomStatus?.privateRoomMode == 0 && room.privateRoomStatus?.isNewbieTryService == true) {
            if (sUser.id == room.privateRoomStatus?.serviceUserId) { // 尝试领取奖励
                appCoroutineScope.launch {
                    roomRepository.tryReceiveIntegral(room.roomId)
                }
            }
        }
        //停止盲盒倒计时
        TaskHelper.releaseBlindBoxState()
        job?.cancel()
        job = null
        delayRtc?.cancel()
        delayRtc = null
        appRtcManager?.levelChannel()
        loginCoroutineScope.launch {
            IMCompatCore.quitConversation(room.rongCloudId, ConversationType.CHATROOM, true)
        }
        asrJob?.cancel()
        asrJob = null

        if (room.privateRoomExtra?.fromSceneType == 2 && room.privateRoomExtra.fromSceneId != 0) { // 语音房
            appCoroutineScope.launch {
                delay(300)
                Analytics.appReportEvent(
                    DataPoint.clickBody(
                        "enter_audioroom",
                        extra = "{\"audioroom_id\": ${room.privateRoomExtra.fromSceneId}, \"from\": 1}"
                    )
                )
                joinVoiceChatRoom(room.privateRoomExtra.fromSceneId, RoomTokenRequest(), true) {
                    putExtra("report_vad", sUser.id == room.privateRoomStatus?.newbieUserId)
                }
            }
        }
    }

    private fun handleCollapseRoomMessage(room: Room, message: UCCustomMessage) {
        when (message.cmd) {
            MsgEventCmd.BLACK_USER -> {
                val user = message.getJsonValue<AppUser>("user")
                if (user?.id == sUser.id) {
                    toast(app.getString(R.string.您已被该房间拉黑))
                    leaveCurrentRoom()
                }
            }

            MsgEventCmd.USER_EXIT -> {
                val user = message.getJsonValue<AppUser>("user")
                if (user?.isSelf == true && message.getJsonBoolean("is_forced").orDefault(false)) {
                    toastRes(R.string.你已被踢出房间)
                    leaveCurrentRoom()
                }
            }

            MsgEventCmd.VOICE_CALL_FINISH -> {
                if (room.privateRoomStatus?.privateRoomMode == 2 && room.privateRoomStatus?.inviteCode == message.parseDataJson<VoiceCallNotice>()?.callId) {
                    toastRes(R.string.本次通话已结束)
                    leaveCurrentRoom()
                }
            }

            MsgEventCmd.PRIVATE_ROOM_INTERACT -> {
                val privateRoomStatus = message.parseDataJson<PrivateRoomStatus>()
                if (privateRoomStatus != null && room.privateChatUser != null && room.roomId == privateRoomStatus.id) {
                    if (privateRoomStatus.status == -1) {
                        toast(app.getString(R.string.你们的私密小屋时长不足啦))
                        leaveCurrentRoom()
                        return
                    }
                    currentRoomFlow.update {
                        room.copy(privateRoomExtra = room.privateRoomExtra?.copy(privateRoomStatus = room.privateRoomStatus?.run {
                            privateRoomStatus.copy(
                                newbieLockSeconds = newbieLockSeconds,
                                isNewbieTryService = isNewbieTryService,
                                newbieUserId = newbieUserId,
                                serviceUserId = serviceUserId,
                                inviteCode = inviteCode,
                                starHquType = starHquType,
                                title = title,
                                isCp = isCp,
                                isFriend = isFriend,
                            )
                        } ?: privateRoomStatus))
                    }
                }
            }

            MsgEventCmd.ROOM_SETTINGS -> {
                val roomSettings = message.parseDataJson<RoomSettings>()
                when (roomSettings?.name) {
                    RoomSettings.KEY_TITLE -> {
                        currentRoomFlow.update {
                            room.copy(roomName = roomSettings.value)
                        }
                    }

                    RoomSettings.KEY_ROOM_MODE -> {
                        val settings = roomSettings
                        val value = settings.value.toInt()
                        currentRoomFlow.update {
                            val sudGameInfo = if (value == ROOM_MODE_GAME) {
                                SudGameInfo(settings.playing)
                            } else {
                                SudGameInfo(null)
                            }
                            if (settings.roomBackground.isNullOrBlank()) {
                                room.copy(
                                    roomMode = value,
                                    upMicReqing = false,
                                    pkInfo = if (value == ROOM_MODE_PK) PkInfo() else null,
                                    loveInfo = if (value == ROOM_MODE_LOVE) LoveInfo() else null,
                                    sudGameInfo = sudGameInfo,
                                )
                            } else {
                                room.copy(
                                    roomMode = value,
                                    upMicReqing = false,
                                    roomBackground = settings.roomBackground,
                                    pkInfo = if (value == ROOM_MODE_PK) PkInfo() else null,
                                    loveInfo = if (value == ROOM_MODE_LOVE) LoveInfo() else null,
                                    sudGameInfo = sudGameInfo,
                                )
                            }
                        }
                    }

                    RoomSettings.KEY_MIC_MODE -> {
                        currentRoomFlow.update {
                            room.copy(upMicMode = roomSettings.value.toInt(), upMicReqing = false)
                        }
                    }

                    RoomSettings.KEY_ROOM_BACKGROUND -> {
                        currentRoomFlow.update {
                            room.copy(roomBackground = roomSettings.value)
                        }
                    }

                    RoomSettings.KEY_GAME_START -> {
                        val settings = roomSettings
                        currentRoomFlow.update {
                            room.copy(sudGameInfo = SudGameInfo(settings.playing))
                        }
                    }
                }
            }

            MsgEventCmd.SEATS_CHANGE -> {
                message.getJsonValue<List<Seat>>("seats")?.apply {
                    delayRtc?.cancel()
                    delayRtc = appCoroutineScope.launch {
                        appRtcManager?.updateRemoteAudioStream(mapNotNull {
                            if (it.hasUser) {
                                it.user.id
                            } else {
                                null
                            }
                        }.toSet())
                        val isInMic = any {
                            it.hasUser && it.user.isSelf
                        }
                        if (isInMic) {
                            // 上麦
                            appRtcManager?.upMic(null)
                        } else {
                            // 下麦
                            appRtcManager?.downMic(null)
                        }
                    }
                }
            }

            MsgEventCmd.TAKEAWAY_MIC -> {
                val user = message.getJsonValue<AppUser>("target_user")
                if (user?.isSelf == true) {
                    appRtcManager?.downMic(null)
                }
            }

            MsgEventCmd.AGREE_MIC -> {
                val user = message.getJsonValue<AppUser>("apply_user")
                if (user?.isSelf == true) {
                    fun upMic() {
                        if (appRtcManager == null) {
                            return
                        }
                        appCoroutineScope.launch {
                            roomRepository.receiveUpMic(room.roomId).onSuccess {
                                if (appRtcManager != null) {
                                    appRtcManager?.upMic(it)
                                    if (room.upMicMode == MIC_MODE_REQ) {
                                        currentRoomFlow.update {
                                            room.copy(upMicReqing = false)
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (!PermissionUtil.hasPermissions(Manifest.permission.RECORD_AUDIO)) {
                        ActivityLifecycle.startTopActivity?.asComponentActivity?.let {
                            AppPermissionResultLauncher(it)
                        }?.onlyOnceLaunch(PermissionRequest(Manifest.permission.RECORD_AUDIO)) {
                            if (appRtcManager != null) {
                                upMic()
                            }
                        }
                    } else {
                        upMic()
                    }
                }
            }

            IMEvent.VOICE_MIC_ABANDONED -> {
                if (message.getJsonInt("userid") != sUser.userId) {
                    return
                }
                if (appRtcManager == null) {
                    return
                }
                appCoroutineScope.launch {
                    toast(message.getJsonString("toast"))
                    roomRepository.downMic(room).onSuccess {
                        if (appRtcManager != null) {
                            appRtcManager?.downMic(it)
                        }
                    }.toastError()
                }
            }

            MsgEventCmd.CMD_CROSS_ROOM_PK_MIC_MUTED -> {
                message.parseDataJson<CrossRoomPkMicMutedEvent>()?.also {
                    if (it.targetRoom.id != room.roomId) {
                        if (it.isMicMuted) {
                            appRtcManager?.updateRemoteStreamWhiteList(emptySet())
                        } else {
                            appRtcManager?.updateRemoteStreamWhiteList(setOf(it.thirdPushStreamId))
                        }
                    }
                }
            }
        }
    }

    private fun showFloatView(activity: Activity?, room: Room) {

        fun updateView(view: View) {
            view.click {
                ActivityLifecycle.startTopActivity?.apply {
                    currentRoom?.also {
                        collapseState = false
                        ChatRoomActivity.openExistRoomActivity(this, it)
                    }
                }
            }
            view.findViewById<View>(R.id.btn_close)?.click {
                leaveCurrentRoom()
            }
            val url = if (room.roomMode == ROOM_MODE_PRIVATE) {
                room.privateChatUser!!.avatarUrl
            } else {
                room.owner.avatarUrl
            }
            val drawable = createPlaceholderDrawable()
            view.findViewById<ImageView>(R.id.background_room)?.apply {
                Glide.with(app).load(url).placeholder(drawable).error(drawable).into(this)
            }
        }

        val floatView = EasyFloat.getFloatView(VOICE_ROOM_TAG)
        if (floatView != null) {
            activity?.finish()
            updateView(floatView)
            if (!floatView.isShown) {
                EasyFloat.show(VOICE_ROOM_TAG)
            }
            return
        }

        val weakActivity = WeakReference(activity)
        EasyFloat.with(app)
            // 设置浮窗xml布局文件/自定义View，并可设置详细信息
            .setLayout(R.layout.layout_float_view_voice_room) {
                updateView(it)
            }
            // 设置浮窗显示类型，默认只在当前Activity显示，可选一直显示、仅前台显示
            .setShowPattern(ShowPattern.FOREGROUND)
            // 设置吸附方式，共15种模式，详情参考SidePattern
            .setSidePattern(SidePattern.RESULT_SIDE)
            // 设置浮窗的标签，用于区分多个浮窗
            .setTag(VOICE_ROOM_TAG)
            // 设置浮窗是否可拖拽
            .setDragEnable(true).setBorder(left = 15.dp, right = DisplayUtils.getScreenWidth(app).minus(15.dp))
            // 设置浮窗的对齐方式和坐标偏移量
            .setGravity(Gravity.END or Gravity.CENTER_VERTICAL, (-15).dp, 120.dp)
            // 设置当布局大小变化后，整体view的位置对齐方式
            .setLayoutChangedGravity(Gravity.END).setMatchParent(widthMatch = false, heightMatch = false)
            // 设置浮窗的出入动画，可自定义，实现相应接口即可（策略模式），无需动画直接设置为null
            .setAnimator(DefaultAnimator())
            // 设置系统浮窗的有效显示高度（不包含虚拟导航栏的高度），基本用不到，除非有虚拟导航栏适配问题
            .setDisplayHeight { context -> DisplayUtils.rejectedNavHeight(context) }.setFilter(ChatRoomActivity::class.java)
            // 浮窗的一些状态回调，如：创建结果、显示、隐藏、销毁、touchEvent、拖拽过程、拖拽结束。
            // ps：通过Kotlin DSL实现的回调，可以按需复写方法，用到哪个写哪个
            .registerCallback {
                createResult { isCreated, msg, view ->
                    if (isCreated) {
                        collapseState = true
                        weakActivity.get()?.finish()
                        if (ActivityLifecycle.topActivity is ChatRoomActivity) {
                            view?.isVisible = false
                        }
                    }
                }
                var hide = false
                show {
                    if (ActivityLifecycle.topActivity is ChatRoomActivity) {
                        EasyFloat.hide(VOICE_ROOM_TAG)
                        return@show
                    }
                    if (hide && !PermissionUtils.checkPermission(app)) { // 可能是用户手动关闭了悬浮窗权限
                        // 此时悬浮窗无法显示，但依然在房间内，此时应该退出房间
                        toast(app.getString(R.string.tip4))
                        leaveCurrentRoom()
                    }
                    hide = false
                }
                hide {
                    hide = true
                }
            }
            // 创建浮窗（这是关键哦😂）
            .show()
    }

    suspend fun joinVoiceChatRoom(
        roomId: Int,
        request: RoomTokenRequest = RoomTokenRequest(false),
        forceOpenNew: Boolean = false,
        userid: String = "",
        hint: String = "",
        intentBlock: Intent.() -> Unit = {},
    ) {
        roomRepository.joinChatRoom(roomId, request).onSuccess {
            val (newRoom, token) = it
            val activity = ActivityLifecycle.topActivity ?: return
            if (activity is ChatRoomActivity) {
                val room = ChatRoomActivity.getRoom(activity)
                if (!forceOpenNew && room != null && room.isSameRoom(newRoom)) {
                    return@onSuccess
                }
                activity.finish()
                appCoroutineScope.launch {
                    delay(100)
                    ActivityLifecycle.startTopActivity?.also { act ->
                        ChatRoomActivity.openRoomActivity(act, newRoom, token, userid, hint, intentBlock)
                    }
                }
            } else {
                ChatRoomActivity.openRoomActivity(activity, newRoom, token, userid, hint, intentBlock)
            }
        }.toastError()
    }

    ///////////////////////////////////////////////////////////////////////////
    //  ASR
    ///////////////////////////////////////////////////////////////////////////

    var isAsrActive = false

    private var asrJob: Job? = null

    private fun startAsrMessage() {
        asrJob?.cancel()
        asrJob = AppAsrManager.recognizeFlow.onEach { result ->
            if (!AppAsrManager.sendMessageEnable) {
                return@onEach
            }
            currentRoom?.takeIf {
                isAsrActive
            }?.also { room ->
                IMCompatCore.sendMessage(
                    room.rongCloudId,
                    ConversationType.CHATROOM,
                    MessageBundle.Custom.create(MsgEventCmd.MIC_SPEECH_TEXT, buildJsonObject {
                        put("contentText", result.text)
                    })
                )
            }
        }.launchIn(appCoroutineScope)
    }

    ///////////////////////////////////////////////////////////////////////////
    //  ASR
    ///////////////////////////////////////////////////////////////////////////
}