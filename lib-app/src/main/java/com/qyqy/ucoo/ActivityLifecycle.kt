package com.qyqy.ucoo

import android.app.Activity
import android.app.Application
import android.content.Context
import android.content.ContextWrapper
import android.os.Bundle
import android.util.Log
import androidx.activity.ComponentActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelStoreOwner
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.ui.CupidMainActivity
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.GlobalNotification
import com.qyqy.ucoo.core.upgrade.UpgradeManager
import com.qyqy.ucoo.home.HomeActivity
import com.qyqy.ucoo.utils.GiftEffectHelper
import com.qyqy.ucoo.utils.RouterHelper
import com.qyqy.ucoo.utils.ShushuUtils
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import site.xzwzz.video.XPlayer
import java.lang.ref.WeakReference
import java.util.Date

val Context.asActivity: Activity?
    get() {
        var ctx: Context? = this
        while (ctx != null) {
            if (ctx is Activity) {
                return ctx
            }
            if (ctx is ContextWrapper) {
                ctx = ctx.baseContext
            } else {
                break
            }
        }
        return null
    }

val Context.asComponentActivity: ComponentActivity?
    get() = asActivity as? ComponentActivity

val Context.asFragmentActivity: FragmentActivity?
    get() = asActivity as? FragmentActivity

val Context.asBase: BaseActivity?
    get() = asActivity as? BaseActivity

fun Context.asViewModelStoreOwner(owner: ViewModelStoreOwner) = asComponentActivity ?: owner

fun runWithTopActivity(block: Activity.() -> Unit) {
    ActivityLifecycle.runWithTopActivity(block)
}

object ActivityLifecycle : Application.ActivityLifecycleCallbacks {

    private const val TAG = "UCOOActivityLifecycle"

    private val _topActFlow = MutableStateFlow<WeakReference<Activity>?>(null)

    val topActivityFlow: StateFlow<WeakReference<Activity>?> = _topActFlow.asStateFlow()

    var started = false
        private set(value) {
            if (value != field) {
                field = value
                ShushuUtils.userSet(
                    "last_active_time" to Date()
                )
            }
        }

    var resumed = false
        private set

    val topActivity: Activity?
        get() = _topActFlow.value?.get()

    fun runWithTopActivity(block: Activity.() -> Unit) {
        topActivity?.run {
            runOnUiThread {
                block()
            }
        }
    }

    val startTopActivity: Activity?
        get() = topActivity?.takeIf {
            started || resumed
        }

    val resumeTopActivity: Activity?
        get() = topActivity?.takeIf {
            resumed
        }

    var homeTaskId: Int? = null
        private set

    override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
        if (activity is HomeActivity) {
            homeTaskId = activity.taskId
        }
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "taskId: ${activity.taskId}, onActivityCreated: $activity")
        }
        started = false
        resumed = false
        _topActFlow.value = WeakReference(activity)
        RouterHelper.registerCloseRouter(activity)
        UpgradeManager.injectUpgradeFlow(activity)
        if (activity is HomeActivity) {
            app.lazyInitialForUCOO()
            GiftEffectHelper.preloadGiftList()
            GlobalNotification.registerRcMessage(UserPartition.UCOO)
        } else if (activity is CupidMainActivity) {
            GiftEffectHelper.preloadGiftList()
            GlobalNotification.registerRcMessage(UserPartition.Cupid)
        }
    }

    override fun onActivityStarted(activity: Activity) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "onActivityStarted: $activity")
        }
        started = true
        resumed = true
        if (topActivity !== activity) {
            _topActFlow.value = WeakReference(activity)
        }
    }

    override fun onActivityResumed(activity: Activity) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "onActivityResumed: $activity")
        }
        started = true
        resumed = true
        if (topActivity !== activity) {
            _topActFlow.value = WeakReference(activity)
        }
    }

    override fun onActivityPaused(activity: Activity) {
        if (topActivity === activity) {
            started = !activity.isFinishing
            resumed = false
        }
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "onActivityPaused: $activity")
        }
    }

    override fun onActivityStopped(activity: Activity) {
        if (topActivity === activity) {
            started = false
            resumed = false
        }
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "onActivityStopped: $activity")
        }
    }

    override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "onActivitySaveInstanceState: $activity")
        }
    }

    override fun onActivityDestroyed(activity: Activity) {
        if (activity is HomeActivity) {
            if (homeTaskId == activity.taskId) {
                homeTaskId = null
            }
        }
        if (BuildConfig.DEBUG) {
            Log.d(TAG, "onActivityDestroyed: $activity")
        }
        if (topActivity === activity) {
            started = false
            resumed = false
            _topActFlow.value = null
        }
        XPlayer.pool().removeWithContext(activity)
        if (activity is HomeActivity) {
            GlobalNotification.unregisterRcMessage()
        } else if (activity is CupidMainActivity) {
            GlobalNotification.unregisterRcMessage()
        }
    }
}

fun getTopActivity(): BaseActivity? = ActivityLifecycle.resumeTopActivity as? BaseActivity