package com.qyqy.ucoo.multilingual

import android.annotation.SuppressLint
import android.app.Application
import android.content.Context
import android.content.res.Configuration
import android.content.res.Resources
import android.os.Build
import android.util.Xml
import androidx.appcompat.app.AppCompatDelegate
import androidx.appcompat.app.BaseContextWrappingDelegate
import com.qyqy.ucoo.AppUserPartition
import androidx.collection.mutableScatterMapOf
import com.coolerfall.download.Priority
import com.hjq.language.MultiLanguages
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.compose.presentation.translation.TranslateApi
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.isSimplifiedChinese
import com.qyqy.ucoo.multilingual.Philology
import com.qyqy.ucoo.multilingual.PhilologyInterceptor
import com.qyqy.ucoo.multilingual.PhilologyRepository
import com.qyqy.ucoo.multilingual.PhilologyRepositoryFactory
import com.qyqy.ucoo.multilingual.currentLocale
import com.qyqy.ucoo.sAppKV
import com.qyqy.ucoo.sIsSignIn
import com.qyqy.ucoo.utils.download.DownloadHelper
import io.github.inflationx.viewpump.ViewPump
import io.github.inflationx.viewpump.ViewPumpContextWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import org.xmlpull.v1.XmlPullParser
import java.io.File
import java.util.Locale
import kotlin.time.Duration
import kotlin.time.Duration.Companion.seconds


private const val LANGUAGE_TYPE = "language"

@SuppressLint("StaticFieldLeak")
object AppLanguage {

    private lateinit var viewPump: ViewPump

    private lateinit var previousLanguage: Locale

    private var initialed = false

    private var preLoadPhilologyRepository: PhilologyRepository? = null

    private val api by lazy(LazyThreadSafetyMode.NONE) {
        createApi(TranslateApi::class.java)
    }

    private lateinit var philologyContext: Context

    fun init(application: Application) {
        MultiLanguages.init(application)
        Philology.init(object : PhilologyRepositoryFactory {

            override fun getPhilologyRepository(locale: Locale): PhilologyRepository =
                preLoadPhilologyRepository?.takeIf { it.locale == locale } ?: createPhilologyRepository(locale).also {
                    preLoadPhilologyRepository = null
                }
        })  // Init Philology with our PhilologyRepositoryFactory
        // Add PhilologyInterceptor to ViewPump
        // If you are already using Calligraphy you can add both interceptors, there is no problem
        viewPump = ViewPump.builder().addInterceptor(PhilologyInterceptor).build()
        previousLanguage = getAppLanguage(application) {
            application.resources.currentLocale()
        }
        if (!previousLanguage.isSimplifiedChinese) { // 忽略简体中文
            preLoadPhilologyRepository = createPhilologyRepository(previousLanguage)
        }
        initialed = true
    }

    fun attachApplication(base: Context): Context {
        val context = try {
            attach(base).also {
                philologyContext = Philology.wrap(it)
            }
        } catch (e: Exception) {
            base
        }
        return context
    }

    fun attach(base: Context): Context {
        val resources: Resources = base.resources
        val systemLanguage = resources.currentLocale()
        val currentLanguage = getAppLanguage(base) {
            systemLanguage
        }
        if (systemLanguage == currentLanguage) {
            return base
        }
        val config = Configuration(resources.configuration)
        config.setLocale(currentLanguage)
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
            base.createConfigurationContext(config)
        } else {
            resources.updateConfiguration(config, resources.displayMetrics)
            base
        }
    }

    fun getResources(): Resources? {
        return if (::philologyContext.isInitialized) {
            philologyContext.resources
        } else {
            null
        }
    }

    fun onConfigurationChanged(newConfig: Configuration) {
        // 获取当前配置的语言
        val currentLanguage = if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N) {
            @Suppress("DEPRECATION")
            newConfig.locale
        } else {
            newConfig.locales[0]
        }
        if (currentLanguage != previousLanguage && !currentLanguage.isSimplifiedChinese) { // 语言发生了变化, 忽略中文，包括繁体
            preLoadPhilologyRepository = createPhilologyRepository(currentLanguage)
        }
        // 更新之前的语言设置
        previousLanguage = currentLanguage
    }

    fun getDelegate(superDelegate: AppCompatDelegate): AppCompatDelegate {
        return if (initialed) {
            BaseContextWrappingDelegate(superDelegate) { context ->
                ViewPumpContextWrapper.wrap(Philology.wrap(context), viewPump)
            }
        } else {
            superDelegate
        }
    }

    suspend fun awaitLanguageLoaded(timeout: Duration = 2.seconds) {
        if (initialed) {
            Philology.currentPhilologyRepository?.awaitLoaded(timeout)
        }
    }

    private fun createPhilologyRepository(locale: Locale) = BasicPhilologyRepository(locale) {
        withContext(Dispatchers.IO) {
            runApiCatching {
                api.queryLanguagePackage(it)
            }.mapCatching {
                it.getStringOrNull("link")
            }
        }.getOrNull().orEmpty()
    }


    private const val KEY_LANGUAGE = "key_language"

    private const val KEY_COUNTRY = "key_country"

    private const val SharedPreferencesName = "language_setting"

    private fun getAppLanguage(context: Context, fallback: () -> Locale): Locale {
        return if (::previousLanguage.isInitialized) {
            previousLanguage
        } else {
            context.getSharedPreferences(SharedPreferencesName, Context.MODE_PRIVATE).run {
                val language = getString(KEY_LANGUAGE, null)
                val country = getString(KEY_COUNTRY, null)
                if (!language.isNullOrEmpty()) {
                    Locale(language, country.orEmpty())
                } else {
                    fallback()
                }
            }.also {
                previousLanguage = it
            }
        }
    }

}


private class BasicPhilologyRepository(
    override val locale: Locale,
    private val providerUrl: suspend (String) -> String,
) : PhilologyRepository {

    private var released = false

    private var ready = false

    private val textMap by lazy(LazyThreadSafetyMode.NONE) {
        mutableScatterMapOf<String, String>()
    }

    private val textArrayMap by lazy(LazyThreadSafetyMode.NONE) {
        mutableScatterMapOf<String, Array<CharSequence>>()
    }

    private val job: Job

    override val isAvailable: Boolean
        get() = !released && ready

    init {
        // 理论上这里都是非中文locale
        val languageCode = locale.toLanguageTag()

        var url = sAppKV.getString(languageCode)

        var checkLatestVersionLanguagePackage = true

        job = appCoroutineScope.launch {
            if (url.isEmpty()) {
                checkLatestVersionLanguagePackage = false
                url = providerUrl(languageCode)
                if (url.isEmpty()) {
                    // 获取失败或者是没有对应的语言包
                    return@launch
                }
                sAppKV.putString(languageCode, url)
            }

            val file = DownloadHelper.executeDownload(
                requestWrapper = DownloadHelper.createRequestWrapper(url, LANGUAGE_TYPE) {
                    priority(Priority.HIGH)
                },
                cancelDownloadIfCancelCoroutine = false
            )

            if (file != null) {
                withContext(Dispatchers.IO) {
                    // 测试下来XmlPullParser的解析方式>Sax>Dom
                    parseStringsXml2(file) //266.2 //228.5 // 232.8
                    ready = true
                }
            }
        }

        job.invokeOnCompletion {
            if (checkLatestVersionLanguagePackage) {
                appCoroutineScope.launch {
                    val newUrl = providerUrl(languageCode)
                    if (newUrl.isNotEmpty() && newUrl != url) {
                        sAppKV.putString(languageCode, newUrl)

                        val file = DownloadHelper.executeDownload(
                            requestWrapper = DownloadHelper.createRequestWrapper(newUrl, LANGUAGE_TYPE) {
                                priority(Priority.NORMAL)
                            },
                            cancelDownloadIfCancelCoroutine = false
                        )

                        if (released || file == null) {
                            return@launch
                        }

                        withContext(Dispatchers.IO) {
                            // 测试下来XmlPullParser的解析方式>Sax>Dom
                            parseStringsXml2(file) //266.2 //228.5 // 232.8
                        }
                    }
                }
            }
        }

    }

    override fun getText(key: String): CharSequence? = run {
        if (released || (sIsSignIn && AppUserPartition.isCupid)) {
            return@run null
        }
        textMap[key]
    }

    override fun getPlural(key: String, quantityString: String): CharSequence? = null

    override fun getTextArray(key: String): Array<CharSequence>? = run {
        if (released || (sIsSignIn && AppUserPartition.isCupid)) {
            return@run null
        }
        textArrayMap[key]
    }

    override fun release() {
        released = true
        job.cancel()
    }

    override suspend fun awaitLoaded(timeout: Duration) {
        if (!released && !job.isCompleted) {
            withTimeoutOrNull(timeout) {
                job.join()
            }
        }
    }

    private fun parseStringsXml2(file: File) {
        if (released) {
            return
        }
        try {
            file.inputStream().use {
                val parser: XmlPullParser = Xml.newPullParser()
                parser.setInput(file.inputStream(), null)
                var eventType = parser.eventType
                val itemsList = mutableListOf<String>()
                while (eventType != XmlPullParser.END_DOCUMENT) {
                    if (eventType == XmlPullParser.START_TAG) {
                        val tagName = parser.name
                        if ("string" == tagName) {
                            val name = parser.getAttributeValue(null, "name")
                            val value = parser.nextText().replace("\\n", "\n").replace("\\@", "@")
                            textMap[name] = value
                        } else if ("string-array" == tagName) {
                            val arrayName = parser.getAttributeValue(null, "name")
                            eventType = parser.next()
                            itemsList.clear()
                            while (!(eventType == XmlPullParser.END_TAG && "string-array" == parser.name)) {
                                if (eventType == XmlPullParser.START_TAG && "item" == parser.name) {
                                    val itemValue = parser.nextText().replace("\\n", "\n").replace("\\@", "@")
                                    itemsList.add(itemValue)
                                }
                                eventType = parser.next()
                            }
                            textArrayMap[arrayName] = itemsList.toTypedArray()
                        }
                    }
                    eventType = parser.next()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}