package com.qyqy.ucoo.multilingual

import android.content.res.Configuration
import android.content.res.Resources
import android.icu.text.PluralRules
import android.os.Build
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.qyqy.ucoo.R
import java.util.Locale
import kotlin.time.Duration

internal class ResourcesUtil(private val baseResources: Resources) {
    private val repository: PhilologyRepository?
        get() = Philology.getPhilologyRepository(baseResources.currentLocale()).takeIf { it.isAvailable }

    @Throws(Resources.NotFoundException::class)
    fun getText(id: Int): CharSequence {
        return repository?.getText(getResourceEntryName(id))
            ?: baseResources.getText(id)
    }

    @Throws(Resources.NotFoundException::class)
    fun getString(id: Int): String = getText(id).toString()

    @Throws(Resources.NotFoundException::class)
    fun getQuantityText(id: Int, quantity: Int): CharSequence = repository?.getPlural(
        getResourceEntryName(id),
        quantity.toPluralKeyword(baseResources)
    ) ?: baseResources.getQuantityText(id, quantity)

    @Throws(Resources.NotFoundException::class)
    fun getQuantityString(id: Int, quantity: Int): String = getQuantityText(id, quantity).toString()

    @Throws(Resources.NotFoundException::class)
    fun getQuantityString(id: Int, quantity: Int, vararg formatArgs: Any?): String =
        String.format(getQuantityString(id, quantity), *formatArgs)

    fun getStringArray(id: Int): Array<String> =
        getTextArray(id).map { it.toString() }.toTypedArray()

    fun getTextArray(id: Int): Array<CharSequence> {
        return repository?.getTextArray(getResourceEntryName(id))
            ?: baseResources.getTextArray(id)
    }

    private fun getResourceEntryName(id: Int): String {
        return try {
            baseResources.getResourceEntryName(id)
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(e)
            ""
        }
    }
}

interface PhilologyRepository {

    val locale: Locale?
        get() = null

    val isAvailable: Boolean
        get() = false

    fun getText(key: String): CharSequence? = null
    fun getPlural(key: String, quantityString: String): CharSequence? = null
    fun getTextArray(key: String): Array<CharSequence>? = null
    fun release() = Unit
    suspend fun awaitLoaded(timeout: Duration) = Unit
}

@SuppressWarnings("NewApi")
fun Resources.currentLocale(): Locale = if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N) {
    @Suppress("DEPRECATION")
    configuration.locale
} else {
    configuration.locales[0]
}

private fun Int.toPluralKeyword(baseResources: Resources): String = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
    PluralRules.forLocale(baseResources.currentLocale()).select(this.toDouble())
} else {
    baseResources.getQuantityString(R.plurals.com_jcminarro_philology_quantity_string, this)
}

val Configuration.currentLocale: Locale
    get() = if (Build.VERSION.SDK_INT <= Build.VERSION_CODES.N) {
        @Suppress("DEPRECATION")
        locale
    } else {
        locales.get(0) ?: Locale.getDefault()
    }

val Resources.currentLocale: Locale
    get() = configuration.currentLocale
