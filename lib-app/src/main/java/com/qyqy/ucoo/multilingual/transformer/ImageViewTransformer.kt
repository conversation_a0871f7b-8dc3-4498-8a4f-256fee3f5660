package com.qyqy.ucoo.multilingual.transformer

import android.util.AttributeSet
import android.view.View
import android.widget.ImageView
import com.overseas.common.utils.isLayoutRtl
import com.qyqy.ucoo.multilingual.ViewTransformer

internal object ImageViewTransformer : ViewTransformer {


    override fun reword(view: View, attributeSet: AttributeSet): View = view.apply {
        when (this) {
            is ImageView -> reword(attributeSet)
        }
    }

    private fun ImageView.reword(attributeSet: AttributeSet) {
        if (!context.getBooleanValue(attributeSet, android.R.attr.autoMirrored, false)) {
            return
        }
        val scale = scaleX
        if (isLayoutRtl) {
            if (scale > 0f) {
                scaleX = -scale
            }
        } else {
            if (scale < 0f) {
                scaleX = -scale
            }
        }
    }
}
