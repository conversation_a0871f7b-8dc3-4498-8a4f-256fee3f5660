package com.qyqy.ucoo.multilingual

import androidx.appcompat.app.AppCompatDelegate
import com.qyqy.ucoo.multilingual.AppLanguage


class PhilologyAppCompatDelegateHolder {

    private var baseContextWrappingDelegate: AppCompatDelegate? = null

    fun getDelegate(superDelegate: AppCompatDelegate) =
        baseContextWrappingDelegate ?: AppLanguage.getDelegate(superDelegate).apply { baseContextWrappingDelegate = this }
}