package com.qyqy.ucoo.multilingual

import android.content.Context
import android.content.ContextWrapper
import android.os.Build
import android.view.View
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import com.qyqy.ucoo.isSimplifiedChinese
import com.qyqy.ucoo.multilingual.transformer.ImageViewTransformer
import com.qyqy.ucoo.multilingual.transformer.NoneViewTransformer
import com.qyqy.ucoo.multilingual.transformer.SupportToolbarViewTransformer
import com.qyqy.ucoo.multilingual.transformer.TextViewTransformer
import com.qyqy.ucoo.multilingual.transformer.ToolbarViewTransformer
import java.util.Locale

/**
 * github: <p>https://github.com/JcMinarro/Philology</p>
 */
object Philology {

    var currentPhilologyRepository: PhilologyRepository? = null
        private set

    private var factory: PhilologyRepositoryFactory = object : PhilologyRepositoryFactory {
        override fun getPhilologyRepository(locale: Locale): PhilologyRepository? = null
    }
    private var viewTransformerFactory: ViewTransformerFactory = emptyViewTransformerFactory

    @JvmOverloads
    fun init(
        factory: PhilologyRepositoryFactory,
        viewTransformerFactory: ViewTransformerFactory = emptyViewTransformerFactory,
    ) {
        this.factory = factory
        this.viewTransformerFactory = viewTransformerFactory
        currentPhilologyRepository?.release()
        currentPhilologyRepository = null
    }

    fun wrap(baseContext: Context): ContextWrapper = PhilologyContextWrapper(baseContext)

    internal fun getPhilologyRepository(locale: Locale): PhilologyRepository =
        currentPhilologyRepository?.takeIf {
            it.locale == locale
        } ?: run {
            if (locale.isSimplifiedChinese) { // 中文本地维护
                emptyPhilologyRepository
            } else {
                factory.getPhilologyRepository(locale) ?: emptyPhilologyRepository
            }.also {
                currentPhilologyRepository?.release()
                currentPhilologyRepository = it
            }
        }

    internal fun getViewTransformer(view: View): ViewTransformer =
        viewTransformerFactory.getViewTransformer(view) ?: internalViewTransformerFactory.getViewTransformer(view)
}

interface PhilologyRepositoryFactory {
    fun getPhilologyRepository(locale: Locale): PhilologyRepository?
}

interface ViewTransformerFactory {
    fun getViewTransformer(view: View): ViewTransformer?
}

private val emptyPhilologyRepository = object : PhilologyRepository {}

private val emptyViewTransformerFactory = object : ViewTransformerFactory {
    override fun getViewTransformer(view: View): ViewTransformer? = null
}

private val internalViewTransformerFactory = object : ViewTransformerFactory {
    override fun getViewTransformer(view: View): ViewTransformer =
        getNewApiViewTransformer(view) ?: getSupportedViewTransformer(view) ?: NoneViewTransformer

    private fun getSupportedViewTransformer(view: View): ViewTransformer? = when (view) {
        is Toolbar -> SupportToolbarViewTransformer
        is TextView -> TextViewTransformer
        else -> null
    }

    @SuppressWarnings("NewApi")
    private fun getNewApiViewTransformer(view: View): ViewTransformer? =
        if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP) {
            null
        } else {
            when (view) {
                is android.widget.Toolbar -> ToolbarViewTransformer
                is android.widget.ImageView -> ImageViewTransformer
                else -> null
            }
        }
}

