package com.qyqy.ucoo.component

import android.os.Bundle
import com.qyqy.ucoo.base.WrapperDialogFragment
import androidx.fragment.app.Fragment
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.utils.web.JsBridgeWebFragment

class JsBridgeDialogFragment : WrapperDialogFragment() {

    companion object {
        fun newInstance(url: String, height: Int = 0): JsBridgeDialogFragment {
            val args = Bundle()
            val fragment = JsBridgeDialogFragment()
            args.putString(Const.KEY_URL, url)
            args.putInt(Const.KEY_WINDOW_HEIGHT, height)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateFragment(): Fragment {
        return JsBridgeWebFragment.newInstance(
            arguments?.getString(Const.KEY_URL).orEmpty(),
            arguments?.getInt(Const.KEY_WINDOW_HEIGHT) ?: 650
        )
    }
}