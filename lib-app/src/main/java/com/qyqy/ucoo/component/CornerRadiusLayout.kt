package com.qyqy.ucoo.component

import android.content.Context
import android.graphics.Canvas
import android.graphics.Path
import android.util.AttributeSet
import android.view.View
import android.widget.FrameLayout


class CornerRadiusLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : FrameLayout(context, attrs) {

    private val path = Path()
    private var cornerRadius = floatArrayOf()

    fun setCornerRadius(arr: FloatArray) {
        cornerRadius = arr
    }

    override fun drawChild(canvas: Canvas, child: View?, drawingTime: Long): Boolean {
        val result: Boolean
        if (cornerRadius.size < 8) {
            result = super.drawChild(canvas, child, drawingTime)
        } else {
            path.reset()
            path.addRoundRect(0f, 0f, measuredWidth.toFloat(), measuredHeight.toFloat(), cornerRadius, Path.Direction.CW)
            canvas.save()
            canvas.clipPath(path)
            result = super.drawChild(canvas, child, drawingTime)
            canvas.restore()
        }
        return result
    }

}