package com.qyqy.ucoo.component

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.text.TextUtils
import androidx.compose.runtime.CompositionLocalProvider
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.viewmodel.compose.LocalViewModelStoreOwner
import com.overseas.common.ext.startActivity
import com.overseas.common.ext.startPage
import com.qyqy.cupid.ui.AppNavController
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.TracePoints
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.asBase
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.loadingLaunch
import com.qyqy.ucoo.base.loadingLaunchCompat
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.bean.MentionedInfoWrapper
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.pages.CPHouseDestination
import com.qyqy.ucoo.compose.pages.CPHouseNavigator
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.game.DiamondDestination
import com.qyqy.ucoo.compose.presentation.game.DiamondScreenNavigator
import com.qyqy.ucoo.compose.presentation.greets.CustomGreetingsNav
import com.qyqy.ucoo.compose.presentation.select_user.SelectUserNavigator
import com.qyqy.ucoo.compose.presentation.voice.VoiceListNav
import com.qyqy.ucoo.compose.presentation.wedding.BookingNow
import com.qyqy.ucoo.compose.presentation.wedding.WeddingNavigator
import com.qyqy.ucoo.compose.presentation.welfare_center.WelfareCenterNavigator
import com.qyqy.ucoo.compose.ui.dialogs.FirstRechargeDialog
import com.qyqy.ucoo.compose.ui.dialogs.MatchAlert
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.home.HomeActivity
import com.qyqy.ucoo.home.main.features.MainFeatureManager
import com.qyqy.ucoo.http.ApiResponse
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.bean.RoomTokenRequest
import com.qyqy.ucoo.im.bean.isPrivate
import com.qyqy.ucoo.im.chat.ChatActivity
import com.qyqy.ucoo.im.chat.ChatFragment
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.im.room.EntertainmentFragment
import com.qyqy.ucoo.map.Container
import com.qyqy.ucoo.mine.UserProfileActivity
import com.qyqy.ucoo.mine.dressup.MineDressUpActivity
import com.qyqy.ucoo.runWithTopActivity
import com.qyqy.ucoo.sIsSignIn
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.tribe.TribeSquareActivity
import com.qyqy.ucoo.tribe.TribeUserPanelDialogFragment
import com.qyqy.ucoo.tribe.TribeUserPanelDialogFragment.Companion.makeShow
import com.qyqy.ucoo.tribe.launchTribeActivity
import com.qyqy.ucoo.tribe.model.ComposeSendGiftViewModel
import com.qyqy.ucoo.user.RechargeDialogFragment
import com.qyqy.ucoo.user.UserRepository
import com.qyqy.ucoo.utils.GoogleBillingHelper
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.utils.RouterHelper
import com.qyqy.ucoo.utils.VoiceRoomHelper
import com.qyqy.ucoo.utils.ifInstance
import com.qyqy.ucoo.utils.isInstance
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import com.qyqy.ucoo.utils.web.JsBridgeWebFragment
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.serialization.json.JsonObject
import retrofit2.http.Body
import retrofit2.http.POST
import java.net.URLDecoder

interface LinkApi {
    @POST("api/common/v1/action")
    suspend fun action(@Body map: Map<String, String>): ApiResponse<JsonObject>
}

internal object AppLinkManager {

    const val BASE_URL = "ucoo://page"
    const val PATH_MAIN = "/main"
    const val PATH_CHAT_ROOM = "/chatroom"
    const val NEWBIE_GIFTPACK = "/newbie_charge"

    var controller: AppNavController? = null

    private val api = createApi<LinkApi>()

    fun open(context: Context, url: String, container: Container = Container()) {
        if (url.isNotEmpty()) {
            open(context, Uri.parse(url), container)
        }
    }

    fun openChatRoom(context: Context, roomId: String, from: Int? = null) {
        var url = "$BASE_URL$PATH_CHAT_ROOM?roomId=$roomId"
        if (from != null) {
            url += "&from=$from"
        }
        open(context, url)
    }

    fun open(context: Context, uri: Uri?, container: Container = Container()) {
        uri ?: return
        if (uri.toString().isEmpty()) {
            toast("jump link is empty...")
            return
        }
        val path = uri.path.orEmpty()
        val authority = uri.authority
        val host = uri.host
        val scheme = uri.scheme
        val action = uri.getQueryParameter("action").orEmpty()
        val fullscreen = container.heightRatio <= 0
        val activity = ActivityLifecycle.topActivity ?: return
        val screenHeight: Int = container.heightRatio
        LogUtil.i(
            "url:$uri" +
                    "\n=====================" +
                    "path:$path," +
                    "auth:$authority," +
                    "host:$host," +
                    "sch:$scheme," +
                    "action:$action"
        )
        if ("action" == authority) {
            handleAction(context, uri, path)
            return
        }
        if (TextUtils.equals(scheme, "ucoo")) {
            when (path) {
                //婚礼殿堂首页
                "/wedding_home" -> {
                    val from = uri.getQueryParameter("from").orEmpty().toIntOrNull() ?: 0
                    WeddingNavigator.navigate(context, from = from)
                }
                //婚礼墙
                "/wedding_wall" -> {
                    val onlySelf = uri.getQueryParameter("only_self").orEmpty().lowercase().toBooleanStrictOrNull() ?: false
                    WeddingNavigator.startWeddingWall(context, onlySelf)
                }
                //婚礼预告
                "/wedding_booking" -> {
                    val onlySelf = uri.getQueryParameter("only_self").orEmpty().lowercase().toBooleanStrictOrNull() ?: false
                    WeddingNavigator.startWeddingBooking(context, onlySelf)
                }

                "/wedding_reservation" -> {
                    val rid = uri.getQueryParameter("rid")?.toIntOrNull() ?: return
                    runWithTopActivity {
                        showComposeDialog { dialog ->
                            CompositionLocalProvider(LocalViewModelStoreOwner provides this as ViewModelStoreOwner) {
                                BookingNow(ringLightId = rid) {
                                    dialog.dismiss()
                                }
                            }
                        }
                    }
                }
                //亲密小屋
                "/cproom" -> {
                    val roomId = uri.getQueryParameter("roomId")?.toIntOrNull().orDefault(0)
                    CPHouseNavigator.navigate(context, roomId)
                }

                //亲密小屋装扮
                "/cproomdressup" -> {
                    val roomId = uri.getQueryParameter("roomId")?.toIntOrNull().orDefault(0)
                    CPHouseNavigator.navigate(context, roomId, CPHouseDestination.HouseDressUpDest)
                }

                "/chatroom" -> {
                    val roomId = uri.getQueryParameter("roomId")?.toIntOrNull().orDefault(0)
                    val from = uri.getQueryParameter("from")?.toIntOrNull()
                    //私密小屋
                    val isPrivateRoom =
                        uri.getQueryParameter("is_private_room")?.toBoolean() ?: false
                    if (activity is ChatRoomActivity) {
                        val room = ChatRoomActivity.getRoom(activity)
                        if (room != null && (room.isPrivate == isPrivateRoom) && room.roomId == roomId) {
                            return
                        }
                    }
                    val act = activity as? BaseActivity
                    suspend fun openRoom() {
                        if (from != null) {
                            Analytics.reportClickEvent("audio_room_enter", extra = "{\"from\":${from},\"room_id\":${roomId}}", useShushu = true)
                        }
                        VoiceRoomHelper.joinVoiceChatRoom(
                            roomId,
                            RoomTokenRequest(isPrivateRoom),
                            false
                        ) {
                            putExtra(Const.KEY_ENTER_ACTION, action)
                            putExtra(
                                Const.KEY_GIFT_TAB_ID,
                                uri.getQueryParameter("gift_tab")?.toIntOrNull()
                                    .orDefault(-100)
                            )
                            putExtra(
                                Const.KEY_GIFT_ID,
                                uri.getQueryParameter("gift_id")?.toIntOrNull()
                                    .orDefault(-100)
                            )
                        }
                    }
                    act?.loadingLaunch {
                        openRoom()
                    } ?: run {
                        appCoroutineScope.launch {
                            openRoom()
                        }
                    }
                }

                "/main" -> {
                    val tab = uri.getQueryParameter("tab").orEmpty()
                    val subTab = uri.getQueryParameter("subTab")?.toIntOrNull().orDefault(-1)
                    val router = RouterHelper.sendCloseEvent {
                        it.isInstance<HomeActivity>().not()
                    }
                    val mineTab = uri.getQueryParameter("mine_tab").orEmpty()
                    appCoroutineScope.launch {
                        delay(200)
                        router.release()
                        runIfLogin {
                            HomeActivity.homeTabSelector.emit(tab)
                            EntertainmentFragment.selectTabId = subTab
                            if (mineTab.isNotEmpty()) {
                                RouterHelper.mineTabFlow.emit(mineTab)
                            }
                            if (action == "refresh") {
                                delay(200)
                                ActivityLifecycle.topActivity.ifInstance<HomeActivity> {
                                    it.performRefresh()
                                }
                            }
                        }
                    }
                }

                //新人任务
                "/newbie_task" -> {
                    runIfLogin {
                        activity.also {
                            WelfareCenterNavigator.navigate(it)
                        }
                    }
                }
                //私聊
                "/private_chat" -> {
                    runIfLogin {
                        val userId = uri.getQueryParameter("userid").orEmpty()
                        val giftId = uri.getQueryParameter("gift_id")?.toIntOrNull() ?: -100
                        // tabId 定位
                        val giftTab = uri.getQueryParameter("gift_tab")?.toIntOrNull() ?: -100
                        //礼物数量
                        val giftCount = uri.getQueryParameter("gift_count")?.toIntOrNull()?:1
                        val act = action.let {
                            when (it) {
                                "show_cp_dialog" -> 2
                                "show_private_room_dialog" -> 1
                                "open_gift_panel" -> ChatFragment.ACTION_SHOW_GIFT_PANEL
                                else -> -1
                            }
                        }
                        activity.run {
                            startActivity(
                                ChatActivity.createIntent(context, AppUser(userId), act)
                                    .also {
                                        it.putExtra(Const.KEY_GIFT_ID, giftId)
                                        it.putExtra(Const.KEY_GIFT_TAB_ID, giftTab)
                                        it.putExtra(Const.KEY_GIFT_COUNT, giftCount)
                                    })
                        }
                    }
                }
                //部落广场
                "/tribe_square" -> {
                    activity.run {
                        startPage<TribeSquareActivity>()
                    }
                }
                //"部落大厅"
                "/tribe_hall" -> {
                    activity.let {
                        if (it is BaseActivity) {
                            with(it) {
                                val tribeId = uri.getQueryParameter("tribe_id").orEmpty()
                                launchTribeActivity(tribeId, action)
                            }
                        }
                    }
                }

                "/user_home" -> {
                    activity.run {
                        val userid = uri.getQueryParameter("userid")
                        if (userid.isNullOrEmpty()) {
                            return@run
                        }
                        val userRepo = UserRepository()
                        appCoroutineScope.launch {
                            userRepo.getUserById(userid)?.let { user ->
                                startActivity(UserProfileActivity.createIntent(context, user))
                            }
                        }
                    }
                }

                "/choose_contact" -> {
                    SelectUserNavigator.navigate(
                        context,
                        uri.getQueryParameter("subject"),
                        uri.getQueryParameter("action_label"),
                        uri.getQueryParameter("action_params")
                    )
                }

                "/voice_list" -> {
                    activity.run {
                        VoiceListNav.start(this)
                    }
                }

                "/webview" -> {
                    val url = uri.getQueryParameter("url")?.let { URLDecoder.decode(it) }.orEmpty()
                    if (url.isNotEmpty()) {
                        open(activity, url)
                    } else {
                        toast("url is empty")
                    }
                }

                "/sayhi" -> {//自定义打招呼
                    CustomGreetingsNav.navigate(context)
                }

                "/diamond_store" -> {
                    DiamondScreenNavigator.navigate(context, DiamondDestination.Mall)
                }

                NEWBIE_GIFTPACK -> {//新人首充礼包弹窗
                    Analytics.reportClickEvent(TracePoints.FIRST_CHARGE_PENDANT)
                    activity.loadingLaunchCompat { coroutineScope ->
                        GoogleBillingHelper.fetchFirstRecharge(coroutineScope).onSuccess {
                            FirstRechargeDialog.show(this, it)
                        }.toastError()
                    }
                }

                "/charge_alert" -> {
                    (activity as? FragmentActivity)?.also {
                        RechargeDialogFragment.newInstance("link")
                            .show(it.supportFragmentManager, "RechargeDialogFragment")
                    }
                }

                "/link_request" -> {
                    activity.loadingLaunchCompat {
                        val paramStr = uri.getQueryParameter("param_str")
                        val from = uri.getQueryParameter("from") ?: ""
                        paramStr?.let { params ->
                            //done 2.41.0 link_request需要携带from参数
                            runApiCatching {
                                api.action(mapOf("params" to params, "from" to from))
                            }
                                .toastError()
                                .onSuccess {
                                    val link = it.parseValue<String>("client_jump_link")
                                        ?: return@onSuccess
                                    open(context, link)
                                }
                        }
                    }
                }


                "/web_frame" -> {
                    ActivityLifecycle.startTopActivity?.asBase?.also {
                        WebFrameDialogFragment.show(it.supportFragmentManager, uri.toString())
                    }
                }


                "/dress_up" -> {
                    ActivityLifecycle.startTopActivity?.apply {
                        MineDressUpActivity::class.java.startActivity()
                    }
                }

                else -> {
                    if (!MainFeatureManager.process(uri)) {
                        toast("not support -> $uri")
                    }
                }
            }
        } else {
            if (container.t == 1) {
                showComposeDialog { composeDialog ->
                    MatchAlert(
                        title = container.title,
                        buttonText = container.buttonText,
                        link = uri.toString()
                    ) {
                        composeDialog.dismiss()
                    }
                }
            } else {
                if (fullscreen) {
                    context.startActivity(
                        JsBridgeWebActivity.newIntent(context, uri.toString()).also {
                            if (context !is BaseActivity) {
                                it.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                            }
                        })
                } else {
                    if (activity is BaseActivity) {
                        activity.showFragment(
                            JsBridgeWebFragment.newInstance(
                                uri.toString(),
                                height = screenHeight
                            ), true
                        )
                    }
                }
            }
        }
    }

    fun parseTaskUrl(originUrl: String): Pair<String, Map<String, String>>? {
        if (!originUrl.startsWith(AppLinkManager.BASE_URL)) {
            return originUrl to mapOf()
        }
        return parseUri(originUrl)
    }

    private fun parseUri(uriString: String): Pair<String, Map<String, String>> {
        // 解析URI字符串
        val uri = Uri.parse(uriString)

        val path = uri.path.orEmpty()

        // 获取参数并存入HashMap
        val paramsMap = HashMap<String, String>()
        val queryParams = uri.queryParameterNames
        for (key in queryParams) {
            paramsMap[key] = uri.getQueryParameter(key) ?: ""
        }

        return Pair(path, paramsMap)
    }

    private fun handleAction(context: Context, uri: Uri, path: String) {
        when (path) {
            "/${MentionedInfoWrapper.SCENE_CHAT_GROUP}/press_at_user" -> {
                val uid = uri.getQueryParameter("uid") ?: return
                val sceneId = uri.getQueryParameter("sceneid") ?: return
                val roomId = sceneId.toIntOrNull() ?: return
                val act = ActivityLifecycle.topActivity as? BaseActivity
                act?.run {
                    TribeUserPanelDialogFragment.newInstance(
                        uid,
                        roomId,
                        ComposeSendGiftViewModel.FROM_GROUP
                    )
                        .makeShow(this.supportFragmentManager, "chatgroup_user_panel")
                }
            }

            else -> {}
        }
    }

    private inline fun runIfLogin(block: () -> Unit) {
        if (sIsSignIn) {
            block.invoke()
        }
    }
}