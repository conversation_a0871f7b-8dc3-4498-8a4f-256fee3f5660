package com.qyqy.ucoo.component


import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class WebFrameInfo(
    val cancelable: Boolean = true,
    val gravity: String = "center",
    val height: Int = 600,
    val radius: Radius? = null,
    @SerialName("target_url")
    val targetUrl: String = "",
    val width: Int = -1
) {
    @Serializable
    data class Radius(
        @SerialName("left_bottom")
        val leftBottom: Int = 0,
        @SerialName("left_top")
        val leftTop: Int = 0,
        @SerialName("right_bottom")
        val rightBottom: Int = 0,
        @SerialName("right_top")
        val rightTop: Int = 0
    ) {
        val array: FloatArray
            get() = floatArrayOf(
                leftTop.toFloat(), leftTop.toFloat(),
                rightTop.toFloat(), rightTop.toFloat(),
                rightBottom.toFloat(), rightBottom.toFloat(),
                leftBottom.toFloat(), leftBottom.toFloat()
            )
    }
}