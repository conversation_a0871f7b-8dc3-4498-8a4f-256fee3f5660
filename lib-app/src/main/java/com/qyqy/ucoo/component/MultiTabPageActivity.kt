package com.qyqy.ucoo.component

import android.content.Context
import android.content.Intent
import com.overseas.common.ext.FragmentCreator
import com.overseas.common.utils.dp
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseMultiTabPageActivity
import com.qyqy.ucoo.tribe.rank.ContributeRankListFragment
import java.io.Serializable


class MultiTabPageActivity : BaseMultiTabPageActivity() {

    enum class TabPage : Serializable {
        TribeRankList,

    }

    companion object {
        const val KEY_TAB_PAGE = "key_tab_page"

        @JvmStatic
        fun start(context: Context, tabPage: TabPage) {
            val starter = Intent(context, MultiTabPageActivity::class.java)
                .putExtra(KEY_TAB_PAGE, tabPage)
            context.startActivity(starter)
        }
    }


    private val tabPage by lazy { intent.getSerializableExtra(KEY_TAB_PAGE) as? TabPage }


    override fun getTabPageConfig(): TabPageConfig {
        tabPage ?: error("TabPage is NULL")
        return when (tabPage) {
            TabPage.TribeRankList -> TribeRankTabPage(this)
            else -> {
                error("not set $tabPage")
            }
        }
    }

    inner class TribeRankTabPage(context: Context) : TabPageConfig {
        override val titleRes: Int = R.string.tribe_rank
        override val fragmentCreators: List<FragmentCreator> = listOf({ ContributeRankListFragment.newInstance(1) }, { ContributeRankListFragment.newInstance(2) })
        override val tabTitles: Array<String> = arrayOf(R.string.rank_daily, R.string.rank_weekly).map { context.getString(it) }.toTypedArray()
    }
}