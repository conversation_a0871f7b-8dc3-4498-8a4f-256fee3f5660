package com.qyqy.ucoo.component

import android.net.Uri
import android.os.Build
import androidx.core.content.FileProvider
import com.qyqy.ucoo.app
import java.io.File

/**
 * <AUTHOR>
 * @date 2023/6/14 17:05
 *
 */
class MyFileProvider : FileProvider() {
    companion object {
//        /external/images/media/12772
        @JvmStatic
        fun getUri(file: File) =
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                getUriForFile(app, "${app.packageName}.my_fileprovider", file)
            } else Uri.fromFile(file)
    }
}