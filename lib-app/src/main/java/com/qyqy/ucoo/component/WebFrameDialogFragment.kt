package com.qyqy.ucoo.component

import android.app.Dialog
import android.net.Uri
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.commit
import com.overseas.common.utils.dp
import com.qyqy.ucoo.BuildConfig

import com.qyqy.ucoo.base.BaseDialogFragment
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.utils.web.JsBridgeWebFragment
import com.qyqy.ucoo.widget.dialog.AppBottomSheetDialog
import com.qyqy.ucoo.widget.dialog.AppDialog
import com.qyqy.ucoo.widget.dialog.AppTopSheetDialog
import java.net.URLDecoder

class WebFrameDialogFragment : BaseDialogFragment() {

    companion object {
        private fun newInstance(link: String): WebFrameDialogFragment {
            val args = Bundle()
            args.putString("web_link", link)
            val fragment = WebFrameDialogFragment()
            fragment.arguments = args
            return fragment
        }

        fun show(fragmentManager: FragmentManager, link: String) {
            val frag = (fragmentManager.findFragmentByTag(link)) as? WebFrameDialogFragment
            if (frag == null) {
                newInstance(link).show(fragmentManager, link)
            }
        }
    }

    val link by lazy { arguments?.getString("web_link") }

    private val webFrameInfo by lazy {
        link?.let { lk ->
            val url = runCatching { Uri.parse(lk) }.getOrNull()
            val json = url?.getQueryParameter("info")?.let { URLDecoder.decode(it) } ?: "{}"
            sAppJson.decodeFromString<WebFrameInfo>(json)
//        } ?: WebFrameInfo(targetUrl = "https://api.ucoofun.com")
        } ?: WebFrameInfo(targetUrl = BuildConfig.API_HOST)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        LogUtil.d("web frame info : $webFrameInfo")
        if (webFrameInfo.targetUrl.isNotEmpty()) {
            val gravity = webFrameInfo.gravity
            val width = webFrameInfo.width
            val height = webFrameInfo.height
            val cancelable = webFrameInfo.cancelable
            val context = requireContext()
            val applyParams: (AppDialog.Builder<*>) -> Unit = {
                if (width == -1) {
                    it.setWidth(-1)
                } else {
                    it.setWidth(width.dp)
                }
                it.setHeight(height.dp)
                it.setView(CornerRadiusLayout(context).also { view ->
                    view.id = View.generateViewId()
                    childFragmentManager.commit {
                        val url = URLDecoder.decode(webFrameInfo.targetUrl)
                        replace(view.id, JsBridgeWebFragment.newInstance(url))
                    }
                    view.layoutParams =
                        ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT)
                    LogUtil.d("web frame layoutparams :${view.layoutParams}")
                    applyRadius(view)
                })
            }
            this.isCancelable = cancelable
            return when (gravity) {
                "top" -> AppTopSheetDialog.Builder(context).apply(applyParams)
                "bottom" -> AppBottomSheetDialog.Builder(context).apply(applyParams)
                else -> AppDialog.Builder(context).apply(applyParams)
            }.create()
        }

        return super.onCreateDialog(savedInstanceState)
    }


    private fun applyRadius(shapeFrameLayout: CornerRadiusLayout) {
        val arr = webFrameInfo.radius?.array ?: floatArrayOf()
        if (arr.isEmpty() || arr.size < 4) {
            return
        }
        shapeFrameLayout.setCornerRadius(floatArrayOf(arr[0], arr[0], arr[1], arr[1], arr[2], arr[2], arr[3], arr[3]))
    }

}