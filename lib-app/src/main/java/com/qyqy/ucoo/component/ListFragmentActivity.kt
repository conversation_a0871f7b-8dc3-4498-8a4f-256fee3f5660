package com.qyqy.ucoo.component

import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.annotation.IdRes
import androidx.annotation.StringRes
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentTransaction
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.overseas.common.ext.viewBinding
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.databinding.ActivitySingleFragmentBinding
import com.qyqy.ucoo.feat.like.ListSbLikeYouFragment
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.sUserFlow
import com.qyqy.ucoo.selfIsVip
import com.qyqy.ucoo.tribe.task.sign.UnsignedTribeMemberListFragment
import com.qyqy.ucoo.user.vip.VipForCheckLikesFragment
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import java.io.Serializable

class ListFragmentActivity : BaseActivity() {

    enum class FragmentEnum : Serializable {
        Unsign,//部落未签到成员
        UnGiveGift,//为送礼成员
        SomeoneLikeYou,//喜欢你的人

    }

    companion object {
        const val key_fragment_enum = "key_fragment_enum"

        @JvmStatic
        fun newIntent(context: Context, fragmentEnum: FragmentEnum) =
            Intent(context, ListFragmentActivity::class.java)
                .putExtra(key_fragment_enum, fragmentEnum)
    }

    private val binding by viewBinding(ActivitySingleFragmentBinding::inflate)

    private val fragmentEnum by lazy { intent.getSerializableExtra(key_fragment_enum) as? FragmentEnum }
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        val fragmentConfig = getFragmentConfig()
        binding.titleBar.title = getString(fragmentConfig.titleRes)
        supportFragmentManager.beginTransaction()
            .add(binding.flContainer.id, fragmentConfig.createFragment(), "fragment_${fragmentConfig::class.java.simpleName}")
            .apply {
                fragmentConfig.configTransaction(this@ListFragmentActivity, this, binding.flContainerBottom.id)
            }
            .commit()
    }

    private fun getFragmentConfig(): FragmentConfig {
        fragmentEnum ?: error("fragment config is NULL")
        return when (fragmentEnum) {
            FragmentEnum.Unsign -> UnSignMemberConfig()
            FragmentEnum.SomeoneLikeYou -> LikeYouConfig()
            else -> error("$fragmentEnum not set config")
        }
    }


    interface FragmentConfig {
        @get:StringRes
        val titleRes: Int

        fun createFragment(): Fragment

        fun configTransaction(activity: ListFragmentActivity, fragmentTransaction: FragmentTransaction, @IdRes containerId: Int) {

        }

    }

    inner class LikeYouConfig : FragmentConfig {
        override val titleRes: Int
            get() = R.string.people_like

        override fun createFragment(): Fragment {
            return ListSbLikeYouFragment()
        }

        override fun configTransaction(activity: ListFragmentActivity, fragmentTransaction: FragmentTransaction, @IdRes containerId: Int) {
            if (selfIsVip.not()) {
                val fragment = VipForCheckLikesFragment()
                fragmentTransaction.add(containerId, fragment, "vip_for_check")
                activity.lifecycleScope.launch {
                    sUserFlow.map { it.isVip }
                        .flowWithLifecycle(lifecycle)
                        .distinctUntilChanged()
                        .collectLatest {
                            if (it) {
                                activity.supportFragmentManager.beginTransaction()
                                    .remove(fragment)
                                    .commit()
                            }
                        }

                }
            }
        }
    }

    inner class UnSignMemberConfig : FragmentConfig {
        override val titleRes: Int = R.string.member_unsign

        override fun createFragment(): Fragment {
            return UnsignedTribeMemberListFragment()
        }
    }
}