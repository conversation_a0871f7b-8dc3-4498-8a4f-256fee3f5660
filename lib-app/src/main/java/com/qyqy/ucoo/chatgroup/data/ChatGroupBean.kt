package com.qyqy.ucoo.chatgroup.data


import android.os.Parcelable
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import androidx.annotation.Keep
import kotlinx.parcelize.Parcelize

@Keep
@Serializable
data class ChatGroupBean(
    @SerialName("chatgroups")
    val chatgroups: List<Chatgroup> = listOf()
) {
    @Keep
    @Parcelize
    @Serializable
    data class Chatgroup(
        @SerialName("avatar_url")
        val avatarUrl: String = "",
        @SerialName("id")
        val id: Int = 0,
        @SerialName("intro")
        val intro: String = "",
        @SerialName("is_need_review")
        val isNeedReview: Boolean = false,
        @SerialName("member_cnt")
        val memberCnt: Int = 0,
        @SerialName("name")
        val name: String = "",
        @SerialName("public_id")
        val publicId: String = ""
    ): Parcelable
}