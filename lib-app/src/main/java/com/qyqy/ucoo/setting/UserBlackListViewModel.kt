package com.qyqy.ucoo.setting

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.viewModelScope
import com.qyqy.ucoo.base.BaseViewModel
import com.qyqy.ucoo.base.DataState
import com.qyqy.ucoo.base.LoadMoreState
import com.qyqy.ucoo.base.RefreshState
import com.qyqy.ucoo.base.ToastEffect
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.user.UserApi
import kotlinx.coroutines.launch

class UserBlackListViewModel : BaseViewModel<UserBlackListContract.Event, UserBlackListContract.State, ToastEffect>() {

    private val api = createApi(UserApi::class.java)

    private val _loaded: MutableState<Boolean> = mutableStateOf(false)
    val loaded: State<Boolean> = _loaded

    override fun createInitialState(): UserBlackListContract.State {
        return UserBlackListContract.State()
    }

    override fun handleEvent(event: UserBlackListContract.Event) {
        when (event) {
            is UserBlackListContract.Event.Refresh -> {
                refresh()
            }

            is UserBlackListContract.Event.LoadMore -> {
                loadMore()
            }

            is UserBlackListContract.Event.OnBlackClicked -> {
                updateBlackShip(event.userId, event.black)
            }

        }
    }

    private fun refresh() {
        setState {
            copy(refreshState = RefreshState.Refreshing)
        }
        viewModelScope.launch {
            runApiCatching {
                api.getBlackList()
            }.apply {
                onSuccess {
                    setState {
                        copy(
                            refreshState = RefreshState.Idle,
                            loadMoreState = if (it.blacklist.isEmpty()) LoadMoreState.Done else LoadMoreState.Idle,
                            listState = DataState.Success(it.blacklist)
                        )
                    }
                }
                onFailure {
                    setState {
                        copy(refreshState = RefreshState.Idle)
                    }
                    setEffect {
                        ToastEffect(it.message)
                    }
                }
            }
            _loaded.value = true
        }
    }

    private fun loadMore() {
        setState {
            copy(loadMoreState = LoadMoreState.Loading)
        }
        viewModelScope.launch {
            runApiCatching {
                api.getBlackList(currentState.listState.getOrNull()?.lastOrNull()?.relationId ?: 0)
            }.apply {
                onSuccess {
                    setState {
                        copy(
                            loadMoreState = if (it.blacklist.isEmpty()) LoadMoreState.Done else LoadMoreState.Idle,
                            listState = DataState.Success(buildList {
                                addAll(listState.getOrNull() ?: emptyList())
                                addAll(it.blacklist)
                            })
                        )
                    }
                }
                onFailure {
                    setState {
                        copy(loadMoreState = LoadMoreState.Idle)
                    }
                    setEffect {
                        ToastEffect(it.message)
                    }
                }
            }
        }
    }

    private fun updateBlackShip(userId: String, black: Boolean) {
        viewModelScope.launch {
            val params = mapOf<String, Any>(
                "userid" to userId.toInt(),
                "black" to black.toString()
            )
            runApiCatching {
                api.updateBlackShip(params)
            }.apply {
                onSuccess {
                    val list = currentState.listState.getOrNull() ?: return@apply
                    setState {
                        val newList = if (!black) {
                            list.filter {
                                it.id != userId
                            }
                        } else {
                            list
                        }
                        copy(listState = DataState.Success(newList))
                    }
                }
                onFailure {
                    setEffect {
                        ToastEffect(it.message)
                    }
                }
            }
        }
    }


}