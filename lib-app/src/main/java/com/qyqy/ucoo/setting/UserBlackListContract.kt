package com.qyqy.ucoo.setting

import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.base.DataState
import com.qyqy.ucoo.base.LoadMoreState
import com.qyqy.ucoo.base.RefreshState
import com.qyqy.ucoo.base.UiEvent
import com.qyqy.ucoo.base.UiState

class UserBlackListContract {

    sealed class Event : UiEvent {
        object Refresh : Event()
        object LoadMore : Event()
        class OnBlackClicked(val userId: String, val black: Boolean) : Event()
    }

    data class State(
        val refreshState: RefreshState = RefreshState.Idle,
        val loadMoreState: LoadMoreState = LoadMoreState.Idle,
        val listState: DataState<List<UserInfo>> = DataState.Idle(),
    ) : UiState
}