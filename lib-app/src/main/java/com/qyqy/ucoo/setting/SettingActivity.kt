package com.qyqy.ucoo.setting

import android.graphics.Color
import android.net.Uri
import android.os.Bundle
import androidx.appcompat.widget.AppCompatTextView
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.res.stringResource
import androidx.core.view.WindowCompat
import androidx.core.view.isVisible
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.elvishew.xlog.LogUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.overseas.common.ext.click
import com.overseas.common.ext.clickGoto
import com.overseas.common.ext.startActivity
import com.overseas.common.ext.viewBinding
import com.overseas.common.sntp.SNTPManager
import com.qyqy.cupid.ui.dialog.DialogUIStyle
import com.qyqy.cupid.ui.dialog.EnsureContent
import com.qyqy.cupid.ui.home.mine.BlackListNavigator
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isHighQuality
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.loadingLaunch
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.MainNavigator
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.oss.OSSObject
import com.qyqy.ucoo.databinding.ActivitySettingBinding
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.login.LoginReason
import com.qyqy.ucoo.sAppKV
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.self
import com.qyqy.ucoo.setting.account_bind.AccountBindActivity
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.UserRepository
import com.qyqy.ucoo.utils.LogUtil
import com.qyqy.ucoo.utils.taskflow.taskShowWithStack
import com.qyqy.ucoo.widget.dialog.DatePickerDialog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.text.SimpleDateFormat
import java.util.Locale
import java.util.concurrent.TimeUnit

class SettingActivity : BaseActivity() {

    private val binding by viewBinding(ActivitySettingBinding::inflate)

    private var debugMode = !isRelease || sUser.isHighQuality || sAppKV.getBoolean("debugMode")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)
        if (resources.getBoolean(R.bool.isLightTheme)) {
            window?.also {
                it.statusBarColor = getColor(R.color.colorStatusBar)
                WindowCompat.getInsetsController(it, it.decorView).isAppearanceLightStatusBars =
                    true
            }
            binding.toolbar.root.setBackgroundColor(Color.WHITE)
            binding.content.setBackgroundColor(Color.WHITE)
            binding.content1.setBackgroundColor(Color.WHITE)
            binding.layoutAccountBind.isVisible = false
            binding.layoutTrans.isVisible = false
//            binding.tvLanguageSettings.isVisible = false
        }
        with(binding) {
            toolbar.title.text = getString(R.string.setting)
            applySurprise(toolbar.title)
            toolbar.btnEnd.isVisible = false
            toolbar.btnStart.click {
                onBackPressedDispatcher.onBackPressed()
            }
            //魅力值 仅女性用户可见
            tvCharm.isVisible = false
            tvCharm.clickGoto<CharmActivity>()

            tvLanguageSettings.click {
                MainNavigator.start(this@SettingActivity, MainNavigator.LANGUAGE_SETTINGS)
            }

            lifecycleScope.launch {
                UIConfig.configFlow.collectLatest {
                    if (it.isSuccess) {
                        val conf = it.get()
                        swBtn.isChecked = conf.inAppNotifyEnabled
                        swBtn2.isChecked = conf.micAsrEnabled
                        swBtnCleaner.isChecked = conf.autoCleanConversation
                    }
                }
            }

            swBtn.setOnCheckedChangeListener { _, isChecked ->
                UIConfig.updateAppNotifyEnabled(isChecked)
            }

            swBtn2.setOnCheckedChangeListener { _, isChecked ->
                UIConfig.updateMicAsrEnabled(isChecked)
            }
            layoutConversationCleaner.isVisible = self.isBoy

            swBtnCleaner.setOnCheckedChangeListener { _, isChecked ->
                lifecycleScope.launch {
                    UIConfig.updateConversationCleanerState(isChecked)
                        .onFailure {
                            //恢复原来状态
//                            swBtnCleaner.isChecked = !isChecked
                        }.toastError()
                }
            }
            layoutBlackUser.click {
                if (isLightTheme()) {
                    BlackListNavigator.navigate(this@SettingActivity)
                } else {
                    UserBlackListActivity::class.java.startActivity()
                }
            }
            layoutFeedBack.click {
                FeedbackActivity::class.java.startActivity()
            }
            layoutAbout.click {
                AboutActivity::class.java.startActivity()
            }
            logout.click {
                lifecycleScope.launch {
                    app.accountManager.logout(LoginReason.Exit)
                }
            }
            logoff.click {
                showLogOffPrompt()
            }
            layoutAccountBind.click {
                AccountBindActivity::class.java.startActivity()
            }
        }
        lifecycleScope.launch {
            launch {
                SettingsRepository.fetchSystemSettings()
            }
            launch {
                app.accountManager.accountBindFlow
                    .flowWithLifecycle(lifecycle)
                    .distinctUntilChanged()
                    .collectLatest {
                        binding.layoutAccountBind.isVisible = it.showBindAccountButton
                    }
            }
        }
        app.accountManager.refreshBindState()

    }


    //    private var clickCount = 0
    private fun applySurprise(title: AppCompatTextView) {
        if (!isRelease) {
            title.setOnClickListener {
                DebugActivity::class.java.startActivity()
            }
        } else if (!debugMode) {
            var count = 0
            title.setOnClickListener {
                count++
                if (count >= 7) {
                    title.setOnClickListener(null)
                    debugMode = true
                    sAppKV.putBoolean("debugMode", true)
                    checkDebugMode()
                    toast("已打开开发者模式")
                } else if (count == 4) {
                    toast("再点击3下进入开发者模式")
                }
            }
        }
        checkDebugMode()
    }

    private fun checkDebugMode() {
        if (!debugMode) {
            return
        }
        with(binding) {
            clearConversation.isVisible = true

            val sdf = SimpleDateFormat("yyy-MM-dd", Locale.CHINA)

            fun confirmClear(startTime: String, endTime: String) {
                MaterialAlertDialogBuilder(this@SettingActivity)
                    .setTitle("确定要清理最后活跃时间在以下时间段的会话吗？")
                    .setMessage("起始时间: $startTime\n起始时间: $endTime")
                    .setNegativeButton(R.string.rc_cancel, null)
                    .setPositiveButton(R.string.confirm) { _, _ ->
//                        IMManager.deleteConversations(
//                            sdf.parse(startTime).time,
//                            sdf.parse(endTime).time.plus(TimeUnit.DAYS.toMillis(1))
//                        )
                    }.taskShowWithStack()
            }

            var startTime = ""
            var endTime = ""

            fun show(action: Int) {
                DatePickerDialog.Builder(this@SettingActivity)
                    .setDate(
                        dateStart = "2023-06-01",
                        dateEnd = "2030-01-01",
                        datePosition = sdf.format(System.currentTimeMillis())
                    ).setSelectDateListener { date ->
                        if (action == 0) {
                            startTime = date
                        } else if (action == 1) {
                            endTime = date
                        }
                    }.apply {
                        setOnDismissListener {
                            if (action == 0) {
                                if (startTime.isNotEmpty()) {
                                    toast("请选择要清理会话的截止时间")
                                    show(1)
                                } else {
                                    toast("已取消")
                                }
                            } else if (action == 1) {
                                if (startTime.isNotEmpty() && endTime.isNotEmpty()) {
                                    confirmClear(startTime, endTime)
                                } else {
                                    toast("已取消")
                                }
                            }
                        }
                    }.taskShowWithStack()
            }

            clearConversation.click {
                toast("请选择要清理会话的起始时间")
                startTime = ""
                endTime = ""
                show(0)
            }

            uploadLog.isVisible = true
            uploadLog.click {
                loadingLaunch {
                    try {
                        val outFile = withContext(Dispatchers.IO) {
                            val outFile = File.createTempFile(
                                "${SNTPManager.now()}_log",
                                ".zip",
                                app.getExternalFilesDir(null) ?: app.filesDir
                            )
                            LogUtils.compress(LogUtil.xLogPath, outFile.absolutePath)
                            outFile
                        }
                        UserRepository().uploadAndroidLogByAliyun(
                            outFile.name,
                            OSSObject(outFile.absolutePath, Uri.fromFile(outFile))
                        )
                        toast("上传成功")
                    } catch (_: Exception) {
                    }
                }
            }
        }
    }

    private fun showLogOffPrompt() {
//        MaterialAlertDialogBuilder(this)
//            .setTitle(getString(R.string.want_to_destroy_account))
//            .setNegativeButton(R.string.rc_cancel, null)
//            .setPositiveButton(R.string.confirm) { _, _ ->
//                lifecycleScope.launch {
//                    try {
//                        showLoading()
//                        app.accountManager.logOff()
//                    } finally {
//                        hideLoading()
//                    }
//                }
//            }.taskShowWithStack()

//        2.31.0
        showComposeDialog { d ->
            val scope = rememberCoroutineScope()
            EnsureContent(
                title = stringResource(id = R.string.destroy_account),
                content = stringResource(id = R.string.tip_destroy_account),
                leftButtonText = stringResource(id = R.string.destroy_account_ok),
                rightButtonText = stringResource(id = R.string.取消),
                onLeftClick = {
                    scope.launch {
                        try {
                            showLoading()
                            app.accountManager.logOff()
                        } finally {
                            hideLoading()
                            d.dismiss()
                        }
                    }
                },
                onRightClick = { d.dismiss() },
                style = DialogUIStyle.ucoo
            )
        }
    }
}