package com.qyqy.ucoo.setting

import android.os.Bundle
import android.view.View
import androidx.core.view.isVisible
import androidx.fragment.app.activityViewModels
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.overseas.common.ext.allRadiusDrawable
import com.overseas.common.ext.clickPosition
import com.overseas.common.ext.clickWithEffect
import com.overseas.common.ext.viewBinding
import com.overseas.common.paginate.Paginate
import com.overseas.common.utils.id2Color
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.UserInfo
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.loadAgeGender
import com.qyqy.ucoo.account.toAppUser
import com.qyqy.ucoo.base.collectLoading
import com.qyqy.ucoo.base.collectToast
import com.qyqy.ucoo.base.collectValue
import com.qyqy.ucoo.databinding.FragmentRefreshItemListBinding
import com.qyqy.ucoo.databinding.LayoutListItemBlackUserBinding
import com.qyqy.ucoo.glide.loadAvatar
import com.qyqy.ucoo.mine.UserProfileActivity
import com.qyqy.ucoo.user.UserListFragment
import com.qyqy.ucoo.utils.taskflow.taskShowWithStack
import com.qyqy.ucoo.widget.SpiritViewHolder
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged

class UserBlackListFragment : UserListFragment<LayoutListItemBlackUserBinding>(R.layout.fragment_refresh_item_list) {

    private val binding by viewBinding(FragmentRefreshItemListBinding::bind)

    private val viewModel by activityViewModels<UserBlackListViewModel>()

    private val shapeDrawable = allRadiusDrawable(id2Color(R.color.purple_300_alpha_45), 200f)

    override val recyclerView: RecyclerView
        get() = binding.recyclerView


    override val callbacks: Paginate.Callbacks = object : Paginate.Callbacks {

        override fun onLoadMore() {
            viewModel.sendEvent(UserBlackListContract.Event.LoadMore)
        }

        override fun isLoading(): Boolean {
            return viewModel.currentState.let {
                it.refreshState.isLoading || it.loadMoreState.isLoading
            }
        }

        override fun hasLoadedAllItems(): Boolean {
            return viewModel.currentState.loadMoreState.isDone
        }

    }


    override val itemLayoutId: Int = R.layout.layout_list_item_black_user

    override val providerViewBinding: (View) -> LayoutListItemBlackUserBinding = {
        LayoutListItemBlackUserBinding.bind(it)
    }

    override fun onCreateViewHolder(viewHolder: SpiritViewHolder, viewBinding: LayoutListItemBlackUserBinding) {
        viewBinding.btnBlack.background = shapeDrawable
        viewBinding.btnBlack.clickWithEffect(isBackground = false) {
            val item = spirit.getItem<UserInfo>(viewHolder.clickPosition ?: return@clickWithEffect)
            MaterialAlertDialogBuilder(requireActivity())
                .setTitle(getString(R.string.confirm_cancel_black))
                .setNegativeButton(R.string.rc_cancel, null)
                .setPositiveButton(R.string.rc_confirm) { _, _ ->
                    viewModel.sendEvent(UserBlackListContract.Event.OnBlackClicked(item.id, false))
                }.taskShowWithStack()
        }
    }

    override val viewBindingItem: SpiritViewHolder.(LayoutListItemBlackUserBinding, UserInfo, Int) -> Unit =
        { viewBinding, item, _ ->
            viewBinding.avatar.loadAvatar(item.avatarUrl)
            viewBinding.name.text = item.nickname
            viewBinding.ageGender.loadAgeGender(item.isBoy, item.age)
            viewBinding.btnBlack.setText(R.string.cancel_add_backlist)
        }

    override fun onClickItemView(position: Int) {
        startActivity(UserProfileActivity.createIntent(requireContext(), spirit.getItem<UserInfo>(position).toAppUser()))
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        with(binding) {
            emptyView.setImageResource(R.drawable.ic_empty_for_black)
            refreshLayout.setOnRefreshListener {
                viewModel.sendEvent(UserBlackListContract.Event.Refresh)
            }
        }

        viewModel.uiState.collectValue({
            it.listState
        }) {
            distinctUntilChanged()
                .collectLatest {
                    spirit.setData(it)
                    binding.emptyLayout.isVisible = it.isEmpty()
                    Glide.with(this@UserBlackListFragment).load( R.drawable.ic_empty_for_conversation).into(binding.emptyView)
                    binding.emptyText.text = getString(R.string.暂无黑名单数据)
                }
        }

        viewModel.uiState.collectLoading({
            it.refreshState
        }) {
            collectLatest {
                binding.refreshLayout.isRefreshing = it.isLoading
            }
        }

        viewModel.uiState.collectLoading({
            it.loadMoreState
        }) {
            collectLatest {
                binding.refreshLayout.isEnabled = !it.isLoading
            }
        }

        viewModel.effect.collectToast()

        viewModel.sendEvent(UserBlackListContract.Event.Refresh)
    }

}
