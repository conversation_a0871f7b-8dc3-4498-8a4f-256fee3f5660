package com.qyqy.ucoo.setting

import android.app.Dialog
import android.os.Bundle
import android.util.Log
import android.view.WindowManager
import androidx.activity.viewModels
import androidx.core.view.isVisible
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.overseas.common.ext.click
import com.overseas.common.ext.viewBinding
import com.overseas.common.utils.dp
import com.overseas.common.utils.screenWidth
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.PageLoadUiState
import com.qyqy.ucoo.base.collectCommonEffect
import com.qyqy.ucoo.core.page.PageLoadEvent
import com.qyqy.ucoo.core.page.PageLoadRecyclerViewHelper
import com.qyqy.ucoo.databinding.ActivityCharmBinding
import com.qyqy.ucoo.databinding.DialogCharmRuleBinding
import com.qyqy.ucoo.databinding.ItemCharmListBinding
import com.qyqy.ucoo.databinding.ItemRuleListBinding
import com.qyqy.ucoo.databinding.LayoutMomentEmptyBinding
import com.qyqy.ucoo.glide.loadAvatar
import com.qyqy.ucoo.mine.UserProfileActivity
import com.qyqy.ucoo.setting.charm.CharmEvent
import com.qyqy.ucoo.setting.charm.CharmRecord
import com.qyqy.ucoo.setting.charm.CharmViewModel
import com.qyqy.ucoo.utils.taskflow.taskShowWithStack
import com.qyqy.ucoo.widget.CommonItemDecoration
import com.qyqy.ucoo.widget.dialog.AppDialog
import com.qyqy.ucoo.widget.dialog.DatePickerDialog
import com.qyqy.ucoo.widget.spirit
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.launch
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlin.reflect.KMutableProperty0

/**
 * 我的积分
 */
class CharmActivity : BaseActivity() {

    private val binding by viewBinding(ActivityCharmBinding::inflate)


    private val dialog: Dialog by lazy {
        val bindingRule = DialogCharmRuleBinding.inflate(layoutInflater)
        val rules = app.resources.getStringArray(R.array.array_charm_rules)
        rules[2] = String.format(rules[2],SettingsRepository.charmValue)
        val list = rules.mapIndexed { index, s -> Pair(index.inc(), s) }
        bindingRule.rvRules.apply {
            addItemDecoration(CommonItemDecoration(bottom = 6.dp))
        }.spirit {
            addViewBindingItemType<Pair<Int, String>, ItemRuleListBinding>(R.layout.item_rule_list)
            { itemRuleListBinding, pair, i ->
                itemRuleListBinding.apply {
                    tvIndex.text = pair.first.toString()
                    tvContent.text = pair.second
                }
            }
            setData(list)
        }
        AppDialog.Builder(this)
            .setHeight(-2)
            .setWidth((app.screenWidth * 0.85f).toInt())
            .setHeight(WindowManager.LayoutParams.WRAP_CONTENT)
            .setView(bindingRule.root)
            .create()
    }

    private val viewModel by viewModels<CharmViewModel>()
    private val pageLoadRecyclerViewHelper by lazy {
        PageLoadRecyclerViewHelper(binding.rvList)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        initSelectDate()
        pageLoadRecyclerViewHelper.apply {
            onLoadMore = {
                viewModel.sendEvent(PageLoadEvent.PageLoadMore)
            }

            initPaginate(isLoading = {
                when (viewModel.uiState.value) {
                    PageLoadUiState.Loading, PageLoadUiState.Refreshing -> true
                    else -> false
                }
            }, hasLoadedAllItems = {
                viewModel.hasLoadAllItems
            })
        }
        val spirit = binding.rvList.spirit {
            setEmptyProvider(false) {
                LayoutMomentEmptyBinding.inflate(layoutInflater).apply {
                    this.btnPublishMoment.isVisible = false
                    this.emptyText.text = getString(R.string.no_records)
                }.root
            }
            addViewBindingItemType<CharmRecord, ItemCharmListBinding>(R.layout.item_charm_list) { vb, charmRecord, i ->
                vb.apply {
                    charmRecord.related_user?.let { user ->
                        ivIcon.loadAvatar(user)
                        ivIcon.click {
                            UserProfileActivity.createIntent(this@CharmActivity, user).let {
                                startActivity(it)
                            }
                        }
                        tvUserName.text = user.nickname
                    }
                    tvText.text = charmRecord.changeReason
//                    2023-02-06 · 18:00
                    tvTime.text =
                        SimpleDateFormat("yyyy-MM-dd · HH:mm", Locale.getDefault()).format(
                            charmRecord.createTimestamp * 1000L
                        )
                    val changeAmount = charmRecord.changeAmount
                    val symbol = if (changeAmount > 0) "+" else ""
                    tvValue.text = "${getString(R.string.points)}${symbol}${changeAmount}"

                }
            }
        }
        lifecycleScope.launch {
            launch {
                viewModel.dateTotalFlow
                    .collectLatest {
                        binding.tvTotalValue.text = getString(R.string.format_total, it)
                    }
            }
            launch {
                viewModel.dataListFlow
                    .flowWithLifecycle(lifecycle)
                    .collectLatest {
                        if (it.isSuccess) {
                            spirit.setData(it.get())
                        }
                    }
            }
            launch {
                viewModel.charmValueFlow
                    .distinctUntilChanged()
                    .collectLatest { values ->
                        binding.apply {
                            tvV0.text = values.first
                            tvV1.text = values.second
                            tvV2.text = values.third

                        }
                    }
            }
        }
        collectCommonEffect(viewModel.effect)
        binding.apply {
            tvRule.click {
                dialog.taskShowWithStack()
            }
        }

    }

    private var startDate: Date = Date()
    private var endDate: Date = Date()
    private fun initSelectDate() {
        binding.apply {
            val calendar = Calendar.getInstance()
            endDate = calendar.time
            calendar.add(Calendar.DAY_OF_YEAR, -15)
            startDate = calendar.time
            setDateUI()

            tvStartDate.click {
                showDateSelectDialog(::startDate)
            }
            tvEndDate.click {
                showDateSelectDialog(::endDate)
            }
        }
    }

    private fun showDateSelectDialog(kMutableProperty0: KMutableProperty0<Date>) {
        val format = "yyyy-MM-dd"

        val calendar = Calendar.getInstance()
        val simpleDateFormat = SimpleDateFormat(format, Locale.getDefault())

        val name = kMutableProperty0.name
        val start: String
        val end: String
        val current: String
        if ("startDate" == name) {
            current = simpleDateFormat.format(startDate)
            calendar.add(Calendar.YEAR, -3)
            start = simpleDateFormat.format(calendar.time)
            calendar.time = endDate
            calendar.add(Calendar.DAY_OF_YEAR, -1)
            end = simpleDateFormat.format(calendar.time)
        } else {
            current = simpleDateFormat.format(endDate)
            end = simpleDateFormat.format(calendar.time)
            calendar.time = startDate
            calendar.add(Calendar.DAY_OF_YEAR, 1)
            start = simpleDateFormat.format(calendar.time)
        }
        Log.i("xct", "$current,$start,$end")

        DatePickerDialog.Builder(this)
            .setDate(
                dateStart = start,
                dateEnd = end,
                datePosition = current
            )
            .setSelectDateListener { date ->
                kMutableProperty0.set(SimpleDateFormat(format, Locale.getDefault()).parse(date)!!)
                setDateUI()
            }.taskShowWithStack()
    }


    private fun setDateUI() {
        val format = "MM-dd"
        binding.apply {
            tvStartDate.text = SimpleDateFormat(format, Locale.getDefault()).format(startDate)
            tvEndDate.text = SimpleDateFormat(format, Locale.getDefault()).format(endDate)
        }
        val formatYMD = "yyyy-MM-dd"
        val sdf = SimpleDateFormat(formatYMD, Locale.getDefault())
        viewModel.sendCharmEvent(CharmEvent.ChangeDate(sdf.format(startDate), sdf.format(endDate)))
    }


}