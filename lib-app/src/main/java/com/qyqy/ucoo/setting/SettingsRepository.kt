package com.qyqy.ucoo.setting

import com.overseas.common.utils.ConcurrencyShare
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.base.DataState
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.IMManager
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.sAppKV
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString

object SettingsRepository {

    private const val KEY_SETTINGS = "key_settings"

    private val settingsApi by lazy {
        createApi(SettingsApi::class.java)
    }

    private var _settingsFlow = MutableStateFlow<DataState<AppSettings>>(DataState.Idle())

    val sSettingsFlow = _settingsFlow.asStateFlow()

    private var _loginSettingsFlow = MutableStateFlow<DataState<LoginConfigs>>(DataState.Idle())

    val loginSettingsFlow = _loginSettingsFlow.asStateFlow()

    val videoChatCostPerMinute: Int
        get() = sSettingsFlow.value.getOrNull()?.videoCostPerMinute ?: 0
    val chargeCsId: Int?
        get() = sSettingsFlow.value.let {
            it.getOrNull()?.chargeCsId
        }

    val tribeCsId: Int?
        get() = sSettingsFlow.value.let {
            it.getOrNull()?.tribeCsId
        }

    val loveCsId: Int?
        get() = sSettingsFlow.value.let {
            it.getOrNull()?.loveCsId
        }

    val charmValue: Int
        get() = sSettingsFlow.value.let {
            it.getOrNull()?.charmValue
        } ?: 1000

    val privateRoomTimeGiftId: Int
        get() = sSettingsFlow.value.let {
            it.getOrNull()?.privateRoomTimeGiftId
        } ?: -1

    val rtcAudioSubscribe: Boolean by lazy {
        if (isRelease) {
            sSettingsFlow.value.let {
                it.getOrNull()?.androidFeature?.micsubcribeV3
            } ?: false
        } else {
            true
        }
    }

    val conversationExtraPageSize: Int by lazy {
        if (isRelease) {
            sSettingsFlow.value.let {
                it.getOrNull()?.androidFeature?.conversationExtraPageSize
            } ?: 5
        } else {
            5
        }
    }

    fun isOfficialId(userId: Int): Boolean {
        return sSettingsFlow.value.getOrNull()?.officialUserIds?.any {
            it == userId
        } == true
    }

    init {
        if (!isPreviewOnCompose) {
            appCoroutineScope.launch(Dispatchers.IO) {
                sAppKV.getString(KEY_SETTINGS, "").takeIf {
                    it.isNotBlank()
                }?.let {
                    _settingsFlow.emit(DataState.Success(sAppJson.decodeFromString(it)))
                }
            }
        }
    }

    suspend fun fetchSystemSettings() {
        ConcurrencyShare.globalInstance.joinPreviousOrRun("getSystemSettings") {
            runApiCatching {
                settingsApi.getSystemSettings()
            }.getOrNull()?.also {
                withContext(Dispatchers.IO) {
                    IMManager.saveIMEnvConfig(it)
                    sAppKV.putString(KEY_SETTINGS, sAppJson.encodeToString(it))
                }
                _settingsFlow.emit(DataState.Success(it))
            }
        }
    }

    suspend fun fetchLoginSettings() {
        ConcurrencyShare.globalInstance.joinPreviousOrRun("fetchLoginConfig", 5000) {
            runApiCatching {
                settingsApi.fetchLoginConfig()
            }.getOrNull()?.also {
                _loginSettingsFlow.emit(DataState.Success(it))
            }
        }
    }

    suspend fun preloadConfigs(): Result<PreloadConfigs> {
        return runApiCatching {
            settingsApi.preloadConfigs()
        }
    }

}