package com.qyqy.ucoo.setting

import android.graphics.Color
import android.os.Bundle
import com.overseas.common.ext.click
import com.overseas.common.ext.viewBinding
import com.qyqy.ucoo.R
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.databinding.ActivityUserBlackListBinding

class UserBlackListActivity : BaseActivity() {

    private val binding by viewBinding(ActivityUserBlackListBinding::inflate)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        with(binding) {
            toolbar.title.setText(R.string.black_list)
            toolbar.btnStart.click {
                onBackPressedDispatcher.onBackPressed()
            }
            if (isLightTheme()){
                setLightStatusBar()
                toolbar.root.setBackgroundColor(Color.WHITE)
            }
        }
    }
}