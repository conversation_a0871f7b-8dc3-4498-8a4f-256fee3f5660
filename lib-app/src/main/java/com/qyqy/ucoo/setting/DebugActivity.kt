package com.qyqy.ucoo.setting

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.util.Log
import android.util.TypedValue
import android.view.Gravity
import android.view.ViewGroup
import android.view.Window
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.ImageView
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatDialog
import androidx.core.content.ContextCompat
import androidx.preference.EditTextPreference
import androidx.preference.Preference
import androidx.preference.PreferenceCategory
import androidx.preference.PreferenceFragmentCompat
import androidx.preference.PreferenceScreen
import androidx.preference.SwitchPreference
import com.hjq.language.MultiLanguages
import com.lzf.easyfloat.utils.DisplayUtils
import com.overseas.common.ext.applyRoundRect
import com.overseas.common.ext.click
import com.overseas.common.ext.linearGradient
import com.overseas.common.ext.safeViewLifecycleScope
import com.overseas.common.ext.startActivity
import com.overseas.common.utils.dp
import com.overseas.common.utils.dpF
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.R
import com.qyqy.ucoo.UCOOEnvironment
import com.qyqy.ucoo.UCOOEnvironmentInstance
import com.qyqy.ucoo.accountManager
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.select_user.UserSelectorNavigator
import com.qyqy.ucoo.compose.presentation.voice.VoiceListNav
import com.qyqy.ucoo.compose.presentation.wedding.WeddingAlert
import com.qyqy.ucoo.compose.presentation.wedding.WeddingTheme
import com.qyqy.ucoo.compose.presentation.wedding.bean.WeddingRoomType
import com.qyqy.ucoo.compose.ui.dialogs.showComposeDialog
import com.qyqy.ucoo.config.AppConfig
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.core.banner.GlobalBannerManager
import com.qyqy.ucoo.databinding.ViewBroadcastPublicCpBinding
import com.qyqy.ucoo.glide.loadAvatar
import com.qyqy.ucoo.im.match.MatchUserCallActivity
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.lanCode
import com.qyqy.ucoo.login.LoginReason
import com.qyqy.ucoo.moment.topic.TopicActivity
import com.qyqy.ucoo.restartApp
import com.qyqy.ucoo.sAccountToken
import com.qyqy.ucoo.sAppKV
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.setting.debug.DebugView
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity
import com.qyqy.ucoo.widget.CardPayWaysView
import com.qyqy.ucoo.widget.DraggableFrameLayout
import com.qyqy.ucoo.widget.PayItem
import kotlinx.coroutines.launch
import java.util.Locale


/**
 * # Debug
 * ## 启动命令
 * `adb shell am start -a com.qyqy.ucoo.setting.DebugActivity`
 */
class DebugActivity : BaseActivity() {
    override val from: Int = 0

    companion object {
        private var lastClickTime = 0L
        private var clickTime = 0
        fun clickIntoDebug(context: Context) {
            if (System.currentTimeMillis() - lastClickTime > 500) {
                clickTime = 0
            }
            lastClickTime = System.currentTimeMillis()
            if (clickTime++ > if (BuildConfig.DEBUG) 5 else 20) {
                context.startActivity(Intent(context, DebugActivity::class.java))
                clickTime = 0
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
//        setTheme(android.R.style.Theme_DeviceDefault)
//        val controller: WindowInsetsControllerCompat =
//            WindowCompat.getInsetsController(window, window.decorView)
//        controller.isAppearanceLightStatusBars = true
        super.onCreate(savedInstanceState)
        supportFragmentManager.beginTransaction()
            .replace(android.R.id.content, DebugFragment())
            .commit()


    }
}

interface ItemMenu
private open class Item(val title: String, val summary: String?, val onClick: (Preference) -> Unit) : ItemMenu
private data class ItemList(val title: String, val summary: String?, val items: List<ItemMenu>) : ItemMenu
private data class EditItemList(val key: String, val title: String, val summary: String?) : ItemMenu
private class SwitchItem(val key: String, title: String, summary: String?, onClick: (Preference) -> Unit) :
    Item(title, summary, onClick)

private const val TAG = "DebugActivity"

class DebugFragment : PreferenceFragmentCompat() {

    companion object {
        private const val KEY_OPEN_LOG = "open_log"
        const val KEY_HTTP_REQUEST_SUMMARY = "http_request_summary"
    }

    private fun copyToClipBoard(label: CharSequence?, text: CharSequence?) {
        text ?: return
        val context = requireContext()
        val clipboardManager = ContextCompat.getSystemService(context, ClipboardManager::class.java)
        clipboardManager?.setPrimaryClip(ClipData.newPlainText(label, text))
        toast("已复制到剪切板")
    }

    private fun Preference.disableIcon() {
        icon = ColorDrawable(Color.TRANSPARENT)
    }

    private val configList = buildList<Any> {

        add(
            ItemList(
                "测试功能", summary = null, items = listOf(
                    Item(
                        "环境切换",
                        summary = "当前为${BuildConfig.NAME},点击切换 自动重启",
                        onClick = { changeAppRuntimeEnv(requireContext()) }),
//                    Item("全局横幅", summary = null, onClick = { showGlobalBanner() }),
                    Item("一键匹配", summary = null, onClick = {
                        MatchUserCallActivity::class.java.startActivity()
                    }),
                    Item(title = "日志开关", summary = "是否打开: ${
                        if (LogUtils.logPriority == Log.VERBOSE) "已打开"
                        else "已关闭"
                    }", onClick = {
                        LogUtils.logPriority = if (LogUtils.logPriority == Log.ERROR) Log.VERBOSE else Log.ERROR
                        it.summary = if (LogUtils.logPriority == Log.VERBOSE) "已打开" else "已关闭"
                    }),
                    EditItemList(key = "oneKey", title = "一键登录手机号", summary = null),
                    Item("DebugView", summary = null, onClick = {
                        DebugView.show(requireActivity())
                    }),
                    Item("Voice", summary = null, onClick = {
                        VoiceListNav.start(it.context)
                    }),
                    Item("切换语言", summary = MultiLanguages.getAppLanguage()?.lanCode) { switchLanguage(it) },
                    Item("UCOO WebView 网页", summary = null) {
                        val ctx = requireContext()
                        val intent = JsBridgeWebActivity.newIntent(
                            ctx,
                            "${BuildConfig.API_HOST}/h5/"
                        )
                        startActivity(intent)
                    },
                    Item("打开网页", summary = "") {
                        val context = requireContext()
                        val edt = EditText(context)
                        AlertDialog.Builder(context)
                            .setView(edt)
                            .setPositiveButton("确定") { a, b ->
                                val url = edt.text.toString().trim()
                                JsBridgeWebActivity.newIntent(context, url).also {
                                    startActivity(it)
                                }
                            }.show()
                    },
//                    Item("开启XLog日志", summary = null) { XLog.logLevel(LogLevel.ALL) },
                    Item("测试按钮", summary = null) {
                        val context = it.context
                        TopicActivity::class.java.startActivity()
//                        UserSelectorNavigator.start(context)
//                        showComposeDialog(initBlock = { d -> d.widthRatio = 0.91f }) {
//                            WeddingAlert(
//                                theme = WeddingTheme.getByType(WeddingRoomType.LOVE_FOREVER_RED),
//                                roomTypeDesc = "臻爱官宣",
//                                title = "婚礼开始提醒",
//                                message = "您的戒指点亮用户\"用户昵称\"已成功预约\"2025年3月8日19:00\"婚礼仪式，婚礼房间号\"520\"，让重要时刻不容错过！",
//                                buttonText = "进入房间",
//                                onClose = { it.dismiss() }
//                            ) {
//
//                            }
//                        }
//                        WeddingNavigator.navigate(requireContext())
//                        DialogBuilder.buildAgreementDialog(requireContext(),){
//
//                        }.show()
//                        TaskHelper.routerChatWithCpInvite()
//                        AppLinkManager.open(
//                            requireContext(),
//                            "ucoo://page/web_frame?info=%7B%22target_url%22%3A%22https%3A%2F%2Fapi.test.ucoofun.com%2Fh5%2Fauth_alert%22%2C%22gravity%22%3A%22center%22%2C%22width%22%3A270%2C%22height%22%3A300%2C%22radius%22%3A%7B%22left_top%22%3A16%2C%22right_top%22%3A16%2C%22right_bottom%22%3A16%2C%22left_bottom%22%3A16%7D%2C%22cancelable%22%3Afalse%7D"
//                        )
//                        showComposeDialog {
//                            AlertContent(centerButtonText = "ok", onCancel = {it.dismiss()}, onCenterClick = {it.dismiss()})
//                        }
//                        GameInviteDialog.newInstance(GameInvite(1, sUser as AppUser, "dsfdas")).show(childFragmentManager, "GameInviteDialog")
//                        JsBridgeWebActivity.newIntent(requireContext(),"http://192.168.1.16:7456/web-mobile/web-mobile/index.html").startActivity()
//                        AppLinkManager.open(requireContext(),"ucoo://page/global_chatroom?room_id=globalchat1")
//                        MaterialAlertDialogBuilder(ctx).setView(TextView(ctx).also {
//                            val json = "[{\"type\":1,\"client_jump_link\":\"\",\"rich_text\":\"<font color='#FFFFFF'>新人&#8201;</font>\"},{\"type\":2,\"client_jump_link\":\"ucoo://page/user_home?userid=1888\",\"url\":\"https://media.ucoofun.com/opsite/avatar/male/avatar_male_13.png\",\"width\":32,\"height\":32,\"border_radius\":16,\"icon\":\"https://media.ucoofun.com/opsite/avatar/male/avatar_male_9.png\"},{\"type\":1,\"client_jump_link\":\"\",\"rich_text\":\"<font color='#50FFF3'>&#8201;男随机&#8201;</font>\"},{\"type\":1,\"client_jump_link\":\"\",\"rich_text\":\"<font color='#FFFFFF'>进入了房间</font>\"}]"
//                            val list = sAppJson.decodeFromString<List<RichItem>>(json)
//                            with(it){
//                                showRichContent(list)
//                            }
//                        })
//                            .show()
//                        AppLinkManager.open(ctx, "${AppLinkManager.BASE_URL}${AppLinkManager.PATH_MAIN}?tab=home&action=refresh")
//                        CustomGreetingsNav.navigate(ctx)
//                        AppLinkManager.open(ctx,"ucoo://page/chatroom?action=open_red_packet_panel&roomId=101")
//                        PublishedVideoShowsNavigator.navigate(ctx)
//                        RPEditDialogFragment().show(childFragmentManager,"rp_edit")
//                        VoiceListNav.start(ctx)
//                        val code =
//                            "<font color='#FFD362'>恭喜</font><font color='#FFD362'>&#8201;Lucas&#8201;</font><font color='white'>向他的</font><font color='#FFD362'>&#8201;【闺蜜】&#8201;幼儿园班花&#8201;</font><font color='white'>赠送了1个</font><font color='#FFD362'>梦幻城堡</font><font color='white'>亲密度+999</font>"
//
//                        object : ComposeDialog(ctx) {
//                            @Composable
//                            override fun Content() {
//                                BlessingWordEditLayout { bless ->
//                                    dismiss()
//                                    GlobalBannerManager.showBanner(
//                                        BannerInfoConvert.getBlessBannerTemplate(
//                                            sUser.avatarUrl,
//                                            sUser.avatarUrl,
//                                            Html.fromHtml(code),
//                                            bless
//                                        )
//                                    )
//                                }
//                            }
//                        }.show()
//                        AlertDialog.Builder(ctx)
//                            .setView(AppCompatTextView(ctx).apply {
//                                movementMethod = LinkMovementMethod.getInstance()
//                                layoutParams = ViewGroup.LayoutParams(200.dp, -2)
//                                val code =
//                                    "<font color='#FFD362'>恭喜</font><font color='#FFD362'>&#8201;Lucas&#8201;</font><font color='white'>向他的</font><font color='#FFD362'>&#8201;【闺蜜】&#8201;幼儿园班花&#8201;</font><font color='white'>赠送了1个</font><font color='#FFD362'>梦幻城堡</font><font color='white'>亲密度+999</font>"
//                                val code1 =
//                                    "<b>加粗文本</b>，<i>斜体文本</i>，<font color='#FF0000'>红色文本</font><a href='https://api.ucoofun.com'>官网链接</a> <img src='${sUser.avatarUrl}'>"
//                                val nickName = "哈哈哈"
//                                LogUtil.d("f:${"<font color='#FFFFFF'>谢谢</font><font color='#FFD362'>$nickName</font><font color='#FFFFFF'>的红包，你发红包的样子太帅了！</font>"}")
//                                val sp = HtmlCompat.fromHtml(code1, HtmlCompat.FROM_HTML_OPTION_USE_CSS_COLORS)
//                                append(sp)
////                                append(Html.fromHtml(code1))
//                            }).show()
//                        ThemeAlertDialog(
//                            requireContext(),
//                            title = "哈哈哈",
//                            content = "亲友团礼物只能送给自己的亲友团成员。对方还未加入你的亲友团，快邀请她加入吧！",
//                            buttonText = "邀请她加入我的亲友团",
//                            link = "ucoo://page/main?tab=mine&mine_tab=relationship"
////                            link = "ucoo://page/private_chat?userid=506&action=invite"
//                        ).show()
//                        AppLinkManager.open(requireContext(), Uri.parse("ucoo://page/main?tab=mine&mine_tab=relationship"))
//                        DialogBuilder.buildAgreementDialog(requireContext(),AppConfig.URL_UNION_AGREEMENT){}.show()
//                        ComposeScreen.start(requireContext(),ComposeScreen.screen_welfare_center)
//                        startAct<InviteFamilyActivity>()
//                        ListFragmentActivity.newIntent(requireContext(),ListFragmentActivity.FragmentEnum.SomeoneLikeYou).startActivity()
//                        showDragView()
//                        startAct<VideoShowUploadActivity>()
//                        showVideoChatFragment()
//                        showPayWay()
//                        // 下载文件
//                        val downloader = Downloader.create()
//                        appCoroutineScope.launch {
//                            val request =
//                                Request.Builder("https://media.ucoofun.com/appdist/app-internalGooglePay-release-2.3.0_2023-10-13_14%3A07%3A18.apk").build()
//                            val result = downloader.execute(request) {
//                                Log.d("sdafasdfdassdfasdfadsf", "progress: $it")
//                            }
//                            Log.d("sdafasdfdassdfasdfadsf", "result = $result")
//                        }
                    }
                )
            )
        )

        val user = sUser
        val infoList = listOf(
            Item("app info", summary = """
                applicationId:${BuildConfig.APPLICATION_ID}
                versionCode:${BuildConfig.VERSION_CODE}
                versionName:${BuildConfig.VERSION_NAME}
            """.trimIndent(), onClick = { copyToClipBoard(it.title, it.summary) }),
            Item("env info", summary = """
                NAME:${BuildConfig.NAME}
                RONG_IM_ADDR:${BuildConfig.RONG_IM_ADDR}
                IM_KEY:${BuildConfig.RONG_IM_KEY}
                API_HOST:${BuildConfig.API_HOST}
                FAST_API_HOST:${BuildConfig.FAST_API_HOST}
            """.trimIndent(), onClick = { copyToClipBoard(it.title, it.summary) }),
            Item("build info", """
                debug:${BuildConfig.DEBUG}
                build type:${BuildConfig.FLAVOR}${BuildConfig.BUILD_TYPE}
                build time:${BuildConfig.BUILD_TIME}
                branch :${BuildConfig.CODE_BRANCH}
                commit rev:${BuildConfig.CODE_COMMIT}
            """.trimIndent(), onClick = { copyToClipBoard(it.title, it.summary) }),
            Item("user info", """
                    id:${user.id},
                    nickname:${user.nickname},
                    gender:${user.gender},
                    birthday:${user.birthday},
                    region:${user.region},
                    publicId:${user.publicId}
                """.trimIndent(), onClick = { copyToClipBoard(it.title, it.summary) }),
            Item("token", summary = sAccountToken?.let {
                """
                    authToken:${it.authToken}
                    refreshToken:${it.refreshToken}
                """.trimIndent()
            } ?: "", onClick = { copyToClipBoard(it.title, it.summary) }),
        )
        add(ItemList("信息", summary = null, items = infoList))
    }

    private fun showAgreementDialog(ctx: Context) {
        AlertDialog.Builder(ctx).apply {
            setItems(arrayOf("用户协议", "隐私协议", "充值协议", "主播协议"), DialogInterface.OnClickListener { dialog, which ->
                val link = when (which) {
                    0 -> Const.Url.AGREEMENT_URL
                    1 -> Const.Url.PRIVACY_URL
                    2 -> Const.Url.CHARGE_URL
                    else -> AppConfig.URL_COOPERATION_AGREEMENT
                }
                JsBridgeWebActivity.newIntent(ctx, link, "#999999").startActivity()
            })
        }.show()
    }

    private fun showDragView() {
        val activity = requireActivity()

        DraggableFrameLayout(activity).apply {
            layoutParams = ViewGroup.LayoutParams(80.dp, 80.dp)
            setBackgroundColor(Color.RED)
            activity.findViewById<FrameLayout>(android.R.id.content).addView(this)
            click {
                toast("parent")
            }
        }.also {
            it.addView(ImageView(activity).also {
                it.setImageDrawable(ColorDrawable(Color.GREEN))
                it.click {
                    toast("child")
                }
            }, FrameLayout.LayoutParams(20.dp, 20.dp).also {
                it.gravity = Gravity.CENTER
            })
        }
    }

    private fun showPayWay() {
        val context = requireContext()
        val view = CardPayWaysView(context)
        view.setContent("开通会员或充值金币遇到问题\n可点击下列按钮获取帮助", listOf(
            PayItem("点击前往官网充值", "", "可使用支付宝付款") {
                toast("点击前往官网充值")
            },
            PayItem("点击使用银行卡支付", "", "可使用支付宝付款") {
                toast("点击使用银行卡支付")
            },
            PayItem("联系人工客服代充", "", "可使用支付宝付款") {
                toast("联系人工客服代充")
            }
        ))
        AppCompatDialog(context).also {
            it.supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
            it.setContentView(view)
            it.window?.let {
                it.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                it.attributes.width = 300.dp
            }
            it.show()
        }

    }

    private fun switchLanguage(preference: Preference) {
        val requireContext = requireContext()
        if (MultiLanguages.equalsCountry(MultiLanguages.getAppLanguage(), Locale.SIMPLIFIED_CHINESE)) {
            MultiLanguages.setAppLanguage(requireContext, Locale.TRADITIONAL_CHINESE)
        } else {
            MultiLanguages.setAppLanguage(requireContext, Locale.SIMPLIFIED_CHINESE)
        }
        preference.summary = MultiLanguages.getAppLanguage()?.lanCode
    }

    private fun showGlobalBanner() {

        val banner2 = GlobalBannerManager.BannerViewInfo(R.layout.view_broadcast_public_cp, {
            it as ViewGroup
            val binding = ViewBroadcastPublicCpBinding.bind(it.getChildAt(0))
            binding.root.background =
                linearGradient(
                    GradientDrawable.Orientation.LEFT_RIGHT,
                    Color.parseColor("#E6FC90BA"),
                    Color.parseColor("#E6FC51E6")
                )
            binding.root.applyRoundRect(25.dpF)
            binding.apply {
                iv1.loadAvatar(sUser.avatarUrl)
                iv2.loadAvatar(sUser.avatarUrl)
                tvMessage.text = "恭喜1111111和222222成为CP"
            }
        })
//        GlobalBannerManager.showBanner(banner1)
        GlobalBannerManager.showBanner(banner2)
    }

    override fun onCreatePreferences(savedInstanceState: Bundle?, rootKey: String?) {
        val context = requireContext()
        val list = configList
        preferenceManager.createPreferenceScreen(context).apply {
            preferenceScreen = this

            list.forEach {
                when (it) {
                    is Item -> {
                        addPreference(getItemPre(it))
                    }

                    is ItemList -> addPreference(getItemListPre(this, it))
                    else -> {}
                }
            }

        }
    }

    private fun getItemListPre(screen: PreferenceScreen, list: ItemList): Preference {
        val category = PreferenceCategory(requireContext())
        category.title = list.title
        category.isIconSpaceReserved = false
        screen.addPreference(category)
        list.items.forEach {
            category.addPreference(getItemPre(it))
        }
        return category
    }

    private fun getItemPre(item: ItemMenu): Preference {
        return when (item) {
            is SwitchItem -> {
                SwitchPreference(requireContext()).apply {
                    title = item.title
                    key = item.key
                    isIconSpaceReserved = false
                }
            }

            is EditItemList -> {
                EditTextPreference(requireContext()).apply {
                    key = item.key
                    title = item.title
                    dialogTitle = title
                    summary = item.summary
                    dialogMessage = item.summary
                    summaryProvider = EditTextPreference.SimpleSummaryProvider.getInstance()
                    isIconSpaceReserved = false
                    setOnPreferenceChangeListener { preference, newValue ->
                        sAppKV.putString("onekey", newValue.toString())
                        true
                    }
                }
            }

            is Item -> {
                Preference(requireContext()).apply {
                    title = item.title
                    summary = item.summary
                    isIconSpaceReserved = false
                    onPreferenceClickListener = Preference.OnPreferenceClickListener {
                        item.onClick.invoke(it)
                        true
                    }
                }
            }

            else -> error("")
        }
    }

    private fun changeAppRuntimeEnv(ctx: Context) {
        if (isRelease) {
            toast("\uD83D\uDE48\uD83D\uDE48\uD83D\uDE48")
            activity?.finish()
            return
        }
        AlertDialog.Builder(ctx).apply {
            val ucooEnvironmentTypes = UCOOEnvironmentInstance.entries.map { it.NAME }.toTypedArray()
            setTitle("设置环境host")
            setItems(ucooEnvironmentTypes, DialogInterface.OnClickListener { dialog, which ->
                safeViewLifecycleScope.launch {
                    (requireActivity() as? BaseActivity)?.showLoading()
                    accountManager.logout(LoginReason.Exit)
                    UCOOEnvironment.switchEnvironment(ucooEnvironmentTypes[which])
                    restartApp()
                }
            })
        }.show()
    }
}
