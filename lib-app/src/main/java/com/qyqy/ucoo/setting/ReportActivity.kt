package com.qyqy.ucoo.setting

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.TextView
import com.qyqy.ucoo.AppUserPartition
import androidx.constraintlayout.helper.widget.Flow
import androidx.core.view.isGone
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.overseas.common.ext.AppInputFilter
import com.overseas.common.ext.SingleSelectGroup
import com.overseas.common.ext.argument
import com.overseas.common.ext.click
import com.overseas.common.ext.setItems
import com.overseas.common.ext.viewBinding
import com.overseas.common.utils.dp
import com.overseas.common.utils.removeFirstIfCompat
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.compose.presentation.ComposeScreen.Companion.navigate
import com.qyqy.ucoo.compose.presentation.video_edit.PublishedVideoShowsNavigator
import com.qyqy.ucoo.config.ConfigApi
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.core.oss.Uploader
import com.qyqy.ucoo.databinding.ActivityReportBinding
import com.qyqy.ucoo.databinding.ItemAddPhotoBinding
import com.qyqy.ucoo.databinding.ItemSelectMediaBinding
import com.qyqy.ucoo.glide.load
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.mine.encodeUrl
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.toastRes
import com.qyqy.ucoo.utils.Option
import com.qyqy.ucoo.utils.RequestMultiPhotoPermissionHelper
import com.qyqy.ucoo.utils.TypeMediaFilter
import com.qyqy.ucoo.utils.preview
import com.qyqy.ucoo.widget.Spirit
import com.qyqy.ucoo.widget.dialog.LoadingDialog
import com.qyqy.ucoo.widget.disableItemAnimator
import com.qyqy.ucoo.widget.spirit
import io.github.album.MediaData
import io.github.album.ui.GridItemDecoration
import kotlinx.coroutines.Job
import kotlinx.serialization.encodeToString

/**
 * 通用举报页面
 *
 */
class ReportActivity : BaseActivity() {

    companion object {

        const val KEY_TARGET_ID = "target_id"
        const val KEY_TARGET_TYPE = "target_type"

        const val TYPE_USER = 1
        const val TYPE_AUDIOROOM = 2
        const val TYPE_PRIVATE_USER = 3
        const val TYPE_MOMENT = 4

        private const val KEY_USER_ID = "key_user_id"

        fun newIntent(context: Context, userId: String): Intent {
            return newSccusateIntent(context, TYPE_USER, userId)
        }

        fun newSccusateIntent(context: Context, type: Int, id: String): Intent {
            if (type < TYPE_USER || type > TYPE_MOMENT) {
                throw IllegalArgumentException("unknown target type. please check")
            }
            return Intent(context, ReportActivity::class.java).also {
                it.putExtra(KEY_TARGET_TYPE, type)
                it.putExtra(KEY_TARGET_ID, id)
            }
        }

    }

    private val targetId by argument<String>(KEY_TARGET_ID)
    private val targetType by argument<Int>(KEY_TARGET_TYPE)

    private val commonApi = createApi(ConfigApi::class.java)

    private val binding by viewBinding(ActivityReportBinding::inflate)

    private var job: Job? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        val inputFilter = AppInputFilter()
        inputFilter.lengthFilter(binding.editContent, 400)

        binding.toolbar.btnStart.click {
            onBackPressedDispatcher.onBackPressed()
        }

        binding.toolbar.title.setText(
            when (targetType) {
                TYPE_USER -> R.string.sccusate_user
                TYPE_AUDIOROOM -> R.string.sccusate_audioroom
                TYPE_MOMENT -> R.string.sccusate_moment
                TYPE_PRIVATE_USER -> R.string.sccusate_private
                else -> R.string.select_report_type
            }
        )
        val reportItemList = arrayOf(
            ReportItem(1, getString(R.string.polict_relations)),
            ReportItem(2, getString(R.string.low_sex)),
            ReportItem(3, getString(R.string.voliet)),
            ReportItem(4, getString(R.string.ads_make)),
            ReportItem(5, getString(R.string.report_trick)),
            ReportItem(6, getString(R.string.report_dirty_lang)),
            ReportItem(7, getString(R.string.儿童安全相关)),
            ReportItem(0, getString(R.string.others))
        )
        val views = reportItemList.mapIndexed { idx, item ->
            layoutInflater.inflate(R.layout.layout_flow_item_report, binding.root, false).also {
                it.id = View.generateViewId()
                (it as TextView).text = item.desc
//                it.layoutParams = ViewGroup.MarginLayoutParams(DisplayUtils.getScreenWidth(this) / 3, 55.dp)
            }
        }

        val selectGroup = SingleSelectGroup(0, views.toTypedArray(), false)
        binding.flow.setWrapMode(Flow.WRAP_ALIGNED)
        binding.flow.setItems(reportItemList.size) { index ->
            views[index]
        }
        initRv()
        binding.btnPost.click {
            job = lifecycleScope.launchWhenStarted {
                showLoading()
                val uploadImages = Uploader.uploadMedias(items.filterIsInstance<MediaData>(), "accusate") ?: kotlin.run {
                    hideLoading()
                    if (AppUserPartition.isUCOO) {
                        toastRes(R.string.upload_image_failed)
                    } else {
                        toastRes(R.string.cpd_upload_image_failed)
                    }
                    return@launchWhenStarted
                }

                runApiCatching {
                    commonApi.reportAccusate(
                        buildMap {
                            put("target_type", targetType)
                            put("target_id", targetId)
                            put("accusation_type", reportItemList[selectGroup.selectedIndex].type.toString())
                            val content = binding.editContent.text?.toString().orEmpty()
                            if (content.isNotBlank()) {
                                put("note", content)
                            }
                            if (uploadImages.isNotEmpty()) {
                                put("media_list", sAppJson.encodeToString(uploadImages))
                            }
                        }
                    )
                }.apply {
                    hideLoading()
                    onSuccess {
                        toast(getString(R.string.report_success))
                        finish()
                    }
                    onFailure {
                        toast(it.message)
                    }
                }
            }
        }
    }

    override fun onLoadingDismiss(dialog: LoadingDialog) {
        super.onLoadingDismiss(dialog)
        job?.cancel()
    }


    //选择的媒体文件
    private var items = mutableListOf<Any>(Unit)
    private val imageUrlProvider: (MediaData) -> Any = { it.uri }
    private var notifyPhotoItems: () -> Unit = {}
    private var externalMediaData: MediaData? = null
    private val photoSelector = RequestMultiPhotoPermissionHelper(this, allString = app.getString(R.string.图片和视频),
        request = {
            this.setFilter(object : TypeMediaFilter(Option.ALL) {
                override fun accept(media: MediaData): Boolean {
                    return super.accept(media) && if (media.isVideo) media.duration.div(1000) < 60 else true
                }
            })
        }
    ) { uris: List<MediaData> ->
        items = buildList<Any> {
            addAll(uris)
            if (uris.size < 9) {
                add(Unit)
            }
        }.toMutableList()
        notifyPhotoItems()
    }
    private lateinit var sprirt: Spirit

    private fun initRv() {
        val rv: RecyclerView = binding.viewPhotos
        rv.apply {
            disableItemAnimator()
            layoutManager = GridLayoutManager(this@ReportActivity, 3)
            addItemDecoration(GridItemDecoration(3, 8.dp))
        }
        notifyPhotoItems = {
            sprirt.setData(buildList {
                externalMediaData?.also {
                    add(it)
                }
                addAll(items)
            })
        }
        sprirt = rv.spirit {
            addViewBindingItemType<MediaData, ItemSelectMediaBinding>(R.layout.item_select_media)
            { itemSelectMediaBinding, media: MediaData, i ->
                itemSelectMediaBinding.ivItem.load(media.uri)
                itemSelectMediaBinding.ivVideoPause.isGone = !media.isVideo
                itemSelectMediaBinding.ivDelete.click {
                    if (externalMediaData == media) {
                        externalMediaData = null
                    }
                    items.removeFirstIfCompat {
                        it === media
                    }
                    if (items.isEmpty() || items.last() is MediaData) {
                        items.add(Unit)
                    }
                    notifyPhotoItems()
                }
                //图片预览
                itemSelectMediaBinding.root.click {
                    if (media.isVideo) {
                        PublishedVideoShowsNavigator.navigate(this@ReportActivity) {
                            val videoUrl = media.uri.toString().encodeUrl
                            putExtra(Const.KEY_URL, videoUrl)
                            putExtra("player_version", 1)
                        }
                    } else {
                        itemSelectMediaBinding.ivItem.preview(
                            listOf(media),
                            imageUrlProvider,
                            0
                        )
                    }
                }
            }
            addViewBindingItemType<Unit, ItemAddPhotoBinding>(
                R.layout.item_add_photo,
            ) { itemAddPhotoBinding, _, _ ->
                itemAddPhotoBinding.root.click {
                    photoSelector.select(9, items.filterIsInstance<MediaData>())
                }
            }
        }
        notifyPhotoItems()
    }
}

data class ReportItem(val type: Int, val desc: String)