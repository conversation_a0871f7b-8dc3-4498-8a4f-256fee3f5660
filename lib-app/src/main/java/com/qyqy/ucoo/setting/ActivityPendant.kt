package com.qyqy.ucoo.setting


import com.qyqy.ucoo.map.Container
import com.qyqy.ucoo.utils.IPendantPage
import com.qyqy.ucoo.utils.PendantInfo
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class ActivityPendant(
    @SerialName("icon")
    val icon: String,
    @SerialName("jump_link")
    val jumpLink: String = "",
    @SerialName("name")
    val name: String,
    @SerialName("order_id")
    val orderId: Int,
    @SerialName("type")
    val type: Int,
    @SerialName("visible_pos")
    val visiblePoi: List<Int> = emptyList(),
    val container: Container = Container()
) : PendantInfo {
    override val id: Long = orderId.toLong()
    override val resId: Int = 0

    override fun isVisibleAt(pendantPage: IPendantPage): Boolean {
        return PendantConfig.isPositionSupportPage(visiblePoi, pendantPage)
    }

}