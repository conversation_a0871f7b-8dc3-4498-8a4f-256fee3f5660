package com.qyqy.ucoo.setting

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.RotateDrawable
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.view.ViewCompat.getMinimumWidth
import androidx.core.widget.addTextChangedListener
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.LinearLayoutManager
import com.overseas.common.ext.AppInputFilter
import com.overseas.common.ext.calculateLength
import com.overseas.common.ext.click
import com.overseas.common.ext.setMaxLength
import com.overseas.common.ext.setRightCompoundDrawable
import com.overseas.common.ext.viewBinding
import com.overseas.common.utils.ThemeUtils
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.base.BaseActivity
import com.qyqy.ucoo.base.LoadingState
import com.qyqy.ucoo.bean.FeedbackQABean
import com.qyqy.ucoo.core.AppEventManager.list
import com.qyqy.ucoo.core.Const
import com.qyqy.ucoo.databinding.ActivityFeedBackBinding
import com.qyqy.ucoo.databinding.ItemFeedBackSuggestBinding
import com.qyqy.ucoo.mine.EditUserInfoContract
import com.qyqy.ucoo.mine.EditUserInfoViewModel
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.widget.Spirit
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch

class FeedbackActivity : BaseActivity() {

    private val binding by viewBinding(ActivityFeedBackBinding::inflate)

    private val viewModel by viewModels<EditUserInfoViewModel>()

    private val collapsedDrawable by lazy {
        ContextCompat.getDrawable(this@FeedbackActivity, R.drawable.ic_arrow_right)?.apply {
            setBounds(0, 0, getMinimumWidth(), getMinimumHeight())
        }
    }

    private val expandDrawable by lazy {
        ContextCompat.getDrawable(this@FeedbackActivity, R.drawable.ic_arrow_down)?.apply {
            setBounds(0, 0, getMinimumWidth(), getMinimumHeight())
        }

    }


    companion object {
        const val KEY_DONT_SEND = "dont_send"

        @JvmStatic
        fun start(context: Context, dontSend: Boolean = false) {
            val starter = Intent(context, FeedbackActivity::class.java)
                .putExtra(KEY_DONT_SEND, dontSend)
            context.startActivity(starter)
        }
    }

    private val isDontSend by lazy { intent.getBooleanExtra(KEY_DONT_SEND, false) }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        binding.toolbar.title.setText(R.string.feedback_and_suggest)
        binding.toolbar.btnStart.click {
            onBackPressedDispatcher.onBackPressed()
        }

        if (isLightTheme()) {
            setLightStatusBar()
            binding.toolbar.root.setBackgroundColor(Color.WHITE)
        }
        initInputContent()
        initRecyclerView()
    }

    private lateinit var mSpirit: Spirit
    private fun initRecyclerView() {
        mSpirit = Spirit(null)
        mSpirit.addViewBindingItemType<FeedbackQABean, ItemFeedBackSuggestBinding>(R.layout.item_feed_back_suggest) { binding, data, position ->
            binding.title.text = data.question
            binding.title.setRightCompoundDrawable(if (data.isExpand) expandDrawable else collapsedDrawable)
            binding.content.text = data.answer
            binding.content.visibility = if (data.isExpand) View.VISIBLE else View.GONE

            binding.title.setOnClickListener {
                mSpirit.spiritAdapterInstance.updateItems({ item ->
                    item is FeedbackQABean && (item.isExpand || item == data)
                }, { item ->
                    if (item is FeedbackQABean) {
                        if (item == data) {
                            item.isExpand = !item.isExpand
                        } else {
                            item.isExpand = false
                        }
                    }
                    return@updateItems item
                })
            }
        }

        binding.mRecyclerView.layoutManager = LinearLayoutManager(this)
        binding.mRecyclerView.adapter = mSpirit.adapter

        viewModel.sendEvent(EditUserInfoContract.Event.OnRequestQAList)
    }

    private fun initInputContent() {
        binding.editContent.setLines(8)

        val limit = 500
//        val inputFilter = AppInputFilter()
//        inputFilter.lengthFilter(binding.editContent, limit)
        binding.editContent.setMaxLength(limit)
        binding.editContent.addTextChangedListener {
//            val length = it?.toString().orEmpty().calculateLength(inputFilter.charset)
            val length = it?.toString().orEmpty().length
            binding.lengthLimit.text = "$length/$limit"
            binding.btnPost.isEnabled = !it?.toString()?.trim().isNullOrBlank()
        }

        binding.editContent.text = null
        binding.btnPost.setTextColor(
            ThemeUtils.createDisabledStateList(
                getColor(R.color.white),
                getColor(R.color.white_alpha_50)
            )
        )

        binding.btnPost.click {
            val content = binding.editContent.text?.toString().orEmpty().trim()
            if (isDontSend) {
                if (content.isEmpty()) {
                    toast(
                        app.getString(R.string.input_cant_empty)
                    )
                    return@click
                }
                setResult(RESULT_OK, Intent().apply {
                    putExtra(Const.KEY_CONTENT, content)
                })
                finish()
            } else {
                viewModel.sendEvent(
                    EditUserInfoContract.Event.OnSaveFeedbackCLicked(
                        content
                    )
                )
            }
        }

        lifecycleScope.launchWhenStarted {
            viewModel.effect
                .collect {
                    when (it) {
                        is EditUserInfoContract.Effect.ShowToast -> {
                            toast(it.msg)
                        }

                        is EditUserInfoContract.Effect.Finish -> {
                            setResult(RESULT_OK, Intent().apply {
                                putExtra(Const.KEY_CONTENT, binding.editContent.text?.toString().orEmpty())
                            })
                            finish()
                        }

                        is EditUserInfoContract.Effect.QADataFinished -> {
                            mSpirit.setData(it.list)
                            if (it.list.isNotEmpty()) {
                                binding.tvTitleSuggest.visibility = View.VISIBLE
                            }
                        }
                    }
                }
        }

        lifecycleScope.launch {
            viewModel.uiState
                .map {
                    it.loadingState
                }
                .distinctUntilChanged()
                .flowWithLifecycle(lifecycle)
                .collectLatest {
                    if (it is LoadingState.Idle) {
                        hideLoading()
                    } else {
                        showLoading()
                    }
                }
        }
    }

}
