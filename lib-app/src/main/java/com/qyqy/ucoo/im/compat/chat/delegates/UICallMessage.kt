package com.qyqy.ucoo.im.compat.chat.delegates

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.view.isVisible
import com.qyqy.ucoo.R
import com.qyqy.ucoo.databinding.LayoutListItemMsgVoiceCallFinishBinding
import com.qyqy.ucoo.im.compat.UCVoiceCallMessage
import com.qyqy.ucoo.im.compat.chat.InstanceMessageViewHolder
import com.qyqy.ucoo.im.compat.chat.MessageMultiTypeAdapter
import com.qyqy.ucoo.im.compat.chat.SimpleMessageDelegate
import com.qyqy.ucoo.im.compat.chat.UIMessageEntry
import com.qyqy.ucoo.sUser
import kotlin.reflect.KClass

/**
 * 定义语音通话消息的代理类
 */
class VoiceFinishEventMessageDelegate(
    providerVH: (parent: ViewGroup, adapter: MessageMultiTypeAdapter) -> CallMessageViewHolder,
) : SimpleMessageDelegate<UIMessageEntry<UCVoiceCallMessage.Other>, CallMessageViewHolder>(null, providerVH) {

    override val typeKClass: KClass<UCVoiceCallMessage.Other>
        get() = UCVoiceCallMessage.Other::class

    override fun isForViewType(entry: UIMessageEntry<UCVoiceCallMessage.Other>, position: Int): Boolean {
        return entry.message.showInMsgList
    }
}


// 文本消息的VH
sealed class CallMessageViewHolder(view: View, adapter: MessageMultiTypeAdapter) :
    InstanceMessageViewHolder<UCVoiceCallMessage.Other>(view, adapter) {

    // 单聊
    class C2CVoiceFinishEvent(view: View, adapter: MessageMultiTypeAdapter) : CallMessageViewHolder(view, adapter) {

        companion object {
            fun createDelegate(): VoiceFinishEventMessageDelegate {
                val resource = R.layout.layout_list_item_msg_voice_call_finish
                return VoiceFinishEventMessageDelegate { parent, adapter ->
                    C2CVoiceFinishEvent(LayoutInflater.from(parent.context).inflate(resource, parent, false), adapter)
                }
            }
        }

        private val binding = LayoutListItemMsgVoiceCallFinishBinding.bind(view)


        init {
            binding.btn1.registerChildItemClick()
            binding.btn2.registerChildItemClick()
        }

        override fun onBindInstanceData(entry: UIMessageEntry<UCVoiceCallMessage.Other>, position: Int) {
            val message = entry.message
            val notice = message.notice
            if (!notice.isNewbieGuide && notice.reasonCode == -1) {
                if (notice.actionUserId == sUser.id) {
                    binding.btn1.isVisible = true
                    binding.btn2.isVisible = true
                    binding.btn2.tag = "cp"
                } else {
                    binding.btn1.isVisible = false
                    binding.btn2.isVisible = false
                }
            } else {
                binding.btn1.isVisible = false
                binding.btn2.isVisible = true
                binding.btn2.tag = "send"
                binding.tvText2.text = context.getString(R.string.继续聊天)
            }
            binding.content.text = notice.reason
        }
    }

}