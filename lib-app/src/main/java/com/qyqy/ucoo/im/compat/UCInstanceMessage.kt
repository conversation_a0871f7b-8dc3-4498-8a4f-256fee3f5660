package com.qyqy.ucoo.im.compat

import android.content.Context
import android.net.Uri
import androidx.core.net.toUri
import androidx.core.text.HtmlCompat
import com.overseas.common.utils.id2String
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.R
import com.qyqy.ucoo.UserPartition
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.presentation.chatgroup.data.ChatGroup
import com.qyqy.ucoo.compose.ui.photo.IImageSource
import com.qyqy.ucoo.compose.ui.photo.PhotoPreviewModel
import com.qyqy.ucoo.http.getBoolOrNull
import com.qyqy.ucoo.http.getFloatOrNull
import com.qyqy.ucoo.http.getIntOrNull
import com.qyqy.ucoo.http.getLongOrNull
import com.qyqy.ucoo.http.getOrNull
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.bean.GiftWrapper
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.im.message.AppEventMessage
import com.qyqy.ucoo.im.message.AppPersistMessage
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.im.message.entity.UserRichList
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.tribe.create.TribeTipContent
import com.qyqy.ucoo.user.video.VideoCallInvite
import com.qyqy.ucoo.user.video.VideoCallNotice
import com.qyqy.ucoo.user.video.VideoMatchNotice
import com.qyqy.ucoo.user.voice.VoiceCallInvite
import com.qyqy.ucoo.user.voice.VoiceCallNotice
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.takeIsNotEmpty
import com.tencent.imsdk.v2.V2TIMImageElem
import com.tencent.imsdk.v2.V2TIMMessage
import com.tencent.imsdk.v2.V2TIMValueCallback
import io.rong.imlib.model.UserInfo
import io.rong.message.HQVoiceMessage
import io.rong.message.ImageMessage
import io.rong.message.RecallNotificationMessage
import io.rong.message.TextMessage
import kotlinx.coroutines.runBlocking
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.buildJsonObject
import kotlinx.serialization.json.decodeFromJsonElement
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.put
import java.io.File
import java.util.Locale
import kotlin.coroutines.resume
import kotlin.coroutines.suspendCoroutine
import kotlin.math.abs

interface UCInstanceMessage : IUCMessage {
    val base: UCMessage

    val fromRc: Boolean
        get() = base is UCMessage.RCIM

    /**
     * 有些消息需要在解析的结构体里面获取user
     */
    val user: AppUser?
        get() = base.senderUser?.toUser(null)

    fun getSummaryString(): String

    fun toMsgString(): String {
        if (base.isRevoked) {
            return "(已撤回)${getSummaryString()}"
        }
        return getSummaryString()
    }
}

interface UCFakerMessage : UCInstanceMessage {

    val contentType: String

    fun equalsSame(other: UCFakerMessage?): Boolean
}

/**
 * 这不是一个真实的消息，只是某一个消息上方显示的时间戳，包装了这一条消息
 */
data class UCNewMsgTagMessage(
    override val base: UCMessage,
) : UCFakerMessage, IUCMessage by base {

    override val contentType: String = "UCNewMsgTagMessage"

    override val id: String = "new-message-tag-${base.id}"

    override fun getSummaryString(): String {
        return "以下为新消息"
    }

    override fun equalsSame(other: UCFakerMessage?): Boolean {
        return true
    }
}

/**
 * 这不是一个真实的消息，只是某一个消息上方显示的时间戳，包装了这一条消息
 */
data class UCTimestampMessage(
    override val base: UCMessage,
) : UCFakerMessage, IUCMessage by base {

    private var formatText: String = ""

    private var cacheTimestamp = 0L

    override val contentType: String = "UCTimestampMessage"

    override val id: String = "timestamp-${base.id}"

    /**
     * 消息发送状态恒定为[MsgSendStatus.Idea]
     */
    override val sendStatus: MsgSendStatus = MsgSendStatus.Idea

    override fun getSummaryString(): String {
        return "时间戳：${getMessageTimeFormatText(app)}"
    }

    override fun equalsSame(other: UCFakerMessage?): Boolean {
        return timestamp == other?.timestamp && !checkInValid()
    }

    fun getMessageTimeFormatText(
        context: Context,
        locale: Locale = Locale.getDefault(),
        userPartition: UserPartition = AppUserPartition.current,
    ): String {
        if (formatText.isEmpty() || checkInValid()) {
            formatText = UIMessageUtils.getMessageTimeFormatText(context, timestamp, locale, userPartition = userPartition)
            cacheTimestamp = System.currentTimeMillis()
        }
        return formatText
    }

    private fun checkInValid() = abs(System.currentTimeMillis().minus(cacheTimestamp)) >= 60_000
}


data class UCUnknownMessage(
    override val base: UCMessage,
) : UCInstanceMessage, IUCMessage by base {

    override fun getSummaryString(): String {
        return "未知消息类型：${base.rcMessage?.realMessageContent ?: base.timMessage}"
    }
}

data class UCTextMessage(
    override val base: UCMessage,
    val text: String,
) : UCInstanceMessage, IUCMessage by base {

    val htmlTextSpan: CharSequence by lazy(LazyThreadSafetyMode.NONE) {
        try {
            HtmlCompat.fromHtml(text, HtmlCompat.FROM_HTML_MODE_LEGACY)
        } catch (e: Exception) {
            text
        }
    }

    override fun getSummaryString(): String {
        return "文本消息：$text"
    }
}

data class UCVoiceMessage(
    override val base: UCMessage,
    val duration: Int,
    val url: String,
    val localUri: Uri? = null,
    val uuid: String? = null,
) : UCInstanceMessage, IUCMessage by base {

    override fun getSummaryString(): String {
        return "语音消息, 时长: $duration, url: $url"
    }
}

data class ImageElem(
    val width: Int,
    val height: Int,
    val url: String,
    val localUri: Uri?,
    val uuid: String?,
)

data class UCImageMessage(
    override val base: UCMessage,
    val thumbElem: ImageElem?,
    val largeElem: ImageElem?,
    val originElem: ImageElem?,
) : UCInstanceMessage, IUCMessage by base {

    init {
        require(thumbElem != null || largeElem != null || originElem != null)
    }

    val previewElem: ImageElem
        get() {
            if (largeElem?.localUri != null) {
                return largeElem
            }
            if (originElem?.localUri != null) {
                return originElem
            }
            return (largeElem ?: originElem ?: thumbElem)!!
        }

    override fun getSummaryString(): String {
        return "图片消息, 宽: ${previewElem.width}, 高: ${previewElem.height}, url: ${previewElem.url}"
    }

    fun toPhotoPreviewModel(): PhotoPreviewModel {
        val transitionElem = thumbElem ?: previewElem

        val source = transitionElem.toImageSource()

        val aspectRatio = if (transitionElem.width > 0 && transitionElem.height > 0) {
            transitionElem.width.toFloat().div(transitionElem.height)
        } else {
            -1f
        }

        return PhotoPreviewModel(
            key = id,
            aspectRatio = aspectRatio,
            transitionSource = source,
            previewSource = previewElem.toImageSource(),
            originSource = originElem?.toImageSource(),
        )
    }

    private fun ImageElem.toImageSource(): IImageSource {
        return if (localUri != null) {
            IImageSource.Uri(localUri)
        } else {
            IImageSource.Url(url)
        }
    }
}


interface IUCCustomMessage {

    val cmd: String

    val rawContent: String

    val customJson: JsonObject

    val summary: String?
}

data class UCCustomMessage constructor(
    override val base: UCMessage,
) : UCInstanceMessage, IUCMessage by base, IUCCustomMessage {

    companion object {
        private const val KEY_CMD = "cmd"
        private const val KEY_DATA = "data"
        private const val KEY_DIGEST = "digest"
        const val KEY_CACHE_DATA = "key-cache-custom-content-data"
    }

    override val rawContent: String

    override val cmd: String

    override val customJson: JsonObject

    override val summary: String?

    override val user: AppUser?
        get() = getFallbackUser() ?: super.user

    private var fallbackUser: AppUser? = null

    val contentCache by lazy {
        hashMapOf<String, Any?>()
    }

    init {
        var jsonContent: JsonContent? = null
        rawContent = when (base) {
            is UCMessage.RCIM -> {
                (base.rawMessage.realMessageContent as AppEventMessage).also {
                    jsonContent = it.jsonContent
                }.rawContent.orEmpty()
            }

            is UCMessage.TIM -> {
                String(base.rawMessage.customElem.data)
            }
        }
        val json = sAppJson.decodeFromString<JsonObject>(rawContent)
        cmd = jsonContent?.cmd.takeIsNotEmpty() ?: json.getStringOrNull(KEY_CMD).orEmpty()
        customJson = jsonContent?.data ?: json.getOrNull(KEY_DATA)?.jsonObject ?: JsonObject(emptyMap())
        summary = customJson.getStringOrNull(KEY_DIGEST).takeIsNotEmpty()
    }

    override fun toString(): String {
        return "UCCustomMessage(base=$base, cmd='$cmd', summary='$summary', json='$customJson')"
    }

    override fun getSummaryString(): String {
        return "自定义消息, cmd: $cmd, summary: $summary"
    }

    private fun getFallbackUser(): AppUser? {
        return fallbackUser ?: when (cmd) {
            MsgEventCmd.USER_ENTRANCE, MsgEventCmd.USER_EXIT, MsgEventCmd.APPLY_MIC,
            MsgEventCmd.INVITE_PRIVATE_ROOM2, MsgEventCmd.BEST_MATCH_DIALOG,
            MsgEventCmd.MEMBER_JOIN, ChatGroup.Events.CHATGROUP_MEMBER_JOIN,
            MsgEventCmd.MEMBER_QUIT, ChatGroup.Events.CHATGROUP_MEMBER_QUIT,
            MsgEventCmd.BULLETIN_UPDATE, MsgEventCmd.NAME_UPDATE, MsgEventCmd.USER_LEVEL_CHANGE,
            MsgEventCmd.WEDDING_COMMON_EVENT -> {
                "user"
            }

            MsgEventCmd.INVITE_MIC, MsgEventCmd.AGREE_MIC, MsgEventCmd.REFUSE_MIC, MsgEventCmd.TAKEAWAY_MIC,
            MsgEventCmd.MEMBER_KICKED_OUT, ChatGroup.Events.CHATGROUP_MEMBER_KICKED_OUT -> {
                "admin_user"
            }

            MsgEventCmd.GIVE_GIFT, MsgEventCmd.GIVE_LUCKY_GIFT, MsgEventCmd.GIVE_GIFT_COMBO_FINISHED,
                //fixed 2.40.3 可能还会有一些信令没有对user进行处理, 之后遇到了再加吧
            MsgEventCmd.RED_PACKET_CREATED -> {
                "sender"
            }

            else -> null
        }?.let {
            if (containsKey(it)) {
                getJsonValue<AppUser>(it)?.apply {
                    fallbackUser = this
                }
            } else {
                null
            }
        } ?: run {
            if (cmd == MsgEventCmd.COMMON_CHATROOM_PUBLIC_MESSAGES || cmd == MsgEventCmd.FOLLOW_CHATROOM_PUBLIC_MESSAGES) {
                getJsonValue<List<UserRichList>>("messages")?.firstOrNull()?.sender
            } else {
                null
            }
        }
    }

    fun containsKey(key: String) = contentCache.containsKey(key) || customJson.containsKey(key)

    inline fun <reified T> parseDataJson(): T? {
        return contentCache[KEY_CACHE_DATA] as? T ?: try {
            sAppJson.decodeFromJsonElement<T>(customJson).also {
                contentCache[KEY_CACHE_DATA] = it
            }
        } catch (e: Exception) {
            LogUtils.e("parseDataJson", e)
            null
        }
    }

    inline fun <reified T> getJsonValue(key: String): T? {
        return if (contentCache.containsKey(key)) {
            contentCache[key] as? T
        } else {
            try {
                customJson.parseValue<T?>(key).also {
                    contentCache[key] = it
                }
            } catch (e: Exception) {
                null
            }
        }
    }

    inline fun <reified T> getJsonValue(key: String, default: T): T {
        return getJsonValue(key) ?: default
    }

    fun getJsonInt(key: String): Int? {
        return if (contentCache.containsKey(key)) {
            contentCache[key] as? Int
        } else {
            customJson.getIntOrNull(key)
        }
    }

    fun getJsonInt(key: String, default: Int): Int {
        return getJsonInt(key) ?: default
    }

    fun getJsonLong(key: String): Long? {
        return if (contentCache.containsKey(key)) {
            contentCache[key] as? Long
        } else {
            customJson.getLongOrNull(key)
        }
    }

    fun getJsonLong(key: String, default: Long): Long {
        return getJsonLong(key) ?: default
    }

    fun getJsonFloat(key: String): Float? {
        return if (contentCache.containsKey(key)) {
            contentCache[key] as? Float
        } else {
            customJson.getFloatOrNull(key)
        }
    }

    fun getJsonFloat(key: String, default: Float): Float {
        return getJsonFloat(key) ?: default
    }

    fun getJsonBoolean(key: String): Boolean? {
        return if (contentCache.containsKey(key)) {
            contentCache[key] as? Boolean
        } else {
            customJson.getBoolOrNull(key)
        }
    }

    fun getJsonBoolean(key: String, default: Boolean): Boolean {
        return getJsonBoolean(key) ?: default
    }

    fun getJsonString(key: String): String? {
        return if (contentCache.containsKey(key)) {
            contentCache[key] as? String
        } else {
            customJson.getStringOrNull(key)
        }
    }

    fun getJsonString(key: String, default: String): String {
        return getJsonString(key) ?: default
    }

    fun getContentText(): String? {
        return getJsonValue<String>("contentText")?.takeIsNotEmpty()
            ?: getJsonValue<String>("content")?.takeIsNotEmpty()
            ?: summary
    }

}

data class UCGiftMessage(
    val rawInstanceMessage: UCCustomMessage,
    val gift: GiftWrapper,
    override val base: UCMessage = rawInstanceMessage.base,
) : UCInstanceMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

    override val user: AppUser
        get() = gift.sender

    override fun getSummaryString(): String {
        return "礼物消息, id: ${gift.gift.id}, 名称: ${gift.gift.name}"
    }
}

data class UCEmojiMessage(
    val rawInstanceMessage: UCCustomMessage,
    val type: Int,
    val value: String,
    override val base: UCMessage = rawInstanceMessage.base,
) : UCInstanceMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

    companion object {
        const val TYPE_DICE = 1
        const val TYPE_NUMBER_1 = 2
        const val TYPE_NUMBER_3 = 3
        const val TYPE_NUMBER_5 = 4
        const val TYPE_GUESSING_FIST = 5
    }

    override fun getSummaryString(): String {
        val name = when (type) {
            TYPE_DICE -> "骰子"
            in TYPE_NUMBER_1..TYPE_NUMBER_5 -> "幸运数字"
            TYPE_GUESSING_FIST -> "猜拳"
            else -> "未知"
        }
        return "表情消息(${name}), type: $type, name: $value"
    }
}

data class UCMicEmojiMessage(
    val rawInstanceMessage: UCCustomMessage,
    val content: EmojiMessageContent,
    override val base: UCMessage = rawInstanceMessage.base,
) : UCInstanceMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

    companion object {
        const val TYPE_WEBP = 1
        const val TYPE_WEBP_AND_RESULT = 2

        fun isSupportEmoji(content: EmojiMessageContent) = content.type == 1 || content.type == 2
    }

    val type: Int
        get() = content.type

    override val user: AppUser
        get() = content.user

    override fun getSummaryString(): String {
        val type = when (content.type) {
            TYPE_WEBP -> "普通动态表情"
            TYPE_WEBP_AND_RESULT -> "带结果的动态表情"
            else -> "${content.type}"
        }
        return "表情消息(${content.emojiEffect.name}), type: $type"
    }
}

interface UCCallMessage : UCInstanceMessage, IUCCustomMessage {

    val callId: String

    val showInMsgList: Boolean
        get() = false

}

sealed interface UCVoiceCallMessage : UCCallMessage {

    override fun getSummaryString(): String {
        return "语音通话消息, callId: $callId, cmd: $cmd"
    }

    data class Invite(
        val rawInstanceMessage: UCCustomMessage,
        val invite: VoiceCallInvite,
        override val base: UCMessage = rawInstanceMessage.base,
    ) : UCVoiceCallMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

        override val cmd: String = MsgEventCmd.VOICE_CALL_INVITE

        override val callId: String
            get() = invite.callId
    }

    data class Start(
        val rawInstanceMessage: UCCustomMessage,
        val notice: VoiceCallNotice,
        override val base: UCMessage = rawInstanceMessage.base,
    ) : UCVoiceCallMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

        override val callId: String
            get() = notice.callId

    }

    data class Other(
        val rawInstanceMessage: UCCustomMessage,
        val notice: VoiceCallNotice,
        override val base: UCMessage = rawInstanceMessage.base,
    ) : UCVoiceCallMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

        override val showInMsgList: Boolean
            get() = cmd == MsgEventCmd.VOICE_CALL_FINISH

        override val callId: String
            get() = notice.callId

    }
}


sealed interface UCVideoCallMessage : UCCallMessage {

    override fun getSummaryString(): String {
        return "视频通话消息, callId: $callId, cmd: $cmd"
    }

    data class Invite(
        val rawInstanceMessage: UCCustomMessage,
        val invite: VideoCallInvite,
        override val base: UCMessage = rawInstanceMessage.base,
    ) : UCVideoCallMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

        override val cmd: String = MsgEventCmd.VIDEO_CALL_INVITE

        override val callId: String
            get() = invite.callId

    }

    data class Start(
        val rawInstanceMessage: UCCustomMessage,
        val notice: VideoCallNotice,
        override val base: UCMessage = rawInstanceMessage.base,
    ) : UCVideoCallMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

        override val callId: String
            get() = notice.callId

    }

    data class Match(
        val rawInstanceMessage: UCCustomMessage,
        val notice: VideoMatchNotice,
        override val base: UCMessage = rawInstanceMessage.base,
    ) : UCVideoCallMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

        override val callId: String
            get() = notice.callId

    }

    data class Other(
        val rawInstanceMessage: UCCustomMessage,
        val notice: VideoCallNotice,
        override val base: UCMessage = rawInstanceMessage.base,
    ) : UCVideoCallMessage, IUCMessage by rawInstanceMessage, IUCCustomMessage by rawInstanceMessage {

        override val callId: String
            get() = notice.callId

        override val showInMsgList: Boolean
            get() = cmd == MsgEventCmd.VIDEO_CALL_FINISH
    }
}

/**
 * 线程安全，可以放在子线程调用
 */
fun UCMessage.toUCInstanceMessage(): UCInstanceMessage {
    return try {
        when (this) {
            is UCMessage.RCIM -> toUCInstanceMessage()
            is UCMessage.TIM -> toUCInstanceMessage()
        }
    } catch (e: Exception) {
        if (isProd && isRelease) {
            UCUnknownMessage(base = this)
        } else {
            throw e
        }
    }
}

private fun UCMessage.RCIM.toUCInstanceMessage(): UCInstanceMessage {
    return when (val messageContent = rawMessage.realMessageContent) {
        is TextMessage -> {
            UCTextMessage(base = this, text = messageContent.content.orEmpty())
        }

        is HQVoiceMessage -> {
            UCVoiceMessage(
                base = this,
                duration = messageContent.duration,
                url = messageContent.fileUrl?.toString().orEmpty(),
                localUri = messageContent.localPath,
                uuid = rawMessage.uId.takeIsNotEmpty()
            )
        }

        is ImageMessage -> {

            val (width, height) = getImageSize()

            val thumbElem = ImageElem(
                width = width,
                height = height,
                url = messageContent.thumUri?.takeIf { !IMUtils.isLocalUri(it) }?.toString() ?: messageContent.remoteUri?.toString().orEmpty(),
                localUri = messageContent.thumUri?.takeIf { IMUtils.isLocalUri(it) } ?: messageContent.localUri,
                uuid = rawMessage.uId.takeIsNotEmpty()?.let { "thumb_$it" }
            )

            val originElem = ImageElem(
                width = width,
                height = height,
                url = messageContent.remoteUri?.toString().orEmpty(),
                localUri = messageContent.localUri,
                uuid = rawMessage.uId.takeIsNotEmpty()?.let { "large_$it" }
            )

            UCImageMessage(
                base = this,
                thumbElem = thumbElem,
                largeElem = originElem,
                originElem = originElem,
            )
        }

        is AppEventMessage -> {
            parseCustomMessage(UCCustomMessage(base = this))
        }

        is TribeTipContent -> {
            val rawContent = rawMessage.content
            val transformContent = AppPersistMessage.obtain(
                MessageBundle.Custom.create(
                    cmd = MsgEventCmd.TRIBE_CUSTOM_CONTENT,
                    data = buildJsonObject {
                        put("value", messageContent.tribeId)
                        put("name", messageContent.name)
                    },
                    summary = app.getString(R.string.format_create_tribe, messageContent.tribeId)
                )
            ).also { msg ->
                msg.userInfo = sUser.toIMUser().run {
                    UserInfo(id, nickname, avatarUrl.toUri()).also {
                        it.extra = sAppJson.encodeToString(this)
                    }
                }
            }
            if (rawContent is RecallNotificationMessage) {
                rawContent.originalMessageContent = transformContent
            } else {
                rawMessage.content = transformContent
            }
            UCCustomMessage(base = UCMessage.RCIM(rawMessage))
        }

        else -> {
            UCUnknownMessage(base = this)
        }
    }
}

private fun UCMessage.TIM.toUCInstanceMessage(): UCInstanceMessage {
    return when (rawMessage.elemType) {

        V2TIMMessage.V2TIM_ELEM_TYPE_TEXT -> {
            UCTextMessage(
                base = this,
                text = rawMessage.textElem.text.orEmpty()
            )
        }

        V2TIMMessage.V2TIM_ELEM_TYPE_SOUND -> {
            val soundElem = rawMessage.soundElem
            UCVoiceMessage(
                base = this,
                duration = soundElem.duration,
                url = runBlocking {
                    suspendCoroutine {
                        // 不知道sdk为什么要设计成一个异步回调，看sdk完全就可以写成一个简单同步方法，不是一个耗时操作
                        // 目前这么写没任何问题，没有挂起
                        soundElem.getUrl(object : V2TIMValueCallback<String?> {
                            override fun onSuccess(result: String?) {
                                it.resume(result.orEmpty())
                            }

                            override fun onError(code: Int, desc: String?) {
                                it.resume("")
                            }
                        })
                    }
                },
                localUri = soundElem.path?.let { File(it) }?.takeIf { it.exists() }?.let { Uri.fromFile(it) },
                uuid = soundElem.uuid
            )
        }


        V2TIMMessage.V2TIM_ELEM_TYPE_IMAGE -> {
            val imageElem = rawMessage.imageElem

            var thumbElem: ImageElem? = null
            var largeElem: ImageElem? = null
            var originElem: ImageElem? = null

            val localUri = imageElem.path?.let { File(it) }?.takeIf { it.exists() }?.let { Uri.fromFile(it) }

            for (item in imageElem.imageList) {
                val width = if (item.width == 0) {
                    getImageSize()?.width ?: 0
                } else {
                    item.width
                }
                val height = if (item.height == 0) {
                    getImageSize()?.height ?: 0
                } else {
                    item.height
                }
                when (item.type) {
                    V2TIMImageElem.V2TIM_IMAGE_TYPE_THUMB -> {
                        thumbElem = ImageElem(
                            width = width,
                            height = height,
                            url = item.url.orEmpty(),
                            localUri = localUri,
                            uuid = item.uuid
                        )
                    }

                    V2TIMImageElem.V2TIM_IMAGE_TYPE_LARGE -> {
                        largeElem = ImageElem(
                            width = width,
                            height = height,
                            url = item.url.orEmpty(),
                            localUri = localUri,
                            uuid = item.uuid
                        )
                    }

                    V2TIMImageElem.V2TIM_IMAGE_TYPE_ORIGIN -> {
                        originElem = ImageElem(
                            width = width,
                            height = height,
                            url = item.url.orEmpty(),
                            localUri = localUri,
                            uuid = item.uuid
                        )
                    }
                }
            }

            UCImageMessage(
                base = this,
                thumbElem = thumbElem ?: largeElem ?: originElem,
                largeElem = largeElem ?: originElem ?: thumbElem,
                originElem = originElem,
            )
        }

        V2TIMMessage.V2TIM_ELEM_TYPE_CUSTOM -> {
            parseCustomMessage(UCCustomMessage(base = this))
        }

        else -> {
            UCUnknownMessage(base = this)
        }
    }
}

private fun UCMessage.parseCustomMessage(customMessage: UCCustomMessage): UCInstanceMessage {
    return when (customMessage.cmd) {
        "" -> {
            UCUnknownMessage(base = this)
        }

        MsgEventCmd.TXT_MSG -> {
            UCTextMessage(base = this, text = customMessage.getContentText().orEmpty())
        }

        MsgEventCmd.GIVE_GIFT, MsgEventCmd.GIVE_LUCKY_GIFT -> {
            val gift = customMessage.parseDataJson<GiftWrapper>()
            if (gift != null) {
                if (customMessage.cmd == MsgEventCmd.GIVE_LUCKY_GIFT) {
                    gift.giftType = 2
                }
                UCGiftMessage(rawInstanceMessage = customMessage, gift = gift)
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.GIVE_GIFT_COMBO_FINISHED -> {
            if (AppUserPartition.current.isCupid) {
                val gift = customMessage.parseDataJson<GiftWrapper>()
                if (gift != null) {
                    gift.giftType = 6
                    UCGiftMessage(rawInstanceMessage = customMessage, gift = gift)
                } else {
                    UCUnknownMessage(base = this)
                }
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.INTERACTIVE_MSG -> {
            val type = customMessage.getJsonInt("type", 0)
            if (type in UCEmojiMessage.TYPE_DICE..UCEmojiMessage.TYPE_GUESSING_FIST) {
                UCEmojiMessage(rawInstanceMessage = customMessage, type = type, value = customMessage.getJsonString("result", ""))
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.AUDIOROOM_SEND_EMOJI -> {
            val content = customMessage.parseDataJson<EmojiMessageContent>()
            if (content != null && UCMicEmojiMessage.isSupportEmoji(content)) {
                UCMicEmojiMessage(rawInstanceMessage = customMessage, content = content)
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.VOICE_CALL_INVITE -> {
            val invite = customMessage.parseDataJson<VoiceCallInvite>()
            if (invite != null) {
                UCVoiceCallMessage.Invite(rawInstanceMessage = customMessage, invite = invite)
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.VOICE_CALL_ANSWER,
        MsgEventCmd.VOICE_CALL_START -> {
            val notice = customMessage.parseDataJson<VoiceCallNotice>()
            if (notice != null) {
                UCVoiceCallMessage.Start(rawInstanceMessage = customMessage, notice = notice)
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.VOICE_CALL_REFUSE,
        MsgEventCmd.VOICE_CALL_BUSY,
        MsgEventCmd.VOICE_CALL_CANCEL,
        MsgEventCmd.VOICE_CALL_TIMEOUT,
        MsgEventCmd.VOICE_CALL_FINISH,
        MsgEventCmd.VOICE_CALL_WILL_USE_COIN -> {
            val notice = customMessage.parseDataJson<VoiceCallNotice>()
            if (notice != null) {
                UCVoiceCallMessage.Other(rawInstanceMessage = customMessage, notice = notice)
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.VIDEO_CALL_INVITE -> {
            val invite = customMessage.parseDataJson<VideoCallInvite>()
            if (invite != null) {
                UCVideoCallMessage.Invite(rawInstanceMessage = customMessage, invite = invite)
            } else {
                UCUnknownMessage(base = this)
            }
        }

        MsgEventCmd.VIDEO_CALL_ANSWER,
        MsgEventCmd.VIDEO_CALL_START -> {
            if (customMessage.getJsonInt("invite_method", 0) == 2) {
                val notice = customMessage.parseDataJson<VideoMatchNotice>()
                if (notice != null) {
                    UCVideoCallMessage.Match(rawInstanceMessage = customMessage, notice = notice)
                } else {
                    UCUnknownMessage(base = this)
                }
            } else {
                val notice = customMessage.parseDataJson<VideoCallNotice>()
                if (notice != null) {
                    UCVideoCallMessage.Start(rawInstanceMessage = customMessage, notice = notice)
                } else {
                    UCUnknownMessage(base = this)
                }
            }
        }

        MsgEventCmd.VIDEO_CALL_REFUSE,
        MsgEventCmd.VIDEO_CALL_BUSY,
        MsgEventCmd.VIDEO_CALL_CANCEL,
        MsgEventCmd.VIDEO_CALL_TIMEOUT,
        MsgEventCmd.VIDEO_CALL_FINISH,
        MsgEventCmd.VIDEO_CALL_WILL_OUT_OF_BALANCE,
        MsgEventCmd.VIDEO_CALL_ACCOUNT_INFO -> {
            val notice = customMessage.parseDataJson<VideoCallNotice>()
            if (notice != null) {
                UCVideoCallMessage.Other(rawInstanceMessage = customMessage, notice = notice)
            } else {
                UCUnknownMessage(base = this)
            }
        }

        else -> {
            customMessage
        }
    }
}

val UCInstanceMessage.sequence
    get() = base.sequence

val UCInstanceMessage.isNeedReadReceipt
    get() = base.isNeedReadReceipt

fun UCInstanceMessage.toPushInfo(params: SendParams): PushInfo? {
    val isUCOO = IMCompatCore.userPartition.isUCOO
    val desc = when (this) {
        is UCTextMessage -> {
            text.take(40)
        }

        is UCImageMessage -> {
            if (isUCOO) {
                id2String(R.string.对方发来一张图片)
            } else {
                id2String(R.string.cpd对方发来一张图片)
            }
        }

        is UCVoiceMessage -> {
            if (isUCOO) {
                id2String(R.string.对方发来一段语音)
            } else {
                id2String(R.string.cpd对方发来一段语音)
            }
        }

        is UCEmojiMessage -> {
            if (isUCOO) {
                id2String(R.string.对方发来一个互动表情)
            } else {
                id2String(R.string.cpd对方发来一个互动表情)
            }
        }

        is IUCCustomMessage -> {
            summary ?: if (isUCOO) {
                id2String(R.string._系统通知_)
            } else {
                id2String(R.string.cpd_系统通知_)
            }
        }

        else -> {
            return null
        }
    }

    return if (params.type == ConversationType.C2C) {
        PushInfo(title = sUser.nickname, desc = desc, avatar = sUser.avatarUrl)
    } else {
        PushInfo(
            title = if (params.receiver.contains("tribe")) {
                if (isUCOO) {
                    id2String(R.string.部落消息)
                } else {
                    id2String(R.string.cpd_UCOO群聊消息)
                }
            } else {
                id2String(R.string.UCOO群聊消息)
            },
            desc = desc,
            avatar = ""
        )
    }
}