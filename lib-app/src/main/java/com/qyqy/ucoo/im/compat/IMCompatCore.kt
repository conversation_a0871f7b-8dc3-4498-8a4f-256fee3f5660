package com.qyqy.ucoo.im.compat

import android.content.Context
import android.os.SystemClock
import androidx.annotation.MainThread
import androidx.collection.LruCache
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.hjq.language.MultiLanguages
import com.overseas.common.utils.TaskThrottle
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.InValidCoroutineScope
import com.qyqy.ucoo.UserPartition
import com.qyqy.ucoo.account.AccountToken
import com.qyqy.ucoo.base.toastError
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.translation.TranslateApi
import com.qyqy.ucoo.config.UIConfig
import com.qyqy.ucoo.core.rtm.RtmManager
import com.qyqy.ucoo.getUserPartition
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.getBoolOrNull
import com.qyqy.ucoo.http.getStringOrNull
import com.qyqy.ucoo.http.parseValue
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.IMManager
import com.qyqy.ucoo.im.bean.MsgSendCondition
import com.qyqy.ucoo.im.chat.ChatRepository
import com.qyqy.ucoo.im.compat.chat.utils.UIMessageUtils
import com.qyqy.ucoo.im.compat.conversation.AppConversationManger
import com.qyqy.ucoo.im.message.MsgEventCmd
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.lanCode
import com.qyqy.ucoo.login.LoginReason
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.utils.LogUtils
import com.qyqy.ucoo.utils.asInstance
import com.qyqy.ucoo.utils.takeIsNotEmpty
import com.tencent.imsdk.v2.V2TIMConversation
import com.tencent.imsdk.v2.V2TIMManager
import com.tencent.imsdk.v2.V2TIMMessage
import io.rong.imlib.model.Conversation
import io.rong.imlib.model.Message
import io.rong.imlib.model.ReceivedProfile
import io.rong.message.ReadReceiptMessage
import io.rong.message.RecallNotificationMessage
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.channels.BufferOverflow
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.filterIsInstance
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.coroutines.withTimeoutOrNull
import kotlinx.serialization.json.JsonObject
import kotlin.coroutines.EmptyCoroutineContext

object IMCompatCore {

    private data class CacheMessage(
        val message: UCInstanceMessage,
        val cacheTime: Long = SystemClock.elapsedRealtime(),
    )

    /**
     * 注意线程问题
     * RCIMListener回调是在im线程
     * IMListener回调在ui线程
     */
    private val rcimListener by lazy {
        object : RCIMListener {
            /**
             * 签名过期，目前没有重连机制，重新退出登录
             */
            override fun onUserSigExpired() {
                synchronizedDispatchListener {
                    it.onClientOffline(LoginReason.Expired)
                }
            }

            /**
             * 被其他端踢下线
             */
            override fun onKickedOffline() {
                synchronizedDispatchListener {
                    it.onClientOffline(LoginReason.Kick)
                }
            }

            override fun onConversationChanged(list: List<Conversation>) {
                if (list.isEmpty()) {
                    return
                }
                IMUtils.launchOnUnMain {
                    val conversationList = list.map { UCConversation.RCIM(it) }
                    dispatchListener {
                        it.onConversationChanged(conversationList)
                    }
                }
            }

            override fun onConversationDeleted(idList: Set<String>) {
                if (idList.isEmpty()) {
                    return
                }
                synchronizedDispatchListener {
                    it.onConversationDeleted(idList)
                }
            }

            override fun onSendNewMessage(message: Message, onlyLocal: Boolean) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(message.targetId)) {
                            it.onSendNewMessage(ucMessage, onlyLocal)
                        }
                    }
                }
            }

            override fun onSendMessageResult(message: Message, success: Boolean) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(message.targetId)) {
                            it.onSendMessageResult(ucMessage, success)
                        }
                    }
                }
            }

            override fun onResendMessage(message: Message) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(message.targetId)) {
                            it.onResendMessage(ucMessage)
                        }
                    }
                }

            }

            override fun onReceivedMessage(message: Message, profile: ReceivedProfile) {
                log("onReceivedMessage rc $message")
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchRecvNewMessage(ucMessage, profile.isOffline)
                }
            }

            override fun onReadReceiptReceived(message: Message) {
                val content = message.content.asInstance<ReadReceiptMessage>() ?: return
                synchronizedDispatchListener {
                    if (it.match(message.targetId)) {
                        it.onReadReceiptReceived(MessageReadReceipt.Full(message.targetId, content.lastMessageSendTime))
                    }
                }
            }

            override fun onMessageRecalled(
                message: Message,
                recallNotificationMessage: RecallNotificationMessage,
            ) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onMessageRecalled(ucMessage)
                        }
                    }
                }
            }

            override fun onMessageDeleted(message: Message) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onMessageDeleted(ucMessage)
                        }
                    }
                }
            }

            override fun onMessageExpansionUpdate(message: Message, changes: Map<String, String>) {
                if (changes.isEmpty()) {
                    return
                }
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchListener({
                        messageCache.put(ucMessage.id, CacheMessage(ucMessage))
                    }) {
                        if (it.match(ucMessage)) {
                            it.onMessageExpansionUpdate(ucMessage, true, changes)
                        }
                    }
                }
            }

            override fun onMessageExpansionRemove(message: Message, keyArray: Array<String>) {
                if (keyArray.isEmpty()) {
                    return
                }
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.RCIM(message).toUCInstanceMessage()
                    dispatchListener({
                        messageCache.put(ucMessage.id, CacheMessage(ucMessage))
                    }) {
                        if (it.match(ucMessage)) {
                            it.onMessageExpansionDelete(ucMessage, keyArray)
                        }
                    }
                }
            }
        }
    }

    /**
     * 注意线程问题
     * TIMListener回调是在im线程
     * IMListener回调在ui线程
     */
    private val timListener by lazy {
        object : TIMListener {
            /**
             * 签名过期，尝试重新获取token连接，失败就退出登录
             */
            override fun onUserSigExpired() {
                synchronizedDispatchListener {
                    it.onClientOffline(LoginReason.Expired)
                }
            }

            /**
             * 被其他端踢下线
             */
            override fun onKickedOffline() {
                synchronizedDispatchListener {
                    it.onClientOffline(LoginReason.Kick)
                }
            }

            override fun onNewConversation(list: List<V2TIMConversation>) {
                if (list.isEmpty()) {
                    return
                }
                IMUtils.launchOnUnMain {
                    val conversationList = list.toUCConversationListByTIM()
                    dispatchListener {
                        it.onNewConversation(conversationList)
                    }
                }
            }

            override fun onConversationChanged(list: List<V2TIMConversation>) {
                IMUtils.launchOnUnMain {
                    val conversationList = list.toUCConversationListByTIM()
                    dispatchListener {
                        it.onConversationChanged(conversationList)
                    }
                }
            }

            override fun onConversationDeleted(idList: List<String>) {
                if (idList.isEmpty()) {
                    return
                }
                val ids = idList.toSet()
                synchronizedDispatchListener {
                    it.onConversationDeleted(ids)
                }
            }

            override fun onSendNewMessage(message: V2TIMMessage, onlyLocal: Boolean) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.TIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onSendNewMessage(ucMessage, onlyLocal)
                        }
                    }
                }
            }

            override fun onSendMessageSuccess(message: V2TIMMessage) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.TIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onSendMessageResult(ucMessage, true)
                        }
                    }
                }
            }

            override fun onSendMessageFailure(message: V2TIMMessage, code: Int, desc: String) {
                IMUtils.launchOnUnMain {
                    val timMessage = UCMessage.TIM(message)
                    if (code in 120001..130000 && desc.isNotEmpty() && desc.contains(UCMessage.TIM.CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS)) {
                        (sAppJson.parseToJsonElement(desc) as? JsonObject)
                            ?.parseValue<TimMsgExtensions?>(UCMessage.TIM.CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS)
                            ?.also { timMessage.updateCloudCustomData(UCMessage.TIM.CLOUD_AND_LOCAL_CUSTOM_KEY_EXTENSIONS, it) }
                    }
                    val ucMessage = timMessage.toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onSendMessageResult(ucMessage, false)
                        }
                    }
                }
            }

            override fun onResendMessage(message: V2TIMMessage) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.TIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onResendMessage(ucMessage)
                        }
                    }
                }
            }

            override fun onRecvNewMessage(message: V2TIMMessage) {
                log("onRecvNewMessage tim $message")
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.TIM(message).toUCInstanceMessage()
                    dispatchRecvNewMessage(ucMessage)
                }
            }

            override fun onRecvMessageReadReceipts(readReceipts: List<MessageReadReceipt>) {
                synchronizedDispatchListener {
                    if (it.filter.isEmpty) {
                        it.onReadReceiptsReceived(readReceipts)
                    } else {
                        readReceipts.forEach { item ->
                            if (item.targetId == it.filter.id) {
                                it.onReadReceiptReceived(item)
                            }
                        }
                    }
                }
            }

            override fun onRecvMessageRevoked(message: V2TIMMessage) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.TIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onMessageRecalled(ucMessage)
                        }
                    }
                }
            }

            override fun onMessageDeleted(message: V2TIMMessage) {
                IMUtils.launchOnUnMain {
                    val ucMessage = UCMessage.TIM(message).toUCInstanceMessage()
                    dispatchListener {
                        if (it.match(ucMessage)) {
                            it.onMessageDeleted(ucMessage)
                        }
                    }
                }
            }

            override fun onRecvMessageModified(message: V2TIMMessage) {
                IMUtils.launchOnUnMain {
                    val timMessage = UCMessage.TIM(message)
                    val ucMessage = timMessage.toUCInstanceMessage()

                    var addEntry: Map<String, String>? = null
                    var changeEntry: Map<String, String>? = null
                    var deleteEntry: Array<String>? = null

                    timMessage.extensions?.apply {
                        if (!addKeys.isNullOrEmpty()) {
                            addEntry = buildMap {
                                addKeys!!.forEach {
                                    put(it, data[it].orEmpty())
                                }
                            }
                        }

                        if (!updateKeys.isNullOrEmpty()) {
                            changeEntry = buildMap {
                                updateKeys!!.forEach {
                                    put(it, data[it].orEmpty())
                                }
                            }
                        }

                        if (!deleteKeys.isNullOrEmpty()) {
                            deleteEntry = deleteKeys!!.toTypedArray()
                        }
                    }

                    dispatchListener({
                        messageCache.put(ucMessage.id, CacheMessage(ucMessage))
                    }) {
                        if (it.match(ucMessage)) {
                            it.onMessageModified(ucMessage)
                            if (addEntry != null) {
                                it.onMessageExpansionUpdate(ucMessage, true, addEntry!!)
                            }
                            if (changeEntry != null) {
                                it.onMessageExpansionUpdate(ucMessage, false, changeEntry!!)
                            }
                            if (deleteEntry != null) {
                                it.onMessageExpansionDelete(ucMessage, deleteEntry!!)
                            }
                        }
                    }
                }
            }
        }
    }

    private const val TAG = "IMCompatCore"

    private const val TRANSLATE_TIME_OUT = 3_000L

    // 是否已登录
    private var isLogin = false

    @Volatile
    private var _imChannel: IMChannel? = null

    val imChannel: IMChannel
        get() = synchronized(this) {
            _imChannel ?: (IMManager.getIMChannelFromCache() ?: IMChannel.MAIN_RCIM_SUB_TIM).also {
                _imChannel = it
            }
        }

    @Volatile
    private var _userPartition: UserPartition? = null

    val userPartition: UserPartition
        get() = synchronized(this) {
            _userPartition ?: AppUserPartition.current
        }

    /**
     * 一秒最多发五条消息
     */
    private val msgSendRateLimit by lazy { TaskThrottle(5, 1050) }

    private val imListenerList: MutableList<IMCompatListener> by lazy(LazyThreadSafetyMode.NONE) {
        mutableListOf()
    }

    private val _messageFlow = MutableSharedFlow<UCInstanceMessage>(extraBufferCapacity = 1, onBufferOverflow = BufferOverflow.DROP_OLDEST)

    val messageReceiveFlow = _messageFlow.asSharedFlow()

    val customMessageFlow = _messageFlow.filterIsInstance<UCCustomMessage>()

    private val chatRepo by lazy {
        ChatRepository()
    }

    private val translateApi = createApi(TranslateApi::class.java)

    private val messageCache by lazy {
        LruCache<String, CacheMessage>(20)
    }

    private var _imCoroutineScope: CoroutineScope? = null

    fun obtainLoginCoroutineScope(): CoroutineScope {
        return _imCoroutineScope ?: InValidCoroutineScope
    }

    @Synchronized
    fun addIMListener(listener: IMCompatListener) {
        if (!imListenerList.contains(listener)) {
            imListenerList.add(listener)
        }
    }

    @Synchronized
    fun removeIMListener(listener: IMCompatListener) {
        imListenerList.remove(listener)
    }

    suspend fun dispatchListener(preRun: suspend () -> Unit = {}, action: suspend (IMCompatListener) -> Unit) {
        withContext(Dispatchers.Main.immediate) {
            preRun()
            val current = userPartition
            imListenerList.toList().forEach {
                if (it.filter.userPartition == null || current == it.filter.userPartition) {
                    action(it)
                }
            }
        }
    }

    fun synchronizedDispatchListener(action: suspend (IMCompatListener) -> Unit) {
        IMUtils.launchOnMain {
            val current = userPartition
            imListenerList.toList().forEach {
                if (it.filter.userPartition == null || current == it.filter.userPartition) {
                    action(it)
                }
            }
        }
    }

    fun initIMSDK(context: Context) {
        RCIMCore.initIMSDK(context)
        TIMCore.initIMSDK(context)
    }

    /**
     * @param imChannel 设置IM的发送和接收通道
     * 在一次登录周期内，不能重复调用此方法，否则可能出现数据错乱
     */
    @MainThread
    fun login(imChannel: IMChannel, tokenInfo: AccountToken) {
        if (isLogin) {
            return
        }
        isLogin = true

        val environmentContext = if (isProd && isRelease) {
            CoroutineExceptionHandler { _, throwable ->
                Firebase.crashlytics.recordException(throwable)
            }
        } else {
            EmptyCoroutineContext
        }
        _imCoroutineScope?.cancel()
        _imCoroutineScope = CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate + environmentContext)

        this._imChannel = imChannel
        this._userPartition = tokenInfo.getUserPartition()

        val mainIMChannel: IMChannel

        when (imChannel) {
            IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                TIMCore.setIMListener(null)
                RCIMCore.setIMListener(rcimListener)
                mainIMChannel = IMChannel.RCIM
            }

            IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                RCIMCore.setIMListener(null)
                TIMCore.setIMListener(timListener)
                mainIMChannel = IMChannel.TIM
            }
        }

        AppConversationManger.login(tokenInfo.userId, userPartition.isUCOO)

        var loaded = false

        if (imChannel != IMChannel.TIM) {
            IMUtils.launchOnMain {
                innerLogin(
                    userId = tokenInfo.userId,
                    signText = tokenInfo.rcToken,
                    executeLogin = { _, token ->
                        RCIMCore.login(token) {
                            if (!loaded && mainIMChannel == IMChannel.RCIM) { // 说明这个通道是主通道
                                loaded = true
                                IMConversationController.loadConversations()
                            }
                        }
                    }
                )
            }
        }

        if (imChannel != IMChannel.RCIM) {
            IMUtils.launchOnMain {
                innerLogin(
                    userId = tokenInfo.userId,
                    signText = tokenInfo.timToken,
                    executeLogin = { userId, token ->
                        TIMCore.login(userId, token) {
                            if (!loaded && mainIMChannel == IMChannel.TIM) { // 说明这个通道是主通道
                                loaded = true
                                IMConversationController.loadConversations()
                            }
                        }
                    }
                )
                // 登录成功
                if (mainIMChannel == IMChannel.TIM) { // 加入直播群
                    TIMCore.joinGroup(RtmManager.getTimGlobalGroupId(userPartition.isUCOO))
                }
            }
        }
    }

    /**
     * 退出登录，退出登录后，需要重新登录
     */
    @MainThread
    fun logout() {
        if (!isLogin) {
            return
        }
        isLogin = false
        _userPartition = null

        TIMCore.setIMListener(null)
        RCIMCore.setIMListener(null)
        messageCache.evictAll()

        if (imChannel != IMChannel.TIM) {
            RCIMCore.logout()
        }

        if (imChannel != IMChannel.RCIM) {
            TIMCore.logout()
        }

        AppConversationManger.clearConversations()

        _imCoroutineScope?.cancel()
        _imCoroutineScope = null
    }

    /**
     * 发送C2C消息
     * @param bundle 消息内容主体
     */
    fun sendC2CMessage(id: String, bundle: MessageBundle, atInfo: MessageAtInfo? = null) {
        sendMessage(SendParams(id, ConversationType.C2C, atInfo), bundle)
    }

    /**
     * 发送群组消息
     * @param bundle 消息内容主体
     */
    fun sendGroupMessage(id: String, bundle: MessageBundle, atInfo: MessageAtInfo? = null) {
        sendMessage(SendParams(id, ConversationType.GROUP, atInfo), bundle)
    }

    /**
     * 发送聊天室消息
     * @param bundle 消息内容主体
     */
    fun sendChatRoomMessage(id: String, bundle: MessageBundle, atInfo: MessageAtInfo? = null) {
        sendMessage(SendParams(id, ConversationType.CHATROOM, atInfo), bundle)
    }

    /**
     * 发送消息
     * @param bundle 消息内容主体
     */
    fun sendMessage(
        id: String,
        type: ConversationType,
        bundle: MessageBundle,
        atInfo: MessageAtInfo? = null,
    ) {
        sendMessage(SendParams(id, type, atInfo), bundle)
    }

    /**
     * 发送消息
     * @param params 消息发送参数
     * @param bundle 消息内容主体
     */
    fun sendMessage(params: SendParams, bundle: MessageBundle) {
        sendMessages(params, listOf(bundle))
    }

    /**
     * 发送多条消息
     * @param params 消息发送参数
     * @param bundles 消息内容主体
     */
    fun sendMessages(params: SendParams, bundles: List<MessageBundle>) {
        if (bundles.isEmpty()) {
            return
        }
        IMUtils.launchCoroutine(IMUtils.sendMsgCoroutineContext) {
            bundles.forEach { bundle ->
                if (bundle.isEmpty) { // 消息体为null不发送消息
                    return@forEach
                }
                try {
                    val (mainMessage, subMessage) = messageBundle2UCMessage(params, bundle)
                    sendUCMessage(
                        params = params,
                        bundle = bundle,
                        mainMessage = mainMessage,
                        subMessage = subMessage
                    )
                } catch (e: Exception) {
                    // tim 有时候创建消息会出现异常
                    return@forEach
                }
            }
        }
    }

    fun insertLocalMessage(id: String, type: ConversationType, bundle: MessageBundle) {
        sendMessage(SendParams(id, type, onlyLocal = true), bundle)
    }

    /**
     * 插入本地消息
     * @param params 消息发送参数
     * @param bundle 消息内容主体
     */
    fun insertLocalMessage(params: SendParams, bundle: MessageBundle) {
        sendMessage(params.copy(onlyLocal = true), bundle)
    }

    /**
     * 插入多条本地消息
     * @param params 消息发送参数
     * @param bundle 消息内容主体
     */
    fun insertLocalMessages(params: SendParams, bundles: List<MessageBundle>) {
        sendMessages(params.copy(onlyLocal = true), bundles)
    }

    /**
     * 一般用于重发消息，该消息只发主通道
     */
    fun sendMessage(params: SendParams, ucMessage: UCMessage) {
        if (params.receiver == sUser.id || ucMessage.sendStatus != MsgSendStatus.Failure) {
            return
        }
        if (ucMessage.getExtraBoolean(UIMessageUtils.CONTENT_ILLEGAL, false)) {
            return
        }
        IMUtils.launchCoroutine(IMUtils.sendMsgCoroutineContext) {
            sendUCMessage(
                params = params,
                bundle = null,
                mainMessage = ucMessage,
                subMessage = null
            )
        }
    }

    /**
     * 撤回消息
     */
    fun recallMessage(ucMessage: UCMessage) {
        when (ucMessage) {
            is UCMessage.RCIM -> RCIMCore.recallMessage(ucMessage.rawMessage)
            is UCMessage.TIM -> TIMCore.recallMessage(ucMessage.rawMessage)
        }
    }

    /**
     * 删除消息
     */
    fun deleteMessage(ucMessage: UCMessage) {
        when (ucMessage) {
            is UCMessage.RCIM -> RCIMCore.deleteMessage(ucMessage.rawMessage)
            is UCMessage.TIM -> TIMCore.deleteMessage(ucMessage.rawMessage)
        }
    }

    /**
     * 标记消息已读
     */
    fun markMessageReadReceipts(message: UCInstanceMessage) {
        if (imChannel != IMChannel.TIM) {
            message.base.apply {
                rcMessage?.also {
                    RCIMCore.markMessageReadReceipts(it)
                } ?: run {
                    RCIMCore.markMessageReadReceipts(targetId, ConversationType.C2C.toRcType(), timestamp)
                }
            }
        }

        if (imChannel != IMChannel.RCIM) {
            val conversationId = message.targetId.id2ConvId(if (message.isC2CMsg) ConversationType.C2C else ConversationType.GROUP)
            TIMCore.cleanConversationUnreadCount(
                isMainChannel = imChannel != IMChannel.MAIN_RCIM_SUB_TIM,
                conversationId = conversationId,
                cleanTimestamp = message.timestamp.plus(500).div(1000)
            )
        }
    }

    /**
     * 标记消息已读
     */
    @Deprecated("腾讯设计的太垃圾，使用markMessageReadReceipts(timestamp: Long")
    fun markMessageReadReceipts(list: List<UCInstanceMessage>) {
        if (imChannel != IMChannel.TIM) {
            list.firstOrNull()?.base?.apply {
                rcMessage?.also {
                    RCIMCore.markMessageReadReceipts(it)
                } ?: run {
                    RCIMCore.markMessageReadReceipts(targetId, ConversationType.C2C.toRcType(), timestamp)
                }
            }
        }

        if (imChannel != IMChannel.RCIM) {
            // 腾讯需要message才能标记，这里没有message则忽略，cleanConversationUnreadCount会标记为全部已读
            list.mapNotNull {
                it.base.timMessage
            }.takeIf { it.isNotEmpty() }?.also {
                TIMCore.markMessageReadReceipts(it)
            }
        }
    }

    /**
     * 会话置顶设置
     */
    fun deleteConversation(id: String, type: ConversationType) {
        val mainIMChannel = getMainIMChannel()

        if (imChannel != IMChannel.TIM) {
            RCIMCore.deleteConversation(
                isMainChannel = mainIMChannel == IMChannel.RCIM,
                id = id,
                type = type.toRcType()
            )
        }
        if (imChannel != IMChannel.RCIM) {
            TIMCore.deleteConversation(
                isMainChannel = mainIMChannel == IMChannel.TIM,
                conversationId = id.id2ConvId(type)
            )
        }
    }

    /**
     * 删除会话
     */
    fun deleteConversation(ucConversation: UCConversation) {
        val mainIMChannel = getMainIMChannel()

        if (imChannel != IMChannel.TIM) {
            RCIMCore.deleteConversation(
                isMainChannel = mainIMChannel == IMChannel.RCIM,
                id = ucConversation.id,
                type = ucConversation.type.toRcType()
            )
        }
        if (imChannel != IMChannel.RCIM) {
            TIMCore.deleteConversation(
                isMainChannel = mainIMChannel == IMChannel.TIM,
                conversationId = ucConversation.stableId
            )
        }
    }

    /**
     * 会话置顶设置
     */
    fun setPinnedConversation(id: String, type: ConversationType, isPinned: Boolean) {
        val mainIMChannel = getMainIMChannel()

        if (imChannel != IMChannel.TIM) {
            RCIMCore.setPinnedConversation(
                isMainChannel = mainIMChannel == IMChannel.RCIM,
                id = id,
                type = type.toRcType(),
                isPinned = isPinned
            )
        }
        if (imChannel != IMChannel.RCIM) {
            TIMCore.setPinnedConversation(
                isMainChannel = mainIMChannel == IMChannel.TIM,
                conversationId = id.id2ConvId(type),
                isPinned = isPinned
            )
        }
    }

    /**
     * 会话置顶设置
     */
    fun setPinnedConversation(ucConversation: UCConversation, isPinned: Boolean) {
        val mainIMChannel = getMainIMChannel()

        if (imChannel != IMChannel.TIM) {
            RCIMCore.setPinnedConversation(
                isMainChannel = mainIMChannel == IMChannel.RCIM,
                id = ucConversation.id,
                type = ucConversation.type.toRcType(),
                isPinned = isPinned
            )
        }
        if (imChannel != IMChannel.RCIM) {
            TIMCore.setPinnedConversation(
                isMainChannel = mainIMChannel == IMChannel.TIM,
                conversationId = ucConversation.stableId,
                isPinned = isPinned
            )
        }
    }

    /**
     * 清空会话未读数
     */
    fun cleanConversationUnreadCount(id: String, type: ConversationType) {
        val mainIMChannel = getMainIMChannel()

        if (imChannel != IMChannel.TIM) {
            RCIMCore.cleanConversationUnreadCount(
                isMainChannel = mainIMChannel == IMChannel.RCIM,
                id = id,
                type = type.toRcType()
            )
        }
        if (imChannel != IMChannel.RCIM) {
            TIMCore.cleanConversationUnreadCount(
                isMainChannel = mainIMChannel == IMChannel.TIM,
                conversationId = id.id2ConvId(type)
            )
        }
    }

    /**
     * 清空会话未读数
     */
    fun cleanConversationUnreadCount(ucConversation: UCConversation) {
        val mainIMChannel = getMainIMChannel()

        if (imChannel != IMChannel.TIM) {
            RCIMCore.cleanConversationUnreadCount(
                isMainChannel = mainIMChannel == IMChannel.RCIM,
                id = ucConversation.id,
                type = ucConversation.type.toRcType()
            )
        }
        if (imChannel != IMChannel.RCIM) {
            TIMCore.cleanConversationUnreadCount(
                isMainChannel = mainIMChannel == IMChannel.TIM,
                conversationId = ucConversation.stableId
            )
        }
    }

    /**
     * 清除所有会话未读数
     */
    fun cleanAllConversationUnreadCount() {
        val mainIMChannel = getMainIMChannel()
        if (imChannel != IMChannel.TIM) {
            RCIMCore.cleanAllConversationUnreadCount(mainIMChannel == IMChannel.RCIM)
        }

        if (imChannel != IMChannel.RCIM) {
            TIMCore.cleanAllConversationUnreadCount(mainIMChannel == IMChannel.TIM)
        }
    }

    /**
     * 设置会话消息免打扰
     */
    fun setConversationReceiveMessageOpt(
        id: String,
        type: ConversationType,
        notDisturbEnable: Boolean,
    ) {
        if (imChannel != IMChannel.TIM) {
            RCIMCore.setConversationReceiveMessageOpt(id, type.toRcType(), notDisturbEnable)
        }

        if (imChannel != IMChannel.RCIM) {
            TIMCore.setConversationReceiveMessageOpt(id, type, notDisturbEnable)
        }
    }

    /**
     * 获取历史消息
     * @param descendPullOrder 是否降序拉取
     * @param onlyLocal 是否拉取本地消息
     * @param enableNotify 为true表示请求成功后，同时会回调onFetchHistoryMessage
     * @return 返回值为null表示失败，不为null表示成功
     */
    suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        count: Int = 20,
        lastMsg: UCMessage? = null,
        descendPullOrder: Boolean = true,
        onlyLocal: Boolean = false,
        enableNotify: Boolean = true,
    ): List<UCInstanceMessage>? {
        val list = withContext(IMUtils.imHandleCoroutineContext) {
            when (imChannel) {
                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                    if (!(lastMsg == null || lastMsg is UCMessage.RCIM)) {
                        return@withContext null
                    }
                    RCIMCore.getHistoryMessages(
                        id = id,
                        type = type.toRcType(),
                        count = count,
                        lastMsg = lastMsg?.rcMessage,
                        descendPullOrder = descendPullOrder,
                        onlyLocal = onlyLocal
                    )?.map { msg ->
                        UCMessage.RCIM(msg).toUCInstanceMessage()
                    }
                }

                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                    if (!(lastMsg == null || lastMsg is UCMessage.TIM)) {
                        return@withContext null
                    }
                    TIMCore.getHistoryMessages(
                        id = id,
                        isC2C = type == ConversationType.C2C,
                        count = count,
                        lastMsg = lastMsg?.timMessage,
                        descendPullOrder = descendPullOrder,
                        onlyLocal = onlyLocal
                    )?.map { msg ->
                        UCMessage.TIM(msg).toUCInstanceMessage()
                    }
                }
            }
        }

        if (enableNotify && !list.isNullOrEmpty()) {
            dispatchListener {
                if (it.match(id)) {
                    it.onFetchHistoryMessagesSuccess(list, descendPullOrder)
                }
            }
        }

        return list
    }

    suspend fun getHistoryMessages(
        id: String,
        type: ConversationType,
        sequence: Long,
        before: Int,
        after: Int,
    ): List<UCInstanceMessage>? {
        return withContext(IMUtils.imHandleCoroutineContext) {
            when (imChannel) {
                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                    RCIMCore.getHistoryMessages(
                        id = id,
                        type = type.toRcType(),
                        sequence = sequence,
                        before = before,
                        after = after
                    )?.map { msg ->
                        UCMessage.RCIM(msg).toUCInstanceMessage()
                    }
                }

                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                    TIMCore.getHistoryMessages(
                        id = id,
                        isC2C = type == ConversationType.C2C,
                        sequence = sequence,
                        before = before,
                        after = after
                    )?.map { msg ->
                        UCMessage.TIM(msg).toUCInstanceMessage()
                    }
                }
            }
        }
    }

    /**
     * 获取会话列表
     */
    suspend fun getConversations(nextSeq: Long, count: Int): IMConversationResult? {
        return withContext(IMUtils.imHandleCoroutineContext) {
            when (imChannel) {
                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                    RCIMCore.getConversations(nextSeq, count)?.let { list ->
                        IMConversationResult(
                            isFinished = list.size < count,
                            nextSeq = list.lastOrNull()?.sentTime ?: 0,
                            list = list.map { UCConversation.RCIM(it) })
                    }
                }

                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                    TIMCore.getConversations(nextSeq, count)?.let {
                        IMConversationResult(
                            isFinished = it.isFinished,
                            nextSeq = it.nextSeq,
                            list = it.conversationList.orEmpty().toUCConversationListByTIM()
                        )
                    }
                }
            }
        }
    }

    /**
     * 获取UI会话
     */
    suspend fun getUIConversation(
        id: String,
        type: ConversationType,
        excludeCache: Boolean = false,
    ): IUCConversation? {
        if (!excludeCache) {
            val cache = AppConversationManger.getUIConversations(id)
            if (cache != null) {
                return cache
            }
        }
        return withContext(IMUtils.imHandleCoroutineContext) {
            when (imChannel) {
                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                    RCIMCore.getConversation(id, type.toRcType())?.let {
                        UCConversation.RCIM(it)
                    }
                }

                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                    TIMCore.getConversation(id, type)?.let {
                        UCConversation.TIM(it)
                    }
                }
            }
        }
    }

    /**
     * 获取IM会话
     */
    suspend fun getIMConversation(
        id: String,
        type: ConversationType,
        excludeCache: Boolean = false,
    ): UCConversation? {
        if (!excludeCache) {
            val cache = IMConversationController.getIMConversations(id, type)
            if (cache != null) {
                return cache
            }
        }
        return withContext(IMUtils.imHandleCoroutineContext) {
            when (imChannel) {
                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                    RCIMCore.getConversation(id, type.toRcType())?.let {
                        UCConversation.RCIM(it)
                    }
                }

                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                    TIMCore.getConversation(id, type)?.let {
                        UCConversation.TIM(it)
                    }
                }
            }
        }
    }

    suspend fun joinConversation(id: String, type: ConversationType, clearHistory: Boolean = false): Boolean {
        return coroutineScope {
            when (imChannel) {
                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                    if (imChannel != IMChannel.RCIM) {
                        launch {
                            TIMCore.joinGroup(id, clearHistory)
                        }
                    }
                    RCIMCore.joinConversation(id, type.toRcType())
                }

                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                    if (imChannel != IMChannel.TIM) {
                        launch {
                            RCIMCore.joinConversation(id, type.toRcType())
                        }
                    }
                    TIMCore.joinGroup(id, clearHistory)
                }
            }
        }
    }

    suspend fun quitConversation(id: String, type: ConversationType, clearHistory: Boolean = false) {
        return coroutineScope {
            when (imChannel) {
                IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> {
                    if (imChannel != IMChannel.RCIM) {
                        launch {
                            TIMCore.quitGroup(id, clearHistory)
                        }
                    }
                    RCIMCore.quitConversation(id, type.toRcType())
                }

                IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> {
                    if (imChannel != IMChannel.TIM) {
                        launch {
                            RCIMCore.quitConversation(id, type.toRcType())
                        }
                    }
                    TIMCore.quitGroup(id, clearHistory)
                }
            }
        }
    }

    suspend fun checkCanSendMessageByC2C(receiver: String): MsgSendCondition? {
        return chatRepo.checkCanSendMessage(receiver).fold({ ret ->
            if (!ret.canChat) {
                synchronizedDispatchListener {
                    if (it.match(receiver)) {
                        it.onMessageCheckFail(ret, null)
                    }
                }
            }
            ret
        }) { e ->
            synchronizedDispatchListener {
                if (it.match(receiver)) {
                    it.onMessageCheckFail(null, e)
                }
            }
            null
        }
    }

    private fun getMainIMChannel(): IMChannel {
        return when (imChannel) {
            IMChannel.RCIM, IMChannel.MAIN_RCIM_SUB_TIM -> IMChannel.RCIM
            IMChannel.TIM, IMChannel.MAIN_TIM_SUB_RC -> IMChannel.TIM
        }
    }

    private suspend inline fun CoroutineScope.innerLogin(
        userId: String,
        signText: String?,
        executeLogin: (String, String?) -> Boolean,
    ) {
        var token: String? = signText
        while (isActive) {
            try {
                if (executeLogin(userId, token.takeIsNotEmpty())) { //  登录成功
                    break
                } else {
                    delay(3000)
                }
            } catch (e: Exception) {
                token = null
            }
        }
    }

    private suspend fun dispatchRecvNewMessage(message: UCInstanceMessage, offline: Boolean = false) {
        val receiverTime = SystemClock.elapsedRealtime()
        val pendingTranslateText: String? = if (userPartition.isUCOO) {
            if (message is UCTextMessage) {
                message.text
            } else if (UIConfig.userConf.micAsrEnabled && message is UCCustomMessage && message.cmd == MsgEventCmd.MIC_SPEECH_TEXT) {
                message.getContentText()
            } else {
                null
            }
        } else {
            null
        }

        val dispatchMsg = if (!pendingTranslateText.isNullOrEmpty()) {
            val code = MultiLanguages.getAppLanguage().lanCode
            val translatedText = translateText(code, pendingTranslateText)
            (messageCache.remove(message.id)?.takeIf { it.cacheTime > receiverTime }?.message ?: message).also {
                if (!translatedText.isNullOrEmpty() && translatedText != pendingTranslateText) { // 成功，原文和译文不同保存翻译
                    it.saveTranslateText(code, translatedText)
                }
            }
        } else {
            messageCache.remove(message.id)?.takeIf { it.cacheTime > receiverTime }?.message ?: message
        }

        dispatchListener {
            if (it.match(dispatchMsg)) {
                it.onRecvNewMessage(dispatchMsg, offline)
                when (dispatchMsg) {
                    is UCVoiceCallMessage -> {
                        it.onVoiceCallMessage(dispatchMsg, offline)
                    }

                    is UCVideoCallMessage -> {
                        it.onVideoCallMessage(dispatchMsg, offline)
                    }

                    is UCCustomMessage -> {
                        it.onRecvNewCustomMessage(dispatchMsg, offline)
                    }
                }
            }
        }

        _messageFlow.emit(dispatchMsg)
    }

    /**
     * 根据参数和消息内容创建UCMessage对象
     */
    private fun messageBundle2UCMessage(
        params: SendParams,
        bundle: MessageBundle,
    ): Pair<UCMessage, UCMessage?> {
        return when (imChannel) {
            IMChannel.RCIM -> {
                Pair(RCIMCore.createMessage(true, params, bundle), null)
            }

            IMChannel.TIM -> {
                Pair(TIMCore.createMessage(true, params, bundle), null)
            }

            IMChannel.MAIN_RCIM_SUB_TIM -> {
                Pair(RCIMCore.createMessage(true, params, bundle), TIMCore.createMessage(false, params, bundle))
            }

            IMChannel.MAIN_TIM_SUB_RC -> {
                Pair(TIMCore.createMessage(true, params, bundle), RCIMCore.createMessage(false, params, bundle))
            }
        }
    }

    /**
     * 发送UCMessage对象
     */
    private suspend fun sendUCMessage(
        params: SendParams,
        bundle: MessageBundle?,
        mainMessage: UCMessage,
        subMessage: UCMessage?,
    ) {
        if (!checkCanSendMessage(params, bundle, mainMessage, subMessage)) { // 检查是否可以发送消息
            return
        }
        msgSendRateLimit.post {
            when (mainMessage) {
                is UCMessage.RCIM -> RCIMCore.sendMessage(isMainChannel = true, params = params, message = mainMessage)
                is UCMessage.TIM -> TIMCore.sendMessage(isMainChannel = true, params = params, message = mainMessage)
            }
            subMessage ?: return@post
            when (subMessage) {
                is UCMessage.RCIM -> RCIMCore.sendMessage(isMainChannel = false, params = params, message = subMessage)
                is UCMessage.TIM -> TIMCore.sendMessage(isMainChannel = false, params = params, message = subMessage)
            }
        }
    }

    private fun List<V2TIMConversation>.toUCConversationListByTIM(): List<UCConversation.TIM> {
        return mapNotNull { conversation ->
            if (!conversation.checkValid()) {
                return@mapNotNull null
            }
            if (conversation.type == V2TIMConversation.V2TIM_GROUP) {
                if (conversation.groupType == V2TIMManager.GROUP_TYPE_PUBLIC) { // 群组会话中的public类型
                    UCConversation.TIM(conversation)
                } else {
                    null
                }
            } else { // C2C会话
                UCConversation.TIM(conversation)
            }
        }
    }

    private suspend fun checkCanSendMessage(
        params: SendParams,
        bundle: MessageBundle?,
        mainMessage: UCMessage,
        subMessage: UCMessage?,
    ): Boolean {
        // 不是本地插入消息且不是重发消息，在发送时需要校验是否可以发送
        return if (!params.onlyLocal && mainMessage.sendStatus != MsgSendStatus.Failure) {
            if (params.type == ConversationType.C2C) {
                val ret = checkCanSendMessageByC2C(params.receiver)
                ret?.takeIf { it.canChat }?.msgExtra?.takeIf { it.isNotEmpty() }?.apply {
                    mainMessage.setMsgExtraForSendMessage(this)
                    subMessage?.setMsgExtraForSendMessage(this)
                }
                ret?.canChat == true
            } else if (params.type == ConversationType.CHATROOM && bundle is MessageBundle.Text) {
                runApiCatching {
                    chatRepo.chatApi.checkTextMessage(mapOf("text" to bundle.text))
                }.toastError().map {
                    it.parseValue<Boolean>("is_ok")
                }.getOrNull().orDefault(false)
            } else {
                true
            }
        } else {
            true
        }
    }

    private suspend fun translateText(code: String, text: String): String? {
        return withTimeoutOrNull(TRANSLATE_TIME_OUT) {
            val result = runApiCatching {
                translateApi.translate(
                    mapOf(
                        "target_lang" to code,
                        "text" to text
                    )
                )
            }.getOrNull()
            if (result?.getBoolOrNull("is_translated") == true) {
                result.getStringOrNull("translated_text")
            } else {
                null
            }
        }
    }

    private fun UCMessage.setMsgExtraForSendMessage(extra: JsonObject) {
        when (this) {
            is UCMessage.RCIM -> {
                updateCloudCustomData(extra)
            }

            is UCMessage.TIM -> {
                updateCloudCustomData(extra)
            }
        }
    }

    private fun IMCompatListener.match(message: UCInstanceMessage): Boolean {
        return match(message.targetId)
    }

    fun IMCompatListener.match(id: String): Boolean {
        return filter.isEmpty || filter.id.isNullOrEmpty() || filter.id == id
    }

    fun log(message: String) {
        LogUtils.i(TAG, message)
    }
}

class IMReLoginException : Exception()
