package com.qyqy.ucoo.im

import android.content.Context
import com.qyqy.ucoo.account.AccountToken
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.config.UserConf
import com.qyqy.ucoo.http.sAppJson
import com.qyqy.ucoo.im.compat.IMChannel
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.event.AppEventMessageHandler
import com.qyqy.ucoo.im.compat.event.CupidEventMessageHandler
import com.qyqy.ucoo.im.compat.event.UCOOEventMessageHandler
import com.qyqy.ucoo.sAppKV
import com.qyqy.ucoo.setting.AppSettings
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.onEach
import kotlinx.serialization.Serializable
import kotlinx.serialization.encodeToString

@Serializable
data class IMEnvConfig(
    val rcWriteEnable: Boolean = true,
    val timWriteEnable: Boolean = true,
    val readFromTim: Boolean = false,
)

object IMManager {

    private const val KEY_IM_CONFIG = "key_im_config_10086"

    fun init(context: Context) {
        IMCompatCore.initIMSDK(context)

        app.accountManager.accountDistinctIdFlow.onEach { account ->
            if (account == null) {
                IMCompatCore.logout()
            } else {
                login(account.tokenInfo)
            }
        }.launchIn(appCoroutineScope)

        AppEventMessageHandler
        UCOOEventMessageHandler
        CupidEventMessageHandler
    }

    fun saveIMEnvConfig(token: AccountToken) {
        if (token.rcWriteEnable != null && token.timWriteEnable != null && token.readFromTim != null) {
            val config = IMEnvConfig(token.rcWriteEnable, token.timWriteEnable, token.readFromTim)
            sAppKV.putString(KEY_IM_CONFIG, sAppJson.encodeToString(config))
        }
    }

    fun saveIMEnvConfig(userConf: UserConf) {
        if (userConf.rcWriteEnable != null && userConf.timWriteEnable != null && userConf.readFromTim != null) {
            val config = IMEnvConfig(userConf.rcWriteEnable, userConf.timWriteEnable, userConf.readFromTim)
            sAppKV.putString(KEY_IM_CONFIG, sAppJson.encodeToString(config))
        }
    }

    fun saveIMEnvConfig(settings: AppSettings) {
        if (settings.rcWriteEnable != null && settings.timWriteEnable != null && settings.readFromTim != null) {
            val config = IMEnvConfig(settings.rcWriteEnable, settings.timWriteEnable, settings.readFromTim)
            sAppKV.putString(KEY_IM_CONFIG, sAppJson.encodeToString(config))
        }
    }

    fun getIMChannelFromCache(): IMChannel? = sAppKV.getString(KEY_IM_CONFIG).takeIf {
        it.isNotBlank()
    }?.let {
        sAppJson.decodeFromString<IMEnvConfig>(it)
    }?.let {
        if (it.readFromTim) {
            if (it.rcWriteEnable) {
                IMChannel.MAIN_TIM_SUB_RC
            } else {
                IMChannel.TIM
            }
        } else {
            if (it.timWriteEnable) {
                IMChannel.MAIN_RCIM_SUB_TIM
            } else {
                IMChannel.RCIM
            }
        }
    }

    private fun login(tokenInfo: AccountToken) {
        IMCompatCore.login(IMChannel.TIM, tokenInfo)
    }
}


