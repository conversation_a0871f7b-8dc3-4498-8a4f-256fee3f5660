package com.qyqy.ucoo.im.room.layout

import android.animation.ObjectAnimator
import android.animation.PropertyValuesHolder
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.Dialog
import android.graphics.Color
import android.graphics.Rect
import android.graphics.Typeface
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.os.SystemClock
import android.text.style.AbsoluteSizeSpan
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateDecelerateInterpolator
import android.widget.FrameLayout
import android.widget.ImageView
import android.widget.PopupWindow
import android.widget.TextView
import androidx.annotation.CallSuper
import androidx.annotation.StringRes
import androidx.appcompat.widget.AppCompatTextView
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.ui.Modifier
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.animation.doOnEnd
import androidx.core.graphics.toColorInt
import androidx.core.text.bold
import androidx.core.text.buildSpannedString
import androidx.core.text.color
import androidx.core.text.inSpans
import androidx.core.view.doOnLayout
import androidx.core.view.forEach
import androidx.core.view.isGone
import androidx.core.view.isInvisible
import androidx.core.view.isVisible
import androidx.core.view.updatePadding
import androidx.core.widget.TextViewCompat
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.flowWithLifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.LayoutManager
import com.bumptech.glide.Glide
import com.github.penfeizhou.animation.webp.WebPDrawable
import com.overseas.common.ext.Disposable
import com.overseas.common.ext.SingleSelectGroup
import com.overseas.common.ext.click
import com.overseas.common.ext.clickOfVH
import com.overseas.common.ext.doOnLifecycleEvent
import com.overseas.common.ext.launch
import com.overseas.common.ext.launchWhenStarted
import com.overseas.common.ext.linearGradient
import com.overseas.common.ext.parentViewGroup
import com.overseas.common.ext.safePopupWindow
import com.overseas.common.ext.textColor
import com.overseas.common.sntp.SNTPManager
import com.overseas.common.utils.dp
import com.overseas.common.utils.id2Color
import com.overseas.common.utils.sp
import com.overseas.common.utils.suspendOnEnd
import com.overseas.common.utils.task.handleOnDismiss
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.D_DIN_TYPEFACE
import com.qyqy.ucoo.DataPoint
import com.qyqy.ucoo.R
import com.qyqy.ucoo.account.AppUser
import com.qyqy.ucoo.account.INVALID_USER
import com.qyqy.ucoo.account.User
import com.qyqy.ucoo.account.isBoy
import com.qyqy.ucoo.account.isInvalid
import com.qyqy.ucoo.account.isSelf
import com.qyqy.ucoo.account.needAddFriends
import com.qyqy.ucoo.account.orInvalid
import com.qyqy.ucoo.account.userId
import com.qyqy.ucoo.anyStartActivity
import com.qyqy.ucoo.app
import com.qyqy.ucoo.compose.data.PkUserInfo
import com.qyqy.ucoo.compose.orDefault
import com.qyqy.ucoo.compose.presentation.profile.UserProfileNavigator
import com.qyqy.ucoo.compose.presentation.room.WheelPickerBottomSheet
import com.qyqy.ucoo.compose.ui.BottomComposeDialog
import com.qyqy.ucoo.compose.vm.room.ChatRoomExpansionMvi
import com.qyqy.ucoo.compose.vm.room.ChatRoomExpansionViewModel
import com.qyqy.ucoo.compose.vm.room.cross.CrossRoomPkManager
import com.qyqy.ucoo.compose.vm.room.cross.CrossRoomPkRepository
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.databinding.LayoutCpRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutCrossRoomPkModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutGameMicSeatsItemBinding
import com.qyqy.ucoo.databinding.LayoutGameRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutLoveRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutMicSeatsCpItemBinding
import com.qyqy.ucoo.databinding.LayoutMicSeatsItemBinding
import com.qyqy.ucoo.databinding.LayoutMicSeatsLovePairItemBinding
import com.qyqy.ucoo.databinding.LayoutMicSeatsLoveSelectItemBinding
import com.qyqy.ucoo.databinding.LayoutMicSeatsLoveShowItemBinding
import com.qyqy.ucoo.databinding.LayoutNormalRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutPkRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutPrivateRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutPrivateRoomV2ModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutStationRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutTextRoomModePlayerBinding
import com.qyqy.ucoo.databinding.LayoutWeddingMicSeatsItemBinding
import com.qyqy.ucoo.databinding.LayoutWeddingRoomModePlayerBinding
import com.qyqy.ucoo.glide.autoLoad
import com.qyqy.ucoo.glide.fixInto
import com.qyqy.ucoo.glide.loadAvatar
import com.qyqy.ucoo.glide.loadWith
import com.qyqy.ucoo.im.bean.ButtonMode
import com.qyqy.ucoo.im.bean.IMicLove
import com.qyqy.ucoo.im.bean.LoveInfo
import com.qyqy.ucoo.im.bean.MicLovePairSeat
import com.qyqy.ucoo.im.bean.MicLoveShowSeat
import com.qyqy.ucoo.im.bean.MicSeat
import com.qyqy.ucoo.im.bean.PKEvent
import com.qyqy.ucoo.im.bean.PkInfo
import com.qyqy.ucoo.im.bean.PrivateRoomStatus
import com.qyqy.ucoo.im.bean.Room
import com.qyqy.ucoo.im.bean.RoomMode
import com.qyqy.ucoo.im.bean.RoomTokenRequest
import com.qyqy.ucoo.im.bean.Scene
import com.qyqy.ucoo.im.bean.WeddingRoomInfo
import com.qyqy.ucoo.im.bean.sceneType
import com.qyqy.ucoo.im.bean.toAppUser
import com.qyqy.ucoo.im.room.ChatRoomActivity
import com.qyqy.ucoo.im.room.ChatRoomGiftViewModel
import com.qyqy.ucoo.im.room.ChatRoomViewModel
import com.qyqy.ucoo.im.room.CpContract
import com.qyqy.ucoo.im.room.CpViewModel
import com.qyqy.ucoo.im.room.RoomContract
import com.qyqy.ucoo.im.room.dialog.PKDialogController
import com.qyqy.ucoo.im.room.game.GameConf
import com.qyqy.ucoo.im.room.game.QuickStartGameViewModel
import com.qyqy.ucoo.im.room.support.PkSupportRankFragment
import com.qyqy.ucoo.isMyCp
import com.qyqy.ucoo.mine.AlertButtonDialogFragment
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.setting.SettingsRepository
import com.qyqy.ucoo.toast
import com.qyqy.ucoo.user.RechargeDialogFragment
import com.qyqy.ucoo.utils.MyFontTypeSpan
import com.qyqy.ucoo.utils.VoiceRoomHelper
import com.qyqy.ucoo.utils.asInstance
import com.qyqy.ucoo.utils.buildName
import com.qyqy.ucoo.utils.rtc.EMPTY_MIC
import com.qyqy.ucoo.utils.updateLayoutParams
import com.qyqy.ucoo.utils.web.JsBridgeWebActivity.Companion.startJsWebActivity
import com.qyqy.ucoo.widget.Spirit
import com.qyqy.ucoo.widget.SpiritViewHolder
import com.qyqy.ucoo.widget.avatar.AvatarView
import com.qyqy.ucoo.widget.launch
import com.yy.yyeva.util.EvaConstant
import com.yy.yyeva.view.EvaAnimViewV3
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import me.jessyan.autosize.utils.ScreenUtils
import tech.sud.gip.core.SudGIP
import tech.sud.mgp.SudMGPWrapper.model.GameViewInfoModel
import tech.sud.mgp.SudMGPWrapper.state.SudMGPMGState
import kotlin.math.abs


private class MicSeatCallback : DiffUtil.ItemCallback<Any>() {

    override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
        oldItem as MicSeat
        newItem as MicSeat
        return oldItem.index == newItem.index
    }

    override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
        oldItem as MicSeat
        newItem as MicSeat
        if (oldItem.hasUser != newItem.hasUser) {
            return false
        }
        if (!oldItem.hasUser) { // 空麦位是一样的不需要刷新
            return true
        }
        if (oldItem.user.id != newItem.user.id) { // 不同的用户
            return false
        }
        return oldItem.user.nickname == newItem.user.nickname && oldItem.user.avatarUrl == newItem.user.avatarUrl && oldItem.user.avatarFrame == newItem.user.avatarFrame && oldItem.user.publicCP?.id == newItem.user.publicCP?.id && oldItem.user.cpExtraInfo?.levelInfo?.level == newItem.user.cpExtraInfo?.levelInfo?.level && oldItem.mic.isSpeaking == newItem.mic.isSpeaking && oldItem.buttonMode == newItem.buttonMode && oldItem.heartValue == newItem.heartValue && oldItem.mic.mute == newItem.mic.mute && oldItem.hasCrown == newItem.hasCrown && oldItem.loveToUid == newItem.loveToUid && oldItem.loveToIndex == newItem.loveToIndex && oldItem.iLoveUid == newItem.iLoveUid && oldItem.user.colorString() == newItem.user.colorString()
    }

}

fun Room.isSupportRoomMode(): Boolean {
    return roomModeBean !is RoomMode.Unknown
}

abstract class MicSeatsLayout(
    protected val micSeatsLayout: RecyclerView,
    protected val chatRoomViewModel: ChatRoomViewModel,
) {

    protected lateinit var spirit: Spirit

    private var mBubblePopup: PopupWindow? = null

    private val jobs by lazy {
        mutableListOf<Job>()
    }

    protected open val emptyResId: Int = R.drawable.ic_room_mic_add

    abstract fun createLayoutManager(): LayoutManager

    open fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) = Unit

    open fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    open fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    abstract fun mapMicList(list: List<MicSeat>): List<MicSeat>

    init {
        chatRoomViewModel.bindGameViewModel(null)
    }

    @CallSuper
    open fun release() {
        for (job in jobs) {
            job.cancel()
        }
        jobs.clear()
    }

    /**
     * 处理activity finish事件
     * @return true表示已处理
     */
    open fun handleFinishEvent() = false

    /**
     * 是否在游戏中
     */
    open fun playerIsPlaying() = false

    open fun exitGame() = Unit

    open fun init() {
        spirit = createMicListSpirit()
        micSeatsLayout.apply {
            itemAnimator = null
            setHasFixedSize(true)
            setItemViewCacheSize(0)
            recycledViewPool.setMaxRecycledViews(0, 0)
            layoutManager = createLayoutManager()
            adapter = generateAdapter()
        }
    }

    protected open fun createMicListSpirit(): Spirit {
        return Spirit(MicSeatCallback()).apply {
            MicSeat::class.java.viewBindingItem(R.layout.layout_mic_seats_item, LayoutMicSeatsItemBinding::bind, viewBindingCreateVH = {
                it.avatarLayout.clickOfVH { index ->
                    val item = getItem<MicSeat>(index)
                    if (!item.hasUser) {
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ClickUpMic(index + 1)
                        }
                    } else {
                        Analytics.appReportEvent(
                            DataPoint.clickBody(
                                "click_mic_avatar_in_audioroom",
                                extra = "{\"audioroom_id\": ${chatRoomViewModel.roomId}, \"target_user_id\": ${item.user.id}}"
                            )
                        )
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ShowUserPanel(item.user)
                        }
                    }
                }
                viewBindingCreateVH(it)
            }) { viewBinding, item, position ->
                beforeViewBindingOnBindData(viewBinding, item, position)
                viewBinding.locationLabel.isVisible = false
                if (item.hasUser) {
                    val user = item.user
                    viewBinding.avatarLayout.bindUser(user)
                    if (item.mic.isSpeaking && !item.mic.mute) {
                        viewBinding.avatarLayout.startVoiceAnim(user.isBoy)
                    } else {
                        viewBinding.avatarLayout.stopVoiceAnim()
                    }
                    setUserName(viewBinding.name, user)
                    //真爱官宣
                    val publicCP = user.publicCP.orInvalid
                    if (publicCP.isInvalid()) {
                        viewBinding.labelPublicCp.isVisible = false
                    } else {
                        val url = user.cpExtraInfo?.levelInfo?.smallImgUrl
                        if (url.isNullOrEmpty()) {
                            viewBinding.ivShowLove.setImageResource(R.drawable.ic_label_love)
                        } else {
                            viewBinding.ivShowLove.loadWith(url, R.drawable.ic_label_love)
                        }
                        viewBinding.labelPublicCp.isVisible = true
                        viewBinding.ivLover.loadAvatar(publicCP)
                    }
                    viewBinding.roomHostLabel.isVisible = false
                    viewBinding.name.textColor(R.color.white)
                } else {
                    viewBinding.labelPublicCp.isVisible = false
                    viewBinding.avatarLayout.clear()
                    viewBinding.avatarLayout.loadAvatar {
                        setImageResource(emptyResId)
                    }
                    viewBinding.roomHostLabel.isVisible = false
                    viewBinding.name.text = app.getString(R.string.点击加入)
                    viewBinding.name.textColor(R.color.white_alpha_60)
                }
                afterViewBindingOnBindData(viewBinding, item, position)
            }
        }
    }

    protected open fun generateAdapter(): RecyclerView.Adapter<*> {
        return spirit.adapter
    }

    open fun updateMicList(first: Boolean, tipUserId: String, tipText: String, list: List<MicSeat>) {
        val dataList = mapMicList(list)
        spirit.setData(dataList)
        refreshBubble(first, tipUserId, tipText, list) // 不能传dataList
    }

    protected fun refreshBubble(first: Boolean, tipUserId: String, tipText: String, dataList: List<MicSeat>) {
        if (first) {
            val index = dataList.indexOfFirst {
                it.hasUser && it.user.id == tipUserId
            }
            if (index != -1) {
                showBubblePopupWindow(tipUserId, tipText)
            }
        } else if (mBubblePopup?.isShowing == true) {
            if (!dataList.any { it.hasUser && it.user.id == tipUserId }) {
                mBubblePopup?.dismiss()
                mBubblePopup = null
            }
        }
    }

    open fun showUserTipForMicSeats(userId: String): Boolean {
        if (mBubblePopup?.isShowing == true) {
            return false
        }
        chatRoomViewModel.currentState.micListState.getOrNull()?.also { dataList ->
            val index = dataList.indexOfFirst {
                it.hasUser && it.user.id == userId
            }
            if (index != -1) {
                showBubblePopupWindow(userId)
                return true
            }
        }
        return false
    }

    private fun showBubblePopupWindow(userId: String, tipText: String? = null) {
        (micSeatsLayout.context as? FragmentActivity)?.apply {
            val context = this
            launchWhenStarted {
                delay(500)
                val index = chatRoomViewModel.currentState.micListState.getOrNull()?.indexOfFirst {
                    it.hasUser && it.user.id == userId
                } ?: return@launchWhenStarted
                if (index == -1) {
                    return@launchWhenStarted
                }
                val anchorView = micSeatsLayout.parentViewGroup?.let {
                    ChatRoomGiftViewModel.findMicViewById(it, chatRoomViewModel.room, userId, index)
                }
                anchorView ?: return@launchWhenStarted
                val bubblePopup = PopupWindow(context)
                bubblePopup.contentView = AppCompatTextView(context).also {
                    it.updatePadding(top = 4.dp)
                    it.setBackgroundResource(R.drawable.ic_from_mic_user_bubble)
                    it.gravity = Gravity.CENTER_HORIZONTAL
                    it.textColor(R.color.white)
                    it.setLines(1)
                    TextViewCompat.setAutoSizeTextTypeWithDefaults(it, TextViewCompat.AUTO_SIZE_TEXT_TYPE_UNIFORM)
                    TextViewCompat.setAutoSizeTextTypeUniformWithConfiguration(it, 6, 12, 1, TypedValue.COMPLEX_UNIT_DIP)
                    it.text = if (tipText.isNullOrEmpty()) {
                        app.getString(R.string.我在这里哦)
                    } else {
                        tipText
                    }
                }
                bubblePopup.width = 79.dp
                bubblePopup.height = 32.dp
                bubblePopup.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
                bubblePopup.isOutsideTouchable = false
                bubblePopup.isFocusable = false
                bubblePopup.isTouchable = true
                micSeatsLayout.safePopupWindow {
                    bubblePopup.showAsDropDown(
                        anchorView, anchorView.width.minus(79.dp).div(2), anchorView.height.plus(23.dp).unaryMinus()
                    )
                }
                mBubblePopup = bubblePopup
                delay(5000)
                mBubblePopup?.dismiss()
                mBubblePopup = null
            }.recycleIn()
        }
    }

    protected fun List<MicSeat>.take(n: Int): List<MicSeat> {
        require(n >= 0) { "Requested element count $n is less than zero." }
        if (n == size) {
            return this
        }
        if (n == 0) return emptyList()
        if (n == 1) return listOf(first())
        if (n > size) {
            return MutableList(n) {
                getOrNull(it) ?: MicSeat(it, false, INVALID_USER, EMPTY_MIC)
            }.toList()
        }
        return buildList {
            for ((count, item) in <EMAIL>()) {
                add(item)
                if (count + 1 == n) break
            }
        }
    }

    protected fun Job.recycleIn(): Job {
        jobs.add(this)
        return this
    }

    protected fun setUserName(view: TextView, user: User) {
        view.apply {
            text = with(user) {
                buildName()
            }
        }
    }
}


/**
 * 普通模式
 * ROOM_MODE_GOSSIP
 */
class NormalMicSeatsLayout(
    binding: LayoutNormalRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 3)
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) = Unit

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.take(6)
    }
}

/**
 * cp模式
 * ROOM_MODE_CP
 */
class CpMicSeatsLayout(
    binding: LayoutCpRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
    private val cpViewModel: CpViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 2).also {
            it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return if (position == 0) {
                        2
                    } else {
                        1
                    }
                }
            }
        }
    }

    override fun createMicListSpirit(): Spirit {
        return Spirit(MicSeatCallback()).apply {
            MicSeat::class.java.viewBindingItem(R.layout.layout_mic_seats_cp_item, LayoutMicSeatsCpItemBinding::bind, viewBindingCreateVH = {
                it.avatarLayout.clickOfVH { index ->
                    val item = getItem<MicSeat>(index)
                    if (!item.hasUser) {
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ClickUpMic(index + 1)
                        }
                    } else {
                        Analytics.appReportEvent(
                            DataPoint.clickBody(
                                "click_mic_avatar_in_audioroom",
                                extra = "{\"audioroom_id\": ${chatRoomViewModel.roomId}, \"target_user_id\": ${item.user.id}}"
                            )
                        )
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ShowUserPanel(item.user)
                        }
                    }
                }
            }) { viewBinding, item, position ->
                viewBinding.subRoot.layoutParams.asInstance<FrameLayout.LayoutParams>()?.apply {
                    gravity = when (position) {
                        1 -> Gravity.END
                        2 -> Gravity.START
                        else -> Gravity.CENTER_HORIZONTAL
                    }
                    width = if (position == 0) {
                        180.dp
                    } else {
                        168.dp
                    }
                }
                viewBinding.subRoot.autoLoad(
                    when (position) {
                        1 -> R.drawable.bg_cp_seats_male
                        2 -> R.drawable.bg_cp_seats_female
                        else -> ColorDrawable(0x0DFFFFFF)
                    }
                )
                if (position == 0) {
                    viewBinding.subRoot.updateStroke(0.5.dp, 0x4DFFFFFF)
                } else {
                    viewBinding.subRoot.updateStroke(0, Color.TRANSPARENT)
                }
                if (item.hasUser) {
                    val user = item.user
                    viewBinding.avatarLayout.bindUser(user)
                    if (item.mic.isSpeaking && !item.mic.mute) {
                        viewBinding.avatarLayout.startVoiceAnim(user.isBoy)
                    } else {
                        viewBinding.avatarLayout.stopVoiceAnim()
                    }
//                    viewBinding.name.text = user.nickname
                    viewBinding.name.gravity = Gravity.START
                    setUserName(viewBinding.name, user)

                    //真爱官宣
                    val publicCP = user.publicCP.orInvalid
                    if (publicCP.isInvalid()) {
                        viewBinding.labelPublicCp.isVisible = false
                    } else {
                        val url = user.cpExtraInfo?.levelInfo?.smallImgUrl
                        if (url.isNullOrEmpty()) {
                            viewBinding.ivShowLove.setImageResource(R.drawable.ic_label_love)
                        } else {
                            viewBinding.ivShowLove.loadWith(url, R.drawable.ic_label_love)
                        }
                        viewBinding.labelPublicCp.isVisible = true
                        viewBinding.ivLover.loadAvatar(publicCP)
                    }
                    viewBinding.userLabel.isVisible = true
                    viewBinding.btnItemAction.isVisible = true
                    viewBinding.bottomAnchorBackground.isVisible = true
                    viewBinding.userLabel.text = when (position) {
                        1 -> app.getString(R.string.男嘉宾)
                        2 -> app.getString(R.string.女嘉宾)
                        else -> app.getString(R.string.主持人)
                    }
                    viewBinding.userLabel.background = when (position) {
                        1 -> linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFAAD6FF.toInt(), 0xFF319CFF.toInt())
                        2 -> linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFFF9BD8.toInt(), 0xFFFF4CB8.toInt())
                        else -> linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFDD94FF.toInt(), 0xFF6E25FF.toInt())
                    }
                    handleUserLabelForCpRoom(viewBinding, item, position)
                } else {
                    viewBinding.labelPublicCp.isVisible = false
                    viewBinding.avatarLayout.clear()
                    viewBinding.avatarLayout.loadAvatar {
                        setImageResource(
                            when (position) {
                                1 -> R.drawable.ic_empty_seats_male
                                2 -> R.drawable.ic_empty_seats_female
                                else -> R.drawable.ic_empty_seats
                            }
                        )
                    }
                    viewBinding.userLabel.isVisible = false
                    viewBinding.btnItemAction.isVisible = false
                    viewBinding.bottomAnchorBackground.isInvisible = true
                    viewBinding.name.gravity = Gravity.CENTER_HORIZONTAL
                    viewBinding.name.text = when (position) {
                        1 -> app.getString(R.string.男嘉宾)
                        2 -> app.getString(R.string.女嘉宾)
                        else -> app.getString(R.string.主持人)
                    }
                }
            }
        }
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) = Unit

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.take(3)
    }

    private fun handleUserLabelForCpRoom(viewBinding: LayoutMicSeatsCpItemBinding, item: MicSeat, position: Int) {
        when (item.buttonMode) {
            ButtonMode.Normal -> {
                viewBinding.btnItemAction.isVisible = false
            }

            ButtonMode.Cp -> {
                viewBinding.btnItemAction.text = app.getString(R.string.CP空间)
                viewBinding.btnItemAction.background = linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFFF8ECB.toInt(), 0xFFFF4794.toInt())
                viewBinding.btnItemAction.click {
                    anyStartActivity {
                        UserProfileNavigator.obtainIntent(this, sUser)
                    }
                }
            }

            ButtonMode.NextFriends -> {
                viewBinding.btnItemAction.text = app.getString(R.string.加好友)
                viewBinding.btnItemAction.background = linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFAF64F5.toInt(), 0xFF8B56FC.toInt())
                viewBinding.btnItemAction.click {
                    val room = chatRoomViewModel.room
                    val sceneType = room.sceneType
                    val sceneId = room.roomId
                    cpViewModel.sendEvent(CpContract.Event.OnAddFriendsClicked(item.user.id, sceneType, sceneId))
                }
            }

            ButtonMode.InviteFriends -> {
                viewBinding.btnItemAction.text = app.getString(R.string.好友邀请)
                viewBinding.btnItemAction.background = linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFAF64F5.toInt(), 0xFF8B56FC.toInt())
                viewBinding.btnItemAction.click {
                    val room = chatRoomViewModel.room
                    val sceneType = room.sceneType
                    val sceneId = room.roomId
                    cpViewModel.sendEvent(CpContract.Event.OnInviteAddFriendsClicked(item.user.id, sceneType, sceneId, false))
                }
            }

            ButtonMode.NextCp -> {
                viewBinding.btnItemAction.text = app.getString(R.string.组CP)
                viewBinding.btnItemAction.background = linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFFF8ECB.toInt(), 0xFFFF4794.toInt())
                viewBinding.btnItemAction.click {
                    cpViewModel.sendEvent(CpContract.Event.OnInviteCpEvent(item.user))
                }
            }

            ButtonMode.InviteCp -> {
                viewBinding.btnItemAction.text = app.getString(R.string.CP邀请)
                viewBinding.btnItemAction.background = linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFFF8ECB.toInt(), 0xFFFF4794.toInt())
                viewBinding.btnItemAction.click {
                    cpViewModel.sendEvent(CpContract.Event.OnInviteCpEvent(item.user))
                }
            }

            ButtonMode.NextPrivate -> {
                viewBinding.btnItemAction.text = app.getString(R.string.进小屋)
                viewBinding.btnItemAction.background = linearGradient(GradientDrawable.Orientation.LEFT_RIGHT, 0xFFFF8ECB.toInt(), 0xFFFF4794.toInt())
                viewBinding.btnItemAction.click {
                    val room = chatRoomViewModel.room
                    val sceneType = room.sceneType
                    val sceneId = room.roomId
                    cpViewModel.sendEvent(
                        CpContract.Event.OnJoinRoomEvent(
                            -1,
                            RoomTokenRequest(
                                isPrivate = true,
                                onlyToken = false,
                                withConfirm = false,
                                sceneType = sceneType,
                                sceneId = sceneId,
                                targetUserId = item.user.userId
                            )
                        )
                    )
                }
            }
        }
    }
}

interface IPrivateRoom {
    fun updatePrivateStatusByPrivateRoom(privateRoomStatus: PrivateRoomStatus)
}

/**
 * 私密小屋
 * ROOM_MODE_PRIVATE
 */
class PrivateMicSeatsLayout(
    private val activity: FragmentActivity,
    private val binding: LayoutPrivateRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
    private val cpViewModel: CpViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel), IPrivateRoom {

    // 私密小屋语音倒计时
    private var timerJob: Job? = null

    init {
        val privateChatUser = chatRoomViewModel.currentState.roomState.privateChatUser!!.toAppUser()
        activity.launch {
            chatRoomViewModel.obtainUserFlow(privateChatUser).distinctUntilChanged { old, new ->
                old.cp?.id == new.cp?.id && old.needAddFriends == new.needAddFriends
            }.collectLatest {
                handleCpButtonVisible(it as AppUser)
            }
        }.recycleIn()

        binding.btnSendGift.also {
            it.text = buildSpannedString {
                bold {
                    color(id2Color(R.color.black)) {
                        inSpans(AbsoluteSizeSpan(13.sp)) {
                            appendLine(app.getString(R.string.送礼物))
                        }
                    }
                }
                append(app.getString(R.string.增加互动时长))
            }
            it.click {
                cpViewModel.setEffect {
                    CpContract.Effect.ShowGiftPanelByUser(
                        privateChatUser, SettingsRepository.privateRoomTimeGiftId
                    )
                }
            }
        }
    }


    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 2)
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) {
        viewBinding.root.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            topMargin = 0
            height = 168.dp
        }
    }

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.take(2)
    }

    override fun release() {
        super.release()
        timerJob?.cancel()
        timerJob = null

    }

    override fun updatePrivateStatusByPrivateRoom(privateRoomStatus: PrivateRoomStatus) {
        binding.tvCpTips.also {
            val privateChatUser = chatRoomViewModel.currentState.roomState.privateChatUser!!.toAppUser()
            if (isMyCp(privateChatUser)) {
                timerJob?.cancel()
                timerJob = null
                return
            }
            if (privateRoomStatus.status == 1) {
                startPrivateVoiceTimer(privateRoomStatus.endTimestamp.times(1000L), it)
            } else if (privateRoomStatus.status == 5) {
                stopPrivateVoiceTimer(privateRoomStatus.remindSeconds, it)
            }
        }
    }

    private fun handleCpButtonVisible(privateChatUser: AppUser) {
        val isMyCp = isMyCp(privateChatUser)
        binding.group.isInvisible = isMyCp
        if (isMyCp) {
            timerJob?.cancel()
            timerJob = null
        } else {
            val needAddFriends = privateChatUser.needAddFriends
            binding.btnSendGift.isGone = needAddFriends
            binding.btnWantCp.also {
                it.text = buildSpannedString {
                    bold {
                        color(id2Color(R.color.black)) {
                            inSpans(AbsoluteSizeSpan(13.sp)) {
                                if (needAddFriends) {
                                    appendLine(app.getString(R.string.加好友))
                                } else {
                                    appendLine(app.getString(R.string.组CP))
                                }
                            }
                        }
                    }
                    if (needAddFriends) {
                        append(app.getString(R.string.增加互动时长))
                    } else {
                        append(app.getString(R.string.解锁无限时长))
                    }
                }
                it.click {
                    if (needAddFriends) {
                        val room = chatRoomViewModel.room
                        val sceneType = room.sceneType
                        val sceneId = room.roomId
                        cpViewModel.sendEvent(CpContract.Event.OnAddFriendsClicked(privateChatUser.id, sceneType, sceneId))
                    } else {
                        cpViewModel.sendEvent(CpContract.Event.OnInviteCpEvent(privateChatUser))
                    }
                }
            }
        }

    }

    private fun startPrivateVoiceTimer(deadlineTimestamp: Long, tipsView: TextView) {
        timerJob?.cancel()
        timerJob = activity.launchWhenStarted {
            var cur = SNTPManager.now()
            while (deadlineTimestamp > cur) {
                val left = deadlineTimestamp.minus(cur)
                tipsView.text = app.getString(R.string.多少时间后语音互动结束, formatTime(left.plus(500).div(1000).toInt()))
                if (left > 1500) {
                    delay(left.rem(1000))
                } else {
                    delay(left)
                    tipsView.text = app.getString(R.string.多少时间后语音互动结束, formatTime(0))
                    break
                }
                cur = SNTPManager.now()
            }
        }
    }

    private fun stopPrivateVoiceTimer(remindSeconds: Int, tipsView: TextView) {
        tipsView.text = app.getString(R.string.多少时间后语音互动结束, formatTime(remindSeconds))
        timerJob?.cancel()
        timerJob = null
    }

    private fun formatTime(time: Int): String {
        val minute = time.div(60)
        val second = time.rem(60)
        return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
    }
}


/**
 * 私密小屋
 * ROOM_MODE_PRIVATE
 */
class Private2MicSeatsLayout(
    private val activity: FragmentActivity,
    private val binding: LayoutPrivateRoomV2ModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
    private val cpViewModel: CpViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel), IPrivateRoom {

    // 私密小屋语音倒计时
    private var timerJob: Job? = null

    private var isServiceUser = false

    init {
        val privateChatUser = chatRoomViewModel.currentState.roomState.privateChatUser!!.toAppUser()
        isServiceUser = chatRoomViewModel.currentState.roomState.privateRoomStatus?.serviceUserId == sUser.id
        activity.launch {
            chatRoomViewModel.obtainUserFlow(privateChatUser).distinctUntilChanged { old, new ->
                old.cp?.id == new.cp?.id && old.isFriend == new.isFriend
            }.collectLatest {
                handleButtonVisible(isMyCp(privateChatUser) || !it.isFriend)
            }
        }.recycleIn()

        binding.btnAddFriend.also {
            it.text = buildSpannedString {
                bold {
                    appendLine(app.getString(R.string.加好友))
                }
                inSpans(AbsoluteSizeSpan(11.sp)) {
                    append(app.getString(R.string.不限通话时长))
                }
            }
            it.click {
                cpViewModel.sendEvent(
                    CpContract.Event.OnAddFriendsClicked(
                        userId = privateChatUser.id,
                        sceneType = Scene.PRIVATEROOM,
                        sceneId = chatRoomViewModel.roomId
                    )
                )
            }
        }
    }


    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 2)
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) {
        viewBinding.root.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            topMargin = 0
            height = 168.dp
        }
    }

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) {
        if (chatRoomViewModel.room.privateRoomStatus?.payMethod == 3 && item.hasUser && item.user.cityName.isNotEmpty()) {
            viewBinding.locationLabel.isVisible = true
            viewBinding.locationLabel.text = item.user.cityName
        }
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.take(2)
    }

    override fun release() {
        super.release()
        timerJob?.cancel()
        timerJob = null

    }

    override fun updatePrivateStatusByPrivateRoom(privateRoomStatus: PrivateRoomStatus) {
        val privateChatUser = chatRoomViewModel.currentState.roomState.privateChatUser!!.toAppUser()
        val hidden = isMyCp(privateChatUser) || privateRoomStatus.privateRoomMode == 1 || privateRoomStatus.isFriend || privateRoomStatus.isCp
        isServiceUser = privateRoomStatus.serviceUserId == sUser.id
        handleButtonVisible(hidden = hidden)
        if (hidden) {
            return
        }
        val content = if (isServiceUser) {
            privateRoomStatus.starHint
        } else {
            privateRoomStatus.fanHint
        }
        binding.leftTime2.isVisible = isServiceUser && privateRoomStatus.showTogetherMicTimer
        binding.tvTipText.isGone = content.isNullOrEmpty()
        binding.tvTipText.text = content

        if (privateRoomStatus.status == 1) {
            startPrivateVoiceTimer(privateRoomStatus)
        } else if (privateRoomStatus.status == 5) {
            stopPrivateVoiceTimer(privateRoomStatus)
        }
    }

    private fun handleButtonVisible(hidden: Boolean) {
        binding.btnAddFriend.isGone = hidden || isServiceUser
        binding.tipAreaLayout.isGone = hidden
        if (hidden) {
            timerJob?.cancel()
            timerJob = null
        }
        (binding.micSeatsLayout.layoutParams as? ConstraintLayout.LayoutParams)?.apply {
            if (hidden) {
                topMargin = 36.dp
                bottomMargin = 36.dp
            } else {
                topMargin = 10.dp
                bottomMargin = 0
            }
        }
    }

    private fun startPrivateVoiceTimer(privateRoomStatus: PrivateRoomStatus) {
        timerJob?.cancel()
        val startTimestamp = privateRoomStatus.reStartTimestamp
        if (privateRoomStatus.payMethod == 2) {
            timerJob = activity.launchWhenStarted {
                while (isActive) {
                    val left = SystemClock.elapsedRealtime().minus(startTimestamp).plus(500).div(1000).toInt()
                    updateLeftTime(privateRoomStatus.payMethod, privateRoomStatus.useDuration.plus(left))
                    if (isServiceUser && privateRoomStatus.showTogetherMicTimer && privateRoomStatus.togetherMicOngoing) {
                        updateLeftTime2(privateRoomStatus.togetherMicSeconds.plus(left))
                    }
                    delay(1000)
                }
            }
        } else {
            val deadlineTimestamp = privateRoomStatus.endTimestamp.times(1000L)
            timerJob = activity.launchWhenStarted {
                var cur = SNTPManager.now()
                while (deadlineTimestamp > cur) {
                    val left = deadlineTimestamp.minus(cur)
                    val left2 = SystemClock.elapsedRealtime().minus(startTimestamp).plus(500).div(1000).toInt()
                    updateLeftTime(privateRoomStatus.payMethod, left.plus(500).div(1000).toInt())
                    if (isServiceUser && privateRoomStatus.showTogetherMicTimer && privateRoomStatus.togetherMicOngoing) {
                        updateLeftTime2(privateRoomStatus.togetherMicSeconds.plus(left2))
                    }
                    if (left > 1500) {
                        delay(left.rem(1000))
                    } else {
                        delay(left)
                        updateLeftTime(privateRoomStatus.payMethod, 0)
                        if (isServiceUser && privateRoomStatus.showTogetherMicTimer && privateRoomStatus.togetherMicOngoing) {
                            updateLeftTime2(privateRoomStatus.togetherMicSeconds.plus(left2))
                        }
                        break
                    }
                    cur = SNTPManager.now()
                }
            }
        }
    }

    private fun stopPrivateVoiceTimer(privateRoomStatus: PrivateRoomStatus) {
        timerJob?.cancel()
        timerJob = null
        updateLeftTime(
            privateRoomStatus.payMethod, if (privateRoomStatus.payMethod == 2) privateRoomStatus.useDuration else privateRoomStatus.remindSeconds
        )
        if (isServiceUser && privateRoomStatus.showTogetherMicTimer) {
            updateLeftTime2(privateRoomStatus.togetherMicSeconds)
        }
    }

    private fun updateLeftTime(payMethod: Int, time: Int) {
        binding.leftTime.text = buildSpannedString {
            when (payMethod) {
                2 -> {
                    append(activity.getString(R.string.本次通话时长))
                    append(" ")
                    inSpans(MyFontTypeSpan(D_DIN_TYPEFACE)) {
                        append(formatTime(time))
                    }
                }

                3 -> {
                    inSpans(MyFontTypeSpan(D_DIN_TYPEFACE)) {
                        append(formatTime(time))
                    }
                    append(" ")
                    append(activity.getString(R.string.后语音互动结束))
                }

                else -> {
                    append(activity.getString(R.string.剩余免费通话时长))
                    append(" ")
                    inSpans(MyFontTypeSpan(D_DIN_TYPEFACE)) {
                        append(formatTime(time))
                    }
                }
            }
        }
    }

    private fun updateLeftTime2(time: Int) {
        binding.leftTime2.text = buildSpannedString {
            append(activity.getString(R.string.新人匹配奖励))
            append(" ")
            inSpans(MyFontTypeSpan(D_DIN_TYPEFACE)) {
                append(formatTime(time))
            }
        }
    }

    private fun formatTime(time: Int): String {
        val minute = time.div(60)
        val second = time.rem(60)
        return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
    }
}

/**
 * 电台模式
 * ROOM_MODE_STATION
 */
class StationMicSeatsLayout(
    binding: LayoutStationRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    override val emptyResId: Int = R.drawable.ic_room_mic_add_for_station

    init {
        binding.micSeatsLayout.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            height = 278.dp
        }
    }

    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 4).also {
            it.spanSizeLookup = object : GridLayoutManager.SpanSizeLookup() {
                override fun getSpanSize(position: Int): Int {
                    return if (position == 0) {
                        4
                    } else {
                        1
                    }
                }
            }
        }
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) = Unit

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) {
        if (position == 0) {
            if (viewBinding.root.layoutParams.height != 148.dp) {
                viewBinding.root.updateLayoutParams<ViewGroup.MarginLayoutParams> {
                    height = 132.dp
                    bottomMargin = 28.dp
                }
            }
            viewBinding.name.textSize = 14f
        }
    }

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.take(5)
    }
}

/**
 * 8麦模式
 * ROOM_MODE_8_MIC
 */
class Mode8MicSeatsLayout(
    binding: LayoutNormalRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 4)
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) = Unit

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) {
        val cpCrown = getViewOrNull<View>(R.id.cp_crown)
        cpCrown?.isInvisible = true
        if (cpCrown?.tag == null) {
            cpCrown?.doOnLayout {
                cpCrown.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    circleRadius = viewBinding.avatarLayout.getAvatarSize().div(2).plus(cpCrown.height.div(2)).minus(2.dp)
                }
                cpCrown.tag = true
            }
        }
    }

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) {
        if (item.hasUser) {
            val cpCrown = getViewOrNull<View>(R.id.cp_crown)
            if (item.hasCrown) {
                cpCrown?.isVisible = true
            }
            viewBinding.charmValueLayout.isVisible = true
            viewBinding.charmValue.text = item.heartValue.toString()
        } else {
            viewBinding.charmValueLayout.isInvisible = true
        }
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.take(8)
    }
}

class PKMicSeatsLayout(
    private val activity: ChatRoomActivity,
    private val binding: LayoutPkRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    private var timerJob: Job? = null

    private var blueAnimJob: Job? = null

    private var redAnimJob: Job? = null

    private var pkState: State = State.Idea

    private var pkSettingsDialog: Dialog? = null

    private var pkResultDialog: Dialog? = null

    private var animator: ValueAnimator? = null

    init {
        binding.micSeatsLayout.addItemDecoration(object : RecyclerView.ItemDecoration() {

            override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
                val position = parent.getChildAdapterPosition(view)
                if (position > 3) {
                    outRect.top = (-2).dp
                }
                when (position.rem(4)) {
                    0 -> {
                        outRect.left = 5.dp
                    }

                    1 -> {
                        outRect.right = 10.dp
                    }

                    2 -> {
                        outRect.left = 10.dp
                    }

                    3 -> {
                        outRect.right = 5.dp
                    }
                }
            }
        })

        binding.btnPkSettingsPunish.click {
            toast(binding.btnPkSettingsPunish.text?.toString().orEmpty())
        }

        binding.btnPkSettingsFlow.click {
            val room = chatRoomViewModel.currentState.roomState
            if (!room.isAdmin && !room.isOwner) { // 非管理员无法操作
                return@click
            }
            when (val state = pkState) {
                is State.Idea -> {
                    activity.paused = true
                    pkSettingsDialog = PKDialogController.showSettingDialog(activity, "", 5) { penalty, duration ->
                        chatRoomViewModel.sendEvent(RoomContract.Event.OnPkSettingsEvent(1, penalty, duration))
                    }
                    pkSettingsDialog?.handleOnDismiss {
                        activity.paused = false
                        pkSettingsDialog = null
                    }
                }

                is State.PKing -> {
                    pkSettingsDialog = PKDialogController.showModifySettingDialog(activity, state.data.pkPenalty, state.data.pkDurationMinute, {
                        chatRoomViewModel.sendEvent(RoomContract.Event.OnPkSettingsEvent(3))
                    }) {
                        chatRoomViewModel.sendEvent(RoomContract.Event.OnPkSettingsEvent(2, duration = 5))
                    }
                    pkSettingsDialog?.handleOnDismiss { pkSettingsDialog = null }
                }

                is State.Settled -> {
                    chatRoomViewModel.sendEvent(RoomContract.Event.OnPkSettingsEvent(3))
                }
            }
        }

        binding.blueTeamContributorLayout.click {
            if (pkState !is State.Idea) {
                activity.showFragment(
                    PkSupportRankFragment.newInstance(chatRoomViewModel.roomId, true),
                    true,
                    "PkSupportRankFragment",
                    verticalSlide = true,
                    addToBackStack = true
                )
            }
        }

        binding.redTeamContributorLayout.click {
            if (pkState !is State.Idea) {
                activity.showFragment(
                    PkSupportRankFragment.newInstance(chatRoomViewModel.roomId, false),
                    true,
                    "PkSupportRankFragment",
                    verticalSlide = true,
                    addToBackStack = true
                )
            }
        }

        binding.hostAvatarLayout.click {
            val item = it.tag as? MicSeat
            if (item?.hasUser == true) {
                chatRoomViewModel.setEffect {
                    RoomContract.Effect.ShowUserPanel(item.user)
                }
                Analytics.appReportEvent(
                    DataPoint.clickBody(
                        "click_mic_avatar_in_audioroom",
                        extra = "{\"audioroom_id\": ${chatRoomViewModel.roomId}, \"target_user_id\": ${item.user.id}}"
                    )
                )
            } else {
                chatRoomViewModel.setEffect {
                    RoomContract.Effect.ClickUpMic(0)
                }
            }
        }

        activity.launch {
            chatRoomViewModel.uiState.map {
                it.roomState.isAdmin || it.roomState.isOwner
            }.flowWithLifecycle(activity.lifecycle).distinctUntilChanged().collectLatest {
                binding.btnPkSettingsFlow.isVisible = it
            }
        }.recycleIn()

        activity.launch {
            chatRoomViewModel.uiState.map {
                it.roomState.pkInfo
            }.filterNotNull().flowWithLifecycle(activity.lifecycle).distinctUntilChanged().collectLatest {
                updatePkInfo(it)
            }
        }.recycleIn()

        updatePkState(State.Idea, true)
    }

    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 4)
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) {
        viewBinding.root.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            height = 109.dp
        }
        viewBinding.avatarLayout.updateLayoutParams<ConstraintLayout.LayoutParams> {
            matchConstraintPercentHeight = 0.65f
        }
    }

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) {
        if (item.hasUser) {
            viewBinding.charmValueLayout.isVisible = true
            viewBinding.charmValue.text = item.heartValue.toString()
        } else {
            viewBinding.charmValueLayout.isInvisible = true
        }
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.subList(1, list.size).take(8)
    }

    override fun updateMicList(first: Boolean, tipUserId: String, tipText: String, list: List<MicSeat>) {
        if (list.size == 9 && list.first().hasUser) {
            val item = list.first()
            val user = item.user
            binding.hostAvatarLayout.bindUser(user)
            if (item.mic.isSpeaking && !item.mic.mute) {
                binding.hostAvatarLayout.startVoiceAnim(user.isBoy)
            } else {
                binding.hostAvatarLayout.stopVoiceAnim()
            }
//            binding.hostName.text = user.nickname
            setUserName(binding.hostName, user)
            //真爱官宣
            val publicCP = user.publicCP.orInvalid
            if (publicCP.isInvalid()) {
                binding.hostLabelPublicCp.isVisible = false
            } else {
                val url = user.cpExtraInfo?.levelInfo?.smallImgUrl
                if (url.isNullOrEmpty()) {
                    binding.hostIvShowLove.setImageResource(R.drawable.ic_label_love)
                } else {
                    binding.hostIvShowLove.loadWith(url, R.drawable.ic_label_love)
                }
                binding.hostLabelPublicCp.isVisible = true
                binding.hostIvLover.loadAvatar(publicCP)
            }
            binding.hostAvatarLayout.tag = item
            binding.hostName.textColor(R.color.white)
        } else {
            binding.hostLabelPublicCp.isVisible = false
            binding.hostAvatarLayout.clear()
            binding.hostAvatarLayout.loadAvatar {
                setImageResource(emptyResId)
            }
            binding.hostName.text = app.getString(R.string.点击加入)
            binding.hostName.textColor(R.color.white_alpha_60)
            binding.hostAvatarLayout.tag = null
        }
        super.updateMicList(first, tipUserId, tipText, list)
    }

    override fun release() {
        super.release()
        cancelPkJob(true)
    }

    fun handlePkFinishEvent(pkEvent: PKEvent) {
        pkEvent.apply {
            winnerInfo!!
            showPkResultDialog(
                PKResult(
                    when (winnerSide) {
                        1 -> -1
                        2 -> 1
                        else -> 0
                    }, winnerInfo.heartUserInfo.user, winnerInfo.heartUserInfo.value, winnerInfo.cheerUserInfo.user, winnerInfo.cheerUserInfo.value
                )
            )
        }
    }

    private fun updatePkInfo(pkInfo: PkInfo) {
        val oldState = pkState
        val newState = with(pkInfo) {
            when (pkStatus) {
                2 -> {
                    State.PKing(
                        PkData(
                            Team(blueSideValue, blueSideCheersCnt, blueSideCheers),
                            Team(redSideValue, redSideCheersCnt, redSideCheers),
                            endTime.times(1000L),
                            title,
                            pkDuration.div(60),
                        )
                    )
                }

                3 -> {
                    winnerInfo!!
                    State.Settled(
                        PkData(
                            Team(blueSideValue, blueSideCheersCnt, blueSideCheers),
                            Team(redSideValue, redSideCheersCnt, redSideCheers),
                            endTime.times(1000L),
                            title,
                            pkDuration.div(60),
                        ), PKResult(
                            when (winnerSide) {
                                1 -> -1
                                2 -> 1
                                else -> 0
                            },
                            winnerInfo.heartUserInfo.user,
                            winnerInfo.heartUserInfo.value,
                            winnerInfo.cheerUserInfo.user,
                            winnerInfo.cheerUserInfo.value
                        )
                    )
                }

                else -> {
                    State.Idea
                }
            }
        }
        updatePkState(newState, oldState != newState)
    }

    private fun updatePkState(state: State, firstForNewState: Boolean) {
        pkState = state
        cancelPkJob(false)
        binding.apply {
            when (state) {
                is State.Idea -> {
                    if (firstForNewState) {
                        btnPkSettingsPunish.text = activity.getString(R.string.PK惩罚, activity.getString(R.string.等待房管设置))
                        btnPkSettingsPunish.startMarquee = false
                        btnPkSettingsFlow.text = activity.getString(R.string.PK设置)
                        btnPkTimeline.text = activity.getString(R.string.PK未开始)
                        btnPkTimeline.typeface = Typeface.DEFAULT
                        pkResultFlag.isVisible = false
                        calculateScore(null, false)
                        showPkContributeMember(
                            0, emptyList(), blueTeamContributorCount, arrayOf(
                                blueTeamContributor1, blueTeamContributor2, blueTeamContributor3
                            )
                        )
                        showPkContributeMember(
                            0, emptyList(), redTeamContributorCount, arrayOf(
                                redTeamContributor1, redTeamContributor2, redTeamContributor3
                            )
                        )
                    }
                }

                is State.PKing -> {
                    if (firstForNewState) {
                        btnPkSettingsPunish.text = activity.getString(R.string.PK惩罚, state.data.pkPenalty)
                        btnPkSettingsPunish.launch {
                            delay(100)
                            btnPkSettingsPunish.startMarquee = true
                        }
                        btnPkSettingsFlow.text = activity.getString(R.string.PK设置)
                        pkResultFlag.isVisible = false
                        btnPkTimeline.typeface = D_DIN_TYPEFACE
                    }
                    startPkTimer(state.data.deadline)
                    calculateScore(state.data, true)
                    showPkContributeMember(
                        state.data.blueTeam.contributeCount, state.data.blueTeam.contributeUser, blueTeamContributorCount, arrayOf(
                            blueTeamContributor1, blueTeamContributor2, blueTeamContributor3
                        )
                    )
                    showPkContributeMember(
                        state.data.redTeam.contributeCount, state.data.redTeam.contributeUser, redTeamContributorCount, arrayOf(
                            redTeamContributor1, redTeamContributor2, redTeamContributor3
                        )
                    )
                }

                is State.Settled -> {
                    if (firstForNewState) {
                        btnPkSettingsPunish.text = activity.getString(R.string.PK惩罚, state.data.pkPenalty)
                        btnPkSettingsPunish.launch {
                            delay(100)
                            btnPkSettingsPunish.startMarquee = true
                        }
                        btnPkSettingsFlow.text = activity.getString(R.string.重新开始)
                        btnPkTimeline.text = activity.getString(R.string.PK已结束)
                        btnPkTimeline.typeface = Typeface.DEFAULT
                    }
                    calculateScore(state.data, true)
                    showPkContributeMember(
                        state.data.blueTeam.contributeCount, state.data.blueTeam.contributeUser, blueTeamContributorCount, arrayOf(
                            blueTeamContributor1, blueTeamContributor2, blueTeamContributor3
                        )
                    )
                    showPkContributeMember(
                        state.data.redTeam.contributeCount, state.data.redTeam.contributeUser, redTeamContributorCount, arrayOf(
                            redTeamContributor1, redTeamContributor2, redTeamContributor3
                        )
                    )
                    showPkResult(state.result)
                }
            }
        }
    }

    private fun startAnim() {
        stopAnim()
        ObjectAnimator.ofPropertyValuesHolder(
            binding.ivPk,
            PropertyValuesHolder.ofFloat(View.ROTATION, 0f, 45f, -45f, 45f, -45f, 0f),
        ).also {
            it.duration = 800
            it.startDelay = 5000
            it.interpolator = AccelerateDecelerateInterpolator()
            animator = it
            it.doOnEnd {
                animator?.start()
            }
        }.start()
    }

    private fun stopAnim() {
        animator?.cancel()
        animator = null
    }

    private fun calculateScore(data: PkData?, animated: Boolean) {
        if (data == null) {
            binding.pkScoreIndicator.setProgressCompat(500, animated)
            binding.blueTeamScore.text = "0"
            binding.redTeamScore.text = "0"
        } else {
            val progress = data.let {
                val total = it.blueTeam.score.plus(it.redTeam.score)
                if (total == 0) {
                    500
                } else {
                    it.blueTeam.score.times(1000).div(total)
                }
            }.coerceIn(148, 852)
            binding.pkScoreIndicator.setProgressCompat(progress, animated)
            if (animated) {
                blueAnimJob = animateToNumber(binding.blueTeamScore, data.blueTeam.score)
                redAnimJob = animateToNumber(binding.redTeamScore, data.redTeam.score)
            } else {
                binding.blueTeamScore.text = data.blueTeam.score.toString()
                binding.redTeamScore.text = data.redTeam.score.toString()
            }
        }
    }

    @SuppressLint("Recycle")
    private fun animateToNumber(view: TextView, value: Int): Job? {
        val start = view.text.toString().trim().toIntOrNull() ?: 0
        if (abs(value.minus(start)) < 10) {
            view.text = value.toString()
            return null
        }
        return activity.launch {
            ValueAnimator.ofInt(start, value).apply {
                addUpdateListener {
                    view.text = it.animatedValue.toString()
                }
                duration = 450
            }.suspendOnEnd(true)
        }
    }


    private fun showPkResult(result: PKResult) {
        binding.pkResultFlag.isVisible = true
        when {
            result.win < 0 -> {
                binding.pkResultFlag.setImageResource(R.drawable.ic_pk_win_team)
                binding.pkResultFlag.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    startToEnd = binding.blueTeamScore.id
                    startToStart = ConstraintLayout.LayoutParams.UNSET
                    endToEnd = ConstraintLayout.LayoutParams.UNSET
                    endToStart = ConstraintLayout.LayoutParams.UNSET
                }
            }

            result.win == 0 -> {
                binding.pkResultFlag.setImageResource(R.drawable.ic_pk_draw_team)
                binding.pkResultFlag.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                    startToEnd = ConstraintLayout.LayoutParams.UNSET
                    endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                    endToStart = ConstraintLayout.LayoutParams.UNSET
                }
            }

            else -> {
                binding.pkResultFlag.setImageResource(R.drawable.ic_pk_win_team)
                binding.pkResultFlag.updateLayoutParams<ConstraintLayout.LayoutParams> {
                    endToStart = binding.redTeamScore.id
                    endToEnd = ConstraintLayout.LayoutParams.UNSET
                    startToStart = ConstraintLayout.LayoutParams.UNSET
                    startToEnd = ConstraintLayout.LayoutParams.UNSET
                }
            }
        }
    }

    private fun showPkResultDialog(result: PKResult) {
        if (result.win == 0) {
            return
        }
        pkResultDialog = PKDialogController.showVictoryDialog(
            activity, result.win < 0, result.bestContributor, result.pkMvp, result.contributionScore, result.mvpScore
        )

        pkResultDialog?.handleOnDismiss {
            pkResultDialog = null
        }
    }

    private fun showPkContributeMember(contributeCount: Int, contributeUser: List<AppUser>, textView: TextView, views: Array<ImageView>) {
        binding.apply {
            if (contributeCount <= 0) {
                textView.text = activity.getString(R.string.助威团虚位以待)
                views.forEach {
                    it.isVisible = false
                }
            } else {
                textView.text = activity.getString(R.string.PK多少人助威, contributeCount)
                views.forEachIndexed { index, imageView ->
                    val user = contributeUser.getOrNull(index)
                    if (user == null) {
                        imageView.isVisible = false
                    } else {
                        imageView.isVisible = true
                        imageView.loadAvatar(user)
                    }
                }
            }
        }
    }

    private fun startPkTimer(deadlineTimestamp: Long) {
        startAnim()
        timerJob?.cancel()
        timerJob = activity.lifecycleScope.launch {
            activity.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                var cur = SNTPManager.now()
                while (deadlineTimestamp > cur) {
                    val left = deadlineTimestamp.minus(cur)
                    binding.btnPkTimeline.text = formatTime(left.plus(500).div(1000).toInt()) // 四舍五入
                    if (left > 1500) {
                        delay(left.rem(1000))
                    } else {
                        delay(left)
                        binding.btnPkTimeline.text = formatTime(0)
                        break
                    }
                    cur = SNTPManager.now()
                }
                delay(500)
                binding.btnPkTimeline.text = activity.getString(R.string.正在结算)
            }
        }
    }

    private fun cancelPkJob(release: Boolean) {
        timerJob?.cancel()
        timerJob = null
        blueAnimJob?.cancel()
        blueAnimJob = null
        redAnimJob?.cancel()
        redAnimJob = null
        stopAnim()

        if (release || pkState !is State.Settled) {
            if (pkResultDialog?.isShowing == true) {
                pkResultDialog?.dismiss()
            }
            pkResultDialog = null
        }

        if (release || pkState is State.Settled) {
            if (pkSettingsDialog?.isShowing == true) {
                pkSettingsDialog?.dismiss()
            }
            pkSettingsDialog = null
        }
    }

    private fun formatTime(time: Int): String {
        val minute = time.div(60)
        val second = time.rem(60)
        return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
    }

    private sealed class State {

        /**
         * 未开始状态
         */
        object Idea : State()


        /**
         * pk中
         */
        class PKing(val data: PkData) : State()

        /**
         * PK结算
         */
        class Settled(val data: PkData, val result: PKResult) : State()
    }


    private data class PkData(
        val blueTeam: Team, // 蓝队
        val redTeam: Team, // 红队
        val deadline: Long, // 截止时间
        val pkPenalty: String, // pk的惩罚
        val pkDurationMinute: Int, // pk时间
    )

    private data class Team(
        val score: Int, // 总分数
        val contributeCount: Int, // 贡献人数
        val contributeUser: List<AppUser>, // 贡献前三名
    )

    private data class PKResult(
        val win: Int, // 获胜方
        val pkMvp: AppUser, // pk最佳选手
        val mvpScore: Int, // 最佳选手分数
        val bestContributor: AppUser, // pk最佳贡献者
        val contributionScore: Int,// 贡献分
    )
}

class LoveMicSeatsLayout(
    private val activity: ChatRoomActivity,
    private val binding: LayoutLoveRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
    private val expansionViewModel: ChatRoomExpansionViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    private var loveInfo: LoveInfo = LoveInfo()

    private val loveSpirit: Spirit

    // 0 = 观众，1 = 参数选手，2 = 主持人
    private var selfRole = 0

    private val selectGroup: SingleSelectGroup

    init {
        val info = chatRoomViewModel.currentState.roomState.loveInfo
        if (info != null) {
            this.loveInfo = info
        }
        loveSpirit = Spirit(object : DiffUtil.ItemCallback<Any>() {

            override fun areItemsTheSame(oldItem: Any, newItem: Any): Boolean {
                if (oldItem.javaClass != newItem.javaClass) {
                    return false
                }
                oldItem as IMicLove
                newItem as IMicLove
                return oldItem.index == newItem.index
            }

            override fun areContentsTheSame(oldItem: Any, newItem: Any): Boolean {
                oldItem as IMicLove
                newItem as IMicLove
                if (oldItem.leftUser.id != newItem.leftUser.id || oldItem.rightUser.id != newItem.rightUser.id) { // 不同的用户
                    return false
                }
                return oldItem.leftUser.nickname == newItem.leftUser.nickname && oldItem.leftUser.avatarUrl == newItem.leftUser.avatarUrl && oldItem.leftUser.avatarFrame == newItem.leftUser.avatarFrame && oldItem.leftMic.isSpeaking == newItem.leftMic.isSpeaking && oldItem.leftMic.mute == newItem.leftMic.mute && oldItem.leftHeartValue == newItem.leftHeartValue && oldItem.leftOnline == newItem.leftOnline && oldItem.rightUser.nickname == newItem.rightUser.nickname && oldItem.rightUser.avatarUrl == newItem.rightUser.avatarUrl && oldItem.rightUser.avatarFrame == newItem.rightUser.avatarFrame && oldItem.rightMic.isSpeaking == newItem.rightMic.isSpeaking && oldItem.rightMic.mute == newItem.rightMic.mute && oldItem.rightHeartValue == newItem.rightHeartValue && oldItem.rightOnline == newItem.rightOnline
            }

        }).apply {
            MicLovePairSeat::class.java.viewBindingItem(
                R.layout.layout_mic_seats_love_pair_item,
                LayoutMicSeatsLovePairItemBinding::bind,
                viewBindingCreateVH = {
                    it.leftAvatarLayout.clickOfVH { index ->
                        val item = getItem<IMicLove>(index)
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ShowUserPanel(item.leftUser)
                        }
                    }

                    it.rightAvatarLayout.clickOfVH { index ->
                        val item = getItem<IMicLove>(index)
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ShowUserPanel(item.rightUser)
                        }
                    }
                }) { viewBinding, item, position ->
                viewBinding.root.updateLayoutParams {
                    height = if (adapter.itemCount < 3) 144.dp else 114.dp
                }
                val leftUser = item.leftUser
                viewBinding.leftAvatarLayout.bindUser(leftUser, true)
                if (item.leftMic.isSpeaking && !item.leftMic.mute) {
                    viewBinding.leftAvatarLayout.startVoiceAnim(leftUser.isBoy)
                } else {
                    viewBinding.leftAvatarLayout.stopVoiceAnim()
                }
//                viewBinding.leftName.text = leftUser.nickname
//                viewBinding.leftName.textColor(R.color.white)
                setUserName(viewBinding.leftName, leftUser)

                val rightUser = item.rightUser
                viewBinding.rightAvatarLayout.bindUser(rightUser, true)
                if (item.rightMic.isSpeaking && !item.rightMic.mute) {
                    viewBinding.rightAvatarLayout.startVoiceAnim(rightUser.isBoy)
                } else {
                    viewBinding.rightAvatarLayout.stopVoiceAnim()
                }
//                viewBinding.rightName.text = rightUser.nickname
//                viewBinding.rightName.textColor(R.color.white)
                setUserName(viewBinding.rightName, rightUser)

                viewBinding.leftCharmValue.text = item.leftHeartValue.toString()
                viewBinding.rightCharmValue.text = item.rightHeartValue.toString()
                viewBinding.charmValue.text = app.getString(R.string.心动值value, item.heartValue)

                viewBinding.leftAvatarLayout.findViewById<View>(R.id.user_avatar_mask)?.isVisible = !item.leftOnline
                viewBinding.rightAvatarLayout.findViewById<View>(R.id.user_avatar_mask)?.isVisible = !item.rightOnline
            }

            MicLoveShowSeat::class.java.viewBindingItem(
                R.layout.layout_mic_seats_love_show_item,
                LayoutMicSeatsLoveShowItemBinding::bind,
                viewBindingCreateVH = {
                    it.leftAvatarLayout.clickOfVH { index ->
                        val item = getItem<IMicLove>(index)
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ShowUserPanel(item.leftUser)
                        }
                    }

                    it.rightAvatarLayout.clickOfVH { index ->
                        val item = getItem<IMicLove>(index)
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ShowUserPanel(item.rightUser)
                        }
                    }

                    it.btnBlessing.clickOfVH { _ ->
                        activity.showGiftPanelFragment()
                    }
                }) { viewBinding, item, position ->
                val leftUser = item.leftUser
                viewBinding.leftAvatarLayout.bindUser(leftUser, true)
                if (item.leftMic.isSpeaking && !item.leftMic.mute) {
                    viewBinding.leftAvatarLayout.startVoiceAnim(leftUser.isBoy)
                } else {
                    viewBinding.leftAvatarLayout.stopVoiceAnim()
                }
//                viewBinding.leftName.text = leftUser.nickname
//                viewBinding.leftName.textColor(R.color.white)
                setUserName(viewBinding.leftName, leftUser)

                val rightUser = item.rightUser
                viewBinding.rightAvatarLayout.bindUser(rightUser, true)
                if (item.rightMic.isSpeaking && !item.rightMic.mute) {
                    viewBinding.rightAvatarLayout.startVoiceAnim(rightUser.isBoy)
                } else {
                    viewBinding.rightAvatarLayout.stopVoiceAnim()
                }
//                viewBinding.rightName.text = rightUser.nickname
//                viewBinding.rightName.textColor(R.color.white)
                setUserName(viewBinding.rightName, rightUser)

                viewBinding.leftCharmValue.text = item.leftHeartValue.toString()
                viewBinding.rightCharmValue.text = item.rightHeartValue.toString()
                viewBinding.charmValue.text = item.heartValue.toString()

                viewBinding.leftAvatarLayout.findViewById<View>(R.id.user_avatar_mask)?.isVisible = !item.leftOnline
                viewBinding.rightAvatarLayout.findViewById<View>(R.id.user_avatar_mask)?.isVisible = !item.rightOnline

                viewBinding.loveTask.text = loveInfo.loveTip
            }
        }

        val evaAnimView = activity.findViewById<EvaAnimViewV3>(R.id.loveEvaAnimView)
        expansionViewModel.effects.onEach {
            if (it is ChatRoomExpansionMvi.ViewEffect.VideoEffect) {
                if (it.path.isNullOrEmpty()) {
                    evaAnimView.stopPlay()
                } else {
                    if (evaAnimView.isRunning()) {
                        evaAnimView.stopPlay()
                        delay(200)
                    }
                    evaAnimView.setVideoMode(EvaConstant.VIDEO_MODE_SPLIT_HORIZONTAL)
                    evaAnimView.startPlay(activity.assets, it.path)
                }
            }
        }.launchIn(activity.lifecycleScope)

        binding.hostAvatarLayout.click {
            val item = it.tag as? MicSeat
            if (item?.hasUser == true) {
                chatRoomViewModel.setEffect {
                    RoomContract.Effect.ShowUserPanel(item.user)
                }
                Analytics.appReportEvent(
                    DataPoint.clickBody(
                        "click_mic_avatar_in_audioroom",
                        extra = "{\"audioroom_id\": ${chatRoomViewModel.roomId}, \"target_user_id\": ${item.user.id}}"
                    )
                )
            } else {
                chatRoomViewModel.setEffect {
                    RoomContract.Effect.ClickUpMic(0)
                }
            }
        }

        binding.btnNextAction.click {
            if (loveInfo.stage !in 2..5) {
                dialogConfirm(it, R.string.确认开始游戏吗) {
                    chatRoomViewModel.sendEvent(RoomContract.Event.OnLoveStart)
                }
            } else if (loveInfo.stage != 5) {
                dialogConfirm(it, R.string.确认进入下一环节吗) {
                    chatRoomViewModel.sendEvent(RoomContract.Event.OnLoveNext(loveInfo.id))
                }
            }
        }

        binding.btnGameSwitch.click {
            dialogConfirm(it, R.string.确认重新开始吗) {
                chatRoomViewModel.sendEvent(RoomContract.Event.OnLoveReset(loveInfo.id))
            }
        }

        binding.gameQa.click {
            activity.startJsWebActivity(loveInfo.rulePageUrl)
        }

        selectGroup = SingleSelectGroup(loveInfo.stage.minus(2), arrayOf(binding.v1, binding.v3, binding.v5, binding.v7), isClickable = false)
    }

    private fun dialogConfirm(view: View, @StringRes stringRes: Int, onConfirm: () -> Unit) {
        AlertButtonDialogFragment.newInstance(
            app.getString(stringRes),
            app.getString(R.string.取消),
            app.getString(R.string.确认),
        ).setListener { startButton, _ ->
            if (!startButton && view.isVisible) {
                onConfirm()
            }
            true
        }.show(activity.supportFragmentManager, "")
    }

    override fun createMicListSpirit(): Spirit {
        return Spirit(MicSeatCallback()).apply {
            MicSeat::class.java.viewBindingItem(
                R.layout.layout_mic_seats_love_select_item,
                LayoutMicSeatsLoveSelectItemBinding::bind,
                viewBindingCreateVH = {
                    it.avatarLayout.clickOfVH { index ->
                        val item = getItem<MicSeat>(index)
                        if (!item.hasUser) {
                            chatRoomViewModel.setEffect {
                                RoomContract.Effect.ClickUpMic(index + 1)
                            }
                        } else {
                            Analytics.appReportEvent(
                                DataPoint.clickBody(
                                    "click_mic_avatar_in_audioroom",
                                    extra = "{\"audioroom_id\": ${chatRoomViewModel.roomId}, \"target_user_id\": ${item.user.id}}"
                                )
                            )
                            chatRoomViewModel.setEffect {
                                RoomContract.Effect.ShowUserPanel(item.user)
                            }
                        }
                    }
                    it.ivLoveYou.clickOfVH { index ->
                        val item = getItem<MicSeat>(index)
                        if (item.hasUser && item.user.userId == item.iLoveUid) {
                            chatRoomViewModel.sendEvent(RoomContract.Event.OnLoveSelect(loveInfo.id, item.user.id, false))
                        } else {
                            chatRoomViewModel.sendEvent(RoomContract.Event.OnLoveSelect(loveInfo.id, item.user.id, true))
                        }
                    }
                }) { viewBinding, item, position ->
                if (item.hasUser) {
                    val user = item.user
                    viewBinding.orderNumber.isVisible = true
                    viewBinding.orderNumber.text = position.plus(1).toString()
                    viewBinding.avatarLayout.bindUser(user, true)
                    if (item.mic.isSpeaking && !item.mic.mute) {
                        viewBinding.avatarLayout.startVoiceAnim(user.isBoy)
                    } else {
                        viewBinding.avatarLayout.stopVoiceAnim()
                    }
//                    viewBinding.name.text = user.nickname
//                    viewBinding.name.textColor(R.color.white)
                    setUserName(viewBinding.name, user)
                    if (loveInfo.stage > 1) {
                        viewBinding.charmValueLayout.isVisible = true
                        viewBinding.charmValue.text = item.heartValue.toString()
                    } else {
                        viewBinding.charmValueLayout.isInvisible = true
                    }

                    if (loveInfo.stage == 3) {
                        when (selfRole) {
                            0 -> {
                                viewBinding.ivLoveYou.isVisible = false
                                viewBinding.tvLoveNumber.isVisible = false
                            }

                            1 -> {
                                viewBinding.ivLoveYou.isVisible = !user.isSelf
                                viewBinding.tvLoveNumber.isVisible = false
                                if (!user.isSelf) {
                                    if (item.iLoveUid == item.user.userId) {
                                        viewBinding.ivLoveYou.setImageResource(R.drawable.ic_love_selected)
                                    } else {
                                        viewBinding.ivLoveYou.setImageResource(R.drawable.ic_love_unselected)
                                    }
                                }
                            }

                            else -> {
                                viewBinding.ivLoveYou.isVisible = false
                                if (item.loveToIndex != -1) {
                                    viewBinding.tvLoveNumber.isVisible = true
                                    viewBinding.tvLoveNumber.text = item.loveToIndex.toString()
                                } else {
                                    viewBinding.tvLoveNumber.isVisible = false
                                }
                            }
                        }
                    } else {
                        viewBinding.ivLoveYou.isVisible = false
                        viewBinding.tvLoveNumber.isVisible = false
                    }
                } else {
                    viewBinding.orderNumber.isVisible = false
                    viewBinding.ivLoveYou.isVisible = false
                    viewBinding.tvLoveNumber.isVisible = false
                    viewBinding.avatarLayout.clear()
                    viewBinding.avatarLayout.loadAvatar {
                        setImageResource(emptyResId)
                    }
                    viewBinding.name.text = app.getString(R.string.虚位以待)
                    viewBinding.name.textColor(R.color.white_alpha_60)
                    viewBinding.charmValueLayout.isInvisible = true
                }
            }
        }
    }

    override fun createLayoutManager(): LayoutManager {
        return if (loveInfo.stage < 4) {
            LoveSelectLayoutManager()
        } else {
            LovePairLayoutManager(activity)
        }
    }

    override fun generateAdapter(): RecyclerView.Adapter<*> {
        return ConcatAdapter(super.generateAdapter(), loveSpirit.adapter)
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.subList(1, list.size).take(6)
    }

    override fun showUserTipForMicSeats(userId: String): Boolean {
        return if (loveInfo.stage < 4) {
            super.showUserTipForMicSeats(userId)
        } else false
    }

    private val assetEvaArray = arrayOf(
        "love_mode_room_stage_1.mp4",
        "love_mode_room_stage_2.mp4",
        "love_mode_room_stage_3.mp4",
        "love_mode_room_stage_4.mp4",
    )

    @SuppressLint("NotifyDataSetChanged")
    override fun updateMicList(first: Boolean, tipUserId: String, tipText: String, list: List<MicSeat>) {
        val info = chatRoomViewModel.currentState.roomState.loveInfo
        val oldStage = this.loveInfo.stage
        val oldRole = this.selfRole

        if (info != null && info !== this.loveInfo) {
            this.loveInfo = info
        }

        refreshLoveInfo(oldStage, oldRole, list)

        if (list.size == 7 && list.first().hasUser) {
            val item = list.first()
            val user = item.user
            binding.hostAvatarLayout.bindUser(user, true)
            if (item.mic.isSpeaking && !item.mic.mute) {
                binding.hostAvatarLayout.startVoiceAnim(user.isBoy)
            } else {
                binding.hostAvatarLayout.stopVoiceAnim()
            }
            binding.hostAvatarLayout.tag = item
        } else {
            binding.hostAvatarLayout.clear()
            binding.hostAvatarLayout.loadAvatar {
                setImageResource(emptyResId)
            }
            binding.hostAvatarLayout.tag = null
        }
        if (loveInfo.stage != 4 && loveInfo.stage != 5) {
            if (micSeatsLayout.layoutManager !is LoveSelectLayoutManager) {
                micSeatsLayout.layoutManager = createLayoutManager()
            }
            if (loveSpirit.adapter.itemCount > 0) {
                loveSpirit.setData(emptyList(), false)
                spirit.setData(mapMicList(list), false)
                micSeatsLayout.adapter?.notifyDataSetChanged()
                refreshBubble(first, tipUserId, tipText, list)
            } else if (oldStage != loveInfo.stage || oldRole != selfRole) {
                spirit.setData(mapMicList(list), false)
                micSeatsLayout.adapter?.notifyDataSetChanged()
                refreshBubble(first, tipUserId, tipText, list)
            } else {
                super.updateMicList(first, tipUserId, tipText, list)
            }
        } else { // 展示心动配对结果
            if (micSeatsLayout.layoutManager !is LovePairLayoutManager) {
                micSeatsLayout.layoutManager = createLayoutManager()
            }
            val param = buildMap {
                for (micSeat in list) {
                    if (micSeat.hasUser) {
                        put(micSeat.user.userId, micSeat)
                    }
                }
            }
            val loveSeats = loveInfo.pairList.mapIndexed { index, pair ->
                val leftMicSeat = param[pair.leftUser.id] ?: return
                val rightMicSeat = param[pair.rightUser.id] ?: return
                if (loveInfo.stage == 4) {
                    MicLovePairSeat(
                        index,
                        leftMicSeat.user,
                        rightMicSeat.user,
                        leftMicSeat.mic,
                        rightMicSeat.mic,
                        leftMicSeat.heartValue,
                        rightMicSeat.heartValue,
                        pair.leftUser.online,
                        pair.rightUser.online,
                    )
                } else {
                    MicLoveShowSeat(
                        index,
                        leftMicSeat.user,
                        rightMicSeat.user,
                        leftMicSeat.mic,
                        rightMicSeat.mic,
                        leftMicSeat.heartValue,
                        rightMicSeat.heartValue,
                        pair.leftUser.online,
                        pair.rightUser.online,
                    )
                }
            }
            if (spirit.adapter.itemCount > 0) {
                spirit.setData(emptyList(), false)
                loveSpirit.setData(loveSeats, false)
                micSeatsLayout.adapter?.notifyDataSetChanged()
            } else if (oldStage != loveInfo.stage) {
                loveSpirit.setData(loveSeats, false)
                micSeatsLayout.adapter?.notifyDataSetChanged()
            } else {
                loveSpirit.setData(loveSeats)
            }
            refreshBubble(first, tipUserId, tipText, list)
        }
    }

    private fun refreshLoveInfo(oldStage: Int, oldRole: Int, list: List<MicSeat>) {
        selfRole = if (list.firstOrNull()?.user?.id == sUser.id) {
            2
        } else if (list.find { it.user.id == sUser.id } != null) {
            1
        } else {
            0
        }

        if (oldStage != loveInfo.stage) {
            if (loveInfo.stage !in 2..5) {
                binding.gameStatus.setImageResource(R.drawable.ic_love_game_ready)
                selectGroup.unselectCurrent()
                expansionViewModel.processEvent(ChatRoomExpansionMvi.ViewEvent.ChangeVideoEffect(null))
            } else {
                binding.gameStatus.setImageResource(R.drawable.ic_love_game_playing)
                selectGroup.selected(loveInfo.stage.minus(2))
                expansionViewModel.processEvent(ChatRoomExpansionMvi.ViewEvent.ChangeVideoEffect(assetEvaArray[loveInfo.stage.minus(2)]))
            }
        }

        if (selfRole != oldRole || loveInfo.stage != oldStage) {
            if (selfRole == 2) {
                when (loveInfo.stage) {
                    2, 3, 4 -> {
                        binding.btnNextAction.isVisible = true
                        binding.btnGameSwitch.isVisible = true
                        binding.btnNextAction.setText(R.string.下一环节)
                        binding.btnGameSwitch.setText(R.string.重新开始)
                    }

                    5 -> {
                        binding.btnNextAction.isVisible = false
                        binding.btnGameSwitch.isVisible = true
                        binding.btnGameSwitch.setText(R.string.重新开始)
                    }

                    else -> {
                        binding.btnNextAction.isVisible = true
                        binding.btnGameSwitch.isVisible = false
                        binding.btnNextAction.setText(R.string.开始游戏)
                    }
                }
            } else {
                binding.btnNextAction.isVisible = false
                binding.btnGameSwitch.isVisible = false
            }
            binding.gameTip.setTextColor(if (selfRole == 0) "#FFFFE485".toColorInt() else "#FFFF83EB".toColorInt())
            when (loveInfo.stage) {
                3 -> {
                    when (selfRole) {
                        0 -> {
                            binding.gameTip.setText(R.string.麦上玩家正在心动互选中)
                        }

                        1 -> {
                            binding.gameTip.setText(R.string.点亮爱心选择TA互相点亮可一起进入甜蜜告白环节)
                        }

                        else -> binding.gameTip.text = null
                    }
                }

                4 -> {
                    binding.gameTip.setText(R.string.心动值排名第一的情侣可以进入爱的见证环节)
                }

                else -> binding.gameTip.text = null
            }
        }
    }

}

class GameMicSeatsLayout(
    private val activity: ChatRoomActivity,
    private val binding: LayoutGameRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    private val gameViewModel: QuickStartGameViewModel = QuickStartGameViewModel()

    private val gameContainer: FrameLayout = activity.findViewById(R.id.game_container)

    private val btnRestore: View = activity.findViewById(R.id.btn_restore_ui)

    private val btnClose: View = activity.findViewById(R.id.btn_close)

    private val disposable: Disposable

    private var playingGameSet: Set<String>? = null

    init {
        // 获取游戏View容器 English: Retrieve the game view container.
        gameContainer.isVisible = true

        btnRestore.click { _ ->
            chatRoomViewModel.uiMode.value = false
        }

        btnClose.click { _ ->
            chatRoomViewModel.setEffect {
                RoomContract.Effect.CollapseRoomEffect(true)
            }
        }

        gameViewModel.gameViewLiveData.observe(activity) { view ->
            if (view == null) { // 在关闭游戏时，把游戏View给移除 English: When closing the game, remove the game view.
                gameContainer.removeAllViews()
            } else { // 把游戏View添加到容器内 English: Add the game view to the container.
                gameContainer.addView(view, FrameLayout.LayoutParams.MATCH_PARENT, FrameLayout.LayoutParams.MATCH_PARENT)
            }
        }

        gameViewModel.gameStateLiveData.observe(activity) { state ->
            if (gameViewModel.isFullMode) {
                if (state == SudMGPMGState.MGCommonGameState.PLAYING) {
                    toast("为了更好的游戏体验，已自动进入全屏模式")
                }
                chatRoomViewModel.uiMode.value = state == SudMGPMGState.MGCommonGameState.PLAYING
            }
            playingGameSet = if (state == SudMGPMGState.MGCommonGameState.PLAYING) {
                buildSet {
                    val map = gameViewModel.sudFSMMGCache.playerPlayingMap
                    for (element in map) {
                        if (element.value.isPlaying) {
                            add(element.key)
                        }
                    }
                }
            } else {
                null
            }
            try {
                micSeatsLayout.adapter?.notifyDataSetChanged()
            } catch (_: Exception) {
            }
        }

        disposable = activity.doOnLifecycleEvent {
            when (it) {
                Lifecycle.Event.ON_RESUME -> {
                    gameViewModel.onResume()
                }

                Lifecycle.Event.ON_PAUSE -> {
                    gameViewModel.onPause()
                }

                Lifecycle.Event.ON_DESTROY -> {
                    gameViewModel.onDestroy()
                }

                else -> {}
            }
        }

        chatRoomViewModel.bindGameViewModel(gameViewModel)

        activity.launch {
            chatRoomViewModel.uiState.map {
                it.roomState.sudGameInfo?.playing
            }.distinctUntilChanged { old, new ->
                old?.gameId == new?.gameId && old?.mode == new?.mode
            }.collectLatest { gameConf ->
                if (gameConf != null) {
                    switchGame(gameConf)
                } else {
                    gameViewModel.switchGame(activity, "0", 0, false)
                }
            }
        }.recycleIn()

        chatRoomViewModel.uiMode.observe(activity) { clear ->
            toggleUi(clear)
        }

    }

    override fun createMicListSpirit(): Spirit {
        return Spirit(MicSeatCallback()).apply {
            MicSeat::class.java.viewBindingItem(R.layout.layout_game_mic_seats_item, LayoutGameMicSeatsItemBinding::bind, viewBindingCreateVH = {
                it.avatarLayout.clickOfVH { index ->
                    val item = getItem<MicSeat>(index)
                    if (!item.hasUser) {
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ClickUpMic(index + 1)
                        }
                    } else {
                        Analytics.appReportEvent(
                            DataPoint.clickBody(
                                "click_mic_avatar_in_audioroom",
                                extra = "{\"audioroom_id\": ${chatRoomViewModel.roomId}, \"target_user_id\": ${item.user.id}}"
                            )
                        )
                        chatRoomViewModel.setEffect {
                            RoomContract.Effect.ShowUserPanel(item.user)
                        }
                    }
                }

            }) { viewBinding, item, _ ->
                if (item.hasUser) {
                    val user = item.user
                    viewBinding.gameIcon.isVisible = playingGameSet?.contains(user.id) == true
                    viewBinding.avatarLayout.bindUser(user, true)
                    if (item.mic.isSpeaking && !item.mic.mute) {
                        viewBinding.avatarLayout.startVoiceAnim(user.isBoy)
                    } else {
                        viewBinding.avatarLayout.stopVoiceAnim()
                    }
//                    viewBinding.name.text = user.nickname
                    setUserName(viewBinding.name, user)
                } else {
                    viewBinding.gameIcon.isVisible = false
                    viewBinding.avatarLayout.clear()
                    viewBinding.avatarLayout.loadAvatar {
                        setImageResource(emptyResId)
                    }
                    viewBinding.name.text = ""
                }
            }
        }
    }

    override fun createLayoutManager(): LayoutManager {
        return LinearLayoutManager(micSeatsLayout.context, RecyclerView.HORIZONTAL, false)
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list
    }

    override fun release() {
        super.release()
        playingGameSet = null
        gameViewModel.gameViewLiveData.removeObservers(activity)
        gameViewModel.gameStateLiveData.removeObservers(activity)
        chatRoomViewModel.uiMode.removeObservers(activity)
        chatRoomViewModel.gameViewModel = null
        disposable.dispose()
        gameViewModel.onDestroy()
        gameContainer.isVisible = false
        toggleUi(false)
    }

    override fun handleFinishEvent(): Boolean {
        if (gameViewModel.exitGame()) {
            activity.launch {
                delay(500)
                activity.immediateFinish()
            }
            return true
        }
        return false
    }

    override fun playerIsPlaying(): Boolean {
        return gameViewModel.playerIsPlaying(gameViewModel.userId)
    }

    override fun exitGame() {
        gameViewModel.exitGame()
    }

    private fun switchGame(gameConf: GameConf) {
        val isSupport = kotlin.run {
            if (gameConf.minimumVersion.isNotEmpty() && gameConf.minimumVersion.contains(".")) {
                val cur = BuildConfig.VERSION_NAME.split(".")
                val target = gameConf.minimumVersion.split(".")
                repeat(target.size.coerceAtLeast(cur.size)) {
                    if (cur.getOrElse(it) { "0" }.toIntOrNull().orDefault(0) < target.getOrElse(it) { "0" }.toIntOrNull().orDefault(0)) {
                        return@run false
                    }
                }
            }
            true
        }

        if (!isSupport) {
            toast(activity.getString(R.string.当前版本暂不支持该游戏_请升级APP版本))
            activity.finish()
            return
        }

        // 游戏配置
        // Game configuration
        gameViewModel.gameConfigModel.apply {
            gameMode = gameConf.sudGameMode
            ui.ping.hide = true
            ui.version.hide = true // 配置不隐藏ping值
            ui.join_btn.custom = true // 拦截加入游戏事件
            ui.lobby_players.custom = true
            ui.game_settle_close_btn.custom = true
            ui.game_settle_again_btn.custom = true
            ui.game_setting_select_pnl.hide = !gameConf.enableRule
            ui.start_btn.custom = true
        }
        SudGIP.getCfg().showLoadingGameBg = false

        val maxWidth = ScreenUtils.getScreenSize(micSeatsLayout.context)[0]
        val maxHeight = maxWidth.times(gameConf.displayAspectRatio).toInt()

        binding.gameSafeArea.updateLayoutParams {
            width = maxWidth
            height = maxHeight
        }
        // 设置游戏安全操作区域
        // Set the secure operation area for the game.
        binding.gameSafeArea.doOnLayout {
            val rect = Rect(it.left, it.top, it.right, it.bottom)
            val root = binding.root.parent as ViewGroup
            root.offsetDescendantRectToMyCoords(binding.root, rect)

            // top 和 bottom 的调整将影响游戏画面水平线 在垂直方向上的位置，游戏画面是以这个水平画面垂直居中的
            // 原始水平线是指的，gameSafeArea显示区域的垂直方向水平线，通过在垂直方向上调整水平线的位置，达到适配游戏画面的目的
            // 一般情况下如果游戏画面顶部没有透明空白区域，topOffset = 0，只需要动态调整bottomOffset即可
            val gameViewRectModel = GameViewInfoModel.GameViewRectModel()
            if (!gameConf.fullMode) {
                gameViewRectModel.left = rect.left
                gameViewRectModel.top = rect.top.plus(maxHeight.times(gameConf.topOffset).toInt())
                gameViewRectModel.right = root.width.minus(rect.right)
                gameViewRectModel.bottom = root.height.minus(rect.bottom).minus(maxHeight.times(gameConf.bottomOffset).toInt())
            }
            gameViewModel.gameViewRectModel = gameViewRectModel

            // 调用此方法，加载对应的游戏，开发者可根据业务决定什么时候加载游戏。
            // Call this method to load the corresponding game. Developers can decide when to load the game based on their business logic.
            gameViewModel.switchGame(activity, chatRoomViewModel.roomId.toString(), gameConf.gameId, gameConf.fullMode)
        }
    }

    private fun toggleUi(clear: Boolean) {
        btnRestore.isVisible = clear
        btnClose.isVisible = clear
        gameContainer.parentViewGroup?.forEach {
            if (it.tag != "no_clear") {
                it.isVisible = !clear
            }
        }
    }

}

class TextSeatsLayout(
    binding: LayoutTextRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {
    override fun createLayoutManager(): LayoutManager {
        error("createLayoutManager")
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        error("mapMicList")
    }

    override fun init() {
    }

    override fun showUserTipForMicSeats(userId: String): Boolean {
        return false
    }

    override fun updateMicList(first: Boolean, tipUserId: String, tipText: String, list: List<MicSeat>) {

    }
}

class WeddingMicSeatsLayout(
    private val activity: ChatRoomActivity,
    private val binding: LayoutWeddingRoomModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {
    private var weddingInfo = WeddingRoomInfo()
    //配角数量

    override fun createLayoutManager(): LayoutManager {
        return WeddingLayoutManager(activity)
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) = Unit

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun createMicListSpirit(): Spirit {
        return Spirit(MicSeatCallback()).apply {
            MicSeat::class.java.viewBindingItem(
                R.layout.layout_wedding_mic_seats_item,
                LayoutWeddingMicSeatsItemBinding::bind,
                viewBindingCreateVH = {
                    it.avatar.clickOfVH { idx ->
                        val item = getItem<MicSeat>(idx)
                        //因为位置是从3开始的,所以得+3
                        clickSeat(item, idx + 3)
                    }
                }) { viewBinding, item, position ->
                setSeatInfo(
                    item,
                    viewBinding.avatar,
                    viewBinding.userRole,
                    viewBinding.userName,
                    if (position % weddingInfo.columnCount < weddingInfo.columnCount / 2) 4 else 5
                )
            }
        }
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        /**
         * 0 = 司仪
         * 1 = 新娘
         * 2 = 新郎
         */
        return (if (list.size > 3) list.subList(3, list.size) else listOf()).take(weddingInfo.weddingSupportSeatCount)
    }

    override fun updateMicList(
        first: Boolean,
        tipUserId: String,
        tipText: String,
        list: List<MicSeat>
    ) {

        val info = chatRoomViewModel.currentState.roomState.weddingInfo
        if (info != null && info != this.weddingInfo) {
            this.weddingInfo = info
        }


//        if (weddingInfo.roomType == 1) {
//            binding.micSeatsLayout.setPadding(40.dp, 0, 40.dp, 0)
//        } else if (weddingInfo.roomType == 2) {
//            binding.micSeatsLayout.setPadding(12.dp, 0, 40.dp, 0)
//        } else {
//            binding.micSeatsLayout.setPadding(12.dp, 0, 12.dp, 0)
//        }

        //先刷新新娘新郎司仪信息
        refreshTopInfo(list)

        //再将伴郎伴娘的信息拿出来设置到recycler中
        val supportingLists = mapMicList(list)
        spirit.setData(supportingLists)
        refreshBubble(first, tipUserId, tipText, list) // 不能传dataList
    }

    private fun clickSeat(seat: MicSeat, index: Int) {
        if (seat.hasUser) {
            Analytics.appReportEvent(
                DataPoint.clickBody(
                    "click_mic_avatar_in_audioroom",
                    extra = "{\"audioroom_id\": ${chatRoomViewModel.roomId}, \"target_user_id\": ${seat.user.id}}"
                )
            )
            chatRoomViewModel.setEffect {
                RoomContract.Effect.ShowUserPanel(seat.user)
            }
        } else {
            chatRoomViewModel.setEffect {
                RoomContract.Effect.ClickUpMic(index)
            }
        }
    }

    /**
     * 刷新新娘新郎以及司仪的信息
     *
     */
    private fun refreshTopInfo(list: List<MicSeat>) {
        val admin = list.firstOrNull() ?: MicSeat(0, false, INVALID_USER, EMPTY_MIC)
        val groom = list.getOrNull(1) ?: MicSeat(1, false, INVALID_USER, EMPTY_MIC)
        val bride = list.getOrNull(2) ?: MicSeat(2, false, INVALID_USER, EMPTY_MIC)

        setSeatInfo(admin, binding.hostAvatarLayout.avatarView, binding.hostAvatarLayout.userRoleView, binding.hostAvatarLayout.userNameView, 1)
        setSeatInfo(groom, binding.maleHostAvatar.avatarView, binding.maleHostAvatar.userRoleView, binding.maleHostAvatar.userNameView, 2)
        setSeatInfo(bride, binding.femaleHostAvatar.avatarView, binding.femaleHostAvatar.userRoleView, binding.femaleHostAvatar.userNameView, 3)

        binding.hostAvatarLayout.click {
            clickSeat(admin, 0)
        }

        binding.maleHostAvatar.click {
            clickSeat(groom, 1)
        }

        binding.femaleHostAvatar.click {
            clickSeat(bride, 2)
        }
    }

    override val emptyResId: Int
        get() = if (this.weddingInfo.roomType == 2) {
            R.drawable.ic_wedding_default_seat_purple
        } else {
            R.drawable.ic_wedding_default_seat_pink
        }

    /**
     * @param seat 坐席信息
     * @param avatarView 坐席头像view
     * @param role 身份, 0 无身份 1 司仪 2 新郎 3 新娘 4 伴郎 5 伴娘
     */
    private fun setSeatInfo(seat: MicSeat, avatarView: AvatarView, roleView: TextView, nameView: TextView, role: Int = 0) {
        if (seat.hasUser) {
            avatarView.bindUser(seat.user)
            if (seat.mic.isSpeaking && !seat.mic.mute) {
                avatarView.startVoiceAnim(seat.user.isBoy)
            } else {
                avatarView.stopVoiceAnim()
            }
            var isVisible = true
            when (role) {
                1 -> {
                    roleView.text = (app.getString(R.string.司仪))
                    roleView.setBackgroundColor(Color.parseColor("#FF8B56FC"))
                }

                2 -> {
                    roleView.text = (app.getString(R.string.新郎))
                    roleView.setBackgroundColor(Color.parseColor("#FFEF69FB"))
                }

                3 -> {
                    roleView.text = (app.getString(R.string.新娘))
                    roleView.setBackgroundColor(Color.parseColor("#FFEF69FB"))
                }

                4 -> {
                    roleView.text = (app.getString(R.string.伴郎))
                    roleView.setBackgroundColor(Color.parseColor("#FFBC9DFF"))
                }

                5 -> {
                    roleView.text = (app.getString(R.string.伴娘))
                    roleView.setBackgroundColor(Color.parseColor("#FFFFB5D7"))
                }

                else -> {
                    isVisible = false
                }
            }
            roleView.isVisible = isVisible
            setUserName(nameView, seat.user)
//            nameView.text = (seat.user.nickname)
        } else {
            avatarView.clear()
            avatarView.loadAvatar {
                setImageResource(emptyResId)
            }
            roleView.isVisible = false
            nameView.text = ""
        }
    }

}

class CrossPKMicSeatsLayout(
    private val activity: ChatRoomActivity,
    private val binding: LayoutCrossRoomPkModePlayerBinding,
    chatRoomViewModel: ChatRoomViewModel,
) : MicSeatsLayout(binding.micSeatsLayout, chatRoomViewModel) {

    private val coroutineScope = CoroutineScope(Dispatchers.Main.immediate + SupervisorJob())

    private val crossRoomPkManager = CrossRoomPkManager(CrossRoomPkRepository(), chatRoomViewModel, coroutineScope)

    private val roomId = chatRoomViewModel.roomId

    private var timerJob: Job? = null

    private var blueAnimJob: Job? = null

    private var redAnimJob: Job? = null

    private val avatarMask by lazy(LazyThreadSafetyMode.NONE) {
        GradientDrawable().also {
            it.gradientType = GradientDrawable.LINEAR_GRADIENT
            it.colors = intArrayOf(Color.TRANSPARENT, 0x02222220, 0xFF29D4C8.toInt())
            it.orientation = GradientDrawable.Orientation.TOP_BOTTOM
            it.setGradientCenter(0.5f, 0.3f)
        }
    }

    init {
        crossRoomPkManager.pkData.filterNotNull().onEach {
            it.getCurrentRoomPkDetail(roomId)?.also { detail ->
                setContributorUser(binding.redTeamContributor1, detail.cheers?.getOrNull(0))
                setContributorUser(binding.redTeamContributor2, detail.cheers?.getOrNull(1))
                setContributorUser(binding.redTeamContributor3, detail.cheers?.getOrNull(2))
            }

            it.getOpponentRoomPkDetail(roomId)?.also { detail ->
                setContributorUser(binding.blueTeamContributor1, detail.cheers?.getOrNull(0))
                setContributorUser(binding.blueTeamContributor2, detail.cheers?.getOrNull(1))
                setContributorUser(binding.blueTeamContributor3, detail.cheers?.getOrNull(2))
            }

            it.getCurrentRoomBasicInfo(roomId).also { info ->
                binding.selfRoomName.text = info.title
                binding.selfRoomAvatar.loadWith(info.avatarUrl)
            }

            it.getOpponentRoomBasicInfo(roomId).also { info ->
                binding.targetRoomName.text = info.title
                binding.targetRoomAvatar.loadWith(info.avatarUrl)
                binding.targetRoomUserCount.text = activity.getString(R.string.对方参团人数, info.audienceCount)
            }
        }.launchIn(coroutineScope)

        // 分数进度条
        crossRoomPkManager.pkData.filterNotNull().distinctUntilChangedBy {
            (it.pkInfo.fromRoomPkInfo?.roomValue ?: 0) to (it.pkInfo.targetRoomPkInfo?.roomValue ?: 0)
        }.onEach {
            calculateScore(it.getCurrentRoomPkDetail(roomId)?.roomValue ?: 0, it.getOpponentRoomPkDetail(roomId)?.roomValue ?: 0, true)
        }.launchIn(coroutineScope)

        // 倒计时
        crossRoomPkManager.pkData.filterNotNull().distinctUntilChangedBy {
            it.pkInfo.endTime
        }.onEach {
            startPkTimer(it.pkInfo.endTime.times(1000L))
        }.launchIn(coroutineScope)

        coroutineScope.launch {
            crossRoomPkManager.pkData.filterNotNull().map {
                it.getCurrentRoomPkDetail(roomId)?.let { it.isMicMuted to it.thirdPushStreamId }
            }.distinctUntilChanged().collectLatest {
                if (it != null && !it.first) {
                    VoiceRoomHelper.appRtcManager?.updateRemoteStreamWhiteList(setOf(it.second))
                    binding.targetRoomVoice.setImageResource(R.drawable.ic_voice_v2_on)
                    while (isActive) {
                        VoiceRoomHelper.appRtcManager?.getMicById(it.second)?.also { mic ->
                            if (mic.isSpeaking && !mic.mute) {
                                startVoiceAnim()
                            } else {
                                stopVoiceAnim()
                            }
                        }
                        delay(1000)
                    }
                } else {
                    VoiceRoomHelper.appRtcManager?.updateRemoteStreamWhiteList(emptySet())
                    binding.targetRoomVoice.setImageResource(R.drawable.ic_voice_v2_off)
                    stopVoiceAnim()
                }
            }
        }

        coroutineScope.launch {
            chatRoomViewModel.uiState.map {
                it.roomState.isAdmin || it.roomState.isOwner
            }.flowWithLifecycle(activity.lifecycle).distinctUntilChanged().collectLatest {
                binding.btnPkFinish.isVisible = it
                binding.btnPkAddTime.isVisible = it
            }
        }

        binding.btnPkFinish.click {
            Analytics.reportClickEvent("cross_pk_finish", useShushu = true)
            AlertButtonDialogFragment.newInstance(
                app.getString(R.string.confirm_terminate_pk_in_progress),
                app.getString(R.string.end_early),
                app.getString(R.string.continue_pk),
            ).setListener { startButton, _ ->
                if (startButton) {
                    crossRoomPkManager.terminatePk()
                }
                true
            }.show(activity.supportFragmentManager, "确定要结束跨房PK吗")
        }

        binding.btnPkAddTime.click {
            Analytics.reportClickEvent("cross_pk_add_time", useShushu = true)
            coroutineScope.launch {
                val config = crossRoomPkManager.getCrossPkConfig()?.checkFix()
                if (config != null) {
                    BottomComposeDialog(activity) {
                        WheelPickerBottomSheet(
                            initValue = config.extraDurations.first(),
                            durations = config.extraDurations,
                            title = app.getString(R.string.add_time_setting),
                            modifier = Modifier.fillMaxWidth(),
                            onSelectedChanged = {
                                crossRoomPkManager.addPkTime(it)
                            }
                        ) {
                            it.dismiss()
                        }
                    }.show()
                }
            }
        }

        binding.targetRoomAvatar.click(500) {
            val isAdmin = chatRoomViewModel.room.let {
                it.isAdmin || it.isOwner
            }
            if (isAdmin) {
                Analytics.reportClickEvent("cross_pk_click_mic_mute_btn", useShushu = true)
                crossRoomPkManager.setOpponentMuted()
            } else {
                binding.targetRoomInfoLayer.callOnClick()
            }
        }

        binding.targetRoomInfoLayer.click(500) {
            Analytics.reportClickEvent("enter_cross_pk_target_room", useShushu = true)
            AlertButtonDialogFragment.newInstance(
                app.getString(R.string.confirm_enter_opponent_party_room),
                app.getString(R.string.取消),
                app.getString(R.string.确认),
            ).setListener { startButton, _ ->
                if (!startButton) {
                    coroutineScope.launch {
                        crossRoomPkManager.pkData.value?.getOpponentRoomBasicInfo(roomId)?.also {
                            VoiceRoomHelper.joinVoiceChatRoom(it.id)
                        }
                    }
                }
                true
            }.show(activity.supportFragmentManager, "点击确定进入PK对方的派对房")
        }

        coroutineScope.launch {
            crossRoomPkManager.pkData.filterNotNull().map {
                it.getCurrentRoomPkDetail(roomId)?.let { it.isMicMuted to it.thirdPushStreamId }
            }.distinctUntilChanged().collectLatest {
                if (it != null && !it.first) {
                    VoiceRoomHelper.appRtcManager?.updateRemoteStreamWhiteList(setOf(it.second))
                    binding.targetRoomVoice.setImageResource(R.drawable.ic_voice_on)
                    while (isActive) {
                        VoiceRoomHelper.appRtcManager?.getMicById(it.second)?.also { mic ->
                            if (mic.isSpeaking && !mic.mute) {
                                startVoiceAnim()
                            } else {
                                stopVoiceAnim()
                            }
                        }
                        delay(1000)
                    }
                } else {
                    VoiceRoomHelper.appRtcManager?.updateRemoteStreamWhiteList(emptySet())
                    binding.targetRoomVoice.setImageResource(R.drawable.ic_voice_off)
                    stopVoiceAnim()
                }
            }
        }
    }

    override fun createLayoutManager(): LayoutManager {
        return GridLayoutManager(micSeatsLayout.context, 4)
    }

    override fun SpiritViewHolder.viewBindingCreateVH(viewBinding: LayoutMicSeatsItemBinding) {
        viewBinding.root.updateLayoutParams<ViewGroup.MarginLayoutParams> {
            height = 100.dp
        }
        viewBinding.avatarLayout.updateLayoutParams<ConstraintLayout.LayoutParams> {
            matchConstraintPercentHeight = 0.6f
        }
    }

    override fun SpiritViewHolder.beforeViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) = Unit

    override fun SpiritViewHolder.afterViewBindingOnBindData(viewBinding: LayoutMicSeatsItemBinding, item: MicSeat, position: Int) {
        if (item.hasUser) {
            viewBinding.charmValueLayout.isVisible = true
            viewBinding.charmValue.text = item.heartValue.toString()
        } else {
            viewBinding.charmValueLayout.isInvisible = true
        }
    }

    override fun mapMicList(list: List<MicSeat>): List<MicSeat> {
        return list.take(8)
    }

    override fun release() {
        super.release()
        crossRoomPkManager.release()
        coroutineScope.cancel()
    }

    private fun startVoiceAnim() {
        binding.apply {
            voiceAnim.isVisible = true
            voiceAnimMask.foreground = avatarMask
            Glide.with(voiceAnim).load(R.drawable.ic_webp_male_voice).fixInto(voiceAnim)
        }
    }

    private fun stopVoiceAnim() {
        binding.apply {
            Glide.with(voiceAnim).clear(voiceAnim)
            voiceAnimMask.foreground = null
            (voiceAnim.drawable as? WebPDrawable)?.stop()
            voiceAnim.isVisible = false
        }
    }

    private fun setContributorUser(view: ImageView, user: PkUserInfo?) {
        if (user == null) {
            view.loadWith(R.drawable.cupid_room_mic_add)
        } else {
            view.loadAvatar(user.avatarUrl)
        }
    }

    private fun calculateScore(redTeamScore: Int, blueTeamScore: Int, animated: Boolean) {
        val progress = run {
            val total = redTeamScore.plus(blueTeamScore)
            if (total == 0) {
                500
            } else {
                redTeamScore.times(1000).div(total)
            }
        }.coerceIn(148, 852)
        binding.pkScoreIndicator.setProgressCompat(progress, animated)
        if (animated) {
            redAnimJob?.cancel()
            blueAnimJob?.cancel()
            redAnimJob = animateToNumber(binding.redTeamScore, redTeamScore)
            blueAnimJob = animateToNumber(binding.blueTeamScore, blueTeamScore)
        } else {
            binding.redTeamScore.text = redTeamScore.toString()
            binding.blueTeamScore.text = blueTeamScore.toString()
        }
    }

    @SuppressLint("Recycle")
    private fun animateToNumber(view: TextView, value: Int): Job? {
        val start = view.text.toString().trim().toIntOrNull() ?: 0
        if (abs(value.minus(start)) < 10) {
            view.text = value.toString()
            return null
        }
        return coroutineScope.launch {
            ValueAnimator.ofInt(start, value).apply {
                addUpdateListener {
                    view.text = it.animatedValue.toString()
                }
                duration = 450
            }.suspendOnEnd(true)
        }
    }

    private fun startPkTimer(deadlineTimestamp: Long) {
        timerJob?.cancel()
        timerJob = coroutineScope.launch {
            activity.lifecycle.repeatOnLifecycle(Lifecycle.State.STARTED) {
                var cur = SNTPManager.now()
                while (deadlineTimestamp > cur) {
                    val left = deadlineTimestamp.minus(cur)
                    binding.btnPkTimeline.text = formatTime(left.plus(500).div(1000).toInt()) // 四舍五入
                    if (left > 1500) {
                        delay(left.rem(1000))
                    } else {
                        delay(left)
                        binding.btnPkTimeline.text = formatTime(0)
                        break
                    }
                    cur = SNTPManager.now()
                }
                delay(500)
                binding.btnPkTimeline.text = activity.getString(R.string.正在结算)
            }
        }
    }

    private fun formatTime(time: Int): String {
        val minute = time.div(60)
        val second = time.rem(60)
        return "${if (minute > 9) minute else "0$minute"}:${if (second > 9) second else "0$second"}"
    }

}