package com.qyqy.ucoo


sealed interface ABTest {
    object Device : ABTest {

        /**
         * 登录ui样式
         * 1 = 老ui
         * 2 = 新ui
         */
        private const val KEY_LOGIN_UI = "device_ab_key_login_ui"

//        val LoginUI by lazy {
//            val value = sAppKV.getString(KEY_LOGIN_UI, "")
//            value.ifEmpty {
//                Random.nextInt(1, 3).toString().also {
//                    sAppKV.putString(KEY_LOGIN_UI, it)
//                }
//            }
//        }

        val LoginUI :String
            get() = "2"
    }

    sealed interface User : ABTest {

    }
}
