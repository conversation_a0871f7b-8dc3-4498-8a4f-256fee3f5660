package com.qyqy.ucoo.widget

import android.animation.Animator
import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Rect
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.util.TypedValue
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.HorizontalScrollView
import android.widget.TextView
import androidx.appcompat.widget.AppCompatTextView
import androidx.core.animation.addListener
import androidx.core.content.res.use
import androidx.core.view.forEach
import androidx.core.view.isGone
import androidx.core.view.iterator
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import androidx.viewpager.widget.ViewPager
import androidx.viewpager2.widget.ViewPager2
import com.overseas.common.utils.dpF
import com.overseas.common.utils.id2Color
import com.qyqy.ucoo.R
import kotlin.math.roundToInt

/**
 * Author: dylan
 * Version: V1.0
 * Date: 2022/8/22
 * Description: none
 * Modification History: none
 */
@Deprecated("有bug")
class SlideTabLayout @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : HorizontalScrollView(context, attrs, defStyleAttr) {

    private var tabTextSize = 17.dpF

    private var tabGap = 44

    private var maxScrollX: Int? = null

    private val slideContainer = SlideHorizontalLayout(context)

    private var attachWidget: ISlideAttachPagerWidget? = null

    private var iTab: ITab? = null

    init {
        val padding = context.obtainStyledAttributes(attrs, R.styleable.SlideTabLayout).use {
            tabTextSize = it.getDimension(R.styleable.SlideTabLayout_tabTextSize, 17.dpF)
            tabGap = it.getDimension(R.styleable.SlideTabLayout_tabGap, 44f).toInt()
            it.getDimension(R.styleable.SlideTabLayout_tabHorizonPadding, 15.dpF).toInt()
        }
        slideContainer.clipToPadding = false
        slideContainer.setPadding(padding, 0, padding, 0)
        addView(slideContainer, ViewGroup.LayoutParams(ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.MATCH_PARENT))
    }

    override fun scrollTo(x: Int, y: Int) {
        val newScrollX = maxScrollX?.coerceAtMost(x) ?: x
        super.scrollTo(newScrollX, y)
    }

    override fun onOverScrolled(scrollX: Int, scrollY: Int, clampedX: Boolean, clampedY: Boolean) {
        val newScrollX = maxScrollX?.coerceAtMost(scrollX) ?: scrollX
        super.onOverScrolled(newScrollX, scrollY, clampedX, clampedY)
    }

    fun bind(viewPager: ViewPager) {
        bind(object : ISlideAttachPagerWidget {

            private var mScrollState = 0

            override fun registerAdapterDataObserver(callback: OnAdapterDataCallback) {
                viewPager.addOnAdapterChangeListener { viewPager, oldAdapter, newAdapter ->

                }
            }

            override fun registerOnPageChangeCallback(callback: OnPageChangeCallback) {
                viewPager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {

                    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                        callback.onPageScrolled(position, positionOffset, positionOffsetPixels)
                    }

                    override fun onPageSelected(position: Int) {
                        callback.onPageSelected(position)
                    }

                    override fun onPageScrollStateChanged(state: Int) {
                        mScrollState = state
                        callback.onPageScrollStateChanged(state)
                    }
                })
            }

            override val scrollState: Int
                get() = mScrollState

            override val currentItem: Int
                get() = viewPager.currentItem

            override fun setCurrentItem(item: Int, smoothScroll: Boolean) {
                viewPager.setCurrentItem(item, smoothScroll)
            }
        })
    }

    fun bind(viewPager2: ViewPager2) {
        bind(object : ISlideAttachPagerWidget {

            override fun registerAdapterDataObserver(callback: OnAdapterDataCallback) {
//                viewPager2.adapter =
            }

            override fun registerOnPageChangeCallback(callback: OnPageChangeCallback) {
                viewPager2.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {

                    override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                        callback.onPageScrolled(position, positionOffset, positionOffsetPixels)
                    }

                    override fun onPageSelected(position: Int) {
                        callback.onPageSelected(position)
                    }

                    override fun onPageScrollStateChanged(state: Int) {
                        callback.onPageScrollStateChanged(state)
                    }
                })
            }

            override val scrollState: Int
                get() = viewPager2.scrollState

            override val currentItem: Int
                get() = viewPager2.currentItem

            override fun setCurrentItem(item: Int, smoothScroll: Boolean) {
                viewPager2.setCurrentItem(item, smoothScroll)
            }
        })
    }

    fun setITab(iTab: ITab) {
        this.iTab = iTab
        if (attachWidget != null) {
            slideContainer.requestLayout()
        }
    }

    fun bind(attachWidget: ISlideAttachPagerWidget) {
        this.attachWidget = attachWidget
        if (iTab == null) {
            iTab = DefaultTab()
        }
        attachWidget.registerAdapterDataObserver {
            updateTabLists(it)
        }
        attachWidget.registerOnPageChangeCallback(object : OnPageChangeCallback {

            private var tempPrevious = 0

            private var tempNext = 0

            private var tempCheck = false

            private var tempScrollPosition: Pair<Int, Float>? = null

            private var pagerProgress = 0f

            private var lastState = attachWidget.scrollState

            private var lastPosition = attachWidget.currentItem

            // TO 在自动滚动期间，应该没有起始点的概念。在onPageScrolled中，手指滑动时跟随滚动，手指松开后属性动画，从当前位置开始

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                val scrollState = attachWidget.scrollState
                if (scrollState == 0) {
                    return
                }

                if (scrollState == 1) {
                    val mTemp = tempScrollPosition
                    val direction = if (mTemp == null) {
                        positionOffset <= 0.5f
                    } else {
                        if (mTemp.first == position) {
                            positionOffset >= mTemp.second
                        } else {
                            position > mTemp.first
                        }
                    }
                    tempScrollPosition = position to positionOffset
                    tempNext = if (direction) {
                        position + 1
                    } else {
                        position
                    }
                    // TOD 在快速连续滑动时，scrollState 会从2变成1, 如何判断tempNext?（还需要停止动画settlingAnimator）
                } else {

                }
                if (tempPrevious == tempNext) {
                    return
                }
                val lastPagerProgress = pagerProgress
                pagerProgress = if (tempNext > tempPrevious) {
                    (position - tempPrevious + positionOffset) / (tempNext - tempPrevious)
                } else {
                    (tempPrevious - 1 - position + (1 - positionOffset)) / (tempPrevious - tempNext)
                }
                if (tempPrevious >= slideContainer.childCount || tempNext >= slideContainer.childCount) {
                    return
                }
                val reverse = scrollState == 2 && lastPagerProgress > pagerProgress
                if (tempCheck) {
                    tempCheck = false
                    iTab?.onStartSettlingState(slideContainer, tempPrevious, tempNext, reverse, lastPagerProgress)
                }
                iTab?.onPagerScrolled(slideContainer, tempPrevious, tempNext, reverse, pagerProgress, position, positionOffset, positionOffsetPixels)
            }

            override fun onPageSelected(position: Int) {
                lastPosition = position
                if (tempNext != position) {
                    tempNext = position
                }
                if (lastState == 2) {

                    iTab?.onPagerReadyScroll(slideContainer, tempCheck, tempPrevious, tempNext, false)
                }
                iTab?.onPagerTabSelected(slideContainer, isInit = false, isPagerFlowEnd = false)
            }

            override fun onPageScrollStateChanged(state: Int) {
                if (lastState == state) {
                    throw IllegalStateException("不可能，这绝对不可能5")
                }
                if (lastState == 0) {
                    pagerProgress = 0f
                    tempPrevious = lastPosition
                    tempScrollPosition = null
                    iTab?.onPagerStartScroll(slideContainer, state)
                }
                if (state == 2) {
                    tempCheck = true
                    if (lastState == 0) {
                        tempNext = attachWidget.currentItem
                    }
                }
                if (state == 0) {
                    tempScrollPosition = null
                    iTab?.onPagerTabSelected(slideContainer, isInit = false, isPagerFlowEnd = true)
                }
                lastState = state
            }
        })
    }

    fun updateTabLists(sources: List<String>) {
        slideContainer.updateTabLists(sources.filter {
            it.isNotEmpty()
        })
    }

    interface ISlideAttachPagerWidget {

        val scrollState: Int

        val currentItem: Int

        fun registerAdapterDataObserver(callback: OnAdapterDataCallback)

        fun registerOnPageChangeCallback(callback: OnPageChangeCallback)

        fun setCurrentItem(item: Int, smoothScroll: Boolean)

    }

    fun interface OnAdapterDataCallback {
        fun onDataObserver(sources: List<String>)
    }

    /**
     * Callback interface for responding to changing state of the selected page.
     */
    interface OnPageChangeCallback {

        /**
         * This method will be invoked when the current page is scrolled, either as part
         * of a programmatically initiated smooth scroll or a user initiated touch scroll.
         *
         * @param position Position index of the first page currently being displayed.
         * Page position+1 will be visible if positionOffset is nonzero.
         * @param positionOffset Value from [0, 1) indicating the offset from the page at position.
         * @param positionOffsetPixels Value in pixels indicating the offset from position.
         */
        fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int)

        /**
         * This method will be invoked when a new page becomes selected. Animation is not
         * necessarily complete.
         *
         * @param position Position index of the new selected page.
         */
        fun onPageSelected(position: Int)

        /**
         * Called when the scroll state changes. Useful for discovering when the user begins
         * dragging, when a fake drag is started, when the pager is automatically settling to the
         * current page, or when it is fully stopped/idle. `state` can be one of [ViewPager.SCROLL_STATE_IDLE], [ViewPager.SCROLL_STATE_DRAGGING] or [ViewPager.SCROLL_STATE_SETTLING].
         */
        fun onPageScrollStateChanged(state: Int)
    }

    abstract class ITabLayout(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : ViewGroup(context, attrs, defStyleAttr) {

        abstract val scrollState: Int

        abstract val currentItem: Int

        abstract val children: Sequence<View>

        abstract val parentScrollView: SlideTabLayout

        abstract fun setCurrentItem(item: Int, smoothScroll: Boolean)

        abstract fun getTabView(index: Int): TextView

        abstract fun getTabViewLayout(index: Int): Rect

        abstract fun layoutChildren()

    }

    private inner class SlideHorizontalLayout @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : ITabLayout(context, attrs, defStyleAttr) {

        init {
            setPadding(30, 0, 30, 0)
            clipToPadding = false
            setWillNotDraw(false)
        }

        private var measureOffsetFlag = false

        private var tempUpdateFlag = false

        private var childLayouts: Array<Rect>? = null

        override val scrollState: Int
            get() = attachWidget?.scrollState ?: 0

        override val currentItem: Int
            get() = attachWidget?.currentItem ?: 0

        override val children: Sequence<View>
            get() = object : Sequence<View> {
                override fun iterator() = <EMAIL>()
            }

        override val parentScrollView: SlideTabLayout
            get() = this@SlideTabLayout

        override fun setCurrentItem(item: Int, smoothScroll: Boolean) {
            attachWidget?.setCurrentItem(item, smoothScroll)
        }

        override fun getTabView(index: Int): TextView {
            return getChildAt(index) as TextView
        }

        override fun getTabViewLayout(index: Int): Rect {
            return childLayouts?.getOrNull(index) ?: getTabView(index).let {
                Rect(it.left, it.top, it.right, it.bottom)
            }
        }

        override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
            if (childCount == 0) {
                super.onMeasure(widthMeasureSpec, heightMeasureSpec)
                return
            }
            measureChildren(widthMeasureSpec, heightMeasureSpec)
            var childState = 0
            var maxTabWidth = 0
            var maxTabHeight = 0
            var widthTotal = children.foldIndexed(0) { index, acc, child ->
                if (child.isGone) {
                    return@foldIndexed acc
                }
                childState = combineMeasuredStates(childState, child.measuredState)
                val childWidth = child.measuredWidth
                val childHeight = child.measuredHeight
                maxTabWidth = maxTabWidth.coerceAtLeast(childWidth)
                maxTabHeight = maxTabHeight.coerceAtLeast(childHeight)
                acc + childWidth + if (index == 0) 0 else tabGap
            }

            // Account for padding too
            widthTotal += paddingLeft + paddingRight
            maxTabHeight += paddingTop + paddingBottom

            measureOffsetFlag = iTab?.onMeasureOffset(this, maxTabWidth, maxTabHeight)?.let {
                widthTotal += it.first
                maxTabHeight += it.second
                it.first != 0
            } ?: false

            // Check against our minimum height and width
            widthTotal = widthTotal.coerceAtLeast(suggestedMinimumWidth)
            maxTabHeight = maxTabHeight.coerceAtLeast(suggestedMinimumHeight)

            setMeasuredDimension(widthTotal, resolveSizeAndState(maxTabHeight, heightMeasureSpec, childState shl MEASURED_HEIGHT_STATE_SHIFT))
        }

        override fun onLayout(changed: Boolean, l: Int, t: Int, r: Int, b: Int) {
            if (childCount == 0) {
                return
            }
            if (tempUpdateFlag) {
                tempUpdateFlag = false
                iTab?.onPagerTabSelected(slideContainer, isInit = true, isPagerFlowEnd = false)
            }
            layoutChildren()
        }

        override fun onDraw(canvas: Canvas) {
            if (childCount == 0) {
                return
            }
            iTab?.onDraw(this, canvas, true)
            super.onDraw(canvas)
            iTab?.onDraw(this, canvas, false)
        }

        override fun layoutChildren() {
            val parentTop: Int = paddingTop
            val parentBottom: Int = bottom - top - paddingBottom

            var childLeft = paddingLeft
            var originChildLeft = childLeft

            val tempRectList = if (childLayouts == null) ArrayList<Rect>(childCount) else null

            forEach { child ->
                val w = child.measuredWidth
                val h = child.measuredHeight
                val (leftGap, rightGap) = iTab?.onLayoutOffset(this, child) ?: (0 to 0)
                childLeft += leftGap
                val childTop = (parentBottom + parentTop - h) / 2
                val childRight = childLeft + w
                val originChildRight = originChildLeft + w
                child.layout(childLeft, childTop, childRight, childTop + h)
                tempRectList?.add(Rect(originChildLeft, childTop, originChildRight, childTop + h))
                childLeft = childRight + tabGap + rightGap
                originChildLeft = originChildRight + tabGap
            }

            if (tempRectList != null) {
                childLayouts = tempRectList.toTypedArray()
            }
            maxScrollX = if (measureOffsetFlag) {
                (childLeft + paddingRight - tabGap - (parent as View).width).coerceAtLeast(0)
            } else {
                null
            }
        }

        fun updateTabLists(sources: List<String>) {
            val mChildrenCount = childCount
            sources.forEachIndexed { index, entry ->
                val tabView = if (index < mChildrenCount) {
                    requireNotNull(getChildAt(index)) as TabView
                } else {
                    TabView(context).apply { addView(this) }
                }
                tabView.text = entry
                tabView.setOnClickListener {
                    attachWidget?.setCurrentItem(index, true)
                }
            }

            val removeCount = mChildrenCount - sources.size
            if (removeCount > 0) {
                removeViews(sources.size, removeCount)
            }
            tempUpdateFlag = true
        }
    }

    private inner class TabView @JvmOverloads constructor(
        context: Context,
        attrs: AttributeSet? = null,
        defStyleAttr: Int = 0
    ) : AppCompatTextView(context, attrs, defStyleAttr) {

        init {
            setTextSize(TypedValue.COMPLEX_UNIT_PX, tabTextSize)
            gravity = Gravity.CENTER
        }

        override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
            super.onSizeChanged(w, h, oldw, oldh)
            iTab?.onTabViewSizeChanged(this)
        }
    }

}

/**
 * 当用户开始手指滑动[ViewPager]，或者调用[ViewPager.setCurrentItem]，到[ViewPager]停止滚动到目标位置，称为一次pager流程
 * 不管是哪种方式触发pager流程，最终pager流程都是以自动滚动到目标位置结束
 */
interface ITab {

    /**
     * tabView的[View.onSizeChanged]回调
     */
    fun onTabViewSizeChanged(tabView: View) {}

    /**
     * 某些情况下要动态调整测量宽高
     * @return 如需调整，请返回需要调整的offset, [Pair.first] 表示宽度, [Pair.second] 表示高度
     */
    fun onMeasureOffset(tabLayout: SlideTabLayout.ITabLayout, maxTabWidth: Int, maxTabHeight: Int): Pair<Int, Int>? = null

    /**
     * 某些情况下要动态子view的layout参数
     * @return 如需调整，请返回需要调整的offset, [Pair.first] 表示子view左边的间隙, [Pair.second] 表示子view右边的间隙
     */
    fun onLayoutOffset(tabLayout: SlideTabLayout.ITabLayout, childView: View): Pair<Int, Int>? = null

    /**
     * 在SlideTabLayout绘制，如有需要可以进行绘制
     * @param isBefore 表示在目标view的[View.onDraw]之前还是之后绘制，[isBefore]为真表示之前。
     */
    fun onDraw(tabLayout: SlideTabLayout.ITabLayout, canvas: Canvas, isBefore: Boolean) {}

    /**
     * 表示pager流程开始，此时可以做一些记录操作
     * @param scrollState 一般为1或2，1表示手指滑动，2表示开始自动滑动
     */
    fun onPagerStartScroll(tabLayout: SlideTabLayout.ITabLayout, scrollState: Int) {}

    fun onPagerReadyScroll(tabLayout: SlideTabLayout.ITabLayout, isFirstReady: Boolean, startPosition: Int, endPosition: Int, reverse: Boolean) {}

    /**
     * 表示pager流程进入自动滚动，滚动到目标位置
     * @param tabLayout 此时[SlideTabLayout.ITabLayout.scrollState]一定等于2，表示自动滚动阶段
     * @param startPosition 此次自动滚动的起始位置
     * @param endPosition 此次自动滚动的目标位置
     * @param reverse 是否是反向滚动，若为true，则是往startPosition方向滚动
     * @param pagerProgress 当前所处的滚动进度
     */
    fun onStartSettlingState(tabLayout: SlideTabLayout.ITabLayout, startPosition: Int, endPosition: Int, reverse: Boolean, pagerProgress: Float) {}

    /**
     * 表示pager流程进度回调
     * @param tabLayout 此时[SlideTabLayout.ITabLayout.scrollState]一定等于1或2， 1表示手指滑动，2表示自动滚动
     * @param startPosition 此进度的起始位置
     * @param endPosition 此进度的目标位置
     * @param pagerProgress pager流程的进度
     * @param position 同[ViewPager.OnPageChangeListener.onPageScrolled]参数
     * @param positionOffset 同[ViewPager.OnPageChangeListener.onPageScrolled]参数
     * @param positionOffsetPixels 同[ViewPager.OnPageChangeListener.onPageScrolled]参数
     */
    fun onPagerScrolled(
        tabLayout: SlideTabLayout.ITabLayout,
        startPosition: Int,
        endPosition: Int,
        reverse: Boolean,
        pagerProgress: Float,
        position: Int,
        positionOffset: Float,
        positionOffsetPixels: Int
    ) = Unit

    /**
     * 选中了目标位置tab回调
     * @param isPagerFlowEnd true: 表示pager流程结束。false: 表示[ViewPager.OnPageChangeListener.onPageSelected]回调，此回调时pager流程还未结束
     */
    fun onPagerTabSelected(tabLayout: SlideTabLayout.ITabLayout, isInit: Boolean, isPagerFlowEnd: Boolean) {}

}

class EmptyTab : ITab {

    override fun onPagerTabSelected(tabLayout: SlideTabLayout.ITabLayout, isInit: Boolean, isPagerFlowEnd: Boolean) {
        if (isInit || !isPagerFlowEnd) {
            tabLayout.children.forEachIndexed { index, tabView ->
                val isSelected = index == tabLayout.currentItem
                tabView.isSelected = isSelected
                (tabView as TextView).setTextColor(if (isSelected) Color.YELLOW else Color.WHITE)
            }
        }
    }

    override fun onStartSettlingState(
        tabLayout: SlideTabLayout.ITabLayout,
        startPosition: Int,
        endPosition: Int,
        reverse: Boolean,
        pagerProgress: Float
    ) {
        val parentView = tabLayout.parentScrollView
        val targetScrollX = tabLayout.getTabView(endPosition).let {
            (it.left + it.right) / 2 - (parentView.width / 2)
        }.coerceAtLeast(0)
        parentView.smoothScrollTo(targetScrollX, 0)
    }
}

class DefaultTab(private val color: Int = id2Color(R.color.teal_300)) : ITab {

    data class TabRange(
        val startPosition: Int,
        val startCenterX: Int,
        val endPosition: Int,
        val endCenterX: Int,
        val startScrollX: Int,
        val targetScrollX: Int,
        var progress: Float = 0f
    ) {

        val centerXOffset = endCenterX - startCenterX

        private val scrollXOffset = targetScrollX - startScrollX

        fun transformScrollX(): Int {
            return (startScrollX + scrollXOffset * progress).roundToInt()
        }

        fun transformCenterX(): Int {
            return (startCenterX + centerXOffset * progress).roundToInt()
        }

    }

    private val normalTabScale = 1f

    private var selectedTabScale = 1.6f

    private var indicatorDrawable: Drawable? = null

    private var indicatorMargin: Int = 5

    private val indicatorRect = Rect(0, 0, 40, 8)

    private var tabRange: TabRange? = null

    init {
        indicatorDrawable = GradientDrawable().also {
            it.setColor(color)
            it.cornerRadius = indicatorRect.height().toFloat()
        }
    }

    override fun onTabViewSizeChanged(tabView: View) {
        tabView.apply {
            pivotX = 0f
            pivotY = height * 0.8f
        }
    }

    override fun onMeasureOffset(tabLayout: SlideTabLayout.ITabLayout, maxTabWidth: Int, maxTabHeight: Int): Pair<Int, Int> {
        var extraW = 0
        var extraH = 0
        if (maxTabWidth > 0 && selectedTabScale > 1) {
            extraW = ((selectedTabScale - 1f) * maxTabWidth).roundToInt()
        }
        if (maxTabHeight > 0 && selectedTabScale > 1) {
            extraH = ((selectedTabScale - 1f) * maxTabHeight).roundToInt()
        }
        return extraW to extraH
    }

    override fun onLayoutOffset(tabLayout: SlideTabLayout.ITabLayout, childView: View): Pair<Int, Int> {
        val leftGap = 0
        val rightGap = (childView.scaleX - 1).takeIf {
            it != 0f
        }?.let {
            (it * childView.measuredWidth).roundToInt()
        } ?: 0
        return leftGap to rightGap
    }

    override fun onDraw(tabLayout: SlideTabLayout.ITabLayout, canvas: Canvas, isBefore: Boolean) {
        if (!isBefore) {
            return
        }
        tabLayout.apply {
            val realTabRange = obtainTabRange(this, false, tabLayout.currentItem, tabLayout.currentItem + 1)
            val offset = realTabRange.transformCenterX()
            val lineLeft = offset - indicatorRect.width() / 2
            val lineRight = lineLeft + indicatorRect.width()
            indicatorRect.set(lineLeft, height - indicatorMargin - indicatorRect.height(), lineRight, height - indicatorMargin)
            indicatorDrawable?.bounds = indicatorRect
            indicatorDrawable?.draw(canvas)
        }
    }

    override fun onPagerStartScroll(tabLayout: SlideTabLayout.ITabLayout, scrollState: Int) {
        tabRange?.progress = 0f
        if (scrollState == 1) {
            if (settlingAnimator?.isRunning == true) {
                settlingAnimator?.end()
            }
            val targetScrollX = getViewScaleLayout(tabLayout, tabLayout.currentItem, tabLayout.currentItem).let {
                (it.left + it.right - tabLayout.parentScrollView.width).div(2f).roundToInt()
            }.coerceAtLeast(0)
            tabLayout.parentScrollView.scrollTo(targetScrollX, 0)
        }
    }

    override fun onStartSettlingState(
        tabLayout: SlideTabLayout.ITabLayout,
        startPosition: Int,
        endPosition: Int,
        reverse: Boolean,
        pagerProgress: Float
    ) {
        startPagerSettlingAnimator(tabLayout, obtainTabRange(tabLayout, true, startPosition, endPosition).also {
            it.progress = pagerProgress
        }, reverse)
    }

    override fun onPagerScrolled(
        tabLayout: SlideTabLayout.ITabLayout, startPosition: Int, endPosition: Int, reverse: Boolean,
        pagerProgress: Float, position: Int, positionOffset: Float, positionOffsetPixels: Int
    ) {
        if (tabLayout.scrollState != 1) {
            return
        }
        tabLayout.apply {
            if (selectedTabScale - normalTabScale != 0f) {
                tabRange?.endPosition?.also {
                    if (it != startPosition && it != endPosition) {
                        getTabView(it).scaleXy(normalTabScale, true)
                    }
                }
            }
            val realTabRange = obtainTabRange(this, true, startPosition, endPosition)
            realTabRange.progress = pagerProgress
            updateScrolled(realTabRange)
            invalidate()
        }
    }

    override fun onPagerReadyScroll(
        tabLayout: SlideTabLayout.ITabLayout,
        isFirstReady: Boolean,
        startPosition: Int,
        endPosition: Int,
        reverse: Boolean
    ) {
        val oldTabRange = tabRange
        val realTabRange = obtainTabRange(tabLayout, true, startPosition, endPosition)
        if (!isFirstReady) {
            if (oldTabRange != realTabRange && oldTabRange != null) {
                realTabRange.progress = (oldTabRange.transformCenterX() - realTabRange.startCenterX).times(1f).div(realTabRange.centerXOffset)
            }
            startPagerSettlingAnimator(tabLayout, realTabRange, reverse)
        }
    }

    override fun onPagerTabSelected(tabLayout: SlideTabLayout.ITabLayout, isInit: Boolean, isPagerFlowEnd: Boolean) {
        tabLayout.apply {
            val curIndex = currentItem
            children.forEachIndexed { index, view ->
                val tabView = view as TextView
                val isSelected = index == curIndex
                if (isInit) {
                    tabView.scaleXy(if (isSelected) selectedTabScale else normalTabScale, false)
                }
                if (isInit || !isPagerFlowEnd) {
                    tabView.isSelected = isSelected
                    tabView.setTextColor(if (isSelected) id2Color(R.color.white) else id2Color(R.color.white_alpha_30))
                    tabView.typeface = if (isSelected) Typeface.defaultFromStyle(Typeface.BOLD) else Typeface.defaultFromStyle(Typeface.NORMAL)
                }
            }
        }
    }

    private fun obtainTabRange(tabLayout: SlideTabLayout.ITabLayout, checkMode: Boolean, startPosition: Int, endPosition: Int): TabRange {
        return tabRange?.takeIf {
            !checkMode || it.startPosition == startPosition && it.endPosition == endPosition
        } ?: run {
            val startScaleLayout = getViewScaleLayout(tabLayout, startPosition, startPosition)
            val endScaleLayout = getViewScaleLayout(tabLayout, endPosition, endPosition)
            val startScrollX = startScaleLayout.let {
                (it.left + it.right - tabLayout.parentScrollView.width).div(2f).roundToInt()
            }.coerceAtLeast(0)
            val targetScrollX = endScaleLayout.let {
                (it.left + it.right - tabLayout.parentScrollView.width).div(2f).roundToInt()
            }.coerceAtLeast(0)
            TabRange(startPosition, startScaleLayout.centerX(), endPosition, endScaleLayout.centerX(), startScrollX, targetScrollX).also {
                tabRange = it
            }
        }
    }

    private fun getViewScaleLayout(tabLayout: SlideTabLayout.ITabLayout, position: Int, selectedPosition: Int): Rect {
        val layoutRect = tabLayout.getTabViewLayout(position)
        if (position < selectedPosition) {
            return Rect(layoutRect)
        }
        val tabView = tabLayout.getTabView(selectedPosition)
        val offsetX = (tabView.measuredWidth * (selectedTabScale - 1)).toInt()
        return if (position == selectedPosition) {
            Rect(layoutRect).also {
                it.right += offsetX
            }
        } else {
            Rect(layoutRect).also {
                it.left += offsetX
                it.right += offsetX
            }
        }
    }

    private var settlingAnimator: Animator? = null

    private fun startPagerSettlingAnimator(tabLayout: SlideTabLayout.ITabLayout, realTabRange: TabRange, reverse: Boolean) {
        if (settlingAnimator?.isRunning == true) {
            settlingAnimator?.cancel()
        }
        val startProgress = realTabRange.progress
        val endProgress = if (reverse) 0f else 1f
        settlingAnimator = ValueAnimator.ofFloat(startProgress, endProgress).apply {
            interpolator = FastOutSlowInInterpolator()
            duration = 200
            addUpdateListener {
                realTabRange.progress = it.animatedValue as Float
                tabLayout.updateScrolled(realTabRange)
                tabLayout.postInvalidateOnAnimation()
            }
            var isCanceled = false
            addListener(
                onStart = {
                    tabLayout.children.forEachIndexed { index, view ->
                        if (index != realTabRange.startPosition && index != realTabRange.endPosition) {
                            view.scaleXy(normalTabScale, true)
                        }
                    }
                },
                onCancel = {
                    isCanceled = true
                },
                onEnd = {
                    tabLayout.children.forEachIndexed { index, view ->
                        view.scaleXy(if (index == tabLayout.currentItem) selectedTabScale else normalTabScale, false)
                    }
                    tabLayout.layoutChildren()
                }
            )
            start()
        }
    }

    private fun SlideTabLayout.ITabLayout.updateScrolled(realTabRange: TabRange) {
        val startPosition = realTabRange.startPosition
        val endPosition = realTabRange.endPosition
        val startView = getTabView(startPosition)
        val endView = getTabView(endPosition)
        val pagerProgress = realTabRange.progress

        val scaleOffset = selectedTabScale - normalTabScale
        if (scaleOffset != 0f) {
            // 缩放
            val scaleOffsetProgress = scaleOffset * pagerProgress
            startView.scaleXy(selectedTabScale - scaleOffsetProgress, true)
            endView.scaleXy(normalTabScale + scaleOffsetProgress, true)

            // 重新relayoutChildren，因为字体发生了缩放
            layoutChildren()
        }

        parentScrollView.scrollTo(realTabRange.transformScrollX(), 0)
    }

    private fun View.scaleXy(scale: Float, scrolling: Boolean) {
        scaleX = scale
        scaleY = scale
    }

}