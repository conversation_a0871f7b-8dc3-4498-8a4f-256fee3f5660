package com.qyqy.ucoo.widget.video

import android.content.Context
import android.net.Uri
import android.util.AttributeSet
import android.view.Gravity
import androidx.appcompat.widget.AppCompatImageView
import androidx.core.view.isVisible
import androidx.media3.common.MediaItem
import androidx.media3.common.PlaybackException
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.overseas.common.ext.click
import com.qyqy.ucoo.R
import com.qyqy.ucoo.core.player.AVideoPlayer
import com.qyqy.ucoo.core.player.ExoHelper
import com.qyqy.ucoo.im.compat.IMUtils
import com.qyqy.ucoo.user.video.feed.MediaPlayerCacheManager
import com.qyqy.ucoo.utils.LogUtil

@UnstableApi
class MyVideoView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null,
) : PlayerView(context, attrs), AVideoPlayer {

    companion object {
        private const val TAG = "MyVideoView"
    }

    private val player: ExoPlayer = ExoHelper.buildExoPlayer(context)

    init {
        useController = false
        setPlayer(player)
        resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
        player.repeatMode = Player.REPEAT_MODE_OFF
        player.addListener(object : Player.Listener {

            override fun onPlaybackStateChanged(playbackState: Int) {
                super.onPlaybackStateChanged(playbackState)
                onStateChanged(playbackState)
            }

            override fun onSurfaceSizeChanged(width: Int, height: Int) {
                super.onSurfaceSizeChanged(width, height)
                LogUtil.d("onSurfaceSizeChanged: w:$width,h:$height", TAG)
            }

            override fun onPlayerError(error: PlaybackException) {
                super.onPlayerError(error)
                LogUtil.e("$error", TAG)
            }
        })
    }

    private fun onStateChanged(playbackState: Int) {
        LogUtil.d("onStateChanged:$playbackState", TAG)
        when (playbackState) {
            Player.STATE_ENDED -> {
                hasPlayed = false
                seekTo(0)
                pause()
                if (enableController) {
                    ivControl.isVisible = true
                }
            }

            Player.STATE_READY -> {

            }

            else -> {}
        }
    }

    var autoPlay: Boolean = false
        set(value) {
            player.playWhenReady = value
            field = value
        }
        get() = player.playWhenReady


    private val ivControl by lazy {
        AppCompatImageView(context).also {
            it.setImageResource(R.drawable.ic_video_pause)
        }
    }
    var enableController: Boolean = false
        set(value) {
            field = value
            if (value) {
                if (ivControl.parent == null) {
                    click {
                        if (enableController.not()) {
                            return@click
                        }
                        if (isPlaying()) {
                            hasPlayed = false
                            pause()
                            ivControl.isVisible = true
                        } else {
                            hasPlayed = true
                            prepare()
                            play()
                            ivControl.isVisible = false
                        }
                    }
                    addView(ivControl, LayoutParams(-2, -2).also {
                        it.gravity = Gravity.CENTER
                    })
                }
                ivControl.isVisible = true
            } else {
                ivControl.isVisible = false
            }
        }

    override var isLoop: Boolean
        set(value) {
            if (value) {
                player.repeatMode = Player.REPEAT_MODE_ALL
            }
        }
        get() = player.repeatMode == Player.REPEAT_MODE_ALL

    override var hasPlayed: Boolean = false
        private set

    override fun isPlaying(): Boolean {
        return player.isPlaying
    }

    override var speed: Float
        get() = player.playbackParameters.speed
        set(value) {
            player.playbackParameters = player.playbackParameters.withSpeed(value)
        }

    override var isMute: Boolean
        set(value) {
            if (value) {
                player.volume = 0f
            } else {
                player.volume = 1f
            }
        }
        get() = player.volume == 0f

    override fun setSource(url: String) {
        LogUtil.d("setSource:$url", TAG)
        try {
            player.setMediaSource(MediaPlayerCacheManager.buildDataSource(url))
            prepare()
            if (autoPlay) {
                play()
                if (enableController) {
                    ivControl.isVisible = false
                }
            }
        } catch (e: Exception) {
            Firebase.crashlytics.recordException(IllegalArgumentException("setSource: $url"))
        }
    }

    override fun setSource(uri: Uri) {
        LogUtil.d("setSource:$uri", TAG)
        if (IMUtils.isLocalUri(uri)) {
            player.setMediaItem(MediaItem.fromUri(uri))
        } else{
            player.setMediaSource(MediaPlayerCacheManager.buildDataSource(uri.toString()))
        }
        prepare()
        if (autoPlay) {
            play()
            if (enableController) {
                ivControl.isVisible = false
            }
        }
    }

    override fun hasSource(): Boolean {
        return player.mediaItemCount > 0
    }

    override fun play() {
        hasPlayed = true
        player.play()
        LogUtil.d("play", TAG)
    }

    override fun pause() {
        player.pause()
        LogUtil.d("pause", TAG)
    }

    override fun stop() {
        hasPlayed = false
        player.stop()
        LogUtil.d("stop", TAG)
    }

    override fun release() {
        player.release()
    }

    override fun seekTo(positionMs: Long) {
        player.seekTo(positionMs)
    }

    override fun setVolume(volume: Float) {
        player.volume = volume
    }

    override fun getVolume(): Float {
        return player.volume
    }

    override fun prepare() {
        player.prepare()
    }
}