package com.qyqy.ucoo.widget

import android.animation.FloatEvaluator
import android.content.Context
import android.util.AttributeSet
import android.view.View
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.FrameLayout
import android.widget.Scroller
import androidx.core.view.NestedScrollingParent3
import androidx.core.view.NestedScrollingParentHelper
import androidx.core.view.ViewCompat

/**
 * Author: dylan
 * Version: V1.0
 * Date: 2023/3/10
 * Description: none
 * Modification History: none
 */
open class OverScrollLayout @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr), NestedScrollingParent3 {

    interface IOverScrollRatioProvider {

        /**
         * 获取阻尼系数
         * @return 返回在0~1之间
         */
        fun getScrollRatio(offsetY: Int, axes: Int, backOver: Boolean): Float
    }

    var overScrollRatioProvider = object : IOverScrollRatioProvider {

        private val maxOffsetY = 160

        private val baseRatio = 0.6f

        private val typeEvaluator = FloatEvaluator()

        private val interpolator = AccelerateInterpolator()

        override fun getScrollRatio(offsetY: Int, axes: Int, backOver: Boolean): Float {
            return if (backOver) {
                1f
            } else {
                val fraction = kotlin.math.abs(offsetY).div(maxOffsetY.toFloat())
                typeEvaluator.evaluate(interpolator.getInterpolation(fraction), baseRatio, 0f)
            }
        }
    }

    var overScrolledFlag = true
        set(value) {
            if (field && !value) {
                finishOverScroll()
            }
            field = value
        }

    var resetDuration = 150

    var overScrollAxes = ViewCompat.SCROLL_AXIS_VERTICAL

    val upperX: Int
        get() = getResetOverScrollX(-1)

    val lowerX: Int
        get() = getResetOverScrollX(1)

    val upperY: Int
        get() = getResetOverScrollY(-1)

    val lowerY: Int
        get() = getResetOverScrollY(1)

    open val overScrollX: Int
        get() = scrollX

    open val overScrollY: Int
        get() = scrollY

    private val mParentHelper by lazy(LazyThreadSafetyMode.NONE) {
        NestedScrollingParentHelper(this)
    }

    private val overScroller = Scroller(context, DecelerateInterpolator())

    private val mNestedScrollingV2ConsumedCompat = IntArray(2)

    private var moveState = 0

    private var recentlyResetOverScrollX: Int? = null

    private var recentlyResetOverScrollY: Int? = null

    override fun onStartNestedScroll(child: View, target: View, nestedScrollAxes: Int): Boolean {
        return onStartNestedScroll(child, target, nestedScrollAxes, ViewCompat.TYPE_TOUCH)
    }

    override fun onStartNestedScroll(child: View, target: View, axes: Int, type: Int): Boolean {
        return axes and overScrollAxes != 0
    }

    override fun onNestedScrollAccepted(child: View, target: View, axes: Int) {
        onNestedScrollAccepted(child, target, axes, ViewCompat.TYPE_TOUCH)
    }

    override fun onNestedScrollAccepted(child: View, target: View, axes: Int, type: Int) {
        mParentHelper.onNestedScrollAccepted(child, target, axes, type)
    }

    override fun onStopNestedScroll(child: View) {
        onStopNestedScroll(child, ViewCompat.TYPE_TOUCH)
    }

    override fun onStopNestedScroll(target: View, type: Int) {
        mParentHelper.onStopNestedScroll(target, type)
        if (!overScrolledFlag) {
            return
        }
        if (type != ViewCompat.TYPE_NON_TOUCH && moveState != 1) {
            return
        }
        val dx = if (overScrollAxes and ViewCompat.SCROLL_AXIS_HORIZONTAL != 0) {
            getScrollXWhenAbortOverScroll() - overScrollX
        } else 0

        val dy = if (overScrollAxes and ViewCompat.SCROLL_AXIS_VERTICAL != 0) {
            getScrollYWhenAbortOverScroll() - overScrollY
        } else 0

        if (dx != 0 || dy != 0) {
            overScroller.startScroll(overScrollX, overScrollY, dx, dy, resetDuration)
            postInvalidateOnAnimation()
        }
        moveState = 0
        recentlyResetOverScrollX = null
        recentlyResetOverScrollY = null
    }

    override fun onNestedScroll(target: View, dxConsumed: Int, dyConsumed: Int, dxUnconsumed: Int, dyUnconsumed: Int) {
        onNestedScroll(target, dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, ViewCompat.TYPE_TOUCH)
    }

    override fun onNestedScroll(target: View, dxConsumed: Int, dyConsumed: Int, dxUnconsumed: Int, dyUnconsumed: Int, type: Int) {
        onNestedScroll(target, dxConsumed, dyConsumed, dxUnconsumed, dyUnconsumed, type, mNestedScrollingV2ConsumedCompat)
    }

    override fun onNestedScroll(target: View, dxConsumed: Int, dyConsumed: Int, dxUnconsumed: Int, dyUnconsumed: Int, type: Int, consumed: IntArray) {
        moveState = 1
        if (!overScrolledFlag) {
            return
        }
        if (overScrollAxes and ViewCompat.SCROLL_AXIS_VERTICAL != 0) {
            if (dyUnconsumed == 0) {
                return
            }
            val oldOverScrollY = overScrollY
            val resetOverScrollY = getResetOverScrollY(dyUnconsumed)
            if (canConsumedByOverScrollY(resetOverScrollY, oldOverScrollY, dyUnconsumed)) {
                val dy = dyUnconsumed.times(calculateOverScrollYRatio(oldOverScrollY - resetOverScrollY, false)).toInt()
                if (dy != 0) {
                    recentlyResetOverScrollY = resetOverScrollY
                    overScrollBy(getResetOverScrollX(dxUnconsumed), dy)
                    val myConsumed = overScrollY - oldOverScrollY
                    consumed[1] += myConsumed
                }
            }
        }
        if (overScrollAxes and ViewCompat.SCROLL_AXIS_HORIZONTAL != 0) {
            if (dxUnconsumed == 0) {
                return
            }
            val oldOverScrollX = overScrollX
            val resetOverScrollX = getResetOverScrollX(dxUnconsumed)
            if (canConsumedByOverScrollX(resetOverScrollX, oldOverScrollX, dxUnconsumed)) {
                val dx = dxUnconsumed.times(calculateOverScrollXRatio(oldOverScrollX - resetOverScrollX, false)).toInt()
                if (dx != 0) {
                    recentlyResetOverScrollX = resetOverScrollX
                    overScrollBy(dx, getResetOverScrollY(dyUnconsumed))
                    val myConsumed = overScrollX - oldOverScrollX
                    consumed[0] += myConsumed
                }
            }
        }
    }

    override fun onNestedPreScroll(target: View, dx: Int, dy: Int, consumed: IntArray) {
        onNestedPreScroll(target, dx, dy, consumed, ViewCompat.TYPE_TOUCH)
    }

    override fun onNestedPreScroll(target: View, dxUnconsumed: Int, dyUnconsumed: Int, consumed: IntArray, type: Int) {
        if (type != ViewCompat.TYPE_TOUCH || !overScrolledFlag) {
            return
        }
        if (overScrollAxes and ViewCompat.SCROLL_AXIS_VERTICAL != 0) {
            if (dyUnconsumed == 0) {
                return
            }
            val oldOverScrollY = overScrollY
            val resetOverScrollY = getResetOverScrollY(dyUnconsumed)
            if (canConsumedByBackOverScrollY(oldOverScrollY, dyUnconsumed)) {
                val dy = dyUnconsumed.times(calculateOverScrollYRatio(oldOverScrollY - resetOverScrollY, true)).toInt()
                if (dy == 0) {
                    return
                }
                val newOverScrollY = if (dy < 0) {
                    oldOverScrollY.plus(dy).coerceIn(resetOverScrollY, overScrollY)
                } else {
                    oldOverScrollY.plus(dy).coerceIn(overScrollY, resetOverScrollY)
                }
                overScrollTo(getResetOverScrollX(dxUnconsumed), newOverScrollY)
                val myConsumed = overScrollY - oldOverScrollY
                consumed[1] += myConsumed
            }
        }
        if (overScrollAxes and ViewCompat.SCROLL_AXIS_HORIZONTAL != 0) {
            if (dxUnconsumed == 0) {
                return
            }
            val oldOverScrollX = overScrollX
            val resetOverScrollX = getResetOverScrollX(dxUnconsumed)
            if (canConsumedByBackOverScrollX(overScrollX, dxUnconsumed)) {
                val dx = dxUnconsumed.times(calculateOverScrollXRatio(oldOverScrollX - resetOverScrollX, true)).toInt()
                if (dx == 0) {
                    return
                }
                val newOverScrollX = if (dx < 0) {
                    overScrollX.plus(dx).coerceIn(resetOverScrollX, overScrollX)
                } else {
                    overScrollX.plus(dx).coerceIn(overScrollX, resetOverScrollX)
                }
                overScrollTo(newOverScrollX, getResetOverScrollY(dyUnconsumed))
                val myConsumed = overScrollX - oldOverScrollX
                consumed[0] += myConsumed
            }
        }
    }

    override fun onNestedPreFling(target: View, velocityX: Float, velocityY: Float): Boolean {
        if (moveState == 1) {
            moveState = 2
        }
        return false
    }

    override fun getNestedScrollAxes(): Int {
        return mParentHelper.nestedScrollAxes
    }

    override fun computeScroll() {
        if (overScroller.computeScrollOffset()) {
            overScrollTo(overScroller.currX, overScroller.currY)
            invalidate()
        }
    }

    fun finishOverScroll() {
        if (!overScroller.isFinished) {
            overScroller.abortAnimation()
            overScrollTo(overScroller.currX, overScroller.currY)
            invalidate()
        } else {
            if (recentlyResetOverScrollX != null || recentlyResetOverScrollY != null) {
                overScrollTo(recentlyResetOverScrollX ?: overScrollX, recentlyResetOverScrollY ?: overScrollY)
            }
        }
        moveState = 0
        recentlyResetOverScrollX = null
        recentlyResetOverScrollY = null
    }

    protected fun smoothOverScrollBy(dx: Int, dy: Int) {
        if (!overScroller.isFinished) {
            overScroller.forceFinished(true)
        }
        if (dx == 0 && dy == 0) {
            return
        }
        overScroller.startScroll(overScrollX, overScrollY, dx, dy, resetDuration)
        invalidate()
    }

    protected fun smoothOverScrollTo(x: Int, y: Int) {
        smoothOverScrollBy(x - overScrollX, y - overScrollY)
    }

    protected fun overScrollBy(dx: Int, dy: Int) {
        overScrollTo(overScrollX + dx, overScrollY + dy)
    }

    protected open fun overScrollTo(x: Int, y: Int) {
        scrollTo(x, y)
    }

    protected open fun getResetOverScrollX(direction: Int): Int {
        return 0
    }

    protected open fun getResetOverScrollY(direction: Int): Int {
        return 0
    }

    protected open fun calculateOverScrollXRatio(overScrollX: Int, backOver: Boolean): Float {
        return if (overScrollX != 0) overScrollRatioProvider.getScrollRatio(overScrollX, ViewCompat.SCROLL_AXIS_HORIZONTAL, backOver) else 1f
    }

    protected open fun calculateOverScrollYRatio(overScrollY: Int, backOver: Boolean): Float {
        return if (overScrollY != 0) overScrollRatioProvider.getScrollRatio(overScrollY, ViewCompat.SCROLL_AXIS_VERTICAL, backOver) else 1f
    }

    protected open fun canConsumedByOverScrollX(resetOverScrollX: Int, overScrollX: Int, dx: Int): Boolean {
        if (overScrollX <= resetOverScrollX && dx < 0) {
            return true
        }
        if (overScrollX >= resetOverScrollX && dx > 0) {
            return true
        }
        return false
    }

    protected open fun canConsumedByBackOverScrollX(overScrollX: Int, dx: Int): Boolean {
        val resetOverScrollX = getResetOverScrollX(-dx)
        if (overScrollX > resetOverScrollX && dx < 0) {
            return true
        }
        if (overScrollX < resetOverScrollX && dx > 0) {
            return true
        }
        return false
    }

    protected open fun canConsumedByOverScrollY(resetOverScrollY: Int, overScrollY: Int, dy: Int): Boolean {
        if (overScrollY <= resetOverScrollY && dy < 0) {
            return true
        }
        if (overScrollY >= resetOverScrollY && dy > 0) {
            return true
        }
        return false
    }

    protected open fun canConsumedByBackOverScrollY(overScrollY: Int, dy: Int): Boolean {
        val resetOverScrollY = getResetOverScrollY(-dy)
        if (overScrollY > resetOverScrollY && dy < 0) {
            return true
        }
        if (overScrollY < resetOverScrollY && dy > 0) {
            return true
        }
        return false
    }

    protected fun getScrollXWhenAbortOverScroll(): Int {
        val upper = upperX
        val lower = lowerX
        val oldOverScrollX = overScrollX
        return if (oldOverScrollX < upper) {
            upper
        } else if (oldOverScrollX > lower) {
            lower
        } else {
            oldOverScrollX
        }
    }

    protected fun getScrollYWhenAbortOverScroll(): Int {
        val upper = upperY
        val lower = lowerY
        val oldOverScrollY = overScrollY
        return if (oldOverScrollY < upper) {
            upper
        } else if (oldOverScrollY > lower) {
            lower
        } else {
            oldOverScrollY
        }
    }
}