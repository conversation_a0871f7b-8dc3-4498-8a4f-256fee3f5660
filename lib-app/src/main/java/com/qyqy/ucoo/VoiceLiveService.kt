package com.qyqy.ucoo

import android.Manifest
import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.content.pm.ServiceInfo
import android.content.res.Resources
import android.os.Build
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.core.app.ServiceCompat
import androidx.core.content.PermissionChecker
import com.qyqy.ucoo.home.AssistantTaskActivity
import com.qyqy.ucoo.multilingual.AppLanguage
import com.qyqy.ucoo.multilingual.Philology
import com.qyqy.ucoo.utils.LogUtils

class VoiceLiveService : Service() {

    companion object {
        private const val TAG = "VoiceLiveService"

        private val CHANNEL_ID = VoiceLiveService::class.java.name

        private const val ONGOING_NOTIFICATION_ID = 19811127

        /**
         * 0: none
         * 1: 启动中
         * 2: 已启动
         * 3: 停止中
         */
        @Volatile
        private var state = 0

        fun start(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                return
            }
            try {
                if (state == 1 || state == 2) {
                    return
                }
                state = 1
                val intent = Intent(context, VoiceLiveService::class.java)
                context.startForegroundService(intent)
            } catch (e: Exception) {
                LogUtils.e(TAG, "start --> catch_Exception = ${e.message}")
            }
        }

        fun stop(context: Context) {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
                return
            }
            try {
                if (state == 0 || state == 3) {
                    return
                }
                if (state == 1) {
                    state = 3
                } else {
                    val intent = Intent(context, VoiceLiveService::class.java)
                    context.stopService(intent)
                }
            } catch (e: Exception) {
                LogUtils.e(TAG, "stop --> catch_Exception = ${e.message}")
            }
        }
    }

    private var isStartedForeground = false

    private lateinit var philologyContext: Context

    override fun attachBaseContext(newBase: Context) {
        super.attachBaseContext(AppLanguage.attach(newBase).also {
            philologyContext = Philology.wrap(it)
        })
    }

    override fun getResources(): Resources {
        return if (::philologyContext.isInitialized) {
            philologyContext.resources ?: super.getResources()
        } else {
            super.getResources()
        }
    }

    override fun onCreate() {
        super.onCreate()
        LogUtils.d(TAG, "onCreate --> ")
        try {
            if (!isStartedForeground) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // 声明服务类型，这在 Android 10 (API 29) 及以上是必需的
                    val cameraPermission = PermissionChecker.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                    val foregroundServiceType =
                        if (cameraPermission != PermissionChecker.PERMISSION_GRANTED) {
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
                        } else {
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE or ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
                        }
                    ServiceCompat.startForeground(
                        this,
                        ONGOING_NOTIFICATION_ID,
                        getNotification(this),
                        foregroundServiceType,
                    )
                } else {
                    startForeground(ONGOING_NOTIFICATION_ID, getNotification(this)) // ID 不能为 0
                }
                isStartedForeground = true
            }
            if (state == 3) {
                stopSelf()
            } else {
                state = 2
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "startForeground --> catch_Exception = ${e.message}")
        }
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        LogUtils.d(TAG, "onStartCommand --> ")
        try {
            if (!isStartedForeground) {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                    // 声明服务类型，这在 Android 10 (API 29) 及以上是必需的
                    val cameraPermission = PermissionChecker.checkSelfPermission(this, Manifest.permission.RECORD_AUDIO)
                    val foregroundServiceType =
                        if (cameraPermission != PermissionChecker.PERMISSION_GRANTED) {
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
                        } else {
                            ServiceInfo.FOREGROUND_SERVICE_TYPE_MICROPHONE or ServiceInfo.FOREGROUND_SERVICE_TYPE_MEDIA_PLAYBACK
                        }
                    ServiceCompat.startForeground(
                        this,
                        ONGOING_NOTIFICATION_ID,
                        getNotification(this),
                        foregroundServiceType,
                    )
                } else {
                    startForeground(ONGOING_NOTIFICATION_ID, getNotification(this)) // ID 不能为 0
                }
                isStartedForeground = true
            }
            if (state == 3) {
                stopSelf()
            } else {
                state = 2
            }
        } catch (e: Exception) {
            LogUtils.e(TAG, "startForeground --> catch_Exception = ${e.message}")
        }
        return super.onStartCommand(intent, flags, startId)
    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onDestroy() {
        super.onDestroy()
        LogUtils.d(TAG, "onDestroy --> ")
        state = 0
    }

    private fun getNotification(context: Context): Notification {
        val isOngoing = true //是否持续(为不消失的常驻通知)
        val category = Notification.CATEGORY_SERVICE
        val notificationManager = context.getSystemService(NOTIFICATION_SERVICE) as NotificationManager
        val intent = Intent(context, AssistantTaskActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(context, 0, intent, PendingIntent.FLAG_IMMUTABLE)

        val builder: NotificationCompat.Builder = NotificationCompat.Builder(context, CHANNEL_ID)
            .setContentIntent(pendingIntent) //设置PendingIntent
            .setSmallIcon(
                if (AppUserPartition.isUCOO) {
                    R.mipmap.ic_launcher
                } else {
                    R.mipmap.ic_cupid_launcher
                }
            ) //设置状态栏内的小图标
            .setContentTitle(
                if (AppUserPartition.isUCOO) {
                    getString(R.string.ucoo_chat_friends)
                } else {
                    getString(R.string.cpd_ucoo_chat_friends)
                }
            ) //设置标题
            .setContentText(
                if (AppUserPartition.isUCOO) {
                    getString(R.string.UCOO正在后台运行_可以接收语音频道声音)
                } else {
                    getString(R.string.cpd_UCOO正在后台运行_可以接收语音频道声音)
                }
            ) //设置内容
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC) //设置通知公开可见
            .setOngoing(isOngoing) //设置持续(不消失的常驻通知)
            .setCategory(category) //设置类别
            .setPriority(NotificationCompat.PRIORITY_MAX) //优先级为：重要通知
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) { //安卓8.0以上系统要求通知设置Channel,否则会报错
            val notificationChannel = NotificationChannel(
                CHANNEL_ID, if (AppUserPartition.isUCOO) {
                    getString(R.string.ucoo_chat_friends)
                } else {
                    getString(R.string.cpd_ucoo_chat_friends)
                }, NotificationManager.IMPORTANCE_LOW
            )
            notificationChannel.lockscreenVisibility = NotificationCompat.VISIBILITY_PUBLIC //锁屏显示通知
            notificationManager.createNotificationChannel(notificationChannel)
            builder.setChannelId(CHANNEL_ID)
        }
        return builder.build()
    }
}