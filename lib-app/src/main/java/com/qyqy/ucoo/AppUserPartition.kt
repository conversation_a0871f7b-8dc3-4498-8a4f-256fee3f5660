package com.qyqy.ucoo

import androidx.compose.runtime.staticCompositionLocalOf
import com.hjq.language.MultiLanguages
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.ucoo.BuildConfig
import com.qyqy.ucoo.account.AccountToken
import com.qyqy.ucoo.setting.SettingsRepository
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.mapNotNull
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch


sealed interface UserPartition {

    data object UCOO : UserPartition

    data object Cupid : UserPartition

    val isUCOO: Boolean
        get() = this is UCOO

    val isCupid: Boolean
        get() = this is Cupid

}

private const val NATIVE_REGION = "native_region"

private const val GUESS_NATIVE_REGION = "guess_native_region"

val LocalUserPartition = staticCompositionLocalOf<UserPartition> { UserPartition.UCOO }

enum class AppType(val applicationID: String) {
    UCOO("com.qyqy.ucoo"),
    LITE("com.ucoofun.liteandroid"),
    CUPID("com.ucoofun.jpandroid")
}

object AppUserPartition {

    private var _UserPartition: UserPartition? = null

    val current: UserPartition
        get() = when (BuildConfig.APPLICATION_ID) {
            AppType.LITE.applicationID -> UserPartition.UCOO
            AppType.CUPID.applicationID -> UserPartition.Cupid
            else -> {
                app.accountManager.accountTokenWithTemp?.getUserPartition() ?: _UserPartition ?: run {
                    var value = sAppKV.getInt(NATIVE_REGION, -1)
                    if (value >= 0) {
                        return value.valueToUserPartition()
                    }
                    value = SettingsRepository.loginSettingsFlow.value.getOrNull()?.guess_native_region ?: sAppKV.getInt(GUESS_NATIVE_REGION, -1)
                    if (value >= 0) {
                        return value.valueToUserPartition()
                    }
                    val lang = if(isPreviewOnCompose){
                        "zh-CN"
                    }else{
                        MultiLanguages.getAppLanguage().toLanguageTag().orEmpty()
                    }
                    if (lang.contains("jp", ignoreCase = true)) {
                        UserPartition.Cupid
                    } else {
                        UserPartition.UCOO
                    }
                }.also {
                    _UserPartition = it
                }
            }
        }

    val isCupid: Boolean
        get() = current.isCupid

    val isUCOO: Boolean
        get() = current.isUCOO

    fun init() {
        if (BuildConfig.APPLICATION_ID != AppType.UCOO.applicationID) {
            return
        }
        appCoroutineScope.launch {
            SettingsRepository.fetchLoginSettings()
        }

        sAccountTokenFlow.filterNotNull().distinctUntilChangedBy {
            it.userId
        }.onEach {
            sAppKV.putInt(NATIVE_REGION, it.native_region)
            _UserPartition = null // 重置
        }.launchIn(appCoroutineScope)

        SettingsRepository.loginSettingsFlow.mapNotNull {
            it.getOrNull()
        }.distinctUntilChangedBy {
            it.guess_native_region
        }.onEach {
            sAppKV.putInt(GUESS_NATIVE_REGION, it.guess_native_region)
        }.launchIn(appCoroutineScope)
    }
}

fun AccountToken.getUserPartition(): UserPartition {
    return when (BuildConfig.APPLICATION_ID) {
        AppType.LITE.applicationID -> UserPartition.UCOO
        AppType.CUPID.applicationID -> UserPartition.Cupid
        else -> {
            native_region.valueToUserPartition()
        }
    }
}

private fun Int.valueToUserPartition(): UserPartition = when (BuildConfig.APPLICATION_ID) {
    AppType.LITE.applicationID -> UserPartition.UCOO
    AppType.CUPID.applicationID -> UserPartition.Cupid
    else -> {
        if (this == 1) {
            UserPartition.Cupid
        } else {
            UserPartition.UCOO
        }
    }
}