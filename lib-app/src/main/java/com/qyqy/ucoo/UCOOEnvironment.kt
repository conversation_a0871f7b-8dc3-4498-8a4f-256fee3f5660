package com.qyqy.ucoo

import android.util.Log
import com.qyqy.ucoo.utils.LogUtils

object UCOOEnvironment {

    /**
     * 这个host和fastHost对应线上环境的api和fastapi
     * 据说fastapi访问会更快,但是可能支持会没有那么多, 所有固定几个地方才使用fast
     * 当然在线下都没有这个问题啦
     */
//    val host: String
//        get() = BuildConfig.API_HOST
//
//    val fastHost: String
//        get() = BuildConfig.FAST_API_HOST


    /**
     * 初始化环境, 一定要越早越好
     * 因为用到了FastKV, 所以在app初始化时最好将FastKV最先初始化
     */
    fun init() {
        val envName = if (BuildConfig.BUILD_TYPE.equals("release", true)) {
            BuildConfig.NAME
        } else {
            try {
                sAppKV.getString("app_runtime_env_str", BuildConfig.NAME)
            } catch (e: Exception) {
                BuildConfig.NAME
            }
        }
        switchEnvironment(envName)
    }

    fun currentEnv() = BuildConfig.environment

    fun switchEnvironment(envName: String) {
        if (BuildConfigImpl.switchEnv(envName)) {
            sAppKV.putString("app_runtime_env_str", envName)
            LogUtils.i("UCOOEnvironment", "switch env succeed: $envName")
        } else {
            LogUtils.w("UCOOEnvironment", "switch env failed. cannot find $envName")
        }
    }

    fun getAllHosts(): Set<String> {
        val result = mutableSetOf<String>()
        UCOOEnvironmentInstance.entries.forEach {
            result.add(it.API_HOST)
            result.add(it.FAST_API_HOST)
        }
        return result
    }


    fun getDebugEnv(): UCOOEnvironmentInstance {
        return UCOOEnvironmentInstance.DEBUG
    }
}