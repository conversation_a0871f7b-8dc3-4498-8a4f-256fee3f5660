package com.qyqy.ucoo

import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.rememberUpdatedState
import androidx.compose.runtime.staticCompositionLocalOf
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.compose.LocalLifecycleOwner
import com.qyqy.ucoo.core.Analytics
import com.qyqy.ucoo.http.sAppJson
import kotlinx.serialization.encodeToString

@Retention(AnnotationRetention.SOURCE)
@Target(
    AnnotationTarget.FIELD,
    AnnotationTarget.VALUE_PARAMETER
)
annotation class DataTraceExposure

@Retention(AnnotationRetention.SOURCE)
@Target(
    AnnotationTarget.FIELD,
    AnnotationTarget.VALUE_PARAMETER
)
annotation class DataTraceClick

@Retention(AnnotationRetention.SOURCE)
@Target(
    AnnotationTarget.FIELD,
    AnnotationTarget.VALUE_PARAMETER
)
annotation class DataTraceEvent

data class DataPoint(
    val name: String,
    val type: String,
    val deviceAb: String? = null,
    val userAb: String? = null,
    val extra: String? = null,
    val useShushu: Boolean = false
) {

    companion object {
        fun exposureBody(
            @DataTraceExposure name: String,
            deviceAb: String? = null,
            userAb: String? = null,
            extra: String? = null,
            useShushu: Boolean = false
        ): DataPoint {
            return DataPoint(
                name = name,
                type = "1",
                deviceAb = deviceAb,
                userAb = userAb,
                extra = extra,
                useShushu = useShushu
            )
        }

        fun clickBody(
            @DataTraceClick name: String,
            deviceAb: String? = null,
            userAb: String? = null,
            extra: String? = null,
            useShushu: Boolean = false
        ): DataPoint {
            return DataPoint(
                name = name,
                type = "2",
                deviceAb = deviceAb,
                userAb = userAb,
                extra = extra,
                useShushu = useShushu
            )
        }

        fun eventBody(
            @DataTraceEvent name: String,
            deviceAb: String? = null,
            userAb: String? = null,
            extra: String? = null,
            useShushu: Boolean = false
        ): DataPoint {
            return DataPoint(
                name = name,
                type = "3",
                deviceAb = deviceAb,
                userAb = userAb,
                extra = extra,
                useShushu = useShushu
            )
        }
    }

    fun mapBody(): Map<String, String> {
        return buildMap {
            put("event_name", name)
            put("event_type", type)
            if (deviceAb != null) {
                put("device_ab_value", deviceAb)
            }
            if (userAb != null) {
                put("user_ab_value", userAb)
            }
            if (extra != null) {
                put("extra", extra)
            }
        }
    }

    fun report() {
        Analytics.appReportEvent(this)
    }

}

//https://d03r3qddlre.feishu.cn/docx/G0yRdT6T0oaAemxlzwwcX0KZnzd?from=from_copylink
object TracePoints {
    //region 华语区
    // 首页 - 组 cp 页面
    @DataTraceExposure
    const val HOME_MAKE_CP = "home_make_cp"

    // 首页 - 组 CP - 推荐列表 cell 点击
    @DataTraceClick
    const val HOME_MAKE_CP_RECOMMND_CELL_CLICK = "home_make_cp_recommand_cell_click"

    // 首页 - 组 CP - 女友玩法介绍 banner 点击
    @DataTraceClick
    const val HOME_MAKE_CP_GIRLFRIEND_DESC_BANNER_CLICK =
        "home_make_cp_girlfriend_desc_banner_click"

    // 首页 - 找朋友页面
    @DataTraceExposure
    const val HOME_FIND_FRIEND = "home_find_firend"

    // 广场 - 瞬间页面 -推荐
    @DataTraceExposure
    const val SQUARE_MOMENT_RECOMMEND = "square_moment_recommend"

    // 广场 - 瞬间页面 - 关注
    @DataTraceExposure
    const val SQUARE_MOMENT_FOLLOW = "square_moment_follow"

    // 广场 - 瞬间 - 推荐 cell 点击
    @DataTraceClick
    const val SQUARE_MOMENT_RECOMMEND_CELL_CLICK = "square_moment_recommend_cell_click"

    // 广场 - 瞬间 - 关注 cell 点击
    @DataTraceClick
    const val SQUARE_MOMENT_FOLLOW_CELL_CLICK = "square_moment_follow_cell_click"

    // 广场 - 群组页面
    @DataTraceExposure
    const val SQUARE_GROUP = "square_group"

    // 广场 - 群组页面 - 群组 cell 点击
    @DataTraceClick
    const val SQUARE_GROUP_CELL = "square_group_cell"

    // 广场 - 部落页面
    @DataTraceExposure
    const val SQUARE_TRIBE = "square_tribe"

    // 广场 - 部落页面 - cell 点击
    @DataTraceClick
    const val SQUARE_TRIBE_CELL = "square_tribe_cell"

    // 娱乐 - 派对交友页面
    @DataTraceExposure
    const val ENTERTAINMENT_PARTY_MAKE_FRIEND = "entertainment_party_make_firend"

    // 娱乐 - 音乐点唱页面
    @DataTraceExposure
    const val ENTERTAINMENT_PAGE = "entertainment_page"

    // 消息列表页
    @DataTraceExposure
    const val MESSAGE_LIST = "message_list"

    // 消息列表 cell 点击
    @DataTraceClick
    const val MESSAGE_LIST_CELL_CLICK = "message_list_cell_private_chat_click"

    // 消息列表 - 会员入口
    @DataTraceClick
    const val MESSAGE_LIST_MEMBER = "message_list_member_click"

    @DataTraceClick
    const val MESSAGE_LIST_GROUP_CHAT_CLICK = "message_list_group_chat_click"

    @DataTraceClick
    const val MESSAGE_LIST_TRIBE_CLICK = "message_list_tribe_click"

    // 消息列表 - 喜欢你的人入口点击
    @DataTraceClick
    const val MESSAGE_LIST_LIKE_YOU_ENTRY = "message_list_like_you_entry_click"

    //消息列表cell点击-新人专属女友匹配入口点击
    @DataTraceClick
    const val MESSAGE_LIST_NEWGIRLFRIEND_MATCH_CLICK = "message_list_newGirlfriend_match_click"

    //消息列表cell点击-新人推荐入口点击
    @DataTraceClick
    const val message_list_newUser_recommend_click = "message_list_newUser_recommend_click"

    //消息列表cell点击-优质用户推荐入口点击
    const val message_list_hqu_recommend_click = "message_list_hqu_recommend_click"

    // 消息列表 - banner 位点击
    @DataTraceClick
    const val MESSAGE_LIST_BANNER = "message_list_banner"

    // 我的页面
    @DataTraceExposure
    const val MY_PAGE = "my_page"

    // 我的页面 - 金币钱包入口
    @DataTraceClick
    const val MY_PAGE_WALLET = "my_page_wallet"

    // 我的页面 - 我的会员入口
    @DataTraceClick
    const val MY_PAGE_MEMBER_ENTRY = "my_page_member_entry"

    // 我的页面 - 我的装扮入口
    @DataTraceClick
    const val MY_PAGE_DRESS = "my_page_dressup"

    // 我的页面 - 我的等级入口
    @DataTraceClick
    const val MY_PAGE_RANGE = "my_page_range"

    // 我的页面 - 组 CP 按钮点击
    @DataTraceClick
    const val MY_PAGE_MAKE_CP = "my_page_make_cp"

    //我的页面-亲友团cell点击-邀请亲友
    @DataTraceClick
    const val MY_PAGE_RELATIVE_FRIEND_CELL_CLICK_INVITE =
        "my_page_relative_friend_cell_click_invite"

    // 我的页面 - 亲友团 cell 点击
    @DataTraceClick
    const val MY_PAGE_RELATIVE_FRIEND_CELL_CLICK_PROFILE =
        "my_page_relative_friend_cell_click_profile"

    //触发私聊拦截
    @DataTraceEvent
    const val PRIVATE_CHAT_FORBIDDEN = "private_chat_forbidden"

    //私聊消拦截-充值金币按钮点击
    @DataTraceClick
    const val PRIVATE_CHAT_FORBIDDEN_COIN_BUTTON_CLICK = "private_chat_forbidden_coin_button_click"

    //私聊拦截-开通会员按钮点击
    @DataTraceClick
    const val PRIVATE_CHAT_FORBIDDEN_JOIN_MENBER = "private_chat_forbidden_join_menber"

    //首充福利挂件点击
    @DataTraceClick
    const val FIRST_CHARGE_PENDANT = "first_charge_pendant"

    //私密小屋拦截弹窗-开会员按钮点击
    @DataTraceClick
    const val PRIVATE_ROOM_JOIN_MEMBER_BUTTON_CLICK = "private_room_join_member_button_click"

    //cp小屋入口展示
    @DataTraceExposure
    const val CP_ROOM_ENTRY_SHOW = "cp_room_entry_show"

    //cp小屋入口点击
    @DataTraceClick
    const val CP_ROOM_ENTRY_CLICK = "cp_room_entry_click"

    //CP小屋幸运宝箱展示
    @DataTraceClick
    const val CP_ROOM_LUCKY_BOX_SHOW = "cp_room_lucky_box_show"

    //CP小屋幸运宝箱点击
    @DataTraceClick
    const val CP_ROOM_LUCKY_BOX_CLICK = "cp_room_lucky_box_click"

    //CP小屋装扮中心展示
    @DataTraceExposure
    const val CP_ROOM_DECORATE_CENTER_SHOW = "cp_room_decorate_center_show"

    //CP小屋装扮中心点击
    @DataTraceClick
    const val CP_ROOM_DECORATE_CENTER_CLICK = "cp_room_decorate_center_click"

    //CP小屋回忆展示
    @DataTraceExposure
    const val CP_ROOM_MEMORY_LINE_SHOW = "cp_room_memory_line_show"

    //CP小屋回忆点击
    @DataTraceClick
    const val CP_ROOM_MEMORY_LINE_CENTER_CLICK = "cp_room_memory_line_center_click"

    //CP小屋 house 展示
    @DataTraceExposure
    const val CP_ROOM_HOUSE_SHOW = "cp_room_house_show"

    //CP小屋 house 点击
    @DataTraceClick
    const val CP_ROOM_HOUSE_CLICK = "cp_room_house_click"

    //CP小屋 house 展示
    @DataTraceExposure
    const val CP_ROOM_CAR_SHOW = "cp_room_car_show"

    //CP小屋 house 展示
    @DataTraceClick
    const val CP_ROOM_CAR_CLICK = "cp_room_car_click"

    //endregion

    //region 首页
    //首页-推荐列表页访问新男人数，占比
    @DataTraceExposure
    const val VISIT_HOME_RECOMMEND_LIST = "visit_home_recommend_list"

    //首页-推荐列表页-banner点击新男人数，占比
    @DataTraceClick
    const val CLICK_HOME_RECOMMEND_BANNER = "click_home_recommend_banner"

    //首页-语音通话页面访问新男人数，占比
    @DataTraceExposure
    const val VISIT_HOME_VOICE_CALL = "visit_home_voice_call"

    //首页-语音通话页面-推荐列表cell点击新男人数（包含点击头像），占比
    @DataTraceClick
    const val CLICK_HOME_VOICE_CALL_CELL = "click_home_voice_call_cell"

    //首页-语音通话页面-拨打电话按钮点击新男人数，占比
    @DataTraceClick
    const val CLICK_HOME_VOICE_CALL_BUTTON = "click_home_voice_call_button"

    //首页-语音通话页面-语音通话弹窗曝光新男人数，占比
    @DataTraceExposure
    const val VISIT_HOME_VOICE_CALL_DIALOG = "visit_home_voice_call_dialog"
    //endregion


    //region 2.37.0 金币页相关
    const val COIN_VISIT = "visit_coin_page"//金币页访问
    const val COIN_CHARGE_CLICK = "click_coin_charge_button"//金币页-充值按钮点击
    const val COIN_SIGN_CLICK = "click_coin_sign_button"//金币页-签到按钮点击
    const val COIN_TASK_CLICK = "click_coin_task_button"//金币页-点击任务
    const val COIN_GAME_CLICK = "click_coin_game_entry"//金币页-点击游戏入口

//    const val COIN_NEWBIE_TASK_CLICK = "click_coin_newbie_task_button"//金币页-新人任务点击
//    const val COIN_DAILY_TASK_CLICK = "click_coin_newbie_task_button"//金币页-每日任务
//    const val COIN_HIGHLY_TASK_CLICK = "click_coin_highly_task_button"//金币页-进阶任务
//    const val COIN_RACING_CLICK = "click_coin_racing_entry"//金币页-进阶任务
    //endregion

    //region 直播页
    //直播页访问新男人数，占比
    @DataTraceExposure
    const val VISIT_LIVE_PAGE = "visit_live_page"

    //直播页-语音房列表页访问新男人数，占比
    @DataTraceExposure
    const val VISIT_LIVE_AUDIO_ROOM_LIST = "visit_live_audio_room_list"

    //直播页-语音房列表页-banner点击新男人数，占比
    @DataTraceClick
    const val CLICK_AUDIO_ROOM_LIST_BANNER = "click_audio_room_list_banner"

    //直播页-语音房列表页-语音房cell点击新男人数，占比
    @DataTraceClick
    const val CLICK_AUDIO_ROOM_LIST_CELL = "click_audio_room_list_cell"

    //直播页-语音房列表页-创建房间/我的房间按钮点击新男人数，占比
    @DataTraceClick
    const val CLICK_AUDIO_ROOM_LIST_BOTTOM_BUTTON = "click_audio_room_list_bottom_button"

    //直播页-家族广场页访问新男人数，占比
    @DataTraceExposure
    const val VISIT_LIVE_FAMILY_SQUARE = "visit_live_family_square"

    //直播页-家族广场页-家族cell点击新男人数，占比
    @DataTraceClick
    const val CLICK_LIVE_FAMILY_SQUARE_CELL = "click_live_family_square_cell"

    //直播页-家族广场页-创建家族按钮点击新男人数，占比
    @DataTraceClick
    const val CLICK_LIVE_FAMILY_CREATE_BUTTON = "click_live_family_create_button"
    //endregion

    //region 消息页
    //消息页-banner点击新男人数，占比
    @DataTraceClick
    const val CLICK_MESSAGE_LIST_BANNER = "click_message_list_banner"

    //消息页-消息列表cell点击新男人数，占比
    @DataTraceClick
    const val MESSAGE_LIST_PRIVATE_CHAT_CLICK = "message_list_private_chat_click"

    //消息页-家族入口点击新男人数，占比
    @DataTraceClick
    const val CLICK_MESSAGE_LIST_FAMILY_ENTRY = "click_message_list_family_entry"

    //消息页-访客记录入口点击新男人数，占比
    @DataTraceClick
    const val CLICK_MESSAGE_LIST_VISITOR_ENTRY = "click_message_list_visitor_entry"

    //消息页-亲密度页访问新男人数，占比
    @DataTraceExposure
    const val VISIT_INTIMACY_PAGE = "visit_intimacy_page"

    //消息页-亲密度页-消息列表cell点击新男人数，占比
    @DataTraceClick
    const val CLICK_INTIMACY_CELL = "click_intimacy_cell"
    //endregion

    //region 我的
    //我的页访问新男人数，占比
    @DataTraceExposure
    const val JP_MY_PAGE = "my_page"

    //我的页-金币钱包入口点击新男人数，占比
    @DataTraceClick
    const val JP_MY_PAGE_WALLET = "my_page_wallet"

    //我的页-会员入口点击新男人数，占比
    @DataTraceClick
    const val JP_MY_PAGE_MEMBER_ENTRY = "my_page_member_entry"

    //我的页-我的等级入口点击新男人数，占比
    @DataTraceClick
    const val JP_MY_PAGE_RANGE = "my_page_range"

    //我的页-守护入口点击新男人数，占比
    @DataTraceClick
    const val CLICK_MY_PAGE_GUARD = "click_my_page_guard"

    //我的页-我的装扮入口点击新男人数，占比
    @DataTraceClick
    const val MY_PAGE_DRESSUP = "my_page_dressup"

    //我的页-装扮商城入口点击新男人数，占比
    @DataTraceClick
    const val CLICK_MY_PAGE_DRESS_MALL = "click_my_page_dress_mall"

    //我的页-访客记录入口点击新男人数，占比
    @DataTraceClick
    const val CLICK_MY_PAGE_VISITORS = "click_my_page_visitors"

    //我的页-客服入口点击新男人数，占比
    @DataTraceClick
    const val CLICK_MY_PAGE_SUPPORT = "click_my_page_support"

    //我的页-组CP按钮点击新男人数，占比
    @DataTraceClick
    const val JP_MY_PAGE_MAKE_CP = "my_page_make_cp"
    //endregion

    //region 2.40.0

    //首充挂件显示
    const val ZH_FIRST_RECHARGE_PENDANT_SHOW = "newbie_package_pendant_show"

    //首充挂件点击
    const val ZH_FIRST_RECHARGE_PENDANT_CLICK = "newbie_package_pendant_click"

    //首充点击购买
    const val ZH_FIRST_RECHARGE_POPUP_BUY_BTN_CLICK = "newbie_package_popup_buy_btn_click"

    const val JP_NB_PKG_202502_POPUP_SHOW = "jp_nb_pkg_202502_popup_show"
    const val JP_NB_PKG_202502_BUY_BTN_CLICK = "jp_nb_pkg_202502_buy_btn_click"

    //endregion

    //region 2.42.0

    //region 新版礼物墙埋点
    //礼物墙入口点击
    const val GIFT_WALL_ENTRY_CLICK = "gift_wall_entry_click"

    //礼物墙tab展示
    const val GIFT_WALL_TAB_SHOW = "gift_wall_tab_show"

    //礼物墙赠送礼物弹窗
    const val GIFT_WALL_GIFT_POPUP_FOR_GIVE = "gift_wall_gift_popup_for_give"

    //礼物墙赠送礼物成功
    const val GIFT_WALL_GIFT_GIVE_SUCCESS = "gift_wall_gift_give_success"

    //求打赏礼物弹窗
    const val GIFT_WALL_GIFT_POPUP_FOR_BEG = "gift_wall_gift_popup_for_beg"

    //求打赏消息打开
    const val BEG_GIFT_MESSAGE_OPEN = "beg_gift_message_open"

    //endregion


    //endregion
}

object TraceConst {
    const val NONE = -1

    //我的
    const val MINE_PAGE = 100

    //用户个人主页
    const val USER_PROFILE = 101
}

val LocalTracePage = staticCompositionLocalOf { TraceConst.NONE }

sealed interface DataTrace {

    sealed interface Exposure : DataTrace {

        object Login : Exposure {

            /**
             * 登录起始页面
             */
            @DataTraceExposure
            const val OVERVIEW = "exposure_login_overview"

            /**
             * 手机号输入页
             */
            @DataTraceExposure
            const val PHONE_INPUT = "exposure_login_input_phone"

            /**
             * 验证码
             */
            @DataTraceExposure
            const val PHONE_VERIFY = "exposure_login_verify_phone"

            /**
             * 昵称填写
             */
            @DataTraceExposure
            const val EDIT_NAME = "exposure_login_edit_name"

            /**
             * 生活状态填写
             */
            @DataTraceExposure
            const val EDIT_STATUS = "exposure_login_edit_status"

            /**
             * 上传头像
             */
            @DataTraceExposure
            const val EDIT_AVATAR = "exposure_login_edit_avatar"

            /**
             * 填写引力签
             */
            @DataTraceExposure
            const val EDIT_ATTRACTION = "show_attractive_tag"
        }

        data object Recharge : Exposure {
//            1、“私聊消息拦截-充值页面”展示：jp_charge_page_from_chat_show
//            2、“私聊消息拦截-充值页面”充值按钮点击：jp_charge_page_from_chat_action_btn_click
//            3、“其他入口-充值页面”展示：jp_charge_page_from_other_show
//            4、“其他入口-充值页面”充值按钮点击：jp_charge_page_from_other_action_btn_click

            @DataTraceExposure
            const val 私聊消息拦截_充值页面 = "jp_charge_page_from_chat_show"

            @DataTraceExposure
            const val 其他入口_充值页面 = "jp_charge_page_from_other_show"
        }

        data object Message : Exposure {

            @DataTraceExposure
            const val 消息列表页访问 = "jp_message_list_page_show"

            @DataTraceExposure
            const val 私聊页面访问 = "jp_private_chat_page_show"
        }

        data object SubHome : Exposure {

            @DataTraceExposure
            const val 首页访问 = "jp_home_page_show"

        }

        data object QuickReply : Exposure {
            //快捷回复曝光
            @DataTraceExposure
            const val VISIT_QUICK_REPLY = "fast_reply_message_content"

            @DataTraceExposure
            const val VISIT_QUICK_REPLY_JP = "fast_reply_message_content_japan"
        }

    }

    sealed interface Click : DataTrace {

        object Login : Click {

            /**
             * 登录起始页面-快捷登录点击
             */
            @DataTraceClick
            const val QUICK = "click_login_quick"

            /**
             * 登录起始页面-手机号登录点击
             */
            @DataTraceClick
            const val PHONE = "click_login_phone"

            /**
             * 登录起始页面-谷歌登录点击
             */
            @DataTraceClick
            const val GOOGLE = "click_login_google"

            /**
             * 登录起始页面-谷歌登录点击
             */
            @DataTraceClick
            const val LINE = "click_login_google"

            /**
             * 登录起始页面-Facebook登录点击
             */
            @DataTraceClick
            const val FACEBOOK = "click_login_facebook"

            /**
             * 手机号输入页-下一步点击
             */
            @DataTraceClick
            const val PHONE_INPUT_NEXT = "click_login_input_phone_next"

            /**
             * 验证码-下一步点击
             */
            @DataTraceClick
            const val PHONE_VERIFY_NEXT = "click_login_verify_phone_next"

            /**
             * 昵称填写-下一步点击
             */
            @DataTraceClick
            const val EDIT_NAME_NEXT = "click_login_edit_name_next"

            /**
             * 生活状态填写-下一步点击
             */
            @DataTraceClick
            const val EDIT_STATUS_NEXT = "click_login_edit_status_next"

            /**
             * 添加头像
             */
            @DataTraceClick
            const val EDIT_AVATAR = "click_login_edit_avatar"

            /**
             * 添加引力签
             */
            @DataTraceClick
            const val EDIT_ATTRACTION = "click_login_edit_attraction"

            /**
             * 进入App点击
             */
            @DataTraceClick
            const val ENTER_APP = "click_login_enter_app"
        }


        data object Recharge : Click {

            @DataTraceClick
            const val 私聊消息拦截_充值页面_充值按钮点击 =
                "jp_charge_page_from_chat_action_btn_click"

            @DataTraceClick
            const val 其他入口_充值页面_充值按钮点击 = "jp_charge_page_from_other_action_btn_click"
        }

        data object Message : Click {

            @DataTraceClick
            const val 消息列表CELL点击 = "jp_message_list_item_click"

        }

        data object SubHome : Click {

            @DataTraceClick
            const val 首页CELL点击 = "jp_home_cell_click"

            @DataTraceClick
            const val 首页SAY_HI点击 = "jp_say_hi_click"
        }

        data object QuickReply : Click {
            @DataTraceClick
            const val QUICK_REPLY_CLICK = "fast_reply_message_item"

            @DataTraceClick
            const val QUICK_REPLY_CLICK_JP = "fast_reply_message_item_japan"
        }
    }

    sealed interface Event : DataTrace {
        object Login : Event {

            /**
             * 登录失败原因
             */
            @DataTraceEvent
            const val Error = "report_error_info"
        }
    }

}

@Composable
inline fun ReportExposureCompose(
    @DataTraceExposure exposureName: String,
    reExposureMode: Boolean = true,
    useShushu: Boolean = false,
    noinline onExposureEnd: () -> Unit = {},
    noinline onDispose: () -> Unit = {},
    content: @Composable () -> Unit = {},
) {
    ReportExposureCompose(
        reExposureMode = reExposureMode,
        onExposureStart = {
            Analytics.appReportEvent(DataPoint.exposureBody(exposureName, useShushu = useShushu))
        },
        onExposureEnd = onExposureEnd,
        onDispose = onDispose,
        content = content
    )
}


/**
 * @param reExposureMode 重复曝光模式
 */
@Composable
inline fun ReportExposureCompose(
    reExposureMode: Boolean = true,
    noinline onExposureStart: () -> Unit,
    noinline onExposureEnd: () -> Unit = {},
    noinline onDispose: () -> Unit = {},
    content: @Composable () -> Unit = {},
) {

    val currentOnExposureStart by rememberUpdatedState(onExposureStart)

    val currentOnExposureEnd by rememberUpdatedState(onExposureEnd)

    val currentOnDispose by rememberUpdatedState(onDispose)

    val lifecycleOwner = LocalLifecycleOwner.current
    DisposableEffect(lifecycleOwner) {
        val observer = LifecycleEventObserver { _, event ->
            if (event == Lifecycle.Event.ON_RESUME) {
                //曝光
                currentOnExposureStart()
            } else if (event == Lifecycle.Event.ON_PAUSE) {
                //反曝光
                currentOnExposureEnd()
            }
        }
        if (reExposureMode) {
            lifecycleOwner.lifecycle.addObserver(observer)
        } else {
            currentOnExposureStart()
        }

        onDispose {
            //反曝光
            currentOnDispose()
            if (reExposureMode) {
                lifecycleOwner.lifecycle.removeObserver(observer)
            } else {
                currentOnExposureEnd()
            }
        }
    }
    content()
}