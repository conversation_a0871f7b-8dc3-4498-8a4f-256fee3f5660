package com.qyqy.ucoo.account

import android.graphics.Rect
import android.os.Parcelable
import android.text.SpannableStringBuilder
import android.widget.ImageView
import android.widget.TextView
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.core.content.ContextCompat
import androidx.core.graphics.times
import androidx.core.graphics.toColorInt
import androidx.core.text.color
import androidx.core.text.inSpans
import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.Ignore
import androidx.room.PrimaryKey
import com.overseas.common.utils.isPreviewOnCompose
import com.qyqy.cupid.data.NativeProfile
import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.mine.MediaInfo
import com.qyqy.ucoo.mine.url
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.setting.SettingsRepository
import com.qyqy.ucoo.tribe.bean.Tribe
import com.qyqy.ucoo.utils.LinearGradientFontSpan2
import com.qyqy.ucoo.widget.IAdapterItemId
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import kotlinx.serialization.Transient

interface BasicUser {
    val id: String
    val nickname: String
    val gender: Int
    val birthday: String
    val age: Int
    val height: Int
    val avatarUrl: String
}

interface User : BasicUser, Parcelable {
    val fansCount: Int
    val followCount: Int
    val onlineStatus: Int
    val publicId: String
    val region: String
    val regionLabel: String
    val regionReason: Int
    val regionReasonLabel: String
    val shortIntro: String
    val attractiveFlags: List<AttractiveFlag>
    val followed: Boolean
    val blacked: Boolean
    val isVip: Boolean
    val needFriendRelation: Boolean
    val isFriend: Boolean
    val isCp: Boolean
    val cp: AppUser?
    val publicCP: AppUser?
    val cpCardBackground: String?
    val cpExtraInfo: CpExtraInfo?
    val bestMatchUser: AppUser?
    val albumList: List<Album>
    var momentImages: List<Album>
    val isCoinTrade: Boolean//是否是币商
    val balance: Int
    val diamond: Int
    val cash: String
    val silverBalance: Int
    val tribe: Tribe?
    val memberId: String?
    val avatarFrame: String?
    val userTag: String?
    val entryEffect: String?
    val medal: Medal?
    val medalList: List<Medal>
    val subScriptMsg: String?
    val isVideoHqu: Boolean
    val videoShow: MediaInfo?
    val videoGuideHint: String?
    val videoGuideSubHint: String?
    val videoHiScript: String?
    val canFreeVideoChat: Boolean
    val level: Int
    val videoAtiHint: String?
    val videoAtiLabel: String?
    val videoAtiOpen: Boolean
    val taskIcon: String?
    val activities: List<T>?
    val bubble: ChatBubble?
    val privateRoomPendentHint: String?
    val withdrawableValue: String?
    val withdrawableUnit: String?
    val hquType: Int // 0 非主播、1 普通主播、2 台湾素人主播
    val cityName: String
    val isAnonymous: Boolean
    val countryFlag: String
    val career: String
    val locationLabel: String
    val friendCnt: Int
    val nativeRegion: Int
    val nativeProfile: NativeProfile?
    val intimateScore: Int
    val giftWallStats: GiftWallBean
    val characteristicMedalList: List<Medal>
    val entranceBanner: String?
    val background: String
    val cpRoomInfo: CpHouseBasicInfo?
    val ringInfo: WeddingRing?
    val cpCardRingInfo: CpCardEffectInfo?
}

val User.charmLevel get() = (this as? AppUser)?.expLevelInfo?.charmLevel ?: 1

val User.wealthLevel get() = (this as? AppUser)?.expLevelInfo?.wealthLevel ?: 1

val User.videoShowUrl: String
    get() = videoShow?.url.orEmpty()
val User.displayTags: String
    get() = when (sUser.isHighQuality) {
        true -> listOf(regionLabel, regionReasonLabel)
        false -> buildList {
            if (height == 0) {
                val tags = attractiveFlags.take(3).map { it.label }
                addAll(tags)
            } else {
                add("${height}cm")
                addAll(attractiveFlags.take(2).map { it.label })
            }
        }
    }.filter { it.isNotEmpty() }.joinToString("丨")

val User.tags: String
    get() = buildList {
        if (height == 0) {
            val tags = attractiveFlags.take(3).map { it.label }
            addAll(tags)
        } else {
            add("${height}cm")
            addAll(attractiveFlags.take(2).map { it.label })
        }
    }.filter { it.isNotEmpty() }.joinToString("丨")

/**
 * 是否是支持服务的客服
 */
val User.isSupportStaff
    get() = run {
        val staffIds = SettingsRepository.sSettingsFlow.value.getOrNull()?.publicServiceUids ?: emptyList()
        return@run staffIds.contains(this.id.toIntOrNull() ?: -1)
    }

/**
 * 是否是客服君
 */
val User.isDefaultStaff: Boolean
    get() = run {
        val staffId = SettingsRepository.sSettingsFlow.value.getOrNull()?.defaultCsId ?: -1
        return@run id == staffId.toString()
    }

val User.isRegionVisible: Boolean
    get() = regionLabel.isNotEmpty()

val User.regionFormatted: String
    get() = regionLabel

val User.signatureContent: String
    get() = shortIntro.takeIf {
        it.isNotBlank()
    } ?: app.getString(if (isBoy) R.string.他很懒什么也没留下 else R.string.她很懒什么也没留下)

val User.signatureContentByCompose: String
    @Composable
    get() = shortIntro.takeIf {
        it.isNotBlank()
    } ?: with(LocalContext.current) {
        getString(if (isBoy) R.string.他很懒什么也没留下 else R.string.她很懒什么也没留下)
    }

val User.profile: String
    get() = if (isPreviewOnCompose) {
        "他很懒什么也没留下"
    } else if (shortIntro.isNotEmpty()) {
        app.getString(R.string.format_person_sign, shortIntro)
    } else {
        app.getString(if (isBoy) R.string.他很懒什么也没留下 else R.string.她很懒什么也没留下)
    }
val BasicUser.userId: Int
    get() = id.toIntOrNull() ?: 0

val User.isOfficialUser: Boolean
    get() = SettingsRepository.isOfficialId(userId)

val BasicUser.isBoy: Boolean
    get() = gender != 2

val BasicUser.isOppositeSex: Boolean
    get() = gender != sUser.gender

val User.cpUrl: String?
    get() = cpExtraInfo?.levelInfo?.smallImgUrl

context (SpannableStringBuilder)
fun User.appendName() {
    val user = this
    val colorList = user.colorList
    if (colorList != null) {
        inSpans(LinearGradientFontSpan2(user.nickname, colorList)) {
            append(user.nickname)
        }
    } else {
        if (user.isBoy) {
            color("#FF50FFF3".toColorInt()) {
                append(user.nickname)
            }
        } else {
            color("#FFFFA5A5".toColorInt()) {
                append(user.nickname)
            }
        }
    }
}


const val ONLINE_STATUS_ONLINE = 0//在线

//const val ONLINE_STATUS_OFFLINE = 1//离线
const val ONLINE_STATUS_LOGOUT = 2//登出
const val ONLINE_STATUS_INVISIBLE = 1//隐身

//在线
val User.isOnline: Boolean
    get() = onlineStatus == ONLINE_STATUS_ONLINE

//隐身
val User.isInvisible: Boolean
    get() = onlineStatus == ONLINE_STATUS_INVISIBLE

val User.isHighQuality: Boolean
    get() = (this as? AppUser)?.isHighQuality == true

val User.hidePrivateRoomPendant: Boolean
    get() = (this as? AppUser)?.hidePrivateRoomPendant != false

val User.privateRoomType: Int
    get() = (this as? AppUser)?.privateRoomType ?: 0

val User.privateRoomId: Int?
    get() = (this as? AppUser)?.privateRoomId

val User.showTaskPendant: Boolean
    get() = (this as? AppUser)?.showTaskPendant == true

val User.taskUrl: String?
    get() = (this as? AppUser)?.taskUrl

val User.hideCpBillboard: Boolean
    get() = (this as? AppUser)?.hideCpBillboard != false

val User.cpBillboardUrl: String?
    get() = (this as? AppUser)?.cpBillboardUrl

@Composable
fun User.getNameColor(): Color {
    val context = LocalContext.current
    return Color(
        ContextCompat.getColor(
            context, when (isBoy) {
                true -> R.color.color_tribe_male
                false -> R.color.color_tribe_female
            }
        )
    )
}

val User.nameColor: Int
    get() = ContextCompat.getColor(
        app, when (isBoy) {
            true -> R.color.color_tribe_male

            false -> R.color.color_tribe_female
        }
    )
val User?.orInvalid: User
    get() = this ?: INVALID_USER

val User.shortNickName: String
    get() = if (nickname.length > 4) "${nickname.substring(0, 4)}..." else nickname

val Int.isMale: Boolean
    get() = this != 2

val Int.isFemale: Boolean
    get() = isMale.not()

val BasicUser.isSelf: Boolean
    get() = id == sUser.id

val User.hasLabel: Boolean
    get() = publicCP != null || medal != null || isVip || medalList.isNotEmpty()

val User.needAddFriends: Boolean
    get() = needFriendRelation && !isFriend && !isSelf

val User.currentRoomId: Int?
    get() {
        val target = this as? AppUser
        return target?.roomId?.takeIf { target.isInAudioRoom ?: false }
    }
val User.curRoomIdWithNoCondition: Int?
    get() {
        val target = this as? AppUser
        val roomId = target?.roomId ?: (target?.room?.roomId)
        return roomId
    }

val User.isFirstClassUser: Boolean
    get() = (this as AppUser).isFirstClassUser

val User.colorList: List<String>?
    get() = (this as AppUser)._colorList?.takeIf { it.size == 2 }


interface MutableUser : User {
    override var nickname: String
    override var gender: Int
    override var birthday: String
    override var age: Int
    override var height: Int
    override var avatarUrl: String
    override var fansCount: Int
    override var followCount: Int
    override var onlineStatus: Int
    override var publicId: String
    override var region: String
    override var regionLabel: String
    override var regionReason: Int
    override var regionReasonLabel: String
    override var shortIntro: String
    override var attractiveFlags: List<AttractiveFlag>
    override var followed: Boolean
    override var blacked: Boolean
    override var isVip: Boolean
    override var needFriendRelation: Boolean
    override var isFriend: Boolean
    override var isCp: Boolean
    override var publicCP: AppUser?
    override var cp: AppUser?
    override var bestMatchUser: AppUser?
    override var albumList: List<Album>
    override var momentImages: List<Album>
    override var balance: Int
    override var diamond: Int
    override var cash: String
    override var silverBalance: Int
    override var tribe: Tribe?
    override var memberId: String?
    override var avatarFrame: String?
    override var userTag: String?
    override var entryEffect: String?
    override var medal: Medal?
    override var medalList: List<Medal>
    override var subScriptMsg: String?
    override var bubble: ChatBubble?
    override var isAnonymous: Boolean
    override var countryFlag: String
    override var nativeProfile: NativeProfile?
    override var characteristicMedalList: List<Medal>
    override var cpRoomInfo: CpHouseBasicInfo?
    override val ringInfo: WeddingRing?
}

@Serializable
@Parcelize
data class T(
    val t: Int, // 1=圣诞节点亮圣诞树
    val icon: String,
    @SerialName("h5_link") val h5Link: String,
) : Parcelable

@Entity(tableName = "users")
@Serializable
@Parcelize
data class AppUser constructor(
    @PrimaryKey @SerialName("userid") override var id: String = "",
    @ColumnInfo(name = "nick_name") override var nickname: String = "",
    @ColumnInfo(name = "gender") override var gender: Int = 0,
    @ColumnInfo(name = "birthday") override var birthday: String = "",
    @ColumnInfo(name = "age") override var age: Int = 0,
    @Ignore override var height: Int = 0,
    @ColumnInfo(name = "avatar_url") @SerialName("avatar_url") override var avatarUrl: String = "",
    @ColumnInfo(name = "fans_cnt") @SerialName("fans_cnt") override var fansCount: Int = 0,
    @ColumnInfo(name = "follow_cnt") @SerialName("follow_cnt") override var followCount: Int = 0,
    @Ignore @SerialName("online_status") override var onlineStatus: Int = 1,
    @ColumnInfo(name = "public_id") @SerialName("public_id") override var publicId: String = "",
    @ColumnInfo(name = "region") override var region: String = "",
    @ColumnInfo(name = "region_label") @SerialName("region_label") override var regionLabel: String = "",
    @ColumnInfo(name = "region_reason") @SerialName("region_reason") override var regionReason: Int = 0,
    @ColumnInfo(name = "region_reason_label") @SerialName("region_reason_label") override var regionReasonLabel: String = "",
    @ColumnInfo(name = "short_intro") @SerialName("short_intro") override var shortIntro: String = "",
    @Ignore @SerialName("attractive_tags") override var attractiveFlags: List<AttractiveFlag> = emptyList(),
    @ColumnInfo(name = "followed") @SerialName("i_have_followed") override var followed: Boolean = false,
    @ColumnInfo(
        name = "isVip", defaultValue = "false"
    ) @SerialName("is_member") override var isVip: Boolean = false,
    @ColumnInfo(
        name = "blacked", defaultValue = "false"
    ) @SerialName("i_have_blacked") override var blacked: Boolean = false,
    @ColumnInfo(
        name = "needFriendRelation",
        defaultValue = "true"
    ) @SerialName("need_friend_relation") override var needFriendRelation: Boolean = true,
    @ColumnInfo(
        name = "isFriend", defaultValue = "false"
    ) @SerialName("is_friend") override var isFriend: Boolean = false,
    @ColumnInfo(
        name = "isCp", defaultValue = "false"
    ) @SerialName("is_cp") override var isCp: Boolean = false,
    @Ignore @SerialName("public_cp") override var publicCP: AppUser? = null,
    @Ignore @SerialName("cp_card_background") override val cpCardBackground: String? = null,
    @Ignore @SerialName("cp_extra_info") override val cpExtraInfo: CpExtraInfo? = null,
    @Ignore @SerialName("cp") override var cp: AppUser? = null,
    @Ignore @SerialName("best_match_user") override var bestMatchUser: AppUser? = null,
    @Ignore @SerialName("album") override var albumList: List<Album> = emptyList(),
    @Ignore @SerialName("moment_images") override var momentImages: List<Album> = emptyList(),
    @Ignore @SerialName("balance") override var balance: Int = 0,
    @Ignore @SerialName("diamond") override var diamond: Int = 0,
    @Ignore @SerialName("cash") override var cash: String = "0",
    @Ignore @SerialName("silver_balance") override var silverBalance: Int = 0,
    @Ignore override var tribe: Tribe? = null,
    @Ignore @SerialName("member_id") override var memberId: String? = null,
    @ColumnInfo(
        name = "avatarFrame", defaultValue = ""
    ) @SerialName("avatar_frame") override var avatarFrame: String? = null,
    @ColumnInfo(
        name = "userTag", defaultValue = ""
    ) @SerialName("virtual_lover_tag") override var userTag: String? = null,
    @Ignore @SerialName("special_effect_file") override var entryEffect: String? = null,
    @Ignore @SerialName("medal") override var medal: Medal? = null,
    @Ignore @SerialName("medal_list") override var medalList: List<Medal> = emptyList(),
    @Ignore @SerialName("characteristic_medal_list") override var characteristicMedalList: List<Medal> = emptyList(),
    @Ignore @SerialName("is_high_quality") val isHighQuality: Boolean? = null,//优质用户
    @Ignore @SerialName("show_task") val showTaskPendant: Boolean? = null,// 是否显示任务挂件
    @Ignore @SerialName("task_page_url") val taskUrl: String? = null,// 任务h5
    @Ignore @SerialName("hide_private_room_pendant") val hidePrivateRoomPendant: Boolean? = null,
    @Ignore @SerialName("private_room_pendant_t") val privateRoomType: Int = 0, // 私密小屋挂件类型（1、私密小屋；2、心动连线；3、CP小屋）
    @Ignore @SerialName("private_room_id") val privateRoomId: Int? = null,
    @Ignore @SerialName("in_blacklist") val inRoomBlackList: Boolean? = null,
    @Ignore @SerialName("is_admin") val isRoomAdmin: Boolean? = null,
    @Ignore @SerialName("is_coin_trade") override val isCoinTrade: Boolean = false, // 币⬆️
    @Ignore @SerialName("is_in_audio_room") val isInAudioRoom: Boolean? = null,
    //该用户当前所在语音房id
    @Ignore @SerialName("room_id") val roomId: Int? = null,
    @Ignore @SerialName("show_online_duration_task") val hasOnlineTask: Boolean? = null, // 是否显示在线时长任务挂件
    @Ignore @SerialName("online_duration_task_introduce") val onlineTaskRule: List<String>? = null, // 时长任务规则
    @Ignore @SerialName("hide_cp_public_billboard") val hideCpBillboard: Boolean? = null, // 是否隐藏cp榜单挂件
    @Ignore @SerialName("cp_public_billboard_url") val cpBillboardUrl: String? = null, // cp榜单h5
    @Ignore @SerialName("register_timestamp") val registerTimeStamp: Long = 0, // 注册时间
    @Ignore @SerialName("request_ip") val ip: String = "", // ip
    @Ignore @SerialName("is_newbie") val isNewbie: Boolean = false, // ip
    @Ignore @SerialName("subscript_msg") override var subScriptMsg: String? = "",
    @Ignore @SerialName("is_video_hqu") override val isVideoHqu: Boolean = false,
    @Ignore @SerialName("video_show") override val videoShow: MediaInfo? = null,
    @Ignore @SerialName("video_guide_hint") override val videoGuideHint: String? = "",
    @Ignore @SerialName("video_guide_sub_hint") override val videoGuideSubHint: String? = "",
    @Ignore @SerialName("video_hi_script") override val videoHiScript: String? = "",
    @Ignore @SerialName("video_can_free_chat") override val canFreeVideoChat: Boolean = false,
    @Ignore override var level: Int = 0,
    @Ignore @SerialName("video_ati_hint") override val videoAtiHint: String? = null,
    @Ignore @SerialName("video_ati_label") override val videoAtiLabel: String? = null,
    @Ignore @SerialName("video_ati_open") override val videoAtiOpen: Boolean = false,
    @Ignore @SerialName("task_icon") override val taskIcon: String? = null,
    @Ignore var relationships: Relaionships? = null,//亲友团关系
    @Ignore var content: String? = null,//亲友团飘屏消息
    @Ignore @SerialName("visible_activities") override val activities: List<T>? = null,
    @Ignore @SerialName("chat_bubble") override var bubble: ChatBubble? = null,
    @Ignore @SerialName("private_room_encourage_hint") override val privateRoomPendentHint: String? = null,
    @Ignore @SerialName("withdrawable_value") override val withdrawableValue: String? = null,
    @Ignore @SerialName("withdrawable_unit") override val withdrawableUnit: String? = null,
    @Ignore @SerialName("hqu_type") override val hquType: Int = 0, //0 非主播、1 普通主播、2 台湾素人主播
    @Ignore @SerialName("city_name") override val cityName: String = "",
    @Ignore @SerialName("ip_region") val ip_region: String = "",
    @Ignore @SerialName("is_same_city") val isSameCity: Boolean = false,
    @Ignore @SerialName("ever_has_cp") val everHasCp: Boolean = false,
    @Ignore @SerialName("is_anonymous") override var isAnonymous: Boolean = false,
    @Ignore @SerialName("country_flag") override var countryFlag: String = "",
    @Ignore @SerialName("profile_medal") val profileMedal: ProfileMedal = ProfileMedal.EMPTY,
    @Ignore @SerialName("distance_label") val distance: String = "",
    @Ignore @SerialName("can_create_room") val canCreateRoom: Boolean = false,
    @Ignore @SerialName("audioroom") val room: RoomInfo? = null,
    //2.36.0 增加在主页组CP列表的roomInfo, 和上面的roomInfo没区别, 只是因为key不一样加了一个
    @Ignore @SerialName("room") val cpListRoom: NewRoomInfo? = null,
    @Ignore @SerialName("can_create_textroom") val canCreateTextRoom: Boolean = false,
    @Ignore @SerialName("textroom") val textRoom: RoomInfo? = null,
    @Ignore @SerialName("together_mic_in_private_room") val togetherPrivateRoomInMic: Boolean = false,
    @Ignore override val career: String = "",
    @Ignore @SerialName("location_label") override val locationLabel: String = "",
    @Ignore @SerialName("friend_cnt") override val friendCnt: Int = 0,
    @Ignore @SerialName("native_region") override val nativeRegion: Int = 0,
    @Ignore @SerialName("native_profile") override var nativeProfile: NativeProfile? = null,
    @Ignore @SerialName("intimate_score") override val intimateScore: Int = 0,
    @Ignore @SerialName("relation_id") val relation_id: Int = 0,
    @Ignore @SerialName("gift_wall_stats") override val giftWallStats: GiftWallBean = GiftWallBean.EMPTY,
    @Ignore @SerialName("guard_info") val guardInfo: GuardianExtra = GuardianExtra.EMPTY,
    @Ignore @SerialName("exp_level_info") val expLevelInfo: ExpLevelInfo = ExpLevelInfo.EMPTY,
    //麦位声纹
    @Ignore @SerialName("voiceprint") val voiceprint: String = "",
    //语音房资料卡背景
    @Ignore @SerialName("background") override val background: String = "",
    //进场横幅
    @Ignore @SerialName("entrance_banner") override val entranceBanner: String? = null,
    @Ignore @SerialName("can_free_chat") val canFreeChat: Boolean = false,
    @Ignore @SerialName("starsign") val starsign: String = "",
    @Ignore @SerialName("have_certified") val hasCertified: Boolean = false,
    @Ignore @SerialName("default_cp_rule_url") val cpRuleUrl: String? = null,
    @Ignore @SerialName("is_first_class_user") val isFirstClassUser: Boolean = false,
    @Ignore @SerialName("first_class_user_no") val firstClassUserNo: String = "",
    @Ignore @SerialName("colorfulNicknameGradient") val _colorList: List<String>? = null,
    @Ignore @SerialName("cp_room") override var cpRoomInfo: CpHouseBasicInfo? = null,
    @Ignore @SerialName("ring") override val ringInfo: WeddingRing? = null,
    @Ignore @SerialName("cp_card_effect") override val cpCardRingInfo: CpCardEffectInfo? = null,
) : MutableUser, IAdapterItemId {

    /**
     * 只做特殊标记，没有任何实际意义
     */
    @Ignore
    @IgnoredOnParcel
    @Transient
    var innerFlag: Short = 0

    constructor() : this("")

    override fun getItemId(): Long {
        return id.toLong()
    }

    override fun toString(): String {
        return "AppUser(id='$id', nickname='$nickname', gender=$gender, birthday='$birthday', age=$age, avatarUrl='$avatarUrl', publicId='$publicId', regionReason=$regionReason, regionReasonLabel='$regionReasonLabel', shortIntro='$shortIntro', isVip=$isVip, balance=$balance, isHighQuality=$isHighQuality, cpId=${cp?.id}, tribeId=${tribe?.id}, roomId=${roomId}, currentAudioRoom:${currentRoomId})"
    }

    fun colorString(): String {
        return colorList?.joinToString { "," } ?: ""
    }

}

val AppUser.displayRegion
    get() = listOf(regionLabel, regionReasonLabel).filter { it.isNotEmpty() }.joinToString(separator = "丨")

val AppUser.displayTagList: List<String>
    get() = buildList {
        if (height == 0) {
            val tags = attractiveFlags.take(3).map { it.label }
            addAll(tags)
        } else {
            add("${height}cm")
            addAll(attractiveFlags.take(2).map { it.label })
        }
    }

@Serializable
@Parcelize
data class Tag(val name: String) : Parcelable

@Serializable
@Parcelize
data class Relaionships(val intimacy: String, val tag: Tag) : Parcelable

val AppUser.hasRelationship: Boolean
    get() = relationships != null

val AttractiveFlagColors = arrayOf(0xFF8B86BE, 0xFFDEB0BD, 0xFFECB761, 0xFFCBD690, 0xFF86ABBA)

@Parcelize
@Serializable
data class AttractiveFlag(
    val icon: Int, val id: Int, val label: String,
    @SerialName("img_url")
    val url: String? = null,
) : Parcelable

@Parcelize
@Serializable
data class Album(
    val url: String = "", val width: Int = 0, val height: Int = 0, val size: Int = 0,
) : Parcelable {

    @IgnoredOnParcel
    var userId: String = ""

}

val INVALID_USER = AppUser(id = "-1", followed = true, needFriendRelation = false)

fun User.isInvalid() = this.id.isBlank() || this.id == INVALID_USER.id

fun User.isEmpty() = this.nickname.isBlank() && this.birthday.isBlank() && this.avatarUrl.isBlank() && this.gender == 0


fun AppUser.copyUser(user: User): AppUser {
    return copy(
        nickname = user.nickname,
        avatarUrl = user.avatarUrl,
        avatarFrame = user.avatarFrame,
        medal = user.medal,
        medalList = user.medalList,
        age = user.age,
        gender = user.gender,
        isVip = user.isVip,
        publicCP = user.publicCP,
        cpRoomInfo = user.cpRoomInfo,
        cpExtraInfo = user.cpExtraInfo,
        nativeProfile = user.nativeProfile,
        background = user.background,
        ringInfo = user.ringInfo,
        cpCardRingInfo = cpCardRingInfo,
        _colorList = user.colorList
    )
}

@Parcelize
@Serializable
data class UserInfo(
    @SerialName("userid") var id: String = "",
    var nickname: String = "",
    var gender: Int = 0,
    var age: Int = 0,
    val height: Int = 0,
    @SerialName("short_intro") val shortIntro: String = "",
    val birthday: String = "",
    @SerialName("attractive_tags") val attractiveFlags: List<AttractiveFlag> = emptyList(),
    @SerialName("avatar_url") var avatarUrl: String = "",
    @SerialName("online_status") var onlineStatus: Int = 1,
    @SerialName("online_time") var onlineTime: String = "",
    @SerialName("public_id") var publicId: String = "",
    @SerialName("region_label") var regionLabel: String = "",
    @SerialName("region_reason") var regionReason: String = "",
    @SerialName("region_reason_label") var regionReasonLabel: String = "",
    @SerialName("relation_id") var relationId: Int = 0,
    @SerialName("i_have_followed") var followed: Boolean = false,
    @SerialName("album") var album: List<Album> = emptyList(),
    @SerialName("balance") var balance: Int = 0,
    @SerialName("is_member") var isVip: Boolean = false,
    @SerialName("audioroom") var room: RoomInfo? = null,
    @SerialName("city_name") val cityName: String = "",
    @SerialName("distance_label") val distance: String = "",
    @SerialName("i_follow_back")
    var iFollowBack: Boolean = false,
    var hasInvited: Boolean = false,
) : IAdapterItemId, Parcelable {

    override fun getItemId(): Long {
        return id.toLong()
    }

    @IgnoredOnParcel
    val userId: Int = id.toIntOrNull() ?: 0
}

val UserInfo.isRegionVisible: Boolean
    get() = regionLabel.isNotEmpty() && regionReason.isNotEmpty() && (sUser.gender == 2 || userId == sUser.userId)

@Parcelize
@Serializable
data class RoomInfo(
    @SerialName("id") var roomId: Int = 0,
    @SerialName("title") var roomName: String = "",
) : Parcelable

@Parcelize
@Serializable
data class NewRoomInfo(
    @SerialName("id") var roomId: Int = 0,
    @SerialName("title") var roomName: String = "",
    @SerialName("room_locked") var room_locked: Boolean = false,
    @SerialName("text_only") var text_only: Boolean = false,
) : Parcelable, IAdapterItemId {
    override fun getItemId(): Long {
        return roomId.toLong()
    }
}

@Serializable
data class OnlineList(
    @SerialName("users") val list: List<UserOnline>,
)

@Serializable
data class UserOnline(
    @SerialName("userid") val id: String,
    @SerialName("online_status") val status: Int,
    @SerialName("is_member")
    val isVip: Boolean,
    val level: Int,
    @SerialName("intimate_score") val intimateScore: Int = 0,
    @SerialName("city_name") val cityName: String = "",
    @SerialName("in_same_city") val isSameCity: Boolean = false,
    //2.40.0 新增是否为币商
    @SerialName("is_coin_trade") val isCoinTrade: Boolean = false,
    @SerialName("room_id") val roomId: Int = 0,
)

@Parcelize
@Serializable
data class Medal(
    val icon: String = "",
    val width: Int = 0,
    val height: Int = 0,
    @SerialName("product_series") val type: Int = 0,
) : Parcelable {
    companion object {
        val EMPTY = Medal()
    }
}

@Serializable
@Parcelize
data class ProfileMedal(val medal: Medal = Medal.EMPTY, val t: Int = 0) : Parcelable {
    companion object {
        val EMPTY = ProfileMedal()
    }
}

val UserInfo.isBoy: Boolean
    get() = gender != 2
val UserInfo.signatureContent: String
    get() = shortIntro.takeIf {
        it.isNotBlank()
    } ?: app.getString(if (isBoy) R.string.他很懒什么也没留下 else R.string.她很懒什么也没留下)

fun AppUser.toUserInfo(): UserInfo {
    return UserInfo(
        id = id,
        nickname = nickname,
        gender = gender,
        age = age,
        height = height,
        shortIntro = signatureContent,
        birthday = birthday,
        attractiveFlags = attractiveFlags,
        avatarUrl = avatarUrl,
        onlineStatus = onlineStatus,
        publicId = publicId,
        regionLabel = regionLabel,
        regionReason = regionReason.toString(),
        regionReasonLabel = regionReasonLabel,
        followed = followed,
        album = albumList,
        balance = balance,
    )
}

fun UserInfo.toAppUser(): AppUser {
    return AppUser(
        id,
        nickname,
        gender,
        birthday,
        age,
        0,
        avatarUrl,
        onlineStatus = onlineStatus,
        publicId = publicId,
        regionLabel = regionLabel,
        regionReasonLabel = regionReason,
        followed = followed,
        albumList = album,
        balance = balance,
        isVip = isVip,
        cityName = cityName,
    )
}

@Serializable
@Parcelize
data class GiftWallBean(
    @SerialName("light_up_cnt")
    val lightUpCnt: Int = 0,
    @SerialName("total_cnt")
    val totalCnt: Int = 0,
) : Parcelable {
    companion object {
        val EMPTY = GiftWallBean()
    }
}

val User.isEmptyProfile: Boolean
    get() = height <= 0 && attractiveFlags.isEmpty() && albumList.isEmpty()

fun TextView.loadAgeGender(isBoy: Boolean, age: Int) {
    this.text = age.toString()
    if (isBoy) {
        setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.ic_gender_male, 0, 0, 0)
        setBackgroundResource(R.drawable.shape_background_blue100_radius_30)
    } else {
        setCompoundDrawablesRelativeWithIntrinsicBounds(R.drawable.ic_gender_female, 0, 0, 0)
        setBackgroundResource(R.drawable.shape_background_pink100_radius_30)
    }
}

fun ImageView.setGender(isBoy: Boolean) {
    if (isBoy) {
        setImageResource(R.drawable.ic_gender_male)
        setBackgroundResource(R.drawable.shape_background_blue100_radius_30)
    } else {
        setImageResource(R.drawable.ic_gender_female)
        setBackgroundResource(R.drawable.shape_background_pink100_radius_30)
    }
}

@Parcelize
@Serializable
data class CpExtraInfo(
    @SerialName("cp_value") val cpValue: Int = 0,
    @SerialName("level_info") val levelInfo: LevelInfo? = null,
    @SerialName("togather_days") val togatherDays: String = "",
    @SerialName("has_next_level") val hasNextLevel: Boolean = true,
) : Parcelable

@Parcelize
@Serializable
data class LevelInfo(
    @SerialName("footer_img_url") val footerImgUrl: String = "",
    @SerialName("header_img_url") val headerImgUrl: String = "",
    @SerialName("level") val level: Int = 1,
    @SerialName("normal_img_url") val normalImgUrl: String = "",
    @SerialName("small_img_url") val smallImgUrl: String = "",
    @SerialName("title") val title: String = "",
    @SerialName("has_next_level") val hasNextLevel: Boolean = false,
) : Parcelable

@Parcelize
@Serializable
data class WeddingRing(
    @SerialName("id") val id: Int = 1,
    @SerialName("name") val name: String = "",
    @SerialName("icon") val icon: String = "",
    @SerialName("effect_file") val effect_file: String = "",
) : Parcelable

@Parcelize
@Serializable
data class CpCardEffectInfo(
    @SerialName("file_url") val fileUrl: String = "",
    @SerialName("link") val link: String = "",
) : Parcelable

fun cpExtraInfoByUrl(url: String?): CpExtraInfo? {
    return url?.takeIf {
        it.isNotEmpty()
    }?.let {
        CpExtraInfo(levelInfo = LevelInfo(smallImgUrl = it))
    }
}

val String?.cpExtraInfo: CpExtraInfo?
    get() = cpExtraInfoByUrl(this)


private const val PIC_SCALE_DENSITY = 3 // 设计图气泡素材设备像素密度

@Parcelize
@Serializable
data class ChatBubble(
    @SerialName("font_color") val fontColor: String = "",
    @SerialName("left_img") val leftImg: String = "",
    @SerialName("padding_bottom") val paddingBottom: Int = 18,
    @SerialName("padding_left") val paddingLeft: Int = 22,
    @SerialName("padding_right") val paddingRight: Int = 22,
    @SerialName("padding_top") val paddingTop: Int = 18,
    @SerialName("right_img") val rightImg: String = "",
    @SerialName("left_corner_imgs") val startImages: List<String>? = null,
    @SerialName("right_corner_imgs") val endImages: List<String>? = null,
) : Parcelable {

    @IgnoredOnParcel
    @Transient
    val leftPadding = Rect(paddingLeft, paddingTop, paddingRight, paddingBottom).times(PIC_SCALE_DENSITY)

    @IgnoredOnParcel
    @Transient
    val rightPadding = Rect(paddingRight, paddingTop, paddingLeft, paddingBottom).times(PIC_SCALE_DENSITY)
}