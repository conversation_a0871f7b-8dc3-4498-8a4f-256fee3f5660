package com.qyqy.ucoo.account

import android.content.Intent
import android.os.SystemClock
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.ProcessLifecycleOwner
import com.google.firebase.crashlytics.ktx.crashlytics
import com.google.firebase.ktx.Firebase
import com.overseas.common.ext.asLifecycleOwner
import com.overseas.common.ext.launch
import com.overseas.common.ext.lifecycleFlow
import com.overseas.common.utils.ConcurrencyShare
import com.qyqy.cupid.config.ChatListConfig
import com.qyqy.cupid.utils.CupidFamilyManager
import com.qyqy.ucoo.ActivityLifecycle
import com.qyqy.ucoo.AppUserPartition
import com.qyqy.ucoo.InValidCoroutineScope

import com.qyqy.ucoo.R
import com.qyqy.ucoo.app
import com.qyqy.ucoo.appCoroutineScope
import com.qyqy.ucoo.compose.map
import com.qyqy.ucoo.config.AnonymousConfig
import com.qyqy.ucoo.glide.ninepatch.NinePathLoader
import com.qyqy.ucoo.http.ReSignInException
import com.qyqy.ucoo.http.TokenInterceptor
import com.qyqy.ucoo.http.createApi
import com.qyqy.ucoo.http.runApiCatching
import com.qyqy.ucoo.im.IMManager
import com.qyqy.ucoo.im.compat.IMCompatCore
import com.qyqy.ucoo.im.compat.IMCompatListener
import com.qyqy.ucoo.isProd
import com.qyqy.ucoo.isRelease
import com.qyqy.ucoo.login.LoginChannelType
import com.qyqy.ucoo.login.LoginEvent
import com.qyqy.ucoo.login.LoginReason
import com.qyqy.ucoo.login.third.showWarmTipDialog
import com.qyqy.ucoo.sIsSignIn
import com.qyqy.ucoo.sUser
import com.qyqy.ucoo.setting.SettingsRepository
import com.qyqy.ucoo.setting.account_bind.AccountBindActivity
import com.qyqy.ucoo.user.UserApi
import com.qyqy.ucoo.user.UserManager
import com.qyqy.ucoo.utils.UCOOTribeManager
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.NonCancellable
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.SharedFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.distinctUntilChangedBy
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.flowOf
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.sync.Mutex
import kotlinx.coroutines.sync.withLock
import kotlinx.coroutines.withContext
import kotlinx.serialization.json.JsonObject
import java.util.concurrent.TimeUnit
import kotlin.coroutines.EmptyCoroutineContext

data class AppAccountInfo(
    val userInfo: AppUser,
    val tokenInfo: AccountToken,
)

class AppAccountManager(private val isPreviewOnCompose: Boolean = false) {

    companion object {
        private val INTERVAL_REFRESH_BIND_STATE = TimeUnit.MINUTES.toMillis(5)

    }

    private val accountRepository = AccountRepository()

    private val accountInfoFlow: MutableStateFlow<AppAccountInfo?>

    private val _loginEventFlow = MutableSharedFlow<LoginEvent>()

    private val mutex = Mutex()

    private val _userLevelFlow: MutableStateFlow<UserLevel> = MutableStateFlow(UserLevel())
    val userLevelFlow = _userLevelFlow.asStateFlow()

    private val _accountFlow = MutableStateFlow(UserAccount(INVALID_USER, UCOOAccount(), false, false))
    val accountFlow = _accountFlow.asStateFlow()

    private var coldStartCondition = true

    /**
     * 登录事件flow，目前不支持跨进程
     */
    val loginEventFlow: SharedFlow<LoginEvent> = _loginEventFlow.asSharedFlow()

    private val _accountBindFlow: MutableStateFlow<AccountBindInfo> = MutableStateFlow(AccountBindInfo.EMPTY)
    val accountBindFlow = _accountBindFlow.asStateFlow()

    /**
     * 注册阶段暂存的token，主要使用到了userId, authToken, native_region
     */
    private var tempToken: AccountToken? = null

    private var _loginCoroutineScope: CoroutineScope? = null

    init {
        // 初始化读取token
        var tokenInfo = if (isPreviewOnCompose) null else accountRepository.syncAccountToken()
        if (tokenInfo != null) { // 自动登录进入首页
            // 从缓存中读取的token，需要去除rcWriteEnable, timWriteEnable, readFromTim
            tokenInfo = tokenInfo.copy(rcWriteEnable = null, timWriteEnable = null, readFromTim = null)
            val tempUserInfo = AppUser(tokenInfo.userId).also {
                it.innerFlag = Short.MIN_VALUE
            }
            _loginCoroutineScope = createLoginCoroutineScope()
            accountInfoFlow = MutableStateFlow(AppAccountInfo(tempUserInfo, tokenInfo))
            appCoroutineScope.launch(Dispatchers.IO) {
                val user = accountRepository.syncAccountUser()
                if (user != null) {
                    accountInfoFlow.update {
                        if (it?.userInfo?.innerFlag == Short.MIN_VALUE) {
                            it.copy(userInfo = user)
                        } else { // 表示获取服务端用户信息比读取本地信息快，则不更新
                            it
                        }
                    }
                }
            }
            refreshSelfUserByRemote()
        } else { // 表示未登录
            accountInfoFlow = MutableStateFlow(null)
        }
    }

    /**
     * 用户信息flow, 有用户信息就表示已经登录
     */
    val userFlow: StateFlow<User> = accountInfoFlow.map(appCoroutineScope) {
        it?.userInfo ?: INVALID_USER
    }

    /**
     * token信息flow, 只有登录的账户才有token
     * 用户必须要完善资料之后，才算登录态
     */
    val tokenFlow: StateFlow<AccountToken?> = accountInfoFlow.map(appCoroutineScope) {
        it?.tokenInfo
    }

    /**
     * account变化，以id去重，id发生变化才可以观察到
     * 已登录不为null
     * 未登录为null
     */
    val accountDistinctIdFlow: Flow<AppAccountInfo?> = accountInfoFlow.distinctUntilChangedBy {
        it?.userInfo?.id
    }

    val isLoginFlow: StateFlow<Boolean> = accountInfoFlow.map(appCoroutineScope) {
        it != null
    }

    val isLogin: Boolean get() = accountInfoFlow.value != null

    val accountTokenWithTemp: AccountToken?
        get() = (accountInfoFlow.value?.tokenInfo ?: tempToken)

    fun init(): AppAccountManager {
        if (isPreviewOnCompose) {
            return this
        }

        appCoroutineScope.launch {
            SettingsRepository.fetchSystemSettings()
        }

        //登录后查询绑定状态
        appCoroutineScope.launch {
            accountDistinctIdFlow.collectLatest { account ->
                if (account != null) {
                    refreshBindState()
                    refreshUserLevelInfo()
                    AnonymousConfig.init()
                    ChatListConfig.init()
                } else {
                    AnonymousConfig.clear()
                    ChatListConfig.clear()
                }
            }
        }

        appCoroutineScope.launch {
            accountBindFlow
                .filter { it.needBindOutsideAccount }
                .distinctUntilChanged()
                .collectLatest { info ->
                    //延迟1s,因为会出现请求过快导致挂载的Activity被finish掉
                    delay(1000)

                    if (AppUserPartition.isCupid) {
//                        ActivityLifecycle.startTopActivity?.let { act ->
//                            SimpleComposeDialog(act, LayoutParams.WRAP_CONTENT) { dialog ->
////                                dialog.setCancelable(false)
////                                dialog.setCanceledOnTouchOutside(false)
//                                AccountBindContent(info) {
//                                    dialog.dismiss()
//                                }
//                            }.show()
//                        }
                    } else {
                        ActivityLifecycle.startTopActivity?.let { act ->
                            act.asLifecycleOwner.launch {
                                showWarmTipDialog(
                                    act,
                                    info.unboundOutsideAccountTips,
                                    act.getString(R.string.bind_now),
                                    true
                                ).also { confirmed ->
                                    if (confirmed) {
                                        ActivityLifecycle.startTopActivity?.let { curAct ->
                                            curAct.startActivity(Intent(curAct, AccountBindActivity::class.java))
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
        }

        appCoroutineScope.launch {
            isLoginFlow.flatMapLatest {
                if (it) {
                    ProcessLifecycleOwner.get().lifecycle.lifecycleFlow().map { event ->
                        event.targetState.isAtLeast(Lifecycle.State.STARTED)
                    }
                } else {
                    flowOf(null)
                }
            }.debounce(250)
                .distinctUntilChanged()
                .collectLatest { atStarted ->
                    atStarted ?: return@collectLatest
                    val isColdStart = coldStartCondition
                    if (atStarted) {
                        refreshBindState(false)
                        coldStartCondition = false
                    }
                    accountRepository.appStatusSync(atStarted, isColdStart)
                }
        }

        IMCompatCore.addIMListener(object : IMCompatListener {

            override fun onClientOffline(reason: LoginReason) {
                appCoroutineScope.launch {
                    logout(reason)
                }
            }
        })

        return this
    }

    fun obtainLoginCoroutineScope(): CoroutineScope {
        return _loginCoroutineScope ?: InValidCoroutineScope
    }

    private fun createLoginCoroutineScope(): CoroutineScope {
        val environmentContext = if (isProd && isRelease) {
            CoroutineExceptionHandler { _, throwable ->
                Firebase.crashlytics.recordException(throwable)
            }
        } else {
            EmptyCoroutineContext
        }
        return CoroutineScope(SupervisorJob() + Dispatchers.Main.immediate + environmentContext)
    }

    fun refreshUserLevelInfo() {
        appCoroutineScope.launch {
            runApiCatching { userApi.getUserLevelInfo() }
                .onSuccess {
                    _userLevelFlow.emit(it)
                }
        }
    }

    fun refreshUserAccount() {
        appCoroutineScope.launch {
            accountRepository.fetchUserAccount().onSuccess {
                _accountFlow.emit(it)
            }
        }
    }

    /**
     * 暂存token
     */
    fun stageToken(token: AccountToken) {
        tempToken = token
    }

    /**
     * 登录成功
     */
    suspend fun signIn(token: AccountToken) {
        if (token.isInvalid()) {
            return
        }
        withContext(Dispatchers.IO) {
            accountRepository.saveAccountToken(token)
            IMManager.saveIMEnvConfig(token)
            val tempUserInfo = AppUser(token.userId).also {
                it.innerFlag = Short.MIN_VALUE
            }
            _loginCoroutineScope = createLoginCoroutineScope()
            accountInfoFlow.value = AppAccountInfo(tempUserInfo, token)
            tempToken = null
            TokenInterceptor.setTempToken(null)
            _loginEventFlow.emit(LoginEvent.SignIn)
        }
        refreshSelfUserByRemote()
    }

    /**
     * 同步退出登录，不建议在主线程调用，有锁
     */
    fun syncLogout(reason: LoginReason) {
        runBlocking {
            logout(reason)
        }
    }

    /**
     * 退出登录
     */
    suspend fun logout(reason: LoginReason) {
        if (isLogin) {
            mutex.withLock {
                val tokenInfo = accountInfoFlow.value?.tokenInfo
                if (tokenInfo != null) {
                    removeAccount(tokenInfo, reason)
                }
            }
        }
    }

    suspend fun logOff() {
        accountRepository.logOff()
        withContext(NonCancellable) {
            logout(LoginReason.Exit)
        }
    }

    @kotlin.jvm.Throws(ReSignInException::class)
    fun syncRefreshToken(): String {
        val refreshToken = accountInfoFlow.value?.tokenInfo?.refreshToken ?: throw ReSignInException()
        return accountRepository.syncRefreshToken(refreshToken).getOrThrow().also {
            val tokenInfo = accountInfoFlow.value?.tokenInfo ?: throw ReSignInException()
            val newToken = tokenInfo.copy(authToken = it.authToken, refreshToken = it.refreshToken)
            accountRepository.saveAccountToken(newToken)
            accountInfoFlow.update { old ->
                if (old == null) {
                    throw ReSignInException()
                }
                old.copy(tokenInfo = newToken)
            }
        }.refreshToken
    }

    /**
     * 更新用户信息缓存
     */
    fun updateSelfUser(block: MutableUser.() -> Unit): Boolean {
        val user = accountInfoFlow.value?.userInfo ?: return false
        val newUser = user.copy().apply(block)
        if (user == newUser) {
            return false
        }
        appCoroutineScope.launch {
            setUser(newUser)
        }
        return true
    }

    fun updateCpByUser(user: AppUser) {
        if (user.isSelf) {
            return
        }
        if (sUser.cp?.id == user.id && !user.isCp) {
            app.accountManager.updateSelfUser {
                cp = null
            }
        } else if (user.isCp) {
            app.accountManager.updateSelfUser {
                cp = user
            }
        }
    }

    fun setSelfUser(user: AppUser) {
        appCoroutineScope.launch {
            setUser(user)
        }
    }

    fun refreshSelfUserByRemote() {
        appCoroutineScope.launch {
            ConcurrencyShare.globalInstance.joinPreviousOrRun("selfUserInfo") {
                UserManager.fetchSelfUser()
            }?.also {
                UCOOTribeManager.updateMyTribe(it.tribe?.copy(relationWithMe = 10))
                if (AppUserPartition.isCupid) {
                    CupidFamilyManager.refreshFamilyInfo(it.tribe?.id)
                }
                setUser(it)
                NinePathLoader.tryPreload(it.bubble)
            }
        }
    }

    private suspend fun removeAccount(tokenInfo: AccountToken, reason: LoginReason) {
        TokenInterceptor.setTempToken(tokenInfo.authToken)
        withContext(Dispatchers.IO) {
            accountRepository.removeAccount()
        }
        _loginCoroutineScope?.cancel()
        _loginCoroutineScope = null
        accountInfoFlow.emit(null)
        _loginEventFlow.emit(LoginEvent.Logout(reason))
    }

    /**
     * 设置用户信息缓存
     */
    private suspend fun setUser(user: AppUser) {
        if (isLogin) {
            withContext(Dispatchers.IO) {
                accountRepository.saveAccountUser(user)
            }
            accountInfoFlow.update {
                it?.copy(userInfo = user)
            }
        }
    }

    private val userApi by lazy { createApi<UserApi>() }

    /**
     * 修改在线状态
     */
    suspend fun updateOnlineStatus(status: Int): Result<JsonObject> {
        val result =
            runApiCatching { userApi.updateOnlineStatus(mapOf("is_hidden" to (status == ONLINE_STATUS_INVISIBLE).toString())) }
        if (result.isSuccess) {
            updateSelfUser {
                onlineStatus = status
            }
        }
        return result
    }

    fun commitAgreeAgreement() {
        appCoroutineScope.launch {
            runApiCatching {
                userApi.agreeAgreement(mapOf("t" to "1"))
            }
        }
    }

    /**
     * 同意工会协议
     */
    fun commitAgreeAgreementUnion() {
        appCoroutineScope.launch {
            runApiCatching {
                userApi.agreeAgreement(mapOf("t" to "2"))
            }
        }
    }

    fun agreeUserAgreement(type: Int) {
        appCoroutineScope.launch {
            runApiCatching {
                userApi.agreeAgreement(mapOf("t" to type.toString()))
            }
        }
    }

    private var lastRefreshMills = 0L
    fun refreshBindState(force: Boolean = true) {
        if (!sIsSignIn) {
            return
        }
        if (force.not() && SystemClock.elapsedRealtime() - lastRefreshMills < INTERVAL_REFRESH_BIND_STATE) {
            return
        }
        //已有三方账户绑定
        if (_accountBindFlow.value.hasAnyBind) {
            return
        }
        appCoroutineScope.launch {
            accountRepository.queryThirdAccountBindState().getOrNull()?.let {
                _accountBindFlow.emit(it)
            }
            lastRefreshMills = SystemClock.elapsedRealtime()
        }
    }

    suspend fun bindThirdAccount(@LoginChannelType channel: Int, token: String) =
        accountRepository.bindThirdAccount(channel, token).also {
            it.getOrNull()?.let { token ->
                signIn(token)
            }
        }

    private fun AccountToken.isInvalid() =
        !finishFillInfo || this.authToken.isBlank() || this.refreshToken.isBlank() || this.rcToken.isBlank() || this.userId.isBlank()
}